import React, { createContext, useContext, useEffect, useState } from 'react'
import { tauriService } from '@/services/tauriService'

type Theme = 'dark' | 'light' | 'system'

type ThemeProviderProps = {
  children: React.ReactNode
  defaultTheme?: Theme
  storageKey?: string
}

type ThemeProviderState = {
  theme: Theme
  setTheme: (theme: Theme) => Promise<void>
  isLoaded: boolean
}

const initialState: ThemeProviderState = {
  theme: 'system',
  setTheme: async () => {},
  isLoaded: false,
}

const ThemeProviderContext = createContext<ThemeProviderState>(initialState)

export function ThemeProvider({
  children,
  defaultTheme = 'system',
  storageKey = 'vite-ui-theme',
  ...props
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(defaultTheme)
  const [isLoaded, setIsLoaded] = useState(false)

  // Load theme from backend on mount
  useEffect(() => {
    const loadTheme = async () => {
      try {
        const savedTheme = await tauriService.getAppState(storageKey)
        if (savedTheme && ['dark', 'light', 'system'].includes(savedTheme)) {
          setTheme(savedTheme as Theme)
        }
      } catch (error) {
        console.warn('Failed to load theme from backend, using default:', error)
        // Fallback to localStorage for backward compatibility
        const localTheme = localStorage.getItem(storageKey) as Theme
        if (localTheme) {
          setTheme(localTheme)
        }
      } finally {
        setIsLoaded(true)
      }
    }
    loadTheme()
  }, [storageKey])

  useEffect(() => {
    const root = window.document.documentElement

    root.classList.remove('light', 'dark')

    if (theme === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)')
        .matches
        ? 'dark'
        : 'light'

      root.classList.add(systemTheme)
      return
    }

    root.classList.add(theme)
  }, [theme])

  const value = {
    theme,
    isLoaded,
    setTheme: async (newTheme: Theme) => {
      try {
        await tauriService.setAppState(storageKey, newTheme)
        setTheme(newTheme)
      } catch (error) {
        console.warn('Failed to save theme to backend, falling back to localStorage:', error)
        // Fallback to localStorage for backward compatibility
        localStorage.setItem(storageKey, newTheme)
        setTheme(newTheme)
      }
    },
  }

  return (
    <ThemeProviderContext.Provider {...props} value={value}>
      {children}
    </ThemeProviderContext.Provider>
  )
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext)

  if (context === undefined)
    throw new Error('useTheme must be used within a ThemeProvider')

  return context
}