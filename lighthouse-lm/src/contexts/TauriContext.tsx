// Tauri Context - Provides Tauri service initialization and state management

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { tauriService } from '../services/tauriService';
import { authService } from '../services/authService';

interface TauriContextValue {
  isInitialized: boolean;
  isInitializing: boolean;
  error: string | null;
  reinitialize: () => Promise<void>;
}

const TauriContext = createContext<TauriContextValue | undefined>(undefined);

interface TauriProviderProps {
  children: ReactNode;
}

export const TauriProvider: React.FC<TauriProviderProps> = ({ children }) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const initializeTauri = async () => {
    if (isInitializing) return;
    
    setIsInitializing(true);
    setError(null);
    
    try {
      // Initialize Tauri service
      await tauriService.initialize();
      
      // Initialize auth service (will restore session if available)
      // Auth service initialization is handled in its constructor
      
      setIsInitialized(true);
      console.log('Tauri services initialized successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Failed to initialize Tauri services:', err);
    } finally {
      setIsInitializing(false);
    }
  };

  const reinitialize = async () => {
    setIsInitialized(false);
    await initializeTauri();
  };

  useEffect(() => {
    initializeTauri();
    
    // Cleanup function
    return () => {
      if (isInitialized) {
        try {
          tauriService.destroy();
        } catch (err) {
          console.error('Error during Tauri service cleanup:', err);
        }
      }
    };
  }, []);

  const value: TauriContextValue = {
    isInitialized,
    isInitializing,
    error,
    reinitialize,
  };

  return (
    <TauriContext.Provider value={value}>
      {children}
    </TauriContext.Provider>
  );
};

export const useTauri = (): TauriContextValue => {
  const context = useContext(TauriContext);
  if (context === undefined) {
    throw new Error('useTauri must be used within a TauriProvider');
  }
  return context;
};

// Hook for checking if Tauri is ready
export const useTauriReady = (): boolean => {
  const { isInitialized } = useTauri();
  return isInitialized;
};

// Hook for handling Tauri initialization errors
export const useTauriError = (): { error: string | null; retry: () => Promise<void> } => {
  const { error, reinitialize } = useTauri();
  return { error, retry: reinitialize };
};

export default TauriProvider;