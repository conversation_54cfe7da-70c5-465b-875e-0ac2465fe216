import React, { createContext, useContext, useState, useCallback, useEffect, useRef } from 'react';
import { Citation } from '../types/message';
import { useToast } from '../hooks/useToast';
import { lighthouseService, Source, ChatMessage, StudioDocument, AIContext as BackendAIContext } from '../services/lighthouseService';
import { useSourcesWithBackend } from '../hooks/useSourcesWithBackend';
import { useChatMessagesWithBackend } from '../hooks/useChatMessagesWithBackend';
import { useNotebooksWithBackend } from '../hooks/useNotebooksWithBackend';

interface SelectedSource extends Source {
  selected?: boolean;
}

interface ActiveDocument extends StudioDocument {
  lastModified?: Date;
}

interface AIContext extends BackendAIContext {
  capabilities?: string[];
  active?: boolean;
  lastSuggestion?: string;
}

interface NotebookContextType {
  // Source Management
  selectedSources: SelectedSource[];
  activeSource: SelectedSource | null;
  setActiveSource: (source: SelectedSource | null) => void;
  addSourceToContext: (source: SelectedSource) => void;
  removeSourceFromContext: (sourceId: string) => void;
  clearSourceContext: () => void;
  
  // Studio Management
  activeDocument: ActiveDocument | null;
  setActiveDocument: (doc: ActiveDocument | null) => void;
  studioContent: string;
  setStudioContent: (content: string) => void;
  studioMode: 'edit' | 'preview' | 'split';
  setStudioMode: (mode: 'edit' | 'preview' | 'split') => void;
  
  // Chat Integration
  chatContext: string;
  setChatContext: (context: string) => void;
  insertToChat: (content: string) => void;
  appendToChat: (content: string) => void;
  selectedCitation: Citation | null;
  setSelectedCitation: (citation: Citation | null) => void;
  
  // AI Integration
  aiContext: AIContext | null;
  setAIContext: (context: AIContext | null) => void;
  aiSuggestions: string[];
  addAISuggestion: (suggestion: string) => void;
  clearAISuggestions: () => void;
  
  // Cross-Component Communication
  sendToStudio: (content: string, type: 'Note' | 'Diagram' | 'Slide' | 'Code' | 'Markdown' | 'RichText') => void;
  sendToChat: (content: string, withContext?: boolean) => void;
  requestSourceAnalysis: (sourceId: string) => Promise<any>;
  requestAIAssistance: (prompt: string, context?: any) => Promise<string>;
  
  // Synchronization
  syncEnabled: boolean;
  setSyncEnabled: (enabled: boolean) => void;
  lastSyncTime: Date | null;
  syncStatus: 'idle' | 'syncing' | 'error';
  
  // Workspace State
  focusMode: 'chat' | 'sources' | 'studio' | 'split';
  setFocusMode: (mode: 'chat' | 'sources' | 'studio' | 'split') => void;
  isFullscreen: boolean;
  setIsFullscreen: (fullscreen: boolean) => void;
  
  // Backend Hooks
  sources: Source[];
  messages: ChatMessage[];
  isLoadingSources: boolean;
  isLoadingMessages: boolean;
  sendMessage: (content: string, options?: any) => Promise<ChatMessage | null>;
  addSource: (sourceData: any) => void;
  deleteSource: (sourceId: string) => void;
  
  // Events
  onSourceSelect: (handler: (source: SelectedSource) => void) => () => void;
  onStudioUpdate: (handler: (content: string) => void) => () => void;
  onChatMessage: (handler: (message: string) => void) => () => void;
  onAISuggestion: (handler: (suggestion: string) => void) => () => void;
}

const NotebookContext = createContext<NotebookContextType | undefined>(undefined);

export const useNotebookContext = () => {
  const context = useContext(NotebookContext);
  if (!context) {
    throw new Error('useNotebookContext must be used within NotebookProvider');
  }
  return context;
};

interface NotebookProviderProps {
  children: React.ReactNode;
  notebookId?: string;
}

export const NotebookProvider: React.FC<NotebookProviderProps> = ({ children, notebookId }) => {
  const { toast } = useToast();
  
  // Use backend hooks
  const {
    sources,
    isLoading: isLoadingSources,
    addSource,
    deleteSource,
    searchSources,
    subscribeToUpdates: subscribeToSourceUpdates,
  } = useSourcesWithBackend(notebookId);
  
  const {
    messages,
    isLoading: isLoadingMessages,
    sendMessage,
    editMessage,
    deleteMessage,
    regenerateMessage,
    addReaction,
    generateSuggestions,
    subscribeToUpdates: subscribeToChatUpdates,
  } = useChatMessagesWithBackend(notebookId);
  
  const {
    notebooks,
    getNotebook,
    updateNotebook,
    getAnalytics,
  } = useNotebooksWithBackend();
  
  // Source State
  const [selectedSources, setSelectedSources] = useState<SelectedSource[]>([]);
  const [activeSource, setActiveSource] = useState<SelectedSource | null>(null);
  
  // Studio State
  const [activeDocument, setActiveDocument] = useState<ActiveDocument | null>(null);
  const [studioContent, setStudioContent] = useState('');
  const [studioMode, setStudioMode] = useState<'edit' | 'preview' | 'split'>('edit');
  
  // Chat State
  const [chatContext, setChatContext] = useState('');
  const [selectedCitation, setSelectedCitation] = useState<Citation | null>(null);
  const chatInsertRef = useRef<(content: string) => void>();
  
  // AI State
  const [aiContext, setAIContext] = useState<AIContext | null>(null);
  const [aiSuggestions, setAISuggestions] = useState<string[]>([]);
  
  // Sync State
  const [syncEnabled, setSyncEnabled] = useState(true);
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'error'>('idle');
  
  // Workspace State
  const [focusMode, setFocusMode] = useState<'chat' | 'sources' | 'studio' | 'split'>('split');
  const [isFullscreen, setIsFullscreen] = useState(false);
  
  // Event Handlers Storage
  const eventHandlers = useRef<{
    sourceSelect: Set<(source: SelectedSource) => void>;
    studioUpdate: Set<(content: string) => void>;
    chatMessage: Set<(message: string) => void>;
    aiSuggestion: Set<(suggestion: string) => void>;
  }>({
    sourceSelect: new Set(),
    studioUpdate: new Set(),
    chatMessage: new Set(),
    aiSuggestion: new Set(),
  });
  
  // Set up real-time subscriptions
  useEffect(() => {
    if (!notebookId) return;
    
    let unsubscribeSources: (() => void) | undefined;
    let unsubscribeChat: (() => void) | undefined;
    
    const setupSubscriptions = async () => {
      // Subscribe to source updates
      unsubscribeSources = await subscribeToSourceUpdates((event) => {
        setLastSyncTime(new Date());
      });
      
      // Subscribe to chat updates
      unsubscribeChat = await subscribeToChatUpdates((event) => {
        setLastSyncTime(new Date());
      });
    };
    
    setupSubscriptions();
    
    return () => {
      unsubscribeSources?.();
      unsubscribeChat?.();
    };
  }, [notebookId, subscribeToSourceUpdates, subscribeToChatUpdates]);
  
  // Sync AI context with backend
  useEffect(() => {
    if (!notebookId || !syncEnabled) return;
    
    const syncContext = async () => {
      setSyncStatus('syncing');
      try {
        const context = await lighthouseService.syncContext(
          notebookId,
          selectedSources.map(s => s.id),
          activeDocument?.id,
          chatContext
        );
        
        setAIContext({
          ...context,
          capabilities: ['chat', 'analyze', 'generate', 'translate'],
          active: true,
        });
        
        setSyncStatus('idle');
        setLastSyncTime(new Date());
      } catch (error) {
        setSyncStatus('error');
      }
    };
    
    const debounceTimer = setTimeout(syncContext, 1000);
    return () => clearTimeout(debounceTimer);
  }, [notebookId, selectedSources, activeDocument, chatContext, syncEnabled]);
  
  // Source Management Functions
  const addSourceToContext = useCallback((source: SelectedSource) => {
    setSelectedSources(prev => {
      if (prev.find(s => s.id === source.id)) return prev;
      const updated = [...prev, source];
      
      // Notify listeners
      eventHandlers.current.sourceSelect.forEach(handler => handler(source));
      
      toast({
        title: "Source added",
        description: `"${source.title}" added to context`,
      });
      
      return updated;
    });
  }, [toast]);
  
  const removeSourceFromContext = useCallback((sourceId: string) => {
    setSelectedSources(prev => prev.filter(s => s.id !== sourceId));
  }, []);
  
  const clearSourceContext = useCallback(() => {
    setSelectedSources([]);
    setActiveSource(null);
  }, []);
  
  // Chat Functions
  const insertToChat = useCallback((content: string) => {
    chatInsertRef.current?.(content);
    setChatContext(prev => prev + '\n' + content);
  }, []);
  
  const appendToChat = useCallback((content: string) => {
    setChatContext(prev => prev + content);
  }, []);
  
  // AI Functions
  const addAISuggestion = useCallback((suggestion: string) => {
    setAISuggestions(prev => [...prev, suggestion]);
    eventHandlers.current.aiSuggestion.forEach(handler => handler(suggestion));
  }, []);
  
  const clearAISuggestions = useCallback(() => {
    setAISuggestions([]);
  }, []);
  
  // Cross-Component Communication
  const sendToStudio = useCallback(async (content: string, type: 'Note' | 'Diagram' | 'Slide' | 'Code' | 'Markdown' | 'RichText') => {
    if (!notebookId) return;
    
    try {
      const doc = await lighthouseService.createDocument(
        notebookId,
        `New ${type}`,
        content,
        type
      );
      
      setActiveDocument({
        ...doc,
        lastModified: new Date(doc.updated_at),
      });
      setStudioContent(content);
      setFocusMode('studio');
      
      eventHandlers.current.studioUpdate.forEach(handler => handler(content));
      
      toast({
        title: "Content sent to Studio",
        description: `Created new ${type.toLowerCase()} document`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create studio document",
        variant: "destructive",
      });
    }
  }, [notebookId, toast]);
  
  const sendToChat = useCallback(async (content: string, withContext = false) => {
    if (!notebookId) return;
    
    const messageContent = withContext 
      ? `${content}\n\nContext:\n${selectedSources.map(s => s.title).join(', ')}`
      : content;
    
    await sendMessage(messageContent, {
      sources: withContext ? selectedSources.map(s => s.id) : undefined,
    });
    
    eventHandlers.current.chatMessage.forEach(handler => handler(content));
    setFocusMode('chat');
  }, [notebookId, selectedSources, sendMessage]);
  
  const requestSourceAnalysis = useCallback(async (sourceId: string) => {
    if (!notebookId) return null;
    
    const source = sources.find(s => s.id === sourceId);
    if (!source) return null;
    
    const analysis = await sendMessage(
      `Please analyze this source: ${source.title}`,
      { sources: [sourceId] }
    );
    
    return analysis;
  }, [notebookId, sources, sendMessage]);
  
  const requestAIAssistance = useCallback(async (prompt: string, context?: any) => {
    if (!notebookId) return '';
    
    const suggestions = await generateSuggestions(prompt);
    if (suggestions.length > 0) {
      addAISuggestion(suggestions[0]);
      return suggestions[0];
    }
    
    return '';
  }, [notebookId, generateSuggestions, addAISuggestion]);
  
  // Event Subscription Functions
  const onSourceSelect = useCallback((handler: (source: SelectedSource) => void) => {
    eventHandlers.current.sourceSelect.add(handler);
    return () => {
      eventHandlers.current.sourceSelect.delete(handler);
    };
  }, []);
  
  const onStudioUpdate = useCallback((handler: (content: string) => void) => {
    eventHandlers.current.studioUpdate.add(handler);
    return () => {
      eventHandlers.current.studioUpdate.delete(handler);
    };
  }, []);
  
  const onChatMessage = useCallback((handler: (message: string) => void) => {
    eventHandlers.current.chatMessage.add(handler);
    return () => {
      eventHandlers.current.chatMessage.delete(handler);
    };
  }, []);
  
  const onAISuggestion = useCallback((handler: (suggestion: string) => void) => {
    eventHandlers.current.aiSuggestion.add(handler);
    return () => {
      eventHandlers.current.aiSuggestion.delete(handler);
    };
  }, []);
  
  const value: NotebookContextType = {
    // Source Management
    selectedSources,
    activeSource,
    setActiveSource,
    addSourceToContext,
    removeSourceFromContext,
    clearSourceContext,
    
    // Studio Management
    activeDocument,
    setActiveDocument,
    studioContent,
    setStudioContent,
    studioMode,
    setStudioMode,
    
    // Chat Integration
    chatContext,
    setChatContext,
    insertToChat,
    appendToChat,
    selectedCitation,
    setSelectedCitation,
    
    // AI Integration
    aiContext,
    setAIContext,
    aiSuggestions,
    addAISuggestion,
    clearAISuggestions,
    
    // Cross-Component Communication
    sendToStudio,
    sendToChat,
    requestSourceAnalysis,
    requestAIAssistance,
    
    // Synchronization
    syncEnabled,
    setSyncEnabled,
    lastSyncTime,
    syncStatus,
    
    // Workspace State
    focusMode,
    setFocusMode,
    isFullscreen,
    setIsFullscreen,
    
    // Backend Hooks
    sources,
    messages,
    isLoadingSources,
    isLoadingMessages,
    sendMessage,
    addSource,
    deleteSource,
    
    // Events
    onSourceSelect,
    onStudioUpdate,
    onChatMessage,
    onAISuggestion,
  };
  
  return (
    <NotebookContext.Provider value={value}>
      {children}
    </NotebookContext.Provider>
  );
};