import React, { useState, useCallback } from 'react';
import { Plus, Trash2, Save, <PERSON>, Settings, Move, GripVertical, Undo, <PERSON>o, Copy } from 'lucide-react';
import { Template, TemplateVariable, TemplateSection, VariableType } from './types';
import { useTemplateContext } from './TemplateProvider';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Textarea } from '../components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Switch } from '../components/ui/switch';
import { Separator } from '../components/ui/separator';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '../components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '../components/ui/alert-dialog';
import { useToast } from '../hooks/useToast';

interface TemplateEditorProps {
  template?: Template;
  onSave: (template: Template) => void;
  onCancel: () => void;
}

export const TemplateEditor: React.FC<TemplateEditorProps> = ({
  template,
  onSave,
  onCancel,
}) => {
  const {
    createTemplate,
    updateTemplate,
    categories,
    validateTemplate,
    previewTemplate,
    isLoading,
  } = useTemplateContext();
  
  const { toast } = useToast();
  
  const [formData, setFormData] = useState<Partial<Template>>(() => ({
    name: template?.name || '',
    description: template?.description || '',
    category: template?.category || '',
    contentType: template?.contentType || 'notebook',
    version: template?.version || '1.0.0',
    author: template?.author || 'User',
    tags: template?.tags || [],
    isPublic: template?.isPublic || false,
    variables: template?.variables || [],
    sections: template?.sections || [],
  }));
  
  const [activeTab, setActiveTab] = useState<'basic' | 'variables' | 'sections' | 'preview'>('basic');
  const [previewContent, setPreviewContent] = useState<string>('');
  const [previewVariables, setPreviewVariables] = useState<Record<string, any>>({});
  const [editingVariable, setEditingVariable] = useState<TemplateVariable | null>(null);
  const [editingSection, setEditingSection] = useState<TemplateSection | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Mark as changed when form data updates
  React.useEffect(() => {
    if (template) {
      setHasUnsavedChanges(true);
    }
  }, [formData, template]);

  // Generate default variable values for preview
  React.useEffect(() => {
    const defaultValues: Record<string, any> = {};
    formData.variables?.forEach(variable => {
      if (variable.defaultValue !== undefined) {
        defaultValues[variable.id] = variable.defaultValue;
      } else {
        switch (variable.type) {
          case 'text':
          case 'textarea':
          case 'url':
            defaultValues[variable.id] = variable.placeholder || `Sample ${variable.label}`;
            break;
          case 'number':
            defaultValues[variable.id] = 42;
            break;
          case 'date':
            defaultValues[variable.id] = new Date().toISOString().split('T')[0];
            break;
          case 'boolean':
            defaultValues[variable.id] = true;
            break;
          case 'select':
            defaultValues[variable.id] = variable.options?.[0] || 'Option 1';
            break;
          case 'multiselect':
            defaultValues[variable.id] = variable.options?.slice(0, 2) || ['Option 1', 'Option 2'];
            break;
        }
      }
    });
    setPreviewVariables(defaultValues);
  }, [formData.variables]);

  const updateFormData = useCallback((updates: Partial<Template>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  }, []);

  const addVariable = () => {
    const newVariable: TemplateVariable = {
      id: `var_${Date.now()}`,
      name: `var_${Date.now()}`,
      type: 'text',
      label: 'New Variable',
      required: false,
    };
    updateFormData({
      variables: [...(formData.variables || []), newVariable],
    });
    setEditingVariable(newVariable);
  };

  const updateVariable = (id: string, updates: Partial<TemplateVariable>) => {
    updateFormData({
      variables: formData.variables?.map(v => 
        v.id === id ? { ...v, ...updates } : v
      ),
    });
    if (editingVariable?.id === id) {
      setEditingVariable({ ...editingVariable, ...updates });
    }
  };

  const deleteVariable = (id: string) => {
    updateFormData({
      variables: formData.variables?.filter(v => v.id !== id),
    });
    setEditingVariable(null);
  };

  const addSection = () => {
    const newSection: TemplateSection = {
      id: `section_${Date.now()}`,
      title: 'New Section',
      content: '## New Section\n\nAdd your content here...',
      variables: [],
      order: (formData.sections?.length || 0) + 1,
    };
    updateFormData({
      sections: [...(formData.sections || []), newSection],
    });
    setEditingSection(newSection);
  };

  const updateSection = (id: string, updates: Partial<TemplateSection>) => {
    updateFormData({
      sections: formData.sections?.map(s => 
        s.id === id ? { ...s, ...updates } : s
      ),
    });
    if (editingSection?.id === id) {
      setEditingSection({ ...editingSection, ...updates });
    }
  };

  const deleteSection = (id: string) => {
    updateFormData({
      sections: formData.sections?.filter(s => s.id !== id),
    });
    setEditingSection(null);
  };

  const moveSectionUp = (id: string) => {
    const sections = formData.sections || [];
    const index = sections.findIndex(s => s.id === id);
    if (index > 0) {
      const newSections = [...sections];
      [newSections[index - 1], newSections[index]] = [newSections[index], newSections[index - 1]];
      // Update order values
      newSections.forEach((section, i) => {
        section.order = i + 1;
      });
      updateFormData({ sections: newSections });
    }
  };

  const moveSectionDown = (id: string) => {
    const sections = formData.sections || [];
    const index = sections.findIndex(s => s.id === id);
    if (index < sections.length - 1) {
      const newSections = [...sections];
      [newSections[index], newSections[index + 1]] = [newSections[index + 1], newSections[index]];
      // Update order values
      newSections.forEach((section, i) => {
        section.order = i + 1;
      });
      updateFormData({ sections: newSections });
    }
  };

  const generatePreview = async () => {
    if (!formData.variables || !formData.sections) return;
    
    try {
      const tempTemplate: Template = {
        id: 'preview',
        name: formData.name || 'Preview Template',
        description: formData.description || '',
        category: formData.category || '',
        contentType: formData.contentType || 'notebook',
        version: formData.version || '1.0.0',
        author: formData.author || 'User',
        tags: formData.tags || [],
        isPublic: formData.isPublic || false,
        isBuiltIn: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        variables: formData.variables,
        sections: formData.sections,
        usage: { totalUsed: 0 },
      };
      
      const preview = await previewTemplate(tempTemplate, previewVariables);
      setPreviewContent(preview);
    } catch (error) {
      toast({
        title: "Preview Error",
        description: "Failed to generate template preview",
        variant: "destructive",
      });
    }
  };

  const handleSave = async () => {
    const validation = validateTemplate(formData as Template);
    if (!validation.isValid) {
      toast({
        title: "Validation Error",
        description: validation.errors[0]?.message || "Please fix the errors before saving",
        variant: "destructive",
      });
      return;
    }

    try {
      let savedTemplate: Template;
      if (template) {
        savedTemplate = await updateTemplate(template.id, formData);
      } else {
        savedTemplate = await createTemplate(formData as Omit<Template, 'id' | 'createdAt' | 'updatedAt'>);
      }
      
      setHasUnsavedChanges(false);
      onSave(savedTemplate);
      
      toast({
        title: "Template saved",
        description: `Template "${savedTemplate.name}" has been saved successfully`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save template",
        variant: "destructive",
      });
    }
  };

  const VariableTypeOptions: { value: VariableType; label: string }[] = [
    { value: 'text', label: 'Text' },
    { value: 'textarea', label: 'Text Area' },
    { value: 'number', label: 'Number' },
    { value: 'date', label: 'Date' },
    { value: 'select', label: 'Select' },
    { value: 'multiselect', label: 'Multi-Select' },
    { value: 'boolean', label: 'Boolean' },
    { value: 'url', label: 'URL' },
  ];

  const renderVariableEditor = () => {
    if (!editingVariable) return null;

    return (
      <Dialog open={!!editingVariable} onOpenChange={() => setEditingVariable(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Variable</DialogTitle>
            <DialogDescription>
              Configure the variable properties and validation rules
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="var-name">Variable Name</Label>
                <Input
                  id="var-name"
                  value={editingVariable.name}
                  onChange={(e) => updateVariable(editingVariable.id, { name: e.target.value })}
                  placeholder="variable_name"
                />
              </div>
              <div>
                <Label htmlFor="var-type">Type</Label>
                <Select
                  value={editingVariable.type}
                  onValueChange={(value: VariableType) => updateVariable(editingVariable.id, { type: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {VariableTypeOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="var-label">Label</Label>
              <Input
                id="var-label"
                value={editingVariable.label}
                onChange={(e) => updateVariable(editingVariable.id, { label: e.target.value })}
                placeholder="Variable Label"
              />
            </div>

            <div>
              <Label htmlFor="var-description">Description</Label>
              <Textarea
                id="var-description"
                value={editingVariable.description || ''}
                onChange={(e) => updateVariable(editingVariable.id, { description: e.target.value })}
                placeholder="Optional description"
                rows={2}
              />
            </div>

            <div>
              <Label htmlFor="var-placeholder">Placeholder</Label>
              <Input
                id="var-placeholder"
                value={editingVariable.placeholder || ''}
                onChange={(e) => updateVariable(editingVariable.id, { placeholder: e.target.value })}
                placeholder="Placeholder text"
              />
            </div>

            {(editingVariable.type === 'select' || editingVariable.type === 'multiselect') && (
              <div>
                <Label htmlFor="var-options">Options (one per line)</Label>
                <Textarea
                  id="var-options"
                  value={editingVariable.options?.join('\n') || ''}
                  onChange={(e) => updateVariable(editingVariable.id, { 
                    options: e.target.value.split('\n').filter(Boolean) 
                  })}
                  placeholder="Option 1&#10;Option 2&#10;Option 3"
                  rows={4}
                />
              </div>
            )}

            <div className="flex items-center space-x-2">
              <Switch
                id="var-required"
                checked={editingVariable.required || false}
                onCheckedChange={(checked) => updateVariable(editingVariable.id, { required: checked })}
              />
              <Label htmlFor="var-required">Required</Label>
            </div>

            {editingVariable.type === 'text' || editingVariable.type === 'textarea' ? (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="var-min">Min Length</Label>
                  <Input
                    id="var-min"
                    type="number"
                    value={editingVariable.validation?.min || ''}
                    onChange={(e) => updateVariable(editingVariable.id, {
                      validation: {
                        ...editingVariable.validation,
                        min: e.target.value ? parseInt(e.target.value) : undefined,
                      }
                    })}
                  />
                </div>
                <div>
                  <Label htmlFor="var-max">Max Length</Label>
                  <Input
                    id="var-max"
                    type="number"
                    value={editingVariable.validation?.max || ''}
                    onChange={(e) => updateVariable(editingVariable.id, {
                      validation: {
                        ...editingVariable.validation,
                        max: e.target.value ? parseInt(e.target.value) : undefined,
                      }
                    })}
                  />
                </div>
              </div>
            ) : null}

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setEditingVariable(null)}>
                Cancel
              </Button>
              <Button onClick={() => setEditingVariable(null)}>
                Save Variable
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  const renderSectionEditor = () => {
    if (!editingSection) return null;

    return (
      <Dialog open={!!editingSection} onOpenChange={() => setEditingSection(null)}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Section</DialogTitle>
            <DialogDescription>
              Configure the section content and variables
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="section-title">Section Title</Label>
              <Input
                id="section-title"
                value={editingSection.title}
                onChange={(e) => updateSection(editingSection.id, { title: e.target.value })}
                placeholder="Section Title"
              />
            </div>

            <div>
              <Label htmlFor="section-content">Section Content</Label>
              <Textarea
                id="section-content"
                value={editingSection.content}
                onChange={(e) => updateSection(editingSection.id, { content: e.target.value })}
                placeholder="Section content with {{variable}} placeholders"
                rows={12}
                className="font-mono text-sm"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Use {`{{variable_name}}`} to insert variables. Supports conditionals like {`{{#if variable}}...{{/if}}`}
              </p>
            </div>

            <div>
              <Label>Variables Used in This Section</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {formData.variables?.map(variable => (
                  <Badge
                    key={variable.id}
                    variant={editingSection.variables.includes(variable.id) ? 'default' : 'outline'}
                    className="cursor-pointer"
                    onClick={() => {
                      const variables = editingSection.variables.includes(variable.id)
                        ? editingSection.variables.filter(v => v !== variable.id)
                        : [...editingSection.variables, variable.id];
                      updateSection(editingSection.id, { variables });
                    }}
                  >
                    {variable.label}
                  </Badge>
                ))}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="section-optional"
                checked={editingSection.optional || false}
                onCheckedChange={(checked) => updateSection(editingSection.id, { optional: checked })}
              />
              <Label htmlFor="section-optional">Optional Section</Label>
            </div>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setEditingSection(null)}>
                Cancel
              </Button>
              <Button onClick={() => setEditingSection(null)}>
                Save Section
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">
            {template ? `Edit Template: ${template.name}` : 'Create New Template'}
          </h1>
          <p className="text-muted-foreground">
            {template ? 'Modify your template settings and content' : 'Create a new content template'}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isLoading}>
            <Save className="h-4 w-4 mr-2" />
            {isLoading ? 'Saving...' : 'Save Template'}
          </Button>
        </div>
      </div>

      {/* Unsaved Changes Warning */}
      {hasUnsavedChanges && (
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                You have unsaved changes. Make sure to save your template before leaving.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Main Editor */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="variables">Variables</TabsTrigger>
          <TabsTrigger value="sections">Sections</TabsTrigger>
          <TabsTrigger value="preview">Preview</TabsTrigger>
        </TabsList>

        {/* Basic Info Tab */}
        <TabsContent value="basic" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Template Information</CardTitle>
              <CardDescription>Basic template metadata and settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Template Name *</Label>
                  <Input
                    id="name"
                    value={formData.name || ''}
                    onChange={(e) => updateFormData({ name: e.target.value })}
                    placeholder="Enter template name"
                  />
                </div>
                <div>
                  <Label htmlFor="version">Version</Label>
                  <Input
                    id="version"
                    value={formData.version || ''}
                    onChange={(e) => updateFormData({ version: e.target.value })}
                    placeholder="1.0.0"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description || ''}
                  onChange={(e) => updateFormData({ description: e.target.value })}
                  placeholder="Describe what this template is used for"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select
                    value={formData.category || ''}
                    onValueChange={(value) => updateFormData({ category: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(category => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.icon} {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="contentType">Content Type</Label>
                  <Select
                    value={formData.contentType || 'notebook'}
                    onValueChange={(value) => updateFormData({ contentType: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="notebook">Notebook</SelectItem>
                      <SelectItem value="source">Source</SelectItem>
                      <SelectItem value="slide">Slide</SelectItem>
                      <SelectItem value="chat_prompt">Chat Prompt</SelectItem>
                      <SelectItem value="note">Note</SelectItem>
                      <SelectItem value="diagram">Diagram</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="author">Author</Label>
                  <Input
                    id="author"
                    value={formData.author || ''}
                    onChange={(e) => updateFormData({ author: e.target.value })}
                    placeholder="Template author"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="tags">Tags (comma separated)</Label>
                <Input
                  id="tags"
                  value={formData.tags?.join(', ') || ''}
                  onChange={(e) => updateFormData({ 
                    tags: e.target.value.split(',').map(tag => tag.trim()).filter(Boolean) 
                  })}
                  placeholder="tag1, tag2, tag3"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isPublic"
                  checked={formData.isPublic || false}
                  onCheckedChange={(checked) => updateFormData({ isPublic: checked })}
                />
                <Label htmlFor="isPublic">Make this template public</Label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Variables Tab */}
        <TabsContent value="variables" className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold">Template Variables</h3>
              <p className="text-sm text-muted-foreground">
                Define variables that users can customize when using the template
              </p>
            </div>
            <Button onClick={addVariable}>
              <Plus className="h-4 w-4 mr-2" />
              Add Variable
            </Button>
          </div>

          <div className="space-y-3">
            {formData.variables?.map((variable) => (
              <Card key={variable.id}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Badge variant="outline">{variable.type}</Badge>
                      <div>
                        <h4 className="font-medium">{variable.label}</h4>
                        <p className="text-sm text-muted-foreground">
                          {variable.name} {variable.required && '(Required)'}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setEditingVariable(variable)}
                      >
                        <Settings className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => deleteVariable(variable.id)}
                      >
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
            
            {formData.variables?.length === 0 && (
              <Card className="p-8 text-center">
                <p className="text-muted-foreground mb-4">No variables defined yet</p>
                <Button onClick={addVariable}>Add Your First Variable</Button>
              </Card>
            )}
          </div>
        </TabsContent>

        {/* Sections Tab */}
        <TabsContent value="sections" className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold">Template Sections</h3>
              <p className="text-sm text-muted-foreground">
                Define the structure and content of your template
              </p>
            </div>
            <Button onClick={addSection}>
              <Plus className="h-4 w-4 mr-2" />
              Add Section
            </Button>
          </div>

          <div className="space-y-3">
            {formData.sections
              ?.sort((a, b) => a.order - b.order)
              .map((section, index) => (
                <Card key={section.id}>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3 flex-1">
                        <div className="flex flex-col space-y-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => moveSectionUp(section.id)}
                            disabled={index === 0}
                          >
                            <Move className="h-4 w-4 rotate-180" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => moveSectionDown(section.id)}
                            disabled={index === (formData.sections?.length || 0) - 1}
                          >
                            <Move className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <Badge variant="outline">Order {section.order}</Badge>
                            <h4 className="font-medium">{section.title}</h4>
                            {section.optional && (
                              <Badge variant="secondary" className="text-xs">Optional</Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                            {section.content.substring(0, 150)}...
                          </p>
                          <div className="flex flex-wrap gap-1">
                            {section.variables.map(varId => {
                              const variable = formData.variables?.find(v => v.id === varId);
                              return variable ? (
                                <Badge key={varId} variant="outline" className="text-xs">
                                  {variable.label}
                                </Badge>
                              ) : null;
                            })}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => setEditingSection(section)}
                        >
                          <Settings className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => deleteSection(section.id)}
                        >
                          <Trash2 className="h-4 w-4 text-destructive" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            
            {formData.sections?.length === 0 && (
              <Card className="p-8 text-center">
                <p className="text-muted-foreground mb-4">No sections defined yet</p>
                <Button onClick={addSection}>Add Your First Section</Button>
              </Card>
            )}
          </div>
        </TabsContent>

        {/* Preview Tab */}
        <TabsContent value="preview" className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold">Template Preview</h3>
              <p className="text-sm text-muted-foreground">
                See how your template will look with sample data
              </p>
            </div>
            <Button onClick={generatePreview}>
              <Eye className="h-4 w-4 mr-2" />
              Generate Preview
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Variable Values</CardTitle>
                <CardDescription>Adjust these values to test your template</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {formData.variables?.map((variable) => (
                  <div key={variable.id}>
                    <Label htmlFor={`preview-${variable.id}`}>{variable.label}</Label>
                    {variable.type === 'text' || variable.type === 'url' ? (
                      <Input
                        id={`preview-${variable.id}`}
                        value={previewVariables[variable.id] || ''}
                        onChange={(e) => setPreviewVariables(prev => ({
                          ...prev,
                          [variable.id]: e.target.value
                        }))}
                        placeholder={variable.placeholder}
                      />
                    ) : variable.type === 'textarea' ? (
                      <Textarea
                        id={`preview-${variable.id}`}
                        value={previewVariables[variable.id] || ''}
                        onChange={(e) => setPreviewVariables(prev => ({
                          ...prev,
                          [variable.id]: e.target.value
                        }))}
                        placeholder={variable.placeholder}
                        rows={3}
                      />
                    ) : variable.type === 'number' ? (
                      <Input
                        id={`preview-${variable.id}`}
                        type="number"
                        value={previewVariables[variable.id] || ''}
                        onChange={(e) => setPreviewVariables(prev => ({
                          ...prev,
                          [variable.id]: parseFloat(e.target.value) || 0
                        }))}
                      />
                    ) : variable.type === 'date' ? (
                      <Input
                        id={`preview-${variable.id}`}
                        type="date"
                        value={previewVariables[variable.id] || ''}
                        onChange={(e) => setPreviewVariables(prev => ({
                          ...prev,
                          [variable.id]: e.target.value
                        }))}
                      />
                    ) : variable.type === 'select' ? (
                      <Select
                        value={previewVariables[variable.id] || ''}
                        onValueChange={(value) => setPreviewVariables(prev => ({
                          ...prev,
                          [variable.id]: value
                        }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {variable.options?.map(option => (
                            <SelectItem key={option} value={option}>
                              {option}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    ) : null}
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Preview Output</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-muted p-4 rounded-md max-h-96 overflow-y-auto">
                  {previewContent ? (
                    <pre className="whitespace-pre-wrap text-sm">{previewContent}</pre>
                  ) : (
                    <p className="text-muted-foreground text-sm">
                      Click "Generate Preview" to see how your template will look
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Modals */}
      {renderVariableEditor()}
      {renderSectionEditor()}
    </div>
  );
};