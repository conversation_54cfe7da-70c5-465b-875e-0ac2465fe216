import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';
import { Template, TemplateInstance, TemplateCategory, TemplateMarketplace, TemplateValidationResult, TemplateRenderContext, DEFAULT_TEMPLATE_CONFIG, TemplateEngineConfig } from './types';
import { builtInTemplates } from './builtInTemplates';
import { useToast } from '../hooks/useToast';
import { lighthouseService } from '../services/lighthouseService';

interface TemplateState {
  templates: Template[];
  instances: TemplateInstance[];
  categories: TemplateCategory[];
  marketplace: TemplateMarketplace | null;
  activeTemplate: Template | null;
  activeInstance: TemplateInstance | null;
  isLoading: boolean;
  isRendering: boolean;
  searchQuery: string;
  selectedCategory: string | null;
  config: TemplateEngineConfig;
}

type TemplateAction = 
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_RENDERING'; payload: boolean }
  | { type: 'SET_TEMPLATES'; payload: Template[] }
  | { type: 'ADD_TEMPLATE'; payload: Template }
  | { type: 'UPDATE_TEMPLATE'; payload: Template }
  | { type: 'DELETE_TEMPLATE'; payload: string }
  | { type: 'SET_INSTANCES'; payload: TemplateInstance[] }
  | { type: 'ADD_INSTANCE'; payload: TemplateInstance }
  | { type: 'UPDATE_INSTANCE'; payload: TemplateInstance }
  | { type: 'DELETE_INSTANCE'; payload: string }
  | { type: 'SET_CATEGORIES'; payload: TemplateCategory[] }
  | { type: 'SET_MARKETPLACE'; payload: TemplateMarketplace }
  | { type: 'SET_ACTIVE_TEMPLATE'; payload: Template | null }
  | { type: 'SET_ACTIVE_INSTANCE'; payload: TemplateInstance | null }
  | { type: 'SET_SEARCH_QUERY'; payload: string }
  | { type: 'SET_SELECTED_CATEGORY'; payload: string | null }
  | { type: 'UPDATE_CONFIG'; payload: Partial<TemplateEngineConfig> };

const initialState: TemplateState = {
  templates: [],
  instances: [],
  categories: [],
  marketplace: null,
  activeTemplate: null,
  activeInstance: null,
  isLoading: false,
  isRendering: false,
  searchQuery: '',
  selectedCategory: null,
  config: DEFAULT_TEMPLATE_CONFIG,
};

function templateReducer(state: TemplateState, action: TemplateAction): TemplateState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_RENDERING':
      return { ...state, isRendering: action.payload };
    case 'SET_TEMPLATES':
      return { ...state, templates: action.payload };
    case 'ADD_TEMPLATE':
      return { ...state, templates: [action.payload, ...state.templates] };
    case 'UPDATE_TEMPLATE':
      return {
        ...state,
        templates: state.templates.map(t => 
          t.id === action.payload.id ? action.payload : t
        ),
      };
    case 'DELETE_TEMPLATE':
      return {
        ...state,
        templates: state.templates.filter(t => t.id !== action.payload),
        activeTemplate: state.activeTemplate?.id === action.payload ? null : state.activeTemplate,
      };
    case 'SET_INSTANCES':
      return { ...state, instances: action.payload };
    case 'ADD_INSTANCE':
      return { ...state, instances: [action.payload, ...state.instances] };
    case 'UPDATE_INSTANCE':
      return {
        ...state,
        instances: state.instances.map(i => 
          i.id === action.payload.id ? action.payload : i
        ),
      };
    case 'DELETE_INSTANCE':
      return {
        ...state,
        instances: state.instances.filter(i => i.id !== action.payload),
        activeInstance: state.activeInstance?.id === action.payload ? null : state.activeInstance,
      };
    case 'SET_CATEGORIES':
      return { ...state, categories: action.payload };
    case 'SET_MARKETPLACE':
      return { ...state, marketplace: action.payload };
    case 'SET_ACTIVE_TEMPLATE':
      return { ...state, activeTemplate: action.payload };
    case 'SET_ACTIVE_INSTANCE':
      return { ...state, activeInstance: action.payload };
    case 'SET_SEARCH_QUERY':
      return { ...state, searchQuery: action.payload };
    case 'SET_SELECTED_CATEGORY':
      return { ...state, selectedCategory: action.payload };
    case 'UPDATE_CONFIG':
      return { ...state, config: { ...state.config, ...action.payload } };
    default:
      return state;
  }
}

interface TemplateContextType {
  // State
  templates: Template[];
  filteredTemplates: Template[];
  instances: TemplateInstance[];
  categories: TemplateCategory[];
  marketplace: TemplateMarketplace | null;
  activeTemplate: Template | null;
  activeInstance: TemplateInstance | null;
  isLoading: boolean;
  isRendering: boolean;
  searchQuery: string;
  selectedCategory: string | null;
  config: TemplateEngineConfig;
  
  // Template Management
  createTemplate: (template: Omit<Template, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Template>;
  updateTemplate: (id: string, updates: Partial<Template>) => Promise<Template>;
  deleteTemplate: (id: string) => Promise<void>;
  duplicateTemplate: (id: string, name?: string) => Promise<Template>;
  importTemplate: (templateData: string | File) => Promise<Template>;
  exportTemplate: (id: string) => Promise<string>;
  
  // Instance Management
  createInstance: (templateId: string, name: string, variables?: Record<string, any>) => Promise<TemplateInstance>;
  updateInstance: (id: string, updates: Partial<TemplateInstance>) => Promise<TemplateInstance>;
  deleteInstance: (id: string) => Promise<void>;
  renderInstance: (instance: TemplateInstance) => Promise<string>;
  
  // Template Operations
  validateTemplate: (template: Template) => TemplateValidationResult;
  validateInstance: (instance: TemplateInstance) => TemplateValidationResult;
  previewTemplate: (template: Template, variables: Record<string, any>) => Promise<string>;
  
  // Navigation & Search
  setActiveTemplate: (template: Template | null) => void;
  setActiveInstance: (instance: TemplateInstance | null) => void;
  setSearchQuery: (query: string) => void;
  setSelectedCategory: (category: string | null) => void;
  
  // Marketplace
  refreshMarketplace: () => Promise<void>;
  shareTemplate: (id: string, isPublic: boolean) => Promise<void>;
  
  // Integration
  sendToNotebook: (content: string, notebookId?: string) => Promise<void>;
  sendToChat: (content: string, withContext?: boolean) => Promise<void>;
  sendToStudio: (content: string, type: 'Note' | 'Diagram' | 'Slide' | 'Code' | 'Markdown' | 'RichText') => Promise<void>;
}

const TemplateContext = createContext<TemplateContextType | undefined>(undefined);

export const useTemplateContext = () => {
  const context = useContext(TemplateContext);
  if (!context) {
    throw new Error('useTemplateContext must be used within TemplateProvider');
  }
  return context;
};

interface TemplateProviderProps {
  children: React.ReactNode;
  notebookId?: string;
}

export const TemplateProvider: React.FC<TemplateProviderProps> = ({ children, notebookId }) => {
  const [state, dispatch] = useReducer(templateReducer, initialState);
  const { toast } = useToast();
  
  // Initialize built-in templates and load user templates
  useEffect(() => {
    const initializeTemplates = async () => {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      try {
        // Load built-in templates
        const builtInTemplatesList = Object.values(builtInTemplates);
        
        // In a real implementation, you'd load user templates from storage
        // For now, we'll just use built-in templates
        const userTemplates: Template[] = [];
        
        const allTemplates = [...builtInTemplatesList, ...userTemplates];
        dispatch({ type: 'SET_TEMPLATES', payload: allTemplates });
        
        // Load categories
        const categories: TemplateCategory[] = [
          { id: 'research', name: 'Research', description: 'Research and analysis templates', icon: '🔬', color: '#3B82F6', order: 1 },
          { id: 'meetings', name: 'Meetings', description: 'Meeting notes and summaries', icon: '📋', color: '#10B981', order: 2 },
          { id: 'planning', name: 'Planning', description: 'Project and task planning', icon: '📅', color: '#8B5CF6', order: 3 },
          { id: 'analysis', name: 'Analysis', description: 'Data and content analysis', icon: '📊', color: '#F59E0B', order: 4 },
          { id: 'documentation', name: 'Documentation', description: 'Technical and process documentation', icon: '📝', color: '#EF4444', order: 5 },
          { id: 'presentation', name: 'Presentation', description: 'Slides and presentation materials', icon: '🎯', color: '#06B6D4', order: 6 },
        ];
        dispatch({ type: 'SET_CATEGORIES', payload: categories });
        
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load templates",
          variant: "destructive",
        });
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    };
    
    initializeTemplates();
  }, [toast]);
  
  // Template Engine - Render template with variables
  const renderTemplate = useCallback(async (template: Template, variables: Record<string, any>): Promise<string> => {
    dispatch({ type: 'SET_RENDERING', payload: true });
    
    try {
      const context: TemplateRenderContext = {
        variables,
        metadata: {
          createdAt: new Date().toISOString(),
          author: template.author,
          templateName: template.name,
          version: template.version,
        },
        helpers: {
          formatDate: (date: string | Date, format = 'YYYY-MM-DD') => {
            const d = new Date(date);
            return d.toLocaleDateString();
          },
          formatNumber: (num: number, decimals = 2) => {
            return num.toFixed(decimals);
          },
          capitalize: (str: string) => {
            return str.charAt(0).toUpperCase() + str.slice(1);
          },
          plural: (str: string, count: number) => {
            return count === 1 ? str : str + 's';
          },
          random: (items: string[]) => {
            return items[Math.floor(Math.random() * items.length)];
          },
        },
      };
      
      // Render sections in order
      const renderedSections = template.sections
        .sort((a, b) => a.order - b.order)
        .map(section => {
          if (section.optional && !section.variables.some(varId => variables[varId])) {
            return '';
          }
          
          let content = section.content;
          
          // Replace variables
          section.variables.forEach(varId => {
            const variable = template.variables.find(v => v.id === varId);
            if (variable && variables[varId] !== undefined) {
              const value = variables[varId];
              const regex = new RegExp(`\\{\\{\\s*${varId}\\s*\\}\\}`, 'g');
              content = content.replace(regex, String(value));
            }
          });
          
          // Process conditional blocks
          content = content.replace(
            /\{\{#if\s+(\w+)\s*\}\}([\s\S]*?)\{\{\/if\}\}/g,
            (match, varName, block) => {
              const value = variables[varName];
              return value ? block : '';
            }
          );
          
          // Process loops
          content = content.replace(
            /\{\{#each\s+(\w+)\s*\}\}([\s\S]*?)\{\{\/each\}\}/g,
            (match, varName, block) => {
              const items = variables[varName];
              if (Array.isArray(items)) {
                return items.map(item => {
                  let itemBlock = block;
                  if (typeof item === 'object') {
                    Object.keys(item).forEach(key => {
                      const regex = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, 'g');
                      itemBlock = itemBlock.replace(regex, String(item[key]));
                    });
                  } else {
                    itemBlock = itemBlock.replace(/\{\{\s*this\s*\}\}/g, String(item));
                  }
                  return itemBlock;
                }).join('');
              }
              return '';
            }
          );
          
          return content;
        })
        .filter(Boolean)
        .join('\n\n');
      
      return renderedSections;
    } finally {
      dispatch({ type: 'SET_RENDERING', payload: false });
    }
  }, []);
  
  // Template Management Functions
  const createTemplate = useCallback(async (templateData: Omit<Template, 'id' | 'createdAt' | 'updatedAt'>): Promise<Template> => {
    const newTemplate: Template = {
      ...templateData,
      id: `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      usage: {
        totalUsed: 0,
      },
    };
    
    dispatch({ type: 'ADD_TEMPLATE', payload: newTemplate });
    
    toast({
      title: "Template created",
      description: `Template "${newTemplate.name}" has been created successfully`,
    });
    
    return newTemplate;
  }, [toast]);
  
  const updateTemplate = useCallback(async (id: string, updates: Partial<Template>): Promise<Template> => {
    const existingTemplate = state.templates.find(t => t.id === id);
    if (!existingTemplate) {
      throw new Error('Template not found');
    }
    
    const updatedTemplate: Template = {
      ...existingTemplate,
      ...updates,
      updatedAt: new Date().toISOString(),
    };
    
    dispatch({ type: 'UPDATE_TEMPLATE', payload: updatedTemplate });
    
    toast({
      title: "Template updated",
      description: `Template "${updatedTemplate.name}" has been updated`,
    });
    
    return updatedTemplate;
  }, [state.templates, toast]);
  
  const deleteTemplate = useCallback(async (id: string): Promise<void> => {
    const template = state.templates.find(t => t.id === id);
    if (!template) {
      throw new Error('Template not found');
    }
    
    if (template.isBuiltIn) {
      throw new Error('Cannot delete built-in templates');
    }
    
    dispatch({ type: 'DELETE_TEMPLATE', payload: id });
    
    toast({
      title: "Template deleted",
      description: `Template "${template.name}" has been deleted`,
    });
  }, [state.templates, toast]);
  
  const duplicateTemplate = useCallback(async (id: string, name?: string): Promise<Template> => {
    const originalTemplate = state.templates.find(t => t.id === id);
    if (!originalTemplate) {
      throw new Error('Template not found');
    }
    
    return createTemplate({
      ...originalTemplate,
      name: name || `${originalTemplate.name} (Copy)`,
      isBuiltIn: false,
      isPublic: false,
      parentTemplateId: id,
    });
  }, [state.templates, createTemplate]);
  
  // Instance Management Functions
  const createInstance = useCallback(async (
    templateId: string, 
    name: string, 
    variables: Record<string, any> = {}
  ): Promise<TemplateInstance> => {
    const template = state.templates.find(t => t.id === templateId);
    if (!template) {
      throw new Error('Template not found');
    }
    
    const newInstance: TemplateInstance = {
      id: `instance_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      templateId,
      name,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      variableValues: variables,
    };
    
    // Update template usage
    const updatedTemplate = {
      ...template,
      usage: {
        ...template.usage,
        totalUsed: template.usage.totalUsed + 1,
        lastUsed: new Date().toISOString(),
      },
    };
    dispatch({ type: 'UPDATE_TEMPLATE', payload: updatedTemplate });
    dispatch({ type: 'ADD_INSTANCE', payload: newInstance });
    
    return newInstance;
  }, [state.templates]);
  
  const updateInstance = useCallback(async (id: string, updates: Partial<TemplateInstance>): Promise<TemplateInstance> => {
    const existingInstance = state.instances.find(i => i.id === id);
    if (!existingInstance) {
      throw new Error('Instance not found');
    }
    
    const updatedInstance: TemplateInstance = {
      ...existingInstance,
      ...updates,
      updatedAt: new Date().toISOString(),
    };
    
    dispatch({ type: 'UPDATE_INSTANCE', payload: updatedInstance });
    return updatedInstance;
  }, [state.instances]);
  
  const deleteInstance = useCallback(async (id: string): Promise<void> => {
    dispatch({ type: 'DELETE_INSTANCE', payload: id });
  }, []);
  
  const renderInstance = useCallback(async (instance: TemplateInstance): Promise<string> => {
    const template = state.templates.find(t => t.id === instance.templateId);
    if (!template) {
      throw new Error('Template not found');
    }
    
    const rendered = await renderTemplate(template, instance.variableValues);
    
    // Update instance with rendered content
    const updatedInstance = {
      ...instance,
      renderedContent: rendered,
      updatedAt: new Date().toISOString(),
    };
    dispatch({ type: 'UPDATE_INSTANCE', payload: updatedInstance });
    
    return rendered;
  }, [state.templates, renderTemplate]);
  
  // Validation Functions
  const validateTemplate = useCallback((template: Template): TemplateValidationResult => {
    const errors: Array<{ field: string; message: string; type: 'required' | 'validation' | 'format' }> = [];
    const warnings: Array<{ field: string; message: string }> = [];
    
    if (!template.name.trim()) {
      errors.push({ field: 'name', message: 'Template name is required', type: 'required' });
    }
    
    if (!template.description.trim()) {
      warnings.push({ field: 'description', message: 'Consider adding a description' });
    }
    
    if (template.sections.length === 0) {
      errors.push({ field: 'sections', message: 'Template must have at least one section', type: 'validation' });
    }
    
    // Check for variable consistency
    template.sections.forEach(section => {
      section.variables.forEach(varId => {
        if (!template.variables.find(v => v.id === varId)) {
          errors.push({ 
            field: 'variables', 
            message: `Variable ${varId} used in section "${section.title}" but not defined`, 
            type: 'validation' 
          });
        }
      });
    });
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }, []);
  
  const validateInstance = useCallback((instance: TemplateInstance): TemplateValidationResult => {
    const template = state.templates.find(t => t.id === instance.templateId);
    if (!template) {
      return {
        isValid: false,
        errors: [{ field: 'template', message: 'Template not found', type: 'validation' }],
        warnings: [],
      };
    }
    
    const errors: Array<{ field: string; message: string; type: 'required' | 'validation' | 'format' }> = [];
    const warnings: Array<{ field: string; message: string }> = [];
    
    // Check required variables
    template.variables.forEach(variable => {
      if (variable.required && !instance.variableValues[variable.id]) {
        errors.push({ 
          field: variable.id, 
          message: `${variable.label} is required`, 
          type: 'required' 
        });
      }
      
      // Check validation rules
      const value = instance.variableValues[variable.id];
      if (value && variable.validation) {
        if (variable.validation.min && typeof value === 'string' && value.length < variable.validation.min) {
          errors.push({ 
            field: variable.id, 
            message: `${variable.label} must be at least ${variable.validation.min} characters`, 
            type: 'validation' 
          });
        }
        
        if (variable.validation.max && typeof value === 'string' && value.length > variable.validation.max) {
          errors.push({ 
            field: variable.id, 
            message: `${variable.label} must not exceed ${variable.validation.max} characters`, 
            type: 'validation' 
          });
        }
        
        if (variable.validation.pattern && typeof value === 'string') {
          const regex = new RegExp(variable.validation.pattern);
          if (!regex.test(value)) {
            errors.push({ 
              field: variable.id, 
              message: `${variable.label} format is invalid`, 
              type: 'format' 
            });
          }
        }
        
        if (variable.validation.custom && typeof variable.validation.custom === 'function') {
          const result = variable.validation.custom(value);
          if (typeof result === 'string') {
            errors.push({ 
              field: variable.id, 
              message: result, 
              type: 'validation' 
            });
          } else if (!result) {
            errors.push({ 
              field: variable.id, 
              message: `${variable.label} is invalid`, 
              type: 'validation' 
            });
          }
        }
      }
    });
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }, [state.templates]);
  
  const previewTemplate = useCallback(async (template: Template, variables: Record<string, any>): Promise<string> => {
    return renderTemplate(template, variables);
  }, [renderTemplate]);
  
  // Navigation & Search
  const setActiveTemplate = useCallback((template: Template | null) => {
    dispatch({ type: 'SET_ACTIVE_TEMPLATE', payload: template });
  }, []);
  
  const setActiveInstance = useCallback((instance: TemplateInstance | null) => {
    dispatch({ type: 'SET_ACTIVE_INSTANCE', payload: instance });
  }, []);
  
  const setSearchQuery = useCallback((query: string) => {
    dispatch({ type: 'SET_SEARCH_QUERY', payload: query });
  }, []);
  
  const setSelectedCategory = useCallback((category: string | null) => {
    dispatch({ type: 'SET_SELECTED_CATEGORY', payload: category });
  }, []);
  
  // Filtered templates based on search and category
  const filteredTemplates = React.useMemo(() => {
    let filtered = state.templates;
    
    if (state.selectedCategory) {
      filtered = filtered.filter(t => t.category === state.selectedCategory);
    }
    
    if (state.searchQuery) {
      const query = state.searchQuery.toLowerCase();
      filtered = filtered.filter(t => 
        t.name.toLowerCase().includes(query) ||
        t.description.toLowerCase().includes(query) ||
        t.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }
    
    return filtered;
  }, [state.templates, state.selectedCategory, state.searchQuery]);
  
  // Integration Functions
  const sendToNotebook = useCallback(async (content: string, targetNotebookId?: string) => {
    try {
      if (!targetNotebookId && !notebookId) {
        throw new Error('No notebook specified');
      }
      
      await lighthouseService.addSource(
        targetNotebookId || notebookId!,
        'Template Content',
        content,
        'Note'
      );
      
      toast({
        title: "Content sent to notebook",
        description: "Template content has been added as a new source",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send content to notebook",
        variant: "destructive",
      });
    }
  }, [notebookId, toast]);
  
  const sendToChat = useCallback(async (content: string, withContext = false) => {
    try {
      await lighthouseService.sendMessage(notebookId!, content, {
        sources: withContext ? [] : undefined,
      });
      
      toast({
        title: "Content sent to chat",
        description: "Template content has been sent to the chat",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send content to chat",
        variant: "destructive",
      });
    }
  }, [notebookId, toast]);
  
  const sendToStudio = useCallback(async (
    content: string, 
    type: 'Note' | 'Diagram' | 'Slide' | 'Code' | 'Markdown' | 'RichText'
  ) => {
    try {
      await lighthouseService.createDocument(
        notebookId!,
        `Template ${type}`,
        content,
        type
      );
      
      toast({
        title: "Content sent to studio",
        description: `Template content has been created as a new ${type.toLowerCase()} document`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send content to studio",
        variant: "destructive",
      });
    }
  }, [notebookId, toast]);
  
  // Placeholder implementations for remaining functions
  const importTemplate = useCallback(async (templateData: string | File): Promise<Template> => {
    // TODO: Implement template import
    throw new Error('Import not implemented yet');
  }, []);
  
  const exportTemplate = useCallback(async (id: string): Promise<string> => {
    const template = state.templates.find(t => t.id === id);
    if (!template) {
      throw new Error('Template not found');
    }
    return JSON.stringify(template, null, 2);
  }, [state.templates]);
  
  const refreshMarketplace = useCallback(async () => {
    // TODO: Implement marketplace refresh
  }, []);
  
  const shareTemplate = useCallback(async (id: string, isPublic: boolean) => {
    await updateTemplate(id, { isPublic });
  }, [updateTemplate]);
  
  const value: TemplateContextType = {
    // State
    templates: state.templates,
    filteredTemplates,
    instances: state.instances,
    categories: state.categories,
    marketplace: state.marketplace,
    activeTemplate: state.activeTemplate,
    activeInstance: state.activeInstance,
    isLoading: state.isLoading,
    isRendering: state.isRendering,
    searchQuery: state.searchQuery,
    selectedCategory: state.selectedCategory,
    config: state.config,
    
    // Template Management
    createTemplate,
    updateTemplate,
    deleteTemplate,
    duplicateTemplate,
    importTemplate,
    exportTemplate,
    
    // Instance Management
    createInstance,
    updateInstance,
    deleteInstance,
    renderInstance,
    
    // Template Operations
    validateTemplate,
    validateInstance,
    previewTemplate,
    
    // Navigation & Search
    setActiveTemplate,
    setActiveInstance,
    setSearchQuery,
    setSelectedCategory,
    
    // Marketplace
    refreshMarketplace,
    shareTemplate,
    
    // Integration
    sendToNotebook,
    sendToChat,
    sendToStudio,
  };
  
  return (
    <TemplateContext.Provider value={value}>
      {children}
    </TemplateContext.Provider>
  );
};