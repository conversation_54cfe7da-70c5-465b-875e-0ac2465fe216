import React, { useState } from 'react';
import { Template, TemplateInstance } from './types';
import { TemplateProvider } from './TemplateProvider';
import { TemplateLibrary } from './TemplateLibrary';
import { TemplateEditor } from './TemplateEditor';
import { TemplatePreview } from './TemplatePreview';

type ViewMode = 'library' | 'editor' | 'preview';

interface TemplateManagerState {
  mode: ViewMode;
  selectedTemplate: Template | null;
  selectedInstance: TemplateInstance | null;
  editingTemplate: Template | null;
}

interface TemplateManagerProps {
  notebookId?: string;
  onTemplateApplied?: (content: string, template: Template) => void;
  onClose?: () => void;
}

/**
 * Main Template Manager Component
 * 
 * This is the main entry point for the template system. It manages the different
 * views (library, editor, preview) and orchestrates the template workflow.
 */
export const TemplateManager: React.FC<TemplateManagerProps> = ({
  notebookId,
  onTemplateApplied,
  onClose,
}) => {
  const [state, setState] = useState<TemplateManagerState>({
    mode: 'library',
    selectedTemplate: null,
    selectedInstance: null,
    editingTemplate: null,
  });

  const handleTemplateSelect = (template: Template) => {
    setState(prev => ({
      ...prev,
      mode: 'preview',
      selectedTemplate: template,
      selectedInstance: null,
    }));
  };

  const handleCreateNew = () => {
    setState(prev => ({
      ...prev,
      mode: 'editor',
      editingTemplate: null,
      selectedTemplate: null,
    }));
  };

  const handleEditTemplate = (template?: Template) => {
    setState(prev => ({
      ...prev,
      mode: 'editor',
      editingTemplate: template || prev.selectedTemplate,
    }));
  };

  const handleSaveTemplate = (template: Template) => {
    setState(prev => ({
      ...prev,
      mode: 'library',
      editingTemplate: null,
    }));
  };

  const handleCreateInstance = async (variables: Record<string, any>) => {
    if (!state.selectedTemplate) return;

    // In a real implementation, you would create the instance through the context
    // For now, we'll just apply the template content
    try {
      const content = await generateTemplateContent(state.selectedTemplate, variables);
      
      if (onTemplateApplied) {
        onTemplateApplied(content, state.selectedTemplate);
      }
      
      // Navigate back to library
      setState(prev => ({ ...prev, mode: 'library', selectedTemplate: null }));
    } catch (error) {
      console.error('Failed to create template instance:', error);
    }
  };

  const handleCancel = () => {
    setState(prev => ({
      ...prev,
      mode: 'library',
      selectedTemplate: null,
      selectedInstance: null,
      editingTemplate: null,
    }));
  };

  const generateTemplateContent = async (template: Template, variables: Record<string, any>): Promise<string> => {
    // Simple template rendering - in real implementation this would be in the context
    let content = template.sections
      .sort((a, b) => a.order - b.order)
      .map(section => {
        let sectionContent = section.content;
        
        // Replace variables
        Object.keys(variables).forEach(key => {
          const regex = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, 'g');
          sectionContent = sectionContent.replace(regex, String(variables[key] || ''));
        });
        
        return sectionContent;
      })
      .join('\n\n');

    return content;
  };

  const renderCurrentView = () => {
    switch (state.mode) {
      case 'library':
        return (
          <TemplateLibrary
            onTemplateSelect={handleTemplateSelect}
            onCreateNew={handleCreateNew}
          />
        );

      case 'editor':
        return (
          <TemplateEditor
            template={state.editingTemplate || undefined}
            onSave={handleSaveTemplate}
            onCancel={handleCancel}
          />
        );

      case 'preview':
        return state.selectedTemplate ? (
          <TemplatePreview
            template={state.selectedTemplate}
            instance={state.selectedInstance || undefined}
            onCreateInstance={handleCreateInstance}
            onEdit={() => handleEditTemplate(state.selectedTemplate!)}
            onClose={handleCancel}
          />
        ) : null;

      default:
        return null;
    }
  };

  return (
    <TemplateProvider notebookId={notebookId}>
      <div className="h-full">
        {renderCurrentView()}
      </div>
    </TemplateProvider>
  );
};

/**
 * Simplified Template Selector Component
 * 
 * A lightweight component for quick template selection without the full manager UI
 */
interface TemplateSelectorProps {
  onTemplateSelect: (template: Template, variables: Record<string, any>) => void;
  category?: string;
  contentType?: string;
  notebookId?: string;
}

export const TemplateSelector: React.FC<TemplateSelectorProps> = ({
  onTemplateSelect,
  category,
  contentType,
  notebookId,
}) => {
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);

  if (selectedTemplate) {
    return (
      <TemplateProvider notebookId={notebookId}>
        <TemplatePreview
          template={selectedTemplate}
          onCreateInstance={(variables) => {
            onTemplateSelect(selectedTemplate, variables);
            setSelectedTemplate(null);
          }}
          onClose={() => setSelectedTemplate(null)}
        />
      </TemplateProvider>
    );
  }

  return (
    <TemplateProvider notebookId={notebookId}>
      <div className="max-h-96 overflow-y-auto">
        <TemplateLibrary
          onTemplateSelect={setSelectedTemplate}
          onCreateNew={() => {
            // In a simple selector, we might not allow creating new templates
            console.log('Create new template - not implemented in selector');
          }}
        />
      </div>
    </TemplateProvider>
  );
};

/**
 * Template Quick Actions Component
 * 
 * Provides quick access to common template operations
 */
interface TemplateQuickActionsProps {
  onUseTemplate: (template: Template) => void;
  onCreateTemplate: () => void;
  recentTemplates?: Template[];
}

export const TemplateQuickActions: React.FC<TemplateQuickActionsProps> = ({
  onUseTemplate,
  onCreateTemplate,
  recentTemplates = [],
}) => {
  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-sm font-semibold mb-2">Quick Start</h3>
        <div className="grid grid-cols-2 gap-2">
          <button
            onClick={onCreateTemplate}
            className="p-3 text-left rounded-lg border hover:bg-muted transition-colors"
          >
            <div className="text-lg mb-1">📄</div>
            <div className="text-sm font-medium">Blank Template</div>
            <div className="text-xs text-muted-foreground">Start from scratch</div>
          </button>
          
          {/* Add more quick start options */}
        </div>
      </div>

      {recentTemplates.length > 0 && (
        <div>
          <h3 className="text-sm font-semibold mb-2">Recently Used</h3>
          <div className="space-y-2">
            {recentTemplates.slice(0, 3).map(template => (
              <button
                key={template.id}
                onClick={() => onUseTemplate(template)}
                className="w-full p-2 text-left rounded-lg hover:bg-muted transition-colors"
              >
                <div className="flex items-center space-x-2">
                  <span className="text-lg">{template.thumbnail || '📄'}</span>
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium truncate">{template.name}</div>
                    <div className="text-xs text-muted-foreground truncate">
                      {template.description}
                    </div>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default TemplateManager;