import { Template, TEMPLATE_CATEGORIES, BUILT_IN_TEMPLATES } from './types';

export const builtInTemplates: Record<string, Template> = {
  [BUILT_IN_TEMPLATES.RESEARCH_NOTES]: {
    id: BUILT_IN_TEMPLATES.RESEARCH_NOTES,
    name: 'Research Notes',
    description: 'Structured template for academic and professional research documentation',
    category: TEMPLATE_CATEGORIES.RESEARCH,
    contentType: 'notebook',
    version: '1.0.0',
    author: 'Lighthouse LM',
    tags: ['research', 'academic', 'documentation', 'notes'],
    isPublic: true,
    isBuiltIn: true,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
    usage: { totalUsed: 0 },
    thumbnail: '🔬',
    
    variables: [
      {
        id: 'research_topic',
        name: 'research_topic',
        type: 'text',
        label: 'Research Topic',
        description: 'Main research topic or question',
        required: true,
        placeholder: 'Enter your research topic...',
      },
      {
        id: 'research_questions',
        name: 'research_questions',
        type: 'textarea',
        label: 'Research Questions',
        description: 'Key research questions to investigate',
        placeholder: 'List your research questions...',
      },
      {
        id: 'methodology',
        name: 'methodology',
        type: 'select',
        label: 'Research Methodology',
        options: ['Qualitative', 'Quantitative', 'Mixed Methods', 'Literature Review', 'Case Study'],
        defaultValue: 'Qualitative',
      },
      {
        id: 'keywords',
        name: 'keywords',
        type: 'text',
        label: 'Keywords',
        description: 'Comma-separated keywords for this research',
        placeholder: 'keyword1, keyword2, keyword3',
      },
      {
        id: 'deadline',
        name: 'deadline',
        type: 'date',
        label: 'Research Deadline',
      },
    ],
    
    sections: [
      {
        id: 'header',
        title: 'Research Header',
        content: `# {{research_topic}}

**Research Questions:**
{{research_questions}}

**Methodology:** {{methodology}}
**Keywords:** {{keywords}}
**Deadline:** {{deadline}}

---`,
        variables: ['research_topic', 'research_questions', 'methodology', 'keywords', 'deadline'],
        order: 1,
      },
      {
        id: 'objectives',
        title: 'Research Objectives',
        content: `## Research Objectives

### Primary Objectives
- [ ] Define research scope and boundaries
- [ ] Identify key research questions
- [ ] Establish methodology and approach

### Secondary Objectives
- [ ] Review existing literature
- [ ] Collect and analyze data
- [ ] Draw conclusions and recommendations

---`,
        variables: [],
        order: 2,
      },
      {
        id: 'literature_review',
        title: 'Literature Review',
        content: `## Literature Review

### Key Sources
| Source | Author | Year | Relevance | Notes |
|--------|--------|------|-----------|-------|
|        |        |      |           |       |

### Research Gaps Identified
- 

### Theoretical Framework
- 

---`,
        variables: [],
        order: 3,
      },
      {
        id: 'findings',
        title: 'Key Findings',
        content: `## Key Findings

### Main Discoveries
1. 
2. 
3. 

### Supporting Evidence
- 
- 
- 

### Implications
- 

---`,
        variables: [],
        order: 4,
      },
      {
        id: 'conclusions',
        title: 'Conclusions',
        content: `## Conclusions

### Summary
{{#if research_topic}}
Based on the research conducted on "{{research_topic}}", the following conclusions can be drawn:
{{/if}}

### Recommendations
1. 
2. 
3. 

### Future Research Directions
- 
- 
- 

### Limitations
- 
- 

---`,
        variables: ['research_topic'],
        order: 5,
      },
    ],
  },

  [BUILT_IN_TEMPLATES.MEETING_NOTES]: {
    id: BUILT_IN_TEMPLATES.MEETING_NOTES,
    name: 'Meeting Notes',
    description: 'Professional meeting documentation template with action items and follow-ups',
    category: TEMPLATE_CATEGORIES.MEETINGS,
    contentType: 'notebook',
    version: '1.0.0',
    author: 'Lighthouse LM',
    tags: ['meetings', 'notes', 'action-items', 'collaboration'],
    isPublic: true,
    isBuiltIn: true,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
    usage: { totalUsed: 0 },
    thumbnail: '📋',
    
    variables: [
      {
        id: 'meeting_title',
        name: 'meeting_title',
        type: 'text',
        label: 'Meeting Title',
        required: true,
        placeholder: 'Enter meeting title...',
      },
      {
        id: 'meeting_date',
        name: 'meeting_date',
        type: 'date',
        label: 'Meeting Date',
        required: true,
        defaultValue: new Date().toISOString().split('T')[0],
      },
      {
        id: 'meeting_time',
        name: 'meeting_time',
        type: 'text',
        label: 'Meeting Time',
        placeholder: '2:00 PM - 3:00 PM',
      },
      {
        id: 'attendees',
        name: 'attendees',
        type: 'textarea',
        label: 'Attendees',
        description: 'List of meeting attendees (one per line)',
        placeholder: 'John Smith\nJane Doe\nMike Johnson',
      },
      {
        id: 'meeting_type',
        name: 'meeting_type',
        type: 'select',
        label: 'Meeting Type',
        options: ['Team Standup', 'Project Review', 'Planning Session', 'Client Meeting', 'One-on-One', 'All Hands', 'Other'],
        defaultValue: 'Team Meeting',
      },
      {
        id: 'location',
        name: 'location',
        type: 'text',
        label: 'Location/Platform',
        placeholder: 'Conference Room A / Zoom / Teams',
      },
    ],
    
    sections: [
      {
        id: 'header',
        title: 'Meeting Header',
        content: `# {{meeting_title}}

**Date:** {{meeting_date}}
**Time:** {{meeting_time}}
**Type:** {{meeting_type}}
**Location:** {{location}}

## Attendees
{{#each attendees}}
- {{this}}
{{/each}}

---`,
        variables: ['meeting_title', 'meeting_date', 'meeting_time', 'meeting_type', 'location', 'attendees'],
        order: 1,
      },
      {
        id: 'agenda',
        title: 'Agenda',
        content: `## Agenda
1. [ ] 
2. [ ] 
3. [ ] 
4. [ ] 
5. [ ] 

---`,
        variables: [],
        order: 2,
      },
      {
        id: 'discussion',
        title: 'Discussion Points',
        content: `## Discussion Points

### Topic 1:
**Discussion:**
- 
- 
- 

**Key Points:**
- 
- 

### Topic 2:
**Discussion:**
- 
- 
- 

**Key Points:**
- 
- 

---`,
        variables: [],
        order: 3,
      },
      {
        id: 'decisions',
        title: 'Decisions Made',
        content: `## Decisions Made

1. **Decision:** 
   - **Rationale:** 
   - **Impact:** 
   - **Owner:** 

2. **Decision:** 
   - **Rationale:** 
   - **Impact:** 
   - **Owner:** 

---`,
        variables: [],
        order: 4,
      },
      {
        id: 'action_items',
        title: 'Action Items',
        content: `## Action Items

| Task | Owner | Due Date | Priority | Status |
|------|--------|----------|----------|---------|
|      |        |          |          | ⏳ Pending |
|      |        |          |          | ⏳ Pending |
|      |        |          |          | ⏳ Pending |

### Follow-up Meeting
**Date:** 
**Agenda:** 
- Review action items
- 
- 

---`,
        variables: [],
        order: 5,
      },
      {
        id: 'notes',
        title: 'Additional Notes',
        content: `## Additional Notes

### Key Takeaways
- 
- 
- 

### Concerns/Risks Identified
- 
- 

### Next Steps
- 
- 
- 

---

*Meeting notes compiled on {{formatDate(createdAt)}} by {{author}}*`,
        variables: [],
        order: 6,
      },
    ],
  },

  [BUILT_IN_TEMPLATES.PROJECT_PLANNING]: {
    id: BUILT_IN_TEMPLATES.PROJECT_PLANNING,
    name: 'Project Planning',
    description: 'Comprehensive project planning template with phases, milestones, and resource allocation',
    category: TEMPLATE_CATEGORIES.PLANNING,
    contentType: 'notebook',
    version: '1.0.0',
    author: 'Lighthouse LM',
    tags: ['project', 'planning', 'milestones', 'resources'],
    isPublic: true,
    isBuiltIn: true,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
    usage: { totalUsed: 0 },
    thumbnail: '📅',
    
    variables: [
      {
        id: 'project_name',
        name: 'project_name',
        type: 'text',
        label: 'Project Name',
        required: true,
        placeholder: 'Enter project name...',
      },
      {
        id: 'project_manager',
        name: 'project_manager',
        type: 'text',
        label: 'Project Manager',
        required: true,
        placeholder: 'Enter project manager name...',
      },
      {
        id: 'start_date',
        name: 'start_date',
        type: 'date',
        label: 'Project Start Date',
        required: true,
      },
      {
        id: 'end_date',
        name: 'end_date',
        type: 'date',
        label: 'Project End Date',
        required: true,
      },
      {
        id: 'budget',
        name: 'budget',
        type: 'number',
        label: 'Project Budget',
        placeholder: '10000',
      },
      {
        id: 'stakeholders',
        name: 'stakeholders',
        type: 'textarea',
        label: 'Key Stakeholders',
        description: 'List key stakeholders (one per line)',
        placeholder: 'CEO - John Smith\nCTO - Jane Doe\nDevelopment Team Lead - Mike Johnson',
      },
      {
        id: 'priority',
        name: 'priority',
        type: 'select',
        label: 'Project Priority',
        options: ['Low', 'Medium', 'High', 'Critical'],
        defaultValue: 'Medium',
      },
    ],
    
    sections: [
      {
        id: 'overview',
        title: 'Project Overview',
        content: `# {{project_name}} - Project Plan

## Project Overview

**Project Manager:** {{project_manager}}
**Start Date:** {{start_date}}
**End Date:** {{end_date}}
**Priority:** {{priority}}
{{#if budget}}**Budget:** ${{formatNumber(budget)}}{{/if}}

## Key Stakeholders
{{#each stakeholders}}
- {{this}}
{{/each}}

---`,
        variables: ['project_name', 'project_manager', 'start_date', 'end_date', 'priority', 'budget', 'stakeholders'],
        order: 1,
      },
      {
        id: 'objectives',
        title: 'Project Objectives',
        content: `## Project Objectives

### Primary Objectives
1. 
2. 
3. 

### Success Criteria
- [ ] 
- [ ] 
- [ ] 
- [ ] 

### Key Performance Indicators (KPIs)
| Metric | Target | Current | Status |
|--------|--------|---------|---------|
|        |        |         |         |
|        |        |         |         |

---`,
        variables: [],
        order: 2,
      },
      {
        id: 'scope',
        title: 'Project Scope',
        content: `## Project Scope

### In Scope
- 
- 
- 
- 

### Out of Scope
- 
- 
- 

### Assumptions
- 
- 
- 

### Constraints
- 
- 
- 

---`,
        variables: [],
        order: 3,
      },
      {
        id: 'phases',
        title: 'Project Phases',
        content: `## Project Phases

### Phase 1: Planning & Analysis
**Duration:** 
**Key Deliverables:**
- [ ] 
- [ ] 
- [ ] 

### Phase 2: Design & Architecture
**Duration:** 
**Key Deliverables:**
- [ ] 
- [ ] 
- [ ] 

### Phase 3: Development & Implementation
**Duration:** 
**Key Deliverables:**
- [ ] 
- [ ] 
- [ ] 

### Phase 4: Testing & Quality Assurance
**Duration:** 
**Key Deliverables:**
- [ ] 
- [ ] 
- [ ] 

### Phase 5: Deployment & Launch
**Duration:** 
**Key Deliverables:**
- [ ] 
- [ ] 
- [ ] 

---`,
        variables: [],
        order: 4,
      },
      {
        id: 'resources',
        title: 'Resource Allocation',
        content: `## Resource Allocation

### Team Structure
| Role | Name | Allocation | Contact |
|------|------|------------|---------|
|      |      |            |         |
|      |      |            |         |
|      |      |            |         |

### Resource Requirements
| Resource Type | Quantity | Cost | Notes |
|---------------|----------|------|-------|
|               |          |      |       |
|               |          |      |       |

### Budget Breakdown
| Category | Amount | Percentage |
|----------|--------|------------|
|          |        |            |
|          |        |            |
|          |        |            |

---`,
        variables: [],
        order: 5,
      },
      {
        id: 'risks',
        title: 'Risk Management',
        content: `## Risk Management

### Risk Register
| Risk | Probability | Impact | Mitigation Strategy | Owner | Status |
|------|-------------|---------|-------------------|-------|---------|
|      | Low/Med/High| Low/Med/High|                   |       | ⚠️ Monitoring |
|      | Low/Med/High| Low/Med/High|                   |       | ⚠️ Monitoring |

### Contingency Plans
1. **Risk:** 
   **Contingency:** 
   
2. **Risk:** 
   **Contingency:** 

---`,
        variables: [],
        order: 6,
      },
      {
        id: 'milestones',
        title: 'Milestones & Timeline',
        content: `## Key Milestones

| Milestone | Target Date | Dependencies | Status |
|-----------|-------------|-------------|--------|
|           |             |             | 🟡 Planned |
|           |             |             | 🟡 Planned |
|           |             |             | 🟡 Planned |

### Critical Path
- 
- 
- 

### Timeline Summary
**Project Duration:** {{#if start_date}}{{#if end_date}}{{start_date}} to {{end_date}}{{/if}}{{/if}}

---

*Project plan created on {{formatDate(createdAt)}} by {{project_manager}}*`,
        variables: ['start_date', 'end_date', 'project_manager'],
        order: 7,
      },
    ],
  },

  [BUILT_IN_TEMPLATES.LITERATURE_REVIEW]: {
    id: BUILT_IN_TEMPLATES.LITERATURE_REVIEW,
    name: 'Literature Review',
    description: 'Systematic literature review template for academic and research purposes',
    category: TEMPLATE_CATEGORIES.RESEARCH,
    contentType: 'notebook',
    version: '1.0.0',
    author: 'Lighthouse LM',
    tags: ['literature', 'review', 'academic', 'research', 'bibliography'],
    isPublic: true,
    isBuiltIn: true,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
    usage: { totalUsed: 0 },
    thumbnail: '📚',
    
    variables: [
      {
        id: 'review_topic',
        name: 'review_topic',
        type: 'text',
        label: 'Literature Review Topic',
        required: true,
        placeholder: 'Enter the topic of your literature review...',
      },
      {
        id: 'search_terms',
        name: 'search_terms',
        type: 'text',
        label: 'Search Terms',
        description: 'Keywords used to search for literature',
        placeholder: 'term1, term2, term3',
      },
      {
        id: 'databases',
        name: 'databases',
        type: 'multiselect',
        label: 'Databases Searched',
        options: ['PubMed', 'IEEE Xplore', 'ACM Digital Library', 'Google Scholar', 'JSTOR', 'ScienceDirect', 'Web of Science', 'Scopus'],
        defaultValue: ['Google Scholar'],
      },
      {
        id: 'inclusion_criteria',
        name: 'inclusion_criteria',
        type: 'textarea',
        label: 'Inclusion Criteria',
        description: 'Criteria for including sources in the review',
        placeholder: 'Enter inclusion criteria...',
      },
      {
        id: 'exclusion_criteria',
        name: 'exclusion_criteria',
        type: 'textarea',
        label: 'Exclusion Criteria',
        description: 'Criteria for excluding sources from the review',
        placeholder: 'Enter exclusion criteria...',
      },
    ],
    
    sections: [
      {
        id: 'introduction',
        title: 'Introduction',
        content: `# Literature Review: {{review_topic}}

## Introduction

### Purpose and Scope
This literature review examines the current state of research on {{review_topic}}.

### Research Questions
1. 
2. 
3. 

### Search Strategy
**Search Terms:** {{search_terms}}
**Databases Searched:** {{#each databases}}{{this}}{{#unless @last}}, {{/unless}}{{/each}}

**Inclusion Criteria:**
{{inclusion_criteria}}

**Exclusion Criteria:**
{{exclusion_criteria}}

---`,
        variables: ['review_topic', 'search_terms', 'databases', 'inclusion_criteria', 'exclusion_criteria'],
        order: 1,
      },
      {
        id: 'methodology',
        title: 'Review Methodology',
        content: `## Review Methodology

### Search Process
1. **Initial Search:** Conducted on [date] using specified search terms
2. **Screening:** Titles and abstracts reviewed for relevance
3. **Full-text Review:** Selected papers reviewed in full
4. **Citation Tracking:** Reference lists examined for additional sources

### Selection Process
| Stage | Number of Sources | Notes |
|-------|------------------|-------|
| Initial Search | | |
| Title/Abstract Screening | | |
| Full-text Review | | |
| Final Inclusion | | |

---`,
        variables: [],
        order: 2,
      },
      {
        id: 'sources',
        title: 'Source Analysis',
        content: `## Source Analysis

### Key Sources
| Author(s) | Year | Title | Type | Relevance | Notes |
|-----------|------|-------|------|-----------|-------|
|           |      |       |      | High/Med/Low |       |
|           |      |       |      | High/Med/Low |       |
|           |      |       |      | High/Med/Low |       |

### Thematic Categories
#### Theme 1: 
- Source 1:
- Source 2:
- Source 3:

#### Theme 2:
- Source 1:
- Source 2:
- Source 3:

#### Theme 3:
- Source 1:
- Source 2:
- Source 3:

---`,
        variables: [],
        order: 3,
      },
      {
        id: 'synthesis',
        title: 'Synthesis',
        content: `## Synthesis and Analysis

### Key Findings
1. **Finding 1:**
   - Supporting Sources:
   - Evidence:
   
2. **Finding 2:**
   - Supporting Sources:
   - Evidence:

3. **Finding 3:**
   - Supporting Sources:
   - Evidence:

### Theoretical Frameworks
- **Framework 1:** [Description and relevance]
- **Framework 2:** [Description and relevance]
- **Framework 3:** [Description and relevance]

### Methodological Approaches
- **Approach 1:** [Description and frequency in literature]
- **Approach 2:** [Description and frequency in literature]
- **Approach 3:** [Description and frequency in literature]

---`,
        variables: [],
        order: 4,
      },
      {
        id: 'gaps',
        title: 'Research Gaps',
        content: `## Research Gaps and Future Directions

### Identified Gaps
1. **Gap 1:**
   - Description:
   - Potential research questions:
   
2. **Gap 2:**
   - Description:
   - Potential research questions:

3. **Gap 3:**
   - Description:
   - Potential research questions:

### Methodological Limitations
- 
- 
- 

### Recommendations for Future Research
1. 
2. 
3. 
4. 

---`,
        variables: [],
        order: 5,
      },
      {
        id: 'conclusion',
        title: 'Conclusion',
        content: `## Conclusion

### Summary of Findings
{{#if review_topic}}
This literature review on {{review_topic}} has identified several key themes and findings:
{{/if}}

1. 
2. 
3. 

### Implications
- **Theoretical Implications:**
- **Practical Implications:**
- **Methodological Implications:**

### Limitations of This Review
- 
- 
- 

---

## References

[References will be added here in appropriate format]

---

*Literature review completed on {{formatDate(createdAt)}}*`,
        variables: ['review_topic'],
        order: 6,
      },
    ],
  },

  [BUILT_IN_TEMPLATES.WEEKLY_REPORT]: {
    id: BUILT_IN_TEMPLATES.WEEKLY_REPORT,
    name: 'Weekly Report',
    description: 'Weekly progress and status report template for team updates',
    category: TEMPLATE_CATEGORIES.DOCUMENTATION,
    contentType: 'notebook',
    version: '1.0.0',
    author: 'Lighthouse LM',
    tags: ['weekly', 'report', 'progress', 'status', 'team'],
    isPublic: true,
    isBuiltIn: true,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
    usage: { totalUsed: 0 },
    thumbnail: '📊',
    
    variables: [
      {
        id: 'week_ending',
        name: 'week_ending',
        type: 'date',
        label: 'Week Ending',
        required: true,
        defaultValue: new Date().toISOString().split('T')[0],
      },
      {
        id: 'team_member',
        name: 'team_member',
        type: 'text',
        label: 'Team Member',
        required: true,
        placeholder: 'Enter your name...',
      },
      {
        id: 'manager',
        name: 'manager',
        type: 'text',
        label: 'Manager',
        placeholder: 'Enter manager name...',
      },
      {
        id: 'project',
        name: 'project',
        type: 'text',
        label: 'Primary Project',
        placeholder: 'Enter main project name...',
      },
    ],
    
    sections: [
      {
        id: 'header',
        title: 'Report Header',
        content: `# Weekly Report - Week Ending {{week_ending}}

**Team Member:** {{team_member}}
**Manager:** {{manager}}
**Primary Project:** {{project}}
**Report Date:** {{formatDate(createdAt)}}

---`,
        variables: ['week_ending', 'team_member', 'manager', 'project'],
        order: 1,
      },
      {
        id: 'accomplishments',
        title: 'Accomplishments',
        content: `## Key Accomplishments This Week

### Major Milestones
- [ ] ✅ 
- [ ] ✅ 
- [ ] ✅ 

### Tasks Completed
1. **Task Name:** [Description]
   - Impact: 
   - Time Invested: 

2. **Task Name:** [Description]
   - Impact: 
   - Time Invested: 

3. **Task Name:** [Description]
   - Impact: 
   - Time Invested: 

### Metrics/KPIs Achieved
| Metric | Target | Actual | Status |
|--------|--------|--------|---------|
|        |        |        | ✅/❌/🟡 |
|        |        |        | ✅/❌/🟡 |

---`,
        variables: [],
        order: 2,
      },
      {
        id: 'challenges',
        title: 'Challenges & Blockers',
        content: `## Challenges & Blockers

### Current Blockers
1. **Blocker:** 
   - **Impact:** 
   - **Resolution Needed:** 
   - **ETA:** 

2. **Blocker:** 
   - **Impact:** 
   - **Resolution Needed:** 
   - **ETA:** 

### Challenges Overcome
- **Challenge:** 
  **Solution:** 
  **Outcome:** 

### Support Needed
- [ ] 
- [ ] 
- [ ] 

---`,
        variables: [],
        order: 3,
      },
      {
        id: 'upcoming',
        title: 'Upcoming Priorities',
        content: `## Priorities for Next Week

### Top 3 Priorities
1. **Priority 1:** 
   - Expected Outcome: 
   - Time Estimate: 
   
2. **Priority 2:** 
   - Expected Outcome: 
   - Time Estimate: 
   
3. **Priority 3:** 
   - Expected Outcome: 
   - Time Estimate: 

### Scheduled Meetings/Events
| Date | Meeting | Purpose | Preparation Needed |
|------|---------|---------|-------------------|
|      |         |         |                   |
|      |         |         |                   |

### Upcoming Deadlines
- **[Date]:** 
- **[Date]:** 
- **[Date]:** 

---`,
        variables: [],
        order: 4,
      },
      {
        id: 'learning',
        title: 'Learning & Development',
        content: `## Learning & Development

### New Skills/Knowledge Gained
- 
- 
- 

### Training/Courses Completed
- 
- 

### Areas for Improvement
- 
- 
- 

### Learning Goals for Next Week
- [ ] 
- [ ] 
- [ ] 

---`,
        variables: [],
        order: 5,
      },
      {
        id: 'feedback',
        title: 'Feedback & Notes',
        content: `## Additional Notes & Feedback

### Team Collaboration Highlights
- 
- 
- 

### Process Improvements Identified
- 
- 

### Feedback for Management
- 
- 

### Personal Reflection
- **What went well:** 
- **What could be improved:** 
- **Key learnings:** 

---

*Report submitted by {{team_member}} on {{formatDate(createdAt)}}*`,
        variables: ['team_member'],
        order: 6,
      },
    ],
  },
};