# Content Templates System

A comprehensive template system for the Lighthouse LM project that enables users to create, manage, and use structured content templates for notebooks, sources, slides, and chat prompts.

## Features

### 🎯 Core Features
- **Template Library**: Browse and manage templates with categories, search, and filtering
- **Template Editor**: Visual editor for creating and modifying templates
- **Live Preview**: Real-time preview of templates with variable substitution
- **Variable System**: Support for multiple variable types with validation
- **Template Versioning**: Track template versions and changes
- **Built-in Templates**: Pre-configured templates for common use cases

### 🔧 Variable Types
- **Text**: Single-line text input
- **Textarea**: Multi-line text input
- **Number**: Numeric input with validation
- **Date**: Date picker
- **Select**: Single selection from options
- **Multiselect**: Multiple selections from options
- **Boolean**: Toggle switch
- **URL**: URL input with validation

### 📚 Built-in Templates
- **Research Notes**: Structured academic research documentation
- **Meeting Notes**: Professional meeting documentation
- **Project Planning**: Comprehensive project planning template
- **Literature Review**: Systematic literature review template
- **Weekly Report**: Team progress and status reports

### 🏷️ Categories
- Research & Analysis
- Meetings & Collaboration
- Planning & Management
- Documentation
- Presentation
- Personal

## Architecture

### Components

```
templates/
├── types.ts                 # TypeScript definitions
├── TemplateProvider.tsx     # Context provider and state management
├── TemplateLibrary.tsx      # Template browser and management
├── TemplateEditor.tsx       # Template creation and editing
├── TemplatePreview.tsx      # Live template preview and form
├── TemplateManager.tsx      # Main orchestrator component
├── builtInTemplates.ts      # Pre-configured templates
├── index.ts                 # Public API exports
└── README.md               # This file
```

### State Management

The template system uses React Context with `useReducer` for state management:

- **Templates**: CRUD operations for template definitions
- **Instances**: CRUD operations for template instances
- **Categories**: Template categorization and organization
- **Validation**: Form validation and error handling
- **Integration**: Integration with notebook, sources, and studio

## Usage Examples

### Basic Usage

```tsx
import TemplateManager from '@/templates';

function MyApp() {
  return (
    <TemplateManager
      notebookId="notebook-123"
      onTemplateApplied={(content, template) => {
        console.log('Template applied:', template.name);
        // Handle the generated content
      }}
    />
  );
}
```

### Lightweight Template Selector

```tsx
import { TemplateSelector } from '@/templates';

function QuickActions() {
  return (
    <TemplateSelector
      category="research"
      onTemplateSelect={(template, variables) => {
        // Handle template selection
      }}
    />
  );
}
```

### Custom Integration

```tsx
import { TemplateProvider, useTemplateContext } from '@/templates';

function CustomUI() {
  const {
    templates,
    createTemplate,
    renderInstance,
    sendToNotebook
  } = useTemplateContext();
  
  // Custom implementation
  return <div>Custom template UI</div>;
}

function App() {
  return (
    <TemplateProvider notebookId="notebook-123">
      <CustomUI />
    </TemplateProvider>
  );
}
```

## Template Definition

### Template Structure

```typescript
interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  contentType: ContentType;
  version: string;
  author: string;
  tags: string[];
  isPublic: boolean;
  isBuiltIn: boolean;
  
  variables: TemplateVariable[];
  sections: TemplateSection[];
  
  // Metadata
  createdAt: string;
  updatedAt: string;
  usage: TemplateUsage;
}
```

### Variable Definition

```typescript
interface TemplateVariable {
  id: string;
  name: string;
  type: VariableType;
  label: string;
  description?: string;
  required?: boolean;
  defaultValue?: any;
  placeholder?: string;
  options?: string[]; // For select/multiselect
  validation?: ValidationRules;
}
```

### Section Definition

```typescript
interface TemplateSection {
  id: string;
  title: string;
  content: string;
  variables: string[]; // Variable IDs used
  order: number;
  optional?: boolean;
}
```

## Template Engine

The template engine supports:

### Variable Substitution
```markdown
Hello {{name}}, welcome to {{project}}!
```

### Conditional Content
```markdown
{{#if premium}}
Welcome to Premium features!
{{/if}}
```

### Loops
```markdown
{{#each items}}
- {{this}}
{{/each}}
```

### Helper Functions
```markdown
Today is {{formatDate(date, 'YYYY-MM-DD')}}
Total: {{formatNumber(amount, 2)}}
```

## Integration

### Notebook Context
The template system integrates seamlessly with the existing `NotebookContext`:

- Send templates to notebook as sources
- Maintain context with selected sources
- Sync with real-time updates

### Lighthouse Service
Templates integrate with the `lighthouseService` for:

- Creating notebook sources from templates
- Sending content to chat
- Creating studio documents
- Syncing with backend storage

### UI Components
Uses the existing shadcn/ui component library:

- Consistent design language
- Accessible components
- Responsive layouts
- Dark/light theme support

## Extensibility

### Adding New Variable Types

1. Extend the `VariableType` union in `types.ts`:
```typescript
export type VariableType = 'text' | 'number' | 'custom';
```

2. Add input component in `TemplateEditor.tsx`:
```tsx
case 'custom':
  return <CustomInput {...props} />;
```

3. Add preview handling in `TemplatePreview.tsx`:
```tsx
case 'custom':
  // Handle custom variable rendering
```

### Adding New Categories

1. Update `TEMPLATE_CATEGORIES` in `types.ts`:
```typescript
export const TEMPLATE_CATEGORIES = {
  CUSTOM: 'custom',
  // ...existing categories
} as const;
```

2. Add category configuration in `TemplateProvider.tsx`:
```typescript
{
  id: 'custom',
  name: 'Custom Category',
  description: 'Custom templates',
  icon: '🎨',
  color: '#FF6B6B',
  order: 7
}
```

### Adding New Built-in Templates

Create templates in `builtInTemplates.ts`:

```typescript
export const builtInTemplates: Record<string, Template> = {
  MY_TEMPLATE: {
    id: 'my-template-v1',
    name: 'My Template',
    // ...template definition
  }
};
```

## Performance Considerations

- **Lazy Loading**: Templates are loaded on-demand
- **Debounced Preview**: Preview generation is debounced to avoid excessive re-renders
- **Virtual Scrolling**: Large template lists use virtual scrolling
- **Caching**: Template content and previews are cached
- **Optimistic Updates**: UI updates immediately with server sync in background

## Security

- **Input Sanitization**: All user inputs are sanitized
- **Template Validation**: Templates are validated before rendering
- **XSS Protection**: Template content is escaped to prevent XSS
- **Permission Checks**: Template sharing respects user permissions

## Future Enhancements

- **Template Marketplace**: Public template sharing and discovery
- **Collaborative Editing**: Real-time collaborative template editing
- **Template Analytics**: Usage analytics and performance metrics
- **AI-Powered Templates**: AI-generated template suggestions
- **Import/Export**: Template sharing via files
- **Custom Themes**: Template-specific styling and themes
- **Advanced Validation**: Complex validation rules and custom validators
- **Template Inheritance**: Template hierarchies and inheritance
- **Version Control**: Git-like versioning for templates
- **Plugin System**: Extensible plugin architecture for custom functionality