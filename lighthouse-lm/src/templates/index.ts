// Template System Exports
export * from './types';
export * from './TemplateProvider';
export * from './TemplateLibrary';
export * from './TemplateEditor';
export * from './TemplatePreview';
export * from './TemplateManager';
export { builtInTemplates } from './builtInTemplates';

// Re-export commonly used components for convenience
export { TemplateManager as default } from './TemplateManager';

/**
 * Template System Integration Guide
 * 
 * ## Basic Usage
 * 
 * ### Full Template Manager
 * ```tsx
 * import TemplateManager from '@/templates';
 * 
 * function MyComponent() {
 *   return (
 *     <TemplateManager
 *       notebookId="notebook-123"
 *       onTemplateApplied={(content, template) => {
 *         // Handle the generated content
 *         console.log('Template applied:', template.name, content);
 *       }}
 *     />
 *   );
 * }
 * ```
 * 
 * ### Template Selector (Lightweight)
 * ```tsx
 * import { TemplateSelector } from '@/templates';
 * 
 * function QuickTemplates() {
 *   return (
 *     <TemplateSelector
 *       category="research"
 *       onTemplateSelect={(template, variables) => {
 *         // Handle template selection
 *       }}
 *     />
 *   );
 * }
 * ```
 * 
 * ### Context-only Usage
 * ```tsx
 * import { TemplateProvider, useTemplateContext } from '@/templates';
 * 
 * function CustomTemplateUI() {
 *   const { templates, createTemplate, renderInstance } = useTemplateContext();
 *   // Custom implementation using the context
 * }
 * 
 * function App() {
 *   return (
 *     <TemplateProvider notebookId="notebook-123">
 *       <CustomTemplateUI />
 *     </TemplateProvider>
 *   );
 * }
 * ```
 * 
 * ## Integration with Lighthouse LM Components
 * 
 * ### Integration with Notebook Context
 * The template system automatically integrates with the existing NotebookContext
 * to provide seamless content creation and sharing capabilities.
 * 
 * ### Integration with Sources
 * Templates can be sent to the notebook as sources, maintaining the existing
 * workflow while adding structured content creation capabilities.
 * 
 * ### Integration with Chat
 * Templates can be sent directly to chat with optional context from selected sources.
 * 
 * ### Integration with Studio
 * Templates support all Studio document types (Note, Diagram, Slide, Code, Markdown, RichText).
 * 
 * ## Extending the System
 * 
 * ### Custom Variable Types
 * Add new variable types by extending the VariableType union in types.ts
 * and implementing the corresponding input components in TemplateEditor.tsx.
 * 
 * ### Custom Template Categories
 * Add new categories by updating the TEMPLATE_CATEGORIES constant and
 * providing appropriate icons and colors.
 * 
 * ### Custom Built-in Templates
 * Add new templates to builtInTemplates.ts following the existing patterns.
 * 
 * ### Template Marketplace
 * The system is designed to support a future template marketplace with
 * sharing, rating, and discovery features.
 */