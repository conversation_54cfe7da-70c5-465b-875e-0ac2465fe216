/**
 * Template Integration Examples
 * 
 * This file demonstrates how to integrate the template system with
 * the existing Lighthouse LM components and workflows.
 */

import React, { useState } from 'react';
import { Template } from '../types';
import { TemplateManager, TemplateSelector, TemplateQuickActions } from '../TemplateManager';
import { useNotebookContext } from '../../contexts/NotebookContext';
import { Button } from '../../components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../../components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';

/**
 * Example 1: Full Template Manager Integration
 * 
 * Shows how to integrate the complete template manager into a larger application
 */
export const FullTemplateManagerExample: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { sendToStudio, sendToChat } = useNotebookContext();

  const handleTemplateApplied = async (content: string, template: Template) => {
    // Example: Send to studio based on template content type
    switch (template.contentType) {
      case 'slide':
        await sendToStudio(content, 'Slide');
        break;
      case 'note':
        await sendToStudio(content, 'Note');
        break;
      case 'chat_prompt':
        await sendToChat(content);
        break;
      default:
        await sendToStudio(content, 'Markdown');
        break;
    }
    
    setIsOpen(false);
  };

  return (
    <>
      <Button onClick={() => setIsOpen(true)}>
        Open Template Manager
      </Button>
      
      {isOpen && (
        <div className="fixed inset-0 z-50 bg-background">
          <TemplateManager
            notebookId="current-notebook-id"
            onTemplateApplied={handleTemplateApplied}
            onClose={() => setIsOpen(false)}
          />
        </div>
      )}
    </>
  );
};

/**
 * Example 2: Template Selector in Sidebar
 * 
 * Shows how to embed a lightweight template selector in a sidebar
 */
export const SidebarTemplateExample: React.FC = () => {
  const { addSourceToContext, sendToStudio } = useNotebookContext();

  const handleTemplateSelect = async (template: Template, variables: Record<string, any>) => {
    // Generate content from template (simplified)
    const content = await generateContent(template, variables);
    
    // Add as source to current context
    await addSourceToContext({
      id: `template-${Date.now()}`,
      title: `${template.name} - ${variables.title || 'Generated'}`,
      content,
      source_type: 'Note',
      metadata: {
        template_id: template.id,
        template_version: template.version,
        generated_at: new Date().toISOString(),
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      processed: true,
      tags: template.tags,
      notebook_id: 'current-notebook-id',
    });
  };

  return (
    <Card className="w-80">
      <CardHeader>
        <CardTitle className="text-base">Quick Templates</CardTitle>
        <CardDescription>Create structured content from templates</CardDescription>
      </CardHeader>
      <CardContent>
        <TemplateSelector
          category="research"
          onTemplateSelect={handleTemplateSelect}
          notebookId="current-notebook-id"
        />
      </CardContent>
    </Card>
  );
};

/**
 * Example 3: Context Menu Integration
 * 
 * Shows how to add template options to context menus
 */
export const ContextMenuTemplateExample: React.FC = () => {
  const [selectedText, setSelectedText] = useState('');
  const { sendToStudio } = useNotebookContext();

  const handleCreateTemplateFromSelection = () => {
    // Example: Create a template from selected text
    const template: Partial<Template> = {
      name: 'Custom Template from Selection',
      description: 'Generated from selected text',
      category: 'personal',
      contentType: 'note',
      version: '1.0.0',
      author: 'User',
      tags: ['custom', 'generated'],
      isPublic: false,
      variables: [
        {
          id: 'title',
          name: 'title',
          type: 'text',
          label: 'Document Title',
          required: true,
        }
      ],
      sections: [
        {
          id: 'content',
          title: 'Content',
          content: selectedText,
          variables: ['title'],
          order: 1,
        }
      ]
    };
    
    // Open template editor with pre-filled content
    console.log('Would open template editor with:', template);
  };

  return (
    <div className="space-y-4">
      <div>
        <label htmlFor="text-selection" className="block text-sm font-medium mb-2">
          Select text to create template:
        </label>
        <textarea
          id="text-selection"
          value={selectedText}
          onChange={(e) => setSelectedText(e.target.value)}
          className="w-full p-2 border rounded-md"
          rows={4}
          placeholder="Enter or paste text to convert to template..."
        />
      </div>
      
      <Button 
        onClick={handleCreateTemplateFromSelection}
        disabled={!selectedText.trim()}
      >
        Create Template from Selection
      </Button>
    </div>
  );
};

/**
 * Example 4: Dashboard Integration
 * 
 * Shows template quick actions in a dashboard layout
 */
export const DashboardTemplateExample: React.FC = () => {
  const [showTemplateManager, setShowTemplateManager] = useState(false);
  
  // Mock recent templates - in real app this would come from context
  const recentTemplates: Template[] = [
    {
      id: 'research-notes-v1',
      name: 'Research Notes',
      description: 'Academic research documentation',
      category: 'research',
      contentType: 'notebook',
      version: '1.0.0',
      author: 'Lighthouse LM',
      tags: ['research', 'academic'],
      isPublic: true,
      isBuiltIn: true,
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01',
      variables: [],
      sections: [],
      usage: { totalUsed: 15, lastUsed: '2024-01-15' },
      thumbnail: '🔬',
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {/* Template Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Templates</CardTitle>
          <CardDescription>Quick access to templates</CardDescription>
        </CardHeader>
        <CardContent>
          <TemplateQuickActions
            onUseTemplate={(template) => {
              console.log('Use template:', template.name);
              setShowTemplateManager(true);
            }}
            onCreateTemplate={() => setShowTemplateManager(true)}
            recentTemplates={recentTemplates}
          />
        </CardContent>
      </Card>

      {/* Template Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Template Usage</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Templates Used</span>
              <span className="text-sm font-medium">42</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Custom Templates</span>
              <span className="text-sm font-medium">7</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Most Used</span>
              <span className="text-sm font-medium">Research Notes</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Template Categories */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Categories</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {['Research', 'Meetings', 'Planning', 'Documentation'].map(category => (
              <div key={category} className="flex justify-between">
                <span className="text-sm text-muted-foreground">{category}</span>
                <span className="text-sm font-medium">
                  {Math.floor(Math.random() * 10) + 1}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Full Template Manager Dialog */}
      <Dialog open={showTemplateManager} onOpenChange={setShowTemplateManager}>
        <DialogContent className="max-w-7xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Template Manager</DialogTitle>
          </DialogHeader>
          <div className="h-[calc(90vh-6rem)] overflow-y-auto">
            <TemplateManager
              notebookId="current-notebook-id"
              onTemplateApplied={(content, template) => {
                console.log('Template applied:', template.name);
                setShowTemplateManager(false);
              }}
              onClose={() => setShowTemplateManager(false)}
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

/**
 * Example 5: Chat Integration
 * 
 * Shows how to use templates in chat context
 */
export const ChatTemplateExample: React.FC = () => {
  const { sendMessage, selectedSources } = useNotebookContext();
  
  const handleUsePromptTemplate = async (template: Template, variables: Record<string, any>) => {
    const prompt = await generateContent(template, variables);
    
    // Send to chat with current context
    await sendMessage(prompt, {
      sources: selectedSources.map(s => s.id),
      template_id: template.id,
      template_variables: variables,
    });
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Chat Templates</h3>
      <TemplateSelector
        contentType="chat_prompt"
        onTemplateSelect={handleUsePromptTemplate}
        notebookId="current-notebook-id"
      />
    </div>
  );
};

// Helper function (simplified implementation)
const generateContent = async (template: Template, variables: Record<string, any>): Promise<string> => {
  return template.sections
    .sort((a, b) => a.order - b.order)
    .map(section => {
      let content = section.content;
      Object.keys(variables).forEach(key => {
        const regex = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, 'g');
        content = content.replace(regex, String(variables[key] || ''));
      });
      return content;
    })
    .join('\n\n');
};

/**
 * Usage in Main Application
 * 
 * These examples can be integrated into the main Lighthouse LM application:
 * 
 * 1. Add FullTemplateManagerExample to main navigation
 * 2. Add SidebarTemplateExample to sidebar panels
 * 3. Add ContextMenuTemplateExample to text selection menus
 * 4. Add DashboardTemplateExample to dashboard
 * 5. Add ChatTemplateExample to chat interface
 */