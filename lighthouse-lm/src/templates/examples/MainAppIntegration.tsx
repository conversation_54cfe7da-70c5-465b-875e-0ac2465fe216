/**
 * Main Application Integration Example
 * 
 * This file shows how to integrate the template system into the main
 * Lighthouse LM application structure.
 */

import React, { useState } from 'react';
import { Template } from '../types';
import TemplateManager from '../TemplateManager';
import { Button } from '../../components/ui/button';
import { FileText, Plus, Sparkles } from 'lucide-react';
import { useNotebookContext } from '../../contexts/NotebookContext';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../../components/ui/dialog';

/**
 * Enhanced Sidebar with Template Integration
 * 
 * This shows how templates could be integrated into the existing sidebar
 */
export const EnhancedSidebar: React.FC = () => {
  const [showTemplates, setShowTemplates] = useState(false);
  const { sendToStudio, addSource } = useNotebookContext();

  const handleTemplateApplied = async (content: string, template: Template) => {
    // Add the generated content as a new source
    await addSource({
      title: `${template.name} - Generated Content`,
      content: content,
      type: 'Note',
    });
    
    setShowTemplates(false);
  };

  return (
    <div className="w-80 bg-muted/30 p-4 space-y-4">
      {/* Existing sidebar content would go here */}
      
      {/* Template Section */}
      <div className="space-y-2">
        <h3 className="text-sm font-semibold flex items-center space-x-2">
          <Sparkles className="h-4 w-4" />
          <span>Templates</span>
        </h3>
        
        <div className="space-y-1">
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-start"
            onClick={() => setShowTemplates(true)}
          >
            <FileText className="h-4 w-4 mr-2" />
            Browse Templates
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-start"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Template
          </Button>
        </div>

        {/* Quick Template Options */}
        <div className="pt-2 space-y-1">
          <p className="text-xs text-muted-foreground">Quick Start</p>
          {[
            { name: 'Research Notes', icon: '🔬' },
            { name: 'Meeting Notes', icon: '📋' },
            { name: 'Project Plan', icon: '📅' },
          ].map(template => (
            <Button
              key={template.name}
              variant="ghost"
              size="sm"
              className="w-full justify-start text-xs"
            >
              <span className="mr-2">{template.icon}</span>
              {template.name}
            </Button>
          ))}
        </div>
      </div>

      {/* Template Manager Dialog */}
      <Dialog open={showTemplates} onOpenChange={setShowTemplates}>
        <DialogContent className="max-w-7xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Content Templates</DialogTitle>
          </DialogHeader>
          <div className="h-[calc(90vh-4rem)]">
            <TemplateManager
              notebookId="current-notebook"
              onTemplateApplied={handleTemplateApplied}
              onClose={() => setShowTemplates(false)}
            />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

/**
 * Enhanced Main Dashboard
 * 
 * Shows how templates could be integrated into the main dashboard
 */
export const EnhancedDashboard: React.FC = () => {
  const [showTemplates, setShowTemplates] = useState(false);

  return (
    <div className="flex h-screen">
      <EnhancedSidebar />
      
      <div className="flex-1 p-6">
        {/* Existing dashboard content */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold">Lighthouse LM Dashboard</h1>
          <p className="text-muted-foreground">
            Research, analyze, and create with AI assistance
          </p>
        </div>

        {/* Quick Actions with Template Integration */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="p-6 border rounded-lg">
            <h3 className="font-semibold mb-2">Quick Start</h3>
            <div className="space-y-2">
              <Button className="w-full" onClick={() => setShowTemplates(true)}>
                <Sparkles className="h-4 w-4 mr-2" />
                Use Template
              </Button>
              <Button variant="outline" className="w-full">
                <Plus className="h-4 w-4 mr-2" />
                New Notebook
              </Button>
            </div>
          </div>

          {/* Other dashboard widgets would go here */}
        </div>

        {/* Main content area */}
        <div className="border rounded-lg p-6">
          <p className="text-muted-foreground text-center py-8">
            Select a notebook or create content using templates to get started
          </p>
        </div>
      </div>
    </div>
  );
};

/**
 * Integration with Chat Interface
 * 
 * Shows how templates could be integrated into the chat interface
 */
export const EnhancedChatInterface: React.FC = () => {
  const [showPromptTemplates, setShowPromptTemplates] = useState(false);
  const { sendMessage } = useNotebookContext();

  return (
    <div className="flex flex-col h-full">
      {/* Chat messages area */}
      <div className="flex-1 p-4 overflow-y-auto">
        {/* Existing chat messages would go here */}
        <div className="text-center py-8 text-muted-foreground">
          Start a conversation or use a prompt template
        </div>
      </div>

      {/* Enhanced input area with template integration */}
      <div className="p-4 border-t">
        <div className="flex items-end space-x-2">
          <div className="flex-1">
            <textarea
              placeholder="Type your message or use a template..."
              className="w-full p-3 border rounded-lg resize-none"
              rows={3}
            />
          </div>
          
          <div className="flex flex-col space-y-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowPromptTemplates(true)}
            >
              <FileText className="h-4 w-4" />
            </Button>
            <Button>Send</Button>
          </div>
        </div>

        {/* Quick prompt templates */}
        <div className="mt-2 flex flex-wrap gap-2">
          {[
            'Analyze document',
            'Summarize findings',
            'Generate outline',
            'Research questions',
          ].map(prompt => (
            <Button
              key={prompt}
              variant="outline"
              size="sm"
              className="text-xs"
            >
              {prompt}
            </Button>
          ))}
        </div>
      </div>
    </div>
  );
};

/**
 * How to integrate into LighthouseLMDashboard.tsx
 * 
 * Replace the existing dashboard component with the enhanced version:
 * 
 * ```tsx
 * // In LighthouseLMDashboard.tsx
 * import { EnhancedDashboard } from './templates/examples/MainAppIntegration';
 * 
 * export const LighthouseLMDashboard: React.FC = () => {
 *   return <EnhancedDashboard />;
 * };
 * ```
 */