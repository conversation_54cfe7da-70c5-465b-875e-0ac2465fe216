// Template System Types
export type VariableType = 'text' | 'number' | 'date' | 'select' | 'multiselect' | 'boolean' | 'textarea' | 'url';

export interface TemplateVariable {
  id: string;
  name: string;
  type: VariableType;
  label: string;
  description?: string;
  defaultValue?: string | number | boolean | string[];
  required?: boolean;
  placeholder?: string;
  options?: string[]; // For select/multiselect
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    custom?: (value: any) => boolean | string;
  };
}

export type ContentType = 'notebook' | 'source' | 'slide' | 'chat_prompt' | 'note' | 'diagram';

export interface TemplateSection {
  id: string;
  title: string;
  content: string;
  variables: string[]; // Variable IDs used in this section
  order: number;
  optional?: boolean;
}

export interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  contentType: ContentType;
  version: string;
  author: string;
  tags: string[];
  isPublic: boolean;
  isBuiltIn: boolean;
  createdAt: string;
  updatedAt: string;
  
  // Template Structure
  variables: TemplateVariable[];
  sections: TemplateSection[];
  
  // Inheritance
  parentTemplateId?: string;
  overriddenSections?: string[]; // Section IDs that override parent
  
  // Metadata
  usage: {
    totalUsed: number;
    lastUsed?: string;
    averageRating?: number;
    reviews?: number;
  };
  
  // Preview
  thumbnail?: string;
  previewContent?: string;
}

export interface TemplateInstance {
  id: string;
  templateId: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  variableValues: Record<string, any>;
  customSections?: TemplateSection[]; // User-modified sections
  renderedContent?: string;
}

export interface TemplateCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  order: number;
  parentId?: string;
}

export interface TemplateMarketplace {
  featured: Template[];
  categories: TemplateCategory[];
  popular: Template[];
  recent: Template[];
  userTemplates: Template[];
}

export interface TemplateValidationResult {
  isValid: boolean;
  errors: Array<{
    field: string;
    message: string;
    type: 'required' | 'validation' | 'format';
  }>;
  warnings: Array<{
    field: string;
    message: string;
  }>;
}

export interface TemplateRenderContext {
  variables: Record<string, any>;
  metadata: {
    createdAt: string;
    author: string;
    templateName: string;
    version: string;
  };
  helpers: {
    formatDate: (date: string | Date, format?: string) => string;
    formatNumber: (num: number, decimals?: number) => string;
    capitalize: (str: string) => string;
    plural: (str: string, count: number) => string;
    random: (items: string[]) => string;
  };
}

// Built-in Template Configurations
export const BUILT_IN_TEMPLATES = {
  RESEARCH_NOTES: 'research-notes-v1',
  MEETING_NOTES: 'meeting-notes-v1',
  PROJECT_PLANNING: 'project-planning-v1',
  LITERATURE_REVIEW: 'literature-review-v1',
  INTERVIEW_SUMMARY: 'interview-summary-v1',
  TECHNICAL_SPEC: 'technical-spec-v1',
  WEEKLY_REPORT: 'weekly-report-v1',
  PRESENTATION_OUTLINE: 'presentation-outline-v1',
  HYPOTHESIS_TESTING: 'hypothesis-testing-v1',
  DATA_ANALYSIS: 'data-analysis-v1',
} as const;

export const TEMPLATE_CATEGORIES = {
  RESEARCH: 'research',
  MEETINGS: 'meetings', 
  PLANNING: 'planning',
  ANALYSIS: 'analysis',
  DOCUMENTATION: 'documentation',
  PRESENTATION: 'presentation',
  COLLABORATION: 'collaboration',
  PERSONAL: 'personal',
} as const;

// Template Engine Configuration
export interface TemplateEngineConfig {
  variableDelimiters: {
    start: string;
    end: string;
  };
  sectionDelimiters: {
    start: string;
    end: string;
  };
  conditionalDelimiters: {
    if: string;
    else: string;
    endif: string;
  };
  loopDelimiters: {
    for: string;
    endfor: string;
  };
}

export const DEFAULT_TEMPLATE_CONFIG: TemplateEngineConfig = {
  variableDelimiters: {
    start: '{{',
    end: '}}',
  },
  sectionDelimiters: {
    start: '<!-- SECTION:',
    end: '-->',
  },
  conditionalDelimiters: {
    if: '{{#if',
    else: '{{else}}',
    endif: '{{/if}}',
  },
  loopDelimiters: {
    for: '{{#each',
    endfor: '{{/each}}',
  },
};