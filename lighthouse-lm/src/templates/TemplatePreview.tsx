import React, { useState, useEffect } from 'react';
import { Eye, Edit, Download, Share2, Refresh, Maximize2, Minimize2, Co<PERSON>, Check } from 'lucide-react';
import { Template, TemplateInstance, TemplateVariable } from './types';
import { useTemplateContext } from './TemplateProvider';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Textarea } from '../components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Switch } from '../components/ui/switch';
import { Separator } from '../components/ui/separator';
import { ScrollArea } from '../components/ui/scroll-area';
import { useToast } from '../hooks/useToast';
import { useNotebookContext } from '../contexts/NotebookContext';

interface TemplatePreviewProps {
  template: Template;
  instance?: TemplateInstance;
  onCreateInstance?: (variables: Record<string, any>) => void;
  onUpdateInstance?: (variables: Record<string, any>) => void;
  onEdit?: () => void;
  onClose?: () => void;
}

export const TemplatePreview: React.FC<TemplatePreviewProps> = ({
  template,
  instance,
  onCreateInstance,
  onUpdateInstance,
  onEdit,
  onClose,
}) => {
  const {
    previewTemplate,
    validateInstance,
    isRendering,
    sendToNotebook,
    sendToChat,
    sendToStudio,
  } = useTemplateContext();
  
  const { sendToStudio: notebookSendToStudio } = useNotebookContext();
  const { toast } = useToast();
  
  const [variables, setVariables] = useState<Record<string, any>>({});
  const [previewContent, setPreviewContent] = useState<string>('');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [activeTab, setActiveTab] = useState<'form' | 'preview'>('form');
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [copied, setCopied] = useState(false);

  // Initialize variables from instance or defaults
  useEffect(() => {
    const initialVariables: Record<string, any> = {};
    
    if (instance) {
      // Load from existing instance
      Object.assign(initialVariables, instance.variableValues);
    } else {
      // Set default values
      template.variables.forEach(variable => {
        if (variable.defaultValue !== undefined) {
          initialVariables[variable.id] = variable.defaultValue;
        } else {
          switch (variable.type) {
            case 'text':
            case 'textarea':
            case 'url':
              initialVariables[variable.id] = '';
              break;
            case 'number':
              initialVariables[variable.id] = 0;
              break;
            case 'date':
              initialVariables[variable.id] = new Date().toISOString().split('T')[0];
              break;
            case 'boolean':
              initialVariables[variable.id] = false;
              break;
            case 'select':
              initialVariables[variable.id] = variable.options?.[0] || '';
              break;
            case 'multiselect':
              initialVariables[variable.id] = [];
              break;
          }
        }
      });
    }
    
    setVariables(initialVariables);
  }, [template, instance]);

  // Auto-generate preview when variables change
  useEffect(() => {
    const generatePreview = async () => {
      if (Object.keys(variables).length === 0) return;
      
      try {
        const preview = await previewTemplate(template, variables);
        setPreviewContent(preview);
      } catch (error) {
        console.error('Preview generation failed:', error);
        setPreviewContent('Error generating preview');
      }
    };

    const debounceTimer = setTimeout(generatePreview, 500);
    return () => clearTimeout(debounceTimer);
  }, [variables, template, previewTemplate]);

  // Validate form
  useEffect(() => {
    const errors: Record<string, string> = {};
    
    template.variables.forEach(variable => {
      const value = variables[variable.id];
      
      if (variable.required && (!value || (Array.isArray(value) && value.length === 0))) {
        errors[variable.id] = `${variable.label} is required`;
        return;
      }
      
      if (value && variable.validation) {
        if (variable.validation.min && typeof value === 'string' && value.length < variable.validation.min) {
          errors[variable.id] = `Must be at least ${variable.validation.min} characters`;
        }
        
        if (variable.validation.max && typeof value === 'string' && value.length > variable.validation.max) {
          errors[variable.id] = `Must not exceed ${variable.validation.max} characters`;
        }
        
        if (variable.validation.pattern && typeof value === 'string') {
          const regex = new RegExp(variable.validation.pattern);
          if (!regex.test(value)) {
            errors[variable.id] = 'Invalid format';
          }
        }
      }
    });
    
    setValidationErrors(errors);
  }, [variables, template]);

  const updateVariable = (id: string, value: any) => {
    setVariables(prev => ({ ...prev, [id]: value }));
  };

  const handleSave = () => {
    if (Object.keys(validationErrors).length > 0) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors before saving",
        variant: "destructive",
      });
      return;
    }

    if (instance && onUpdateInstance) {
      onUpdateInstance(variables);
    } else if (onCreateInstance) {
      onCreateInstance(variables);
    }
  };

  const handleCopyContent = async () => {
    try {
      await navigator.clipboard.writeText(previewContent);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      toast({
        title: "Copied to clipboard",
        description: "Template content has been copied to your clipboard",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      });
    }
  };

  const handleSendToNotebook = async () => {
    try {
      await sendToNotebook(previewContent);
      toast({
        title: "Sent to notebook",
        description: "Template content has been added to your notebook",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send to notebook",
        variant: "destructive",
      });
    }
  };

  const handleSendToChat = async () => {
    try {
      await sendToChat(previewContent);
      toast({
        title: "Sent to chat",
        description: "Template content has been sent to chat",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send to chat",
        variant: "destructive",
      });
    }
  };

  const handleSendToStudio = async (type: 'Note' | 'Diagram' | 'Slide' | 'Code' | 'Markdown' | 'RichText') => {
    try {
      await notebookSendToStudio(previewContent, type);
      toast({
        title: "Sent to studio",
        description: `Template content has been created as a new ${type.toLowerCase()} document`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send to studio",
        variant: "destructive",
      });
    }
  };

  const renderVariableInput = (variable: TemplateVariable) => {
    const value = variables[variable.id];
    const error = validationErrors[variable.id];

    const commonProps = {
      id: variable.id,
      placeholder: variable.placeholder,
    };

    const inputElement = (() => {
      switch (variable.type) {
        case 'text':
        case 'url':
          return (
            <Input
              {...commonProps}
              type={variable.type === 'url' ? 'url' : 'text'}
              value={value || ''}
              onChange={(e) => updateVariable(variable.id, e.target.value)}
              className={error ? 'border-destructive' : ''}
            />
          );
        
        case 'textarea':
          return (
            <Textarea
              {...commonProps}
              value={value || ''}
              onChange={(e) => updateVariable(variable.id, e.target.value)}
              rows={4}
              className={error ? 'border-destructive' : ''}
            />
          );
        
        case 'number':
          return (
            <Input
              {...commonProps}
              type="number"
              value={value || ''}
              onChange={(e) => updateVariable(variable.id, parseFloat(e.target.value) || 0)}
              className={error ? 'border-destructive' : ''}
            />
          );
        
        case 'date':
          return (
            <Input
              {...commonProps}
              type="date"
              value={value || ''}
              onChange={(e) => updateVariable(variable.id, e.target.value)}
              className={error ? 'border-destructive' : ''}
            />
          );
        
        case 'boolean':
          return (
            <div className="flex items-center space-x-2">
              <Switch
                checked={value || false}
                onCheckedChange={(checked) => updateVariable(variable.id, checked)}
              />
              <Label htmlFor={variable.id} className="text-sm">
                {value ? 'Enabled' : 'Disabled'}
              </Label>
            </div>
          );
        
        case 'select':
          return (
            <Select
              value={value || ''}
              onValueChange={(selectedValue) => updateVariable(variable.id, selectedValue)}
            >
              <SelectTrigger className={error ? 'border-destructive' : ''}>
                <SelectValue placeholder={variable.placeholder || 'Select an option'} />
              </SelectTrigger>
              <SelectContent>
                {variable.options?.map(option => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          );
        
        case 'multiselect':
          return (
            <div className="space-y-2">
              <div className="flex flex-wrap gap-2">
                {(value || []).map((selectedOption: string) => (
                  <Badge
                    key={selectedOption}
                    variant="secondary"
                    className="cursor-pointer"
                    onClick={() => {
                      const newValue = (value || []).filter((v: string) => v !== selectedOption);
                      updateVariable(variable.id, newValue);
                    }}
                  >
                    {selectedOption} ×
                  </Badge>
                ))}
              </div>
              <Select
                value=""
                onValueChange={(selectedValue) => {
                  if (!value?.includes(selectedValue)) {
                    updateVariable(variable.id, [...(value || []), selectedValue]);
                  }
                }}
              >
                <SelectTrigger className={error ? 'border-destructive' : ''}>
                  <SelectValue placeholder="Add option" />
                </SelectTrigger>
                <SelectContent>
                  {variable.options
                    ?.filter(option => !value?.includes(option))
                    .map(option => (
                      <SelectItem key={option} value={option}>
                        {option}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          );
        
        default:
          return <Input {...commonProps} value={value || ''} disabled />;
      }
    })();

    return (
      <div key={variable.id} className="space-y-2">
        <Label htmlFor={variable.id} className="flex items-center space-x-2">
          <span>{variable.label}</span>
          {variable.required && <Badge variant="destructive" className="text-xs px-1">Required</Badge>}
        </Label>
        {variable.description && (
          <p className="text-xs text-muted-foreground">{variable.description}</p>
        )}
        {inputElement}
        {error && <p className="text-xs text-destructive">{error}</p>}
      </div>
    );
  };

  return (
    <div className={`space-y-6 ${isFullscreen ? 'fixed inset-0 z-50 bg-background p-6 overflow-y-auto' : ''}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">{template.thumbnail || '📄'}</span>
          <div>
            <h1 className="text-xl font-bold">{template.name}</h1>
            <p className="text-sm text-muted-foreground">
              {template.description} • v{template.version}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {onEdit && (
            <Button variant="outline" size="sm" onClick={onEdit}>
              <Edit className="h-4 w-4 mr-2" />
              Edit Template
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsFullscreen(!isFullscreen)}
          >
            {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
          </Button>
          {onClose && (
            <Button variant="outline" size="sm" onClick={onClose}>
              Close
            </Button>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className={`grid gap-6 ${isFullscreen ? 'grid-cols-2' : 'grid-cols-1 lg:grid-cols-2'}`}>
        {/* Form Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center justify-between">
              <span>Template Variables</span>
              <Badge variant="secondary">{template.variables.length} variables</Badge>
            </CardTitle>
            <CardDescription>
              Fill in the variables to customize your template
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className={`${isFullscreen ? 'h-[calc(100vh-16rem)]' : 'h-96'} pr-4`}>
              <div className="space-y-6">
                {template.variables.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">
                      This template doesn't have any variables to configure.
                    </p>
                  </div>
                ) : (
                  template.variables.map(renderVariableInput)
                )}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>

        {/* Preview Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center justify-between">
              <span>Preview</span>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopyContent}
                  disabled={!previewContent}
                >
                  {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // Force refresh preview
                    const generatePreview = async () => {
                      const preview = await previewTemplate(template, variables);
                      setPreviewContent(preview);
                    };
                    generatePreview();
                  }}
                >
                  <Refresh className="h-4 w-4" />
                </Button>
              </div>
            </CardTitle>
            <CardDescription>
              Live preview of your template with current variable values
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className={`${isFullscreen ? 'h-[calc(100vh-16rem)]' : 'h-96'} pr-4`}>
              <div className="bg-muted p-4 rounded-md">
                {isRendering ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  </div>
                ) : previewContent ? (
                  <pre className="whitespace-pre-wrap text-sm font-mono">
                    {previewContent}
                  </pre>
                ) : (
                  <p className="text-muted-foreground text-sm">
                    Fill in the variables to see the preview
                  </p>
                )}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button
            onClick={handleSave}
            disabled={Object.keys(validationErrors).length > 0}
          >
            {instance ? 'Update Instance' : 'Create Instance'}
          </Button>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={handleSendToNotebook}>
            Send to Notebook
          </Button>
          <Button variant="outline" size="sm" onClick={handleSendToChat}>
            Send to Chat
          </Button>
          <Select onValueChange={(value) => handleSendToStudio(value as any)}>
            <SelectTrigger asChild>
              <Button variant="outline" size="sm">
                Send to Studio
              </Button>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Note">Note</SelectItem>
              <SelectItem value="Markdown">Markdown</SelectItem>
              <SelectItem value="RichText">Rich Text</SelectItem>
              <SelectItem value="Slide">Slide</SelectItem>
              <SelectItem value="Diagram">Diagram</SelectItem>
              <SelectItem value="Code">Code</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Template Info */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Template Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <p className="text-muted-foreground">Author</p>
              <p className="font-medium">{template.author}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Category</p>
              <p className="font-medium">{template.category}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Usage</p>
              <p className="font-medium">{template.usage?.totalUsed || 0} times</p>
            </div>
            <div>
              <p className="text-muted-foreground">Updated</p>
              <p className="font-medium">
                {new Date(template.updatedAt).toLocaleDateString()}
              </p>
            </div>
          </div>
          
          <Separator className="my-4" />
          
          <div>
            <p className="text-muted-foreground text-sm mb-2">Tags</p>
            <div className="flex flex-wrap gap-2">
              {template.tags.map(tag => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};