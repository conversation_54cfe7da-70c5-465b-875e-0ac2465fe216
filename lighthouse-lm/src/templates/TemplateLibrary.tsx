import React, { useState } from 'react';
import { Search, Plus, Filter, Grid, List, Star, Download, Upload, Share2, Copy, Trash2, <PERSON><PERSON><PERSON>, Eye, BookOpen } from 'lucide-react';
import { Template, TemplateCategory } from './types';
import { useTemplateContext } from './TemplateProvider';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '../components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '../components/ui/alert-dialog';
import { useToast } from '../hooks/useToast';

interface TemplateLibraryProps {
  onTemplateSelect: (template: Template) => void;
  onCreateNew: () => void;
}

export const TemplateLibrary: React.FC<TemplateLibraryProps> = ({
  onTemplateSelect,
  onCreateNew,
}) => {
  const {
    filteredTemplates,
    categories,
    searchQuery,
    selectedCategory,
    setSearchQuery,
    setSelectedCategory,
    duplicateTemplate,
    deleteTemplate,
    exportTemplate,
    shareTemplate,
    isLoading,
  } = useTemplateContext();
  
  const { toast } = useToast();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'name' | 'updated' | 'usage'>('updated');
  const [filterBy, setFilterBy] = useState<'all' | 'builtin' | 'custom' | 'public'>('all');
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);

  // Sort templates
  const sortedTemplates = React.useMemo(() => {
    let sorted = [...filteredTemplates];
    
    // Filter by type
    if (filterBy !== 'all') {
      switch (filterBy) {
        case 'builtin':
          sorted = sorted.filter(t => t.isBuiltIn);
          break;
        case 'custom':
          sorted = sorted.filter(t => !t.isBuiltIn);
          break;
        case 'public':
          sorted = sorted.filter(t => t.isPublic);
          break;
      }
    }
    
    // Sort
    switch (sortBy) {
      case 'name':
        sorted.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'updated':
        sorted.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
        break;
      case 'usage':
        sorted.sort((a, b) => (b.usage?.totalUsed || 0) - (a.usage?.totalUsed || 0));
        break;
    }
    
    return sorted;
  }, [filteredTemplates, sortBy, filterBy]);

  const handleDuplicate = async (template: Template) => {
    try {
      await duplicateTemplate(template.id);
      toast({
        title: "Template duplicated",
        description: `"${template.name}" has been duplicated successfully`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to duplicate template",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (template: Template) => {
    try {
      await deleteTemplate(template.id);
      toast({
        title: "Template deleted",
        description: `"${template.name}" has been deleted successfully`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete template",
        variant: "destructive",
      });
    }
  };

  const handleExport = async (template: Template) => {
    try {
      const exportData = await exportTemplate(template.id);
      const blob = new Blob([exportData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${template.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_template.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast({
        title: "Template exported",
        description: `"${template.name}" has been exported successfully`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to export template",
        variant: "destructive",
      });
    }
  };

  const handleShare = async (template: Template) => {
    try {
      await shareTemplate(template.id, !template.isPublic);
      toast({
        title: template.isPublic ? "Template made private" : "Template made public",
        description: `"${template.name}" is now ${template.isPublic ? 'private' : 'public'}`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update template sharing",
        variant: "destructive",
      });
    }
  };

  const getCategoryById = (id: string): TemplateCategory | undefined => {
    return categories.find(cat => cat.id === id);
  };

  const TemplateCard: React.FC<{ template: Template }> = ({ template }) => {
    const category = getCategoryById(template.category);
    
    return (
      <Card className="group relative overflow-hidden transition-all duration-200 hover:shadow-lg">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-2xl">{template.thumbnail || '📄'}</span>
              <div>
                <CardTitle className="text-base line-clamp-1">{template.name}</CardTitle>
                <CardDescription className="text-xs text-muted-foreground">
                  v{template.version} • {template.author}
                </CardDescription>
              </div>
            </div>
            <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                size="sm"
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation();
                  setSelectedTemplate(template);
                }}
              >
                <Eye className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation();
                  handleDuplicate(template);
                }}
              >
                <Copy className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation();
                  handleExport(template);
                }}
              >
                <Download className="h-4 w-4" />
              </Button>
              {!template.isBuiltIn && (
                <>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleShare(template);
                    }}
                  >
                    <Share2 className="h-4 w-4" />
                  </Button>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete Template</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete "{template.name}"? This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => handleDelete(template)}
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          <div className="space-y-3">
            <p className="text-sm text-muted-foreground line-clamp-2">
              {template.description}
            </p>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {category && (
                  <Badge 
                    variant="secondary" 
                    className="text-xs"
                    style={{ backgroundColor: category.color + '20', color: category.color }}
                  >
                    {category.icon} {category.name}
                  </Badge>
                )}
                {template.isBuiltIn && (
                  <Badge variant="outline" className="text-xs">
                    Built-in
                  </Badge>
                )}
                {template.isPublic && (
                  <Badge variant="outline" className="text-xs">
                    Public
                  </Badge>
                )}
              </div>
              {template.usage?.averageRating && (
                <div className="flex items-center space-x-1">
                  <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                  <span className="text-xs text-muted-foreground">
                    {template.usage.averageRating.toFixed(1)}
                  </span>
                </div>
              )}
            </div>
            
            <div className="flex flex-wrap gap-1">
              {template.tags.slice(0, 3).map((tag) => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {template.tags.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{template.tags.length - 3}
                </Badge>
              )}
            </div>
            
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>
                Used {template.usage?.totalUsed || 0} times
              </span>
              <span>
                Updated {new Date(template.updatedAt).toLocaleDateString()}
              </span>
            </div>
            
            <Button 
              className="w-full mt-3"
              onClick={() => onTemplateSelect(template)}
            >
              Use Template
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  const TemplateListItem: React.FC<{ template: Template }> = ({ template }) => {
    const category = getCategoryById(template.category);
    
    return (
      <Card className="group transition-all duration-200 hover:shadow-md">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 flex-1">
              <span className="text-2xl">{template.thumbnail || '📄'}</span>
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  <h3 className="font-semibold truncate">{template.name}</h3>
                  <span className="text-xs text-muted-foreground">v{template.version}</span>
                  {template.isBuiltIn && (
                    <Badge variant="outline" className="text-xs">Built-in</Badge>
                  )}
                  {template.isPublic && (
                    <Badge variant="outline" className="text-xs">Public</Badge>
                  )}
                </div>
                <p className="text-sm text-muted-foreground line-clamp-1 mb-2">
                  {template.description}
                </p>
                <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                  {category && (
                    <span className="flex items-center space-x-1">
                      <span>{category.icon}</span>
                      <span>{category.name}</span>
                    </span>
                  )}
                  <span>Used {template.usage?.totalUsed || 0} times</span>
                  <span>Updated {new Date(template.updatedAt).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button size="sm" onClick={() => onTemplateSelect(template)}>
                Use Template
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setSelectedTemplate(template)}
              >
                <Eye className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => handleDuplicate(template)}
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Template Library</h1>
          <p className="text-muted-foreground">
            Browse and manage your content templates
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button onClick={onCreateNew} className="flex items-center space-x-2">
            <Plus className="h-4 w-4" />
            <span>Create Template</span>
          </Button>
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:space-y-0 lg:space-x-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search templates..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex items-center space-x-2">
          <Select value={selectedCategory || 'all'} onValueChange={(value) => setSelectedCategory(value === 'all' ? null : value)}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.icon} {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select value={sortBy} onValueChange={(value) => setSortBy(value as any)}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="updated">Recently Updated</SelectItem>
              <SelectItem value="name">Name</SelectItem>
              <SelectItem value="usage">Most Used</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={filterBy} onValueChange={(value) => setFilterBy(value as any)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="builtin">Built-in</SelectItem>
              <SelectItem value="custom">Custom</SelectItem>
              <SelectItem value="public">Public</SelectItem>
            </SelectContent>
          </Select>
          
          <div className="flex items-center border rounded-md">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Categories Overview */}
      {!selectedCategory && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {categories.map((category) => (
            <Card
              key={category.id}
              className="cursor-pointer transition-colors hover:bg-muted/50"
              onClick={() => setSelectedCategory(category.id)}
            >
              <CardContent className="p-4 text-center">
                <div className="text-2xl mb-2">{category.icon}</div>
                <h3 className="font-medium text-sm">{category.name}</h3>
                <p className="text-xs text-muted-foreground mt-1">
                  {filteredTemplates.filter(t => t.category === category.id).length} templates
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Templates Display */}
      <div className="space-y-4">
        {sortedTemplates.length === 0 ? (
          <Card className="p-8 text-center">
            <BookOpen className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold mb-2">No templates found</h3>
            <p className="text-muted-foreground mb-4">
              {searchQuery 
                ? "Try adjusting your search terms or filters"
                : "Get started by creating your first template"}
            </p>
            <Button onClick={onCreateNew}>
              Create New Template
            </Button>
          </Card>
        ) : (
          <>
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {sortedTemplates.map((template) => (
                  <TemplateCard key={template.id} template={template} />
                ))}
              </div>
            ) : (
              <div className="space-y-3">
                {sortedTemplates.map((template) => (
                  <TemplateListItem key={template.id} template={template} />
                ))}
              </div>
            )}
          </>
        )}
      </div>

      {/* Template Preview Dialog */}
      {selectedTemplate && (
        <Dialog open={!!selectedTemplate} onOpenChange={() => setSelectedTemplate(null)}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <span className="text-2xl">{selectedTemplate.thumbnail || '📄'}</span>
                <span>{selectedTemplate.name}</span>
              </DialogTitle>
              <DialogDescription>
                {selectedTemplate.description}
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-6">
              <div className="flex items-center space-x-4">
                <Badge variant="secondary">v{selectedTemplate.version}</Badge>
                <Badge variant="outline">{selectedTemplate.author}</Badge>
                {selectedTemplate.isBuiltIn && (
                  <Badge variant="outline">Built-in</Badge>
                )}
                {selectedTemplate.isPublic && (
                  <Badge variant="outline">Public</Badge>
                )}
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Variables ({selectedTemplate.variables.length})</h4>
                <div className="grid grid-cols-2 gap-4">
                  {selectedTemplate.variables.map((variable) => (
                    <div key={variable.id} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium text-sm">{variable.label}</span>
                        <Badge variant="outline" className="text-xs">
                          {variable.type}
                        </Badge>
                      </div>
                      {variable.description && (
                        <p className="text-xs text-muted-foreground">
                          {variable.description}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Sections ({selectedTemplate.sections.length})</h4>
                <div className="space-y-2">
                  {selectedTemplate.sections
                    .sort((a, b) => a.order - b.order)
                    .map((section) => (
                      <div key={section.id} className="p-3 border rounded-lg">
                        <div className="flex items-center justify-between">
                          <span className="font-medium text-sm">{section.title}</span>
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline" className="text-xs">
                              Order {section.order}
                            </Badge>
                            {section.optional && (
                              <Badge variant="outline" className="text-xs">
                                Optional
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                  ))}
                </div>
              </div>
              
              <div className="flex justify-between">
                <div className="text-sm text-muted-foreground">
                  Used {selectedTemplate.usage?.totalUsed || 0} times • 
                  Updated {new Date(selectedTemplate.updatedAt).toLocaleDateString()}
                </div>
                <Button onClick={() => {
                  onTemplateSelect(selectedTemplate);
                  setSelectedTemplate(null);
                }}>
                  Use This Template
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};