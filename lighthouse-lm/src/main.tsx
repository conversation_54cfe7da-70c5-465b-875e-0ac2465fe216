import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import LighthouseLMDashboard from './LighthouseLMDashboard.tsx'
import { ThemeProvider } from './contexts/ThemeContext'
import { TauriProvider } from './contexts/TauriContext'
import AuthWrapper from './components/auth/AuthWrapper'
import { initializePerformanceMonitoring } from './utils/performance'
import './styles/globals.css'

// Initialize performance monitoring in development
if (process.env.NODE_ENV === 'development') {
  initializePerformanceMonitoring();
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <TauriProvider>
      <ThemeProvider defaultTheme="system" storageKey="lighthouse-ui-theme">
        <BrowserRouter>
          <AuthWrapper>
            <LighthouseLMDashboard />
          </AuthWrapper>
        </BrowserRouter>
      </ThemeProvider>
    </TauriProvider>
  </React.StrictMode>,
)