// Mock Tauri API for web environment

// Mock invoke function
export const invoke = async (command: string, args?: any): Promise<any> => {
  console.warn(`<PERSON><PERSON> invoke called in web environment: ${command}`, args);
  // Return mock responses based on command
  switch (command) {
    case 'get_chat_messages':
      return [];
    case 'send_message':
      return { success: true };
    case 'upload_file':
      return { success: true, path: '/mock/path' };
    case 'process_document':
      return { success: true, content: 'Mock processed content' };
    default:
      return { success: false, error: 'Command not implemented in web environment' };
  }
};

// Mock event system
export const listen = async (event: string, handler: (event: any) => void): Promise<() => void> => {
  console.warn(`Tauri listen called in web environment: ${event}`);
  return () => {}; // Return unsubscribe function
};

export const emit = async (event: string, payload?: any): Promise<void> => {
  console.warn(`Tauri emit called in web environment: ${event}`, payload);
};

// Mock dialog functions
export const open = async (options?: any): Promise<string | string[] | null> => {
  console.warn('Tauri dialog.open called in web environment', options);
  // Create a file input element for web file selection
  return new Promise((resolve) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = options?.multiple || false;
    input.accept = options?.filters?.map((f: any) => f.extensions.map((e: string) => `.${e}`).join(',')).join(',') || '*';
    
    input.onchange = (e) => {
      const files = (e.target as HTMLInputElement).files;
      if (files && files.length > 0) {
        if (options?.multiple) {
          resolve(Array.from(files).map(f => f.name));
        } else {
          resolve(files[0].name);
        }
      } else {
        resolve(null);
      }
    };
    
    input.click();
  });
};

// Mock file system functions
export const readTextFile = async (path: string): Promise<string> => {
  console.warn(`Tauri readTextFile called in web environment: ${path}`);
  return 'Mock file content';
};

// Mock fetch function
export const fetch = async (url: string, options?: any): Promise<Response> => {
  console.warn(`Tauri fetch called in web environment: ${url}`);
  return window.fetch(url, options);
};

export type UnlistenFn = () => void;