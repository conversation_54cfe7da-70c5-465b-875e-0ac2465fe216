import React, { useState } from 'react';
import { useAuth } from '@/services/auth';
import LoginForm from './LoginForm';
import RegisterForm from './RegisterForm';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

interface AuthWrapperProps {
  children: React.ReactNode;
}

type AuthView = 'login' | 'register';

export const AuthWrapper: React.FC<AuthWrapperProps> = ({ children }) => {
  const { user, isLoading } = useAuth();
  const [currentView, setCurrentView] = useState<AuthView>('login');

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin" />
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // If user is authenticated, show the main app
  if (user) {
    return <>{children}</>;
  }

  // If not authenticated, show login/register forms
  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold tracking-tight">Lighthouse LM</h1>
          <p className="text-muted-foreground mt-2">
            Your AI-powered knowledge management system
          </p>
        </div>

        <Card>
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl text-center">
              {currentView === 'login' ? 'Sign In' : 'Create Account'}
            </CardTitle>
            <CardDescription className="text-center">
              {currentView === 'login'
                ? 'Enter your credentials to access your account'
                : 'Create a new account to get started'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {currentView === 'login' ? (
              <LoginForm
                onSuccess={() => {
                  // Authentication state will be updated automatically
                  // The wrapper will re-render and show the main app
                }}
                onSwitchToRegister={() => setCurrentView('register')}
              />
            ) : (
              <RegisterForm
                onSuccess={() => {
                  // After successful registration, switch to login
                  setCurrentView('login');
                }}
                onSwitchToLogin={() => setCurrentView('login')}
              />
            )}

            <div className="mt-6 text-center">
              <Button
                variant="ghost"
                onClick={() => setCurrentView(currentView === 'login' ? 'register' : 'login')}
                className="text-sm"
              >
                {currentView === 'login'
                  ? "Don't have an account? Sign up"
                  : 'Already have an account? Sign in'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AuthWrapper;