import React, { useState, useEffect, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  CheckCircle,
  AlertCircle,
  Clock,
  Loader2,
  Pause,
  Play,
  X,
  RotateCcw,
  Minimize2,
  Maximize2,
  Activity,
  Info,
  Volume2,
  VolumeX,
  Settings,
  TrendingUp,
  Zap
} from 'lucide-react';

export interface ProgressSubtask {
  id: string;
  title: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress?: number;
}

export interface ProgressTask {
  id: string;
  title: string;
  description?: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'paused';
  progress: number;
  startTime?: Date;
  endTime?: Date;
  error?: string;
  subtasks?: ProgressSubtask[];
  canPause?: boolean;
  canCancel?: boolean;
  canRetry?: boolean;
  priority?: 'low' | 'medium' | 'high' | 'critical';
  category?: string;
  progressHistory?: { timestamp: Date; progress: number }[];
  throughput?: number;
  eta?: Date;
}

export interface ProgressTrackerProps {
  tasks: ProgressTask[];
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  onPause?: (taskId: string) => void;
  onResume?: (taskId: string) => void;
  onCancel?: (taskId: string) => void;
  onRetry?: (taskId: string) => void;
  enableSounds?: boolean;
  enableNotifications?: boolean;
  autoHide?: boolean;
  maxVisible?: number;
  showStats?: boolean;
}

const ProgressTracker: React.FC<ProgressTrackerProps> = ({
  tasks,
  position = 'bottom-right',
  onPause,
  onResume,
  onCancel,
  onRetry,
  enableSounds = false,
  enableNotifications = false,
  autoHide = false,
  maxVisible = 5,
  showStats = false
}) => {
  const [minimized, setMinimized] = useState(false);
  const [expandedTasks, setExpandedTasks] = useState<Set<string>>(new Set());
  const [soundEnabled, setSoundEnabled] = useState(enableSounds);
  const [showSettings, setShowSettings] = useState(false);
  const [taskHistory, setTaskHistory] = useState<Map<string, ProgressTask[]>>(new Map());
  const audioContextRef = useRef<AudioContext | null>(null);

  // Initialize audio context
  useEffect(() => {
    if (soundEnabled && !audioContextRef.current) {
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
    }
  }, [soundEnabled]);

  // Real-time monitoring
  useEffect(() => {
    const interval = setInterval(() => {
      tasks.forEach(task => {
        if (task.status === 'running') {
          const history = taskHistory.get(task.id) || [];
          const historyEntry = { ...task };
          history.push(historyEntry);
          if (history.length > 10) history.shift();
          setTaskHistory(prev => new Map(prev.set(task.id, history)));
        }
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [tasks, taskHistory]);

  // Notifications
  useEffect(() => {
    if (!enableNotifications) return;

    tasks.forEach(task => {
      const prevTask = taskHistory.get(task.id)?.[0];
      if (prevTask && prevTask.status !== task.status) {
        if (task.status === 'completed') {
          showNotification(`Task completed: ${task.title}`, 'success');
          if (soundEnabled) playNotificationSound('success');
        } else if (task.status === 'failed') {
          showNotification(`Task failed: ${task.title}`, 'error');
          if (soundEnabled) playNotificationSound('error');
        }
      }
    });
  }, [tasks, taskHistory, enableNotifications, soundEnabled]);

  // Auto-hide completed tasks
  useEffect(() => {
    if (!autoHide) return;

    const timer = setTimeout(() => {
      const completedTasks = tasks.filter(t => t.status === 'completed');
      if (completedTasks.length === tasks.length && tasks.length > 0) {
        setMinimized(true);
      }
    }, 3000);

    return () => clearTimeout(timer);
  }, [tasks, autoHide]);

  const playNotificationSound = useCallback((type: 'success' | 'error' | 'info') => {
    if (!audioContextRef.current) return;

    const ctx = audioContextRef.current;
    const oscillator = ctx.createOscillator();
    const gainNode = ctx.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(ctx.destination);

    const frequencies = {
      success: [523.25, 659.25, 783.99],
      error: [349.23, 293.66],
      info: [440]
    };

    const freq = frequencies[type];
    oscillator.frequency.setValueAtTime(freq[0], ctx.currentTime);
    
    gainNode.gain.setValueAtTime(0.1, ctx.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.3);

    oscillator.start(ctx.currentTime);
    oscillator.stop(ctx.currentTime + 0.3);
  }, []);

  const showNotification = useCallback((message: string, type: 'success' | 'error' | 'info') => {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('Lighthouse LM', {
        body: message,
        icon: type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'
      });
    }
  }, []);

  const calculateStats = useCallback(() => {
    const totalTasks = tasks.length;
    const completedTasks = tasks.filter(t => t.status === 'completed').length;
    const avgProgress = tasks.reduce((sum, task) => sum + task.progress, 0) / totalTasks || 0;
    const avgThroughput = tasks.reduce((sum, task) => sum + (task.throughput || 0), 0) / totalTasks || 0;

    return {
      totalTasks,
      completedTasks,
      avgProgress,
      avgThroughput
    };
  }, [tasks]);

  const formatTime = useCallback((seconds: number): string => {
    if (seconds < 60) return `${Math.round(seconds)}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}m`;
    return `${Math.round(seconds / 3600)}h`;
  }, []);

  const calculateElapsedTime = useCallback((task: ProgressTask): string => {
    if (!task.startTime) return '0s';
    const elapsed = (Date.now() - task.startTime.getTime()) / 1000;
    return formatTime(elapsed);
  }, [formatTime]);

  const calculateRemainingTime = useCallback((task: ProgressTask): string | null => {
    if (!task.startTime || task.progress === 0) return null;
    const elapsed = (Date.now() - task.startTime.getTime()) / 1000;
    const remaining = (elapsed / task.progress) * (100 - task.progress);
    return formatTime(remaining);
  }, [formatTime]);

  const getStatusIcon = useCallback((status: ProgressTask['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'running':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'paused':
        return <Pause className="h-4 w-4 text-yellow-500" />;
      default:
        return <div className="h-4 w-4 rounded-full border-2 border-muted-foreground" />;
    }
  }, []);

  const getPositionClasses = useCallback((pos: string) => {
    switch (pos) {
      case 'top-left':
        return 'top-4 left-4';
      case 'top-right':
        return 'top-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'bottom-right':
      default:
        return 'bottom-4 right-4';
    }
  }, []);

  const toggleTaskExpansion = useCallback((taskId: string) => {
    setExpandedTasks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(taskId)) {
        newSet.delete(taskId);
      } else {
        newSet.add(taskId);
      }
      return newSet;
    });
  }, []);

  const activeTasks = tasks.filter(task => task.status === 'running' || task.status === 'paused');
  const completedTasks = tasks.filter(task => task.status === 'completed');
  const failedTasks = tasks.filter(task => task.status === 'failed');
  const visibleTasks = tasks.slice(0, maxVisible);
  const stats = calculateStats();

  if (tasks.length === 0) return null;

  return (
    <TooltipProvider>
      <div className={`fixed z-50 ${getPositionClasses(position)}`}>
        <AnimatePresence>
          {minimized ? (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="flex items-center gap-2"
            >
              <Button
                variant="outline"
                size="sm"
                onClick={() => setMinimized(false)}
                className="shadow-lg bg-background/95 backdrop-blur"
              >
                <Activity className="h-4 w-4 mr-2" />
                {activeTasks.length} task{activeTasks.length !== 1 ? 's' : ''}
                {failedTasks.length > 0 && (
                  <Badge variant="destructive" className="ml-2">
                    {failedTasks.length}
                  </Badge>
                )}
              </Button>
              
              {showStats && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="shadow-lg"
                    >
                      <TrendingUp className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="text-xs">
                      <div>Progress: {stats.avgProgress.toFixed(1)}%</div>
                      <div>Tasks: {stats.totalTasks}</div>
                    </div>
                  </TooltipContent>
                </Tooltip>
              )}
            </motion.div>
          ) : (
            <motion.div
              initial={{ opacity: 0, y: 20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 20, scale: 0.95 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
              className="w-96 sm:w-80 md:w-96"
            >
              <Card className="shadow-xl border-0 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                <CardHeader className="pb-3 px-3 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1 sm:gap-2">
                      <Activity className="h-3 w-3 sm:h-4 sm:w-4" />
                      <span className="font-semibold text-xs sm:text-sm">Tasks</span>
                      <div className="flex items-center gap-0.5 sm:gap-1">
                        {activeTasks.length > 0 && (
                          <Badge variant="outline" className="text-[10px] sm:text-xs px-1 py-0 h-4 sm:h-auto">
                            <span className="hidden sm:inline">{activeTasks.length} active</span>
                            <span className="sm:hidden">{activeTasks.length}</span>
                          </Badge>
                        )}
                        {completedTasks.length > 0 && (
                          <Badge variant="outline" className="text-[10px] sm:text-xs text-green-600 px-1 py-0 h-4 sm:h-auto">
                            <span className="hidden sm:inline">{completedTasks.length} done</span>
                            <span className="sm:hidden">{completedTasks.length}</span>
                          </Badge>
                        )}
                        {failedTasks.length > 0 && (
                          <Badge variant="destructive" className="text-[10px] sm:text-xs px-1 py-0 h-4 sm:h-auto">
                            <span className="hidden sm:inline">{failedTasks.length} failed</span>
                            <span className="sm:hidden">{failedTasks.length}</span>
                          </Badge>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-0.5 sm:gap-1">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setSoundEnabled(!soundEnabled)}
                            className="h-6 w-6 sm:h-7 sm:w-7 p-0"
                          >
                            {soundEnabled ? (
                              <Volume2 className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
                            ) : (
                              <VolumeX className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
                            )}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          {soundEnabled ? 'Disable sounds' : 'Enable sounds'}
                        </TooltipContent>
                      </Tooltip>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setMinimized(true)}
                        className="h-6 w-6 sm:h-7 sm:w-7 p-0"
                      >
                        <Minimize2 className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
                      </Button>
                    </div>
                  </div>
                  
                  {showStats && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      className="flex items-center gap-2 sm:gap-4 text-[10px] sm:text-xs text-muted-foreground pt-2 border-t"
                    >
                      <div className="flex items-center gap-0.5 sm:gap-1">
                        <Zap className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
                        <span className="hidden sm:inline">Avg: {stats.avgProgress.toFixed(1)}%</span>
                        <span className="sm:hidden">{stats.avgProgress.toFixed(1)}%</span>
                      </div>
                      <div>
                        <span className="hidden sm:inline">Total: {stats.totalTasks}</span>
                        <span className="sm:hidden">{stats.totalTasks}</span>
                      </div>
                      {stats.avgThroughput > 0 && (
                        <div>
                          <span className="hidden sm:inline">Speed: {stats.avgThroughput.toFixed(1)}/s</span>
                          <span className="sm:hidden">{stats.avgThroughput.toFixed(1)}/s</span>
                        </div>
                      )}
                    </motion.div>
                  )}
                </CardHeader>

                <CardContent className="p-0 max-h-80 sm:max-h-96 overflow-y-auto">
                  {visibleTasks.map((task, index) => (
                    <motion.div
                      key={task.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.05 }}
                      className="border-b last:border-b-0 p-2 sm:p-3 hover:bg-muted/50 transition-colors"
                    >
                      <div className="space-y-1.5 sm:space-y-2">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start gap-1.5 sm:gap-2 flex-1">
                            {getStatusIcon(task.status)}
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-1 sm:gap-2">
                                <span className="text-xs sm:text-sm font-medium truncate">
                                  {task.title}
                                </span>
                                {task.priority && (
                                  <Badge 
                                    variant={task.priority === 'critical' ? 'destructive' : 
                                            task.priority === 'high' ? 'default' : 'secondary'}
                                    className="text-[10px] sm:text-xs px-1 py-0 h-4 sm:h-auto flex-shrink-0"
                                  >
                                    {task.priority}
                                  </Badge>
                                )}
                                {task.subtasks && task.subtasks.length > 0 && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => toggleTaskExpansion(task.id)}
                                    className="h-4 w-4 sm:h-5 sm:w-5 p-0 flex-shrink-0"
                                  >
                                    {expandedTasks.has(task.id) ? (
                                      <Minimize2 className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
                                    ) : (
                                      <Maximize2 className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
                                    )}
                                  </Button>
                                )}
                              </div>
                              {task.description && (
                                <p className="text-[10px] sm:text-xs text-muted-foreground mt-0.5 line-clamp-2">
                                  {task.description}
                                </p>
                              )}
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-0.5 sm:gap-1 flex-shrink-0">
                            {task.status === 'running' && task.canPause && onPause && (
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => onPause(task.id)}
                                    className="h-5 w-5 sm:h-6 sm:w-6 p-0"
                                  >
                                    <Pause className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>Pause task</TooltipContent>
                              </Tooltip>
                            )}
                            {task.status === 'paused' && onResume && (
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => onResume(task.id)}
                                    className="h-5 w-5 sm:h-6 sm:w-6 p-0"
                                  >
                                    <Play className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>Resume task</TooltipContent>
                              </Tooltip>
                            )}
                            {task.status === 'failed' && task.canRetry && onRetry && (
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => onRetry(task.id)}
                                    className="h-5 w-5 sm:h-6 sm:w-6 p-0"
                                  >
                                    <RotateCcw className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>Retry task</TooltipContent>
                              </Tooltip>
                            )}
                            {task.canCancel && onCancel && (
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => onCancel(task.id)}
                                    className="h-5 w-5 sm:h-6 sm:w-6 p-0 text-destructive hover:text-destructive"
                                  >
                                    <X className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>Cancel task</TooltipContent>
                              </Tooltip>
                            )}
                          </div>
                        </div>

                        {task.status !== 'pending' && (
                          <div className="space-y-1">
                            <div className="flex items-center justify-between text-[10px] sm:text-xs">
                              <span>{task.progress}%</span>
                              <div className="flex items-center gap-1 sm:gap-2 text-muted-foreground">
                                <span className="flex items-center gap-0.5 sm:gap-1">
                                  <Clock className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
                                  <span className="hidden sm:inline">{calculateElapsedTime(task)}</span>
                                  <span className="sm:hidden">{calculateElapsedTime(task).split(' ')[0]}</span>
                                </span>
                                {calculateRemainingTime(task) && (
                                  <span className="hidden sm:inline">~{calculateRemainingTime(task)} left</span>
                                )}
                              </div>
                            </div>
                            <div className="w-full bg-secondary rounded-full h-1.5 sm:h-2 overflow-hidden">
                              <motion.div
                                className={`h-full rounded-full ${
                                  task.status === 'completed' ? 'bg-green-500' :
                                  task.status === 'failed' ? 'bg-red-500' :
                                  task.status === 'paused' ? 'bg-yellow-500' :
                                  'bg-blue-500'
                                }`}
                                initial={{ width: 0 }}
                                animate={{ width: `${task.progress}%` }}
                                transition={{ duration: 0.5, ease: "easeOut" }}
                              />
                            </div>
                          </div>
                        )}

                        {task.error && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            className="flex items-start gap-1 p-1.5 sm:p-2 bg-destructive/10 rounded text-[10px] sm:text-xs"
                          >
                            <Info className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-destructive mt-0.5 flex-shrink-0" />
                            <span className="text-destructive line-clamp-2">{task.error}</span>
                          </motion.div>
                        )}

                        {expandedTasks.has(task.id) && task.subtasks && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            className="ml-3 sm:ml-6 space-y-1 pt-1"
                          >
                            {task.subtasks.map((subtask) => (
                              <div
                                key={subtask.id}
                                className="flex items-center gap-1.5 sm:gap-2 text-[10px] sm:text-xs"
                              >
                                {subtask.status === 'completed' ? (
                                  <CheckCircle className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-green-500 flex-shrink-0" />
                                ) : subtask.status === 'running' ? (
                                  <Loader2 className="h-2.5 w-2.5 sm:h-3 sm:w-3 animate-spin flex-shrink-0" />
                                ) : subtask.status === 'failed' ? (
                                  <AlertCircle className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-red-500 flex-shrink-0" />
                                ) : (
                                  <div className="h-2.5 w-2.5 sm:h-3 sm:w-3 rounded-full border border-muted-foreground flex-shrink-0" />
                                )}
                                <span className={
                                  `truncate flex-1 ${
                                    subtask.status === 'completed' ? 'line-through text-muted-foreground' : ''
                                  }`
                                }>
                                  {subtask.title}
                                </span>
                                {subtask.progress !== undefined && subtask.status === 'running' && (
                                  <span className="text-muted-foreground ml-auto text-[9px] sm:text-xs flex-shrink-0">
                                    {subtask.progress}%
                                  </span>
                                )}
                              </div>
                            ))}
                          </motion.div>
                        )}

                        {task.status === 'completed' && (
                          <div className="text-[10px] sm:text-xs text-muted-foreground">
                            Completed in {calculateElapsedTime(task)}
                          </div>
                        )}
                      </div>
                    </motion.div>
                  ))}
                  
                  {tasks.length > maxVisible && (
                    <div className="p-2 sm:p-3 text-center text-[10px] sm:text-xs text-muted-foreground border-t">
                      +{tasks.length - maxVisible} more tasks
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </TooltipProvider>
  );
};

export default ProgressTracker;