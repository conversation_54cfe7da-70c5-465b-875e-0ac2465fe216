import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  MessageSquare,
  Plus,
  Edit,
  Trash2,
  Copy,
  Star,
  Search,
  Filter,
  Tag,
  Clock,
  Zap,
  BookOpen,
  Code,
  Brain,
  FileText,
  Lightbulb,
  HelpCircle,
  CheckCircle,
  X,
  MoreVertical,
} from 'lucide-react';
import { cn } from '../../lib/utils';

interface MessageTemplate {
  id: string;
  title: string;
  content: string;
  category: 'question' | 'analysis' | 'creative' | 'technical' | 'research' | 'custom';
  tags: string[];
  variables?: string[]; // Variables like {topic}, {context}, etc.
  isStarred: boolean;
  usageCount: number;
  createdAt: Date;
  updatedAt: Date;
}

interface MessageTemplatesProps {
  onTemplateSelect: (template: MessageTemplate, variables?: Record<string, string>) => void;
  onTemplateCreate: (template: Omit<MessageTemplate, 'id' | 'usageCount' | 'createdAt' | 'updatedAt'>) => void;
  onTemplateUpdate: (id: string, template: Partial<MessageTemplate>) => void;
  onTemplateDelete: (id: string) => void;
}

const defaultTemplates: MessageTemplate[] = [
  {
    id: 'explain-concept',
    title: 'Explain Concept',
    content: 'Can you explain {concept} in simple terms? Please include examples and practical applications.',
    category: 'question',
    tags: ['explanation', 'learning', 'concept'],
    variables: ['concept'],
    isStarred: true,
    usageCount: 45,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: 'analyze-data',
    title: 'Data Analysis Request',
    content: 'Please analyze the following data and provide insights:\n\n{data}\n\nFocus on: {focus_areas}',
    category: 'analysis',
    tags: ['analysis', 'data', 'insights'],
    variables: ['data', 'focus_areas'],
    isStarred: false,
    usageCount: 23,
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02'),
  },
  {
    id: 'brainstorm-ideas',
    title: 'Brainstorming Session',
    content: 'Let\'s brainstorm creative ideas for {topic}. Please provide at least 5 innovative approaches with brief explanations.',
    category: 'creative',
    tags: ['brainstorming', 'creativity', 'ideas'],
    variables: ['topic'],
    isStarred: true,
    usageCount: 67,
    createdAt: new Date('2024-01-03'),
    updatedAt: new Date('2024-01-03'),
  },
  {
    id: 'code-review',
    title: 'Code Review',
    content: 'Please review this code and suggest improvements:\n\n```{language}\n{code}\n```\n\nFocus on: performance, readability, and best practices.',
    category: 'technical',
    tags: ['code', 'review', 'programming'],
    variables: ['language', 'code'],
    isStarred: false,
    usageCount: 34,
    createdAt: new Date('2024-01-04'),
    updatedAt: new Date('2024-01-04'),
  },
  {
    id: 'research-summary',
    title: 'Research Summary',
    content: 'Please provide a comprehensive summary of research on {topic}. Include key findings, methodologies, and implications.',
    category: 'research',
    tags: ['research', 'summary', 'academic'],
    variables: ['topic'],
    isStarred: false,
    usageCount: 18,
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-05'),
  },
];

const categoryIcons = {
  question: HelpCircle,
  analysis: FileText,
  creative: Lightbulb,
  technical: Code,
  research: BookOpen,
  custom: MessageSquare,
};

const categoryColors = {
  question: 'bg-blue-100 text-blue-800',
  analysis: 'bg-green-100 text-green-800',
  creative: 'bg-purple-100 text-purple-800',
  technical: 'bg-orange-100 text-orange-800',
  research: 'bg-indigo-100 text-indigo-800',
  custom: 'bg-gray-100 text-gray-800',
};

const MessageTemplates: React.FC<MessageTemplatesProps> = ({
  onTemplateSelect,
  onTemplateCreate,
  onTemplateUpdate,
  onTemplateDelete,
}) => {
  const [templates, setTemplates] = useState<MessageTemplate[]>(defaultTemplates);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showStarredOnly, setShowStarredOnly] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<MessageTemplate | null>(null);
  const [variableValues, setVariableValues] = useState<Record<string, string>>({});
  const [selectedTemplate, setSelectedTemplate] = useState<MessageTemplate | null>(null);

  // Filter templates
  const filteredTemplates = templates.filter(template => {
    const matchesSearch = !searchQuery || 
      template.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    const matchesStarred = !showStarredOnly || template.isStarred;
    
    return matchesSearch && matchesCategory && matchesStarred;
  });

  // Handle template selection with variable substitution
  const handleTemplateSelect = useCallback((template: MessageTemplate) => {
    if (template.variables && template.variables.length > 0) {
      setSelectedTemplate(template);
      setVariableValues({});
    } else {
      onTemplateSelect(template);
      // Update usage count
      setTemplates(prev => prev.map(t => 
        t.id === template.id ? { ...t, usageCount: t.usageCount + 1 } : t
      ));
    }
  }, [onTemplateSelect]);

  // Handle template use with variables
  const handleUseTemplate = useCallback(() => {
    if (!selectedTemplate) return;
    
    onTemplateSelect(selectedTemplate, variableValues);
    
    // Update usage count
    setTemplates(prev => prev.map(t => 
      t.id === selectedTemplate.id ? { ...t, usageCount: t.usageCount + 1 } : t
    ));
    
    setSelectedTemplate(null);
    setVariableValues({});
  }, [selectedTemplate, variableValues, onTemplateSelect]);

  // Handle template creation
  const handleCreateTemplate = useCallback((templateData: any) => {
    const newTemplate: MessageTemplate = {
      ...templateData,
      id: `template-${Date.now()}`,
      usageCount: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    setTemplates(prev => [...prev, newTemplate]);
    onTemplateCreate(templateData);
    setIsCreating(false);
  }, [onTemplateCreate]);

  // Handle template update
  const handleUpdateTemplate = useCallback((template: MessageTemplate) => {
    setTemplates(prev => prev.map(t => 
      t.id === template.id ? { ...template, updatedAt: new Date() } : t
    ));
    onTemplateUpdate(template.id, template);
    setEditingTemplate(null);
  }, [onTemplateUpdate]);

  // Handle template deletion
  const handleDeleteTemplate = useCallback((id: string) => {
    setTemplates(prev => prev.filter(t => t.id !== id));
    onTemplateDelete(id);
  }, [onTemplateDelete]);

  // Toggle star
  const toggleStar = useCallback((id: string) => {
    setTemplates(prev => prev.map(t => 
      t.id === id ? { ...t, isStarred: !t.isStarred } : t
    ));
  }, []);

  return (
    <motion.div 
      className="w-full max-w-4xl mx-auto p-2 sm:p-4 space-y-3 sm:space-y-4"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
    >
      <Tabs defaultValue="browse" className="w-full">
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <TabsList className="grid w-full grid-cols-2 h-8 sm:h-10">
            <TabsTrigger value="browse" className="text-xs sm:text-sm">Browse Templates</TabsTrigger>
            <TabsTrigger value="create" className="text-xs sm:text-sm">Create Template</TabsTrigger>
          </TabsList>
        </motion.div>
        
        <TabsContent value="browse" className="space-y-3 sm:space-y-4">
          {/* Search and Filters */}
          <motion.div 
            className="flex flex-col sm:flex-row gap-2"
            initial={{ y: -10, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.4, delay: 0.2 }}
          >
            <motion.div 
              className="relative flex-1"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <Search className="absolute left-2 sm:left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-3 h-3 sm:w-4 sm:h-4" />
              <Input
                placeholder="Search templates..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8 sm:pl-10 h-8 sm:h-10 text-sm"
              />
            </motion.div>
            
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="gap-1 sm:gap-2 h-8 sm:h-10 px-2 sm:px-4">
                  <Filter className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="hidden sm:inline">Filter</span>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-72 sm:w-80">
                <div className="space-y-3 sm:space-y-4">
                  <div>
                    <Label className="text-xs sm:text-sm font-medium">Category</Label>
                    <div className="grid grid-cols-2 gap-1 sm:gap-2 mt-2">
                      <Button
                        variant={selectedCategory === 'all' ? 'default' : 'outline'}
                        size="sm"
                        className="h-7 px-2 text-xs"
                        onClick={() => setSelectedCategory('all')}
                      >
                        All
                      </Button>
                      {Object.keys(categoryIcons).map(category => {
                        const Icon = categoryIcons[category as keyof typeof categoryIcons];
                        return (
                          <Button
                            key={category}
                            variant={selectedCategory === category ? 'default' : 'outline'}
                            size="sm"
                            className="h-7 px-2 text-xs"
                            onClick={() => setSelectedCategory(category)}
                          >
                            <Icon className="w-2 h-2 sm:w-3 sm:h-3 mr-1 flex-shrink-0" />
                            <span className="capitalize truncate">{category}</span>
                          </Button>
                        );
                      })}
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="starred-only"
                      checked={showStarredOnly}
                      onChange={(e) => setShowStarredOnly(e.target.checked)}
                      className="rounded w-3 h-3 sm:w-4 sm:h-4"
                    />
                    <Label htmlFor="starred-only" className="text-xs sm:text-sm">
                      Show starred only
                    </Label>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </motion.div>

          {/* Templates Grid */}
          <ScrollArea className="h-80 sm:h-96">
            <motion.div 
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 pr-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <AnimatePresence>
                {filteredTemplates.map((template, index) => {
                const Icon = categoryIcons[template.category];
                
                return (
                  <motion.div
                    key={template.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Card
                      className="cursor-pointer hover:shadow-md transition-shadow group"
                      onClick={() => handleTemplateSelect(template)}
                    >
                    <CardHeader className="pb-2 p-3 sm:p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-2 min-w-0 flex-1">
                          <Icon className="w-3 h-3 sm:w-4 sm:h-4 text-muted-foreground flex-shrink-0" />
                          <CardTitle className="text-xs sm:text-sm font-medium truncate">{template.title}</CardTitle>
                        </div>
                        
                        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-5 w-5 sm:h-6 sm:w-6 p-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              onTemplateUpdate(template.id, { isStarred: !template.isStarred });
                            }}
                          >
                            <Star className={cn(
                              "w-2.5 h-2.5 sm:w-3 sm:h-3",
                              template.isStarred ? "fill-yellow-400 text-yellow-400" : "text-muted-foreground"
                            )} />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-5 w-5 sm:h-6 sm:w-6 p-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              setEditingTemplate(template);
                            }}
                          >
                            <Edit className="w-2.5 h-2.5 sm:w-3 sm:h-3 text-muted-foreground" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-5 w-5 sm:h-6 sm:w-6 p-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              navigator.clipboard.writeText(template.content);
                            }}
                          >
                            <Copy className="w-2.5 h-2.5 sm:w-3 sm:h-3 text-muted-foreground" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-5 w-5 sm:h-6 sm:w-6 p-0 text-destructive hover:text-destructive"
                            onClick={(e) => {
                              e.stopPropagation();
                              onTemplateDelete(template.id);
                            }}
                          >
                            <Trash2 className="w-2.5 h-2.5 sm:w-3 sm:h-3" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="pt-0 p-3 sm:p-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <Badge 
                            variant="secondary" 
                            className={cn("text-xs h-4 px-1.5", categoryColors[template.category])}
                          >
                            {template.category}
                          </Badge>
                          <span className="text-xs">{template.usageCount} uses</span>
                        </div>
                        
                        <p className="text-xs sm:text-sm text-muted-foreground line-clamp-2">
                          {template.content}
                        </p>
                        
                        {template.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {template.tags.slice(0, 3).map(tag => (
                              <Badge key={tag} variant="outline" className="text-xs h-4 px-1">
                                {tag}
                              </Badge>
                            ))}
                            {template.tags.length > 3 && (
                              <Badge variant="outline" className="text-xs h-4 px-1">
                                +{template.tags.length - 3}
                              </Badge>
                            )}
                          </div>
                        )}
                        
                        <Button
                          className="w-full mt-2 h-7 sm:h-8"
                          size="sm"
                          onClick={() => handleTemplateSelect(template)}
                        >
                          <span className="text-xs sm:text-sm">Use Template</span>
                        </Button>
                      </div>
                    </CardContent>
                    </Card>
                  </motion.div>
                );
                })}
              </AnimatePresence>
            </motion.div>
          </ScrollArea>
        </TabsContent>
        
        <TabsContent value="create">
          <TemplateCreator onSave={handleCreateTemplate} onCancel={() => setIsCreating(false)} />
        </TabsContent>
      </Tabs>

      {/* Variable Input Modal */}
      {selectedTemplate && selectedTemplate.variables && selectedTemplate.variables.length > 0 && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-md mx-auto">
            <CardHeader className="p-3 sm:p-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm sm:text-lg">Configure Template</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedTemplate(null)}
                  className="h-5 w-5 sm:h-6 sm:w-6 p-0"
                >
                  <X className="w-3 h-3 sm:w-4 sm:h-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-3 sm:space-y-4 p-3 sm:p-4">
              <div className="p-2 sm:p-3 bg-muted rounded-lg">
                <p className="text-xs sm:text-sm font-medium mb-1 sm:mb-2">{selectedTemplate.title}</p>
                <p className="text-xs text-muted-foreground line-clamp-2">{selectedTemplate.content}</p>
              </div>
              
              <div className="space-y-2 sm:space-y-3">
                {selectedTemplate.variables.map(variable => (
                  <div key={variable}>
                    <Label htmlFor={variable} className="text-xs sm:text-sm font-medium">
                      {variable.replace('_', ' ')}
                    </Label>
                    <Textarea
                      id={variable}
                      value={variableValues[variable] || ''}
                      onChange={(e) => setVariableValues(prev => ({
                        ...prev,
                        [variable]: e.target.value
                      }))}
                      placeholder={`Enter ${variable.replace('_', ' ')}...`}
                      className="mt-1 min-h-[60px] sm:min-h-[80px] text-xs sm:text-sm"
                    />
                  </div>
                ))}
              </div>
              
              <div className="flex flex-col sm:flex-row gap-2 pt-2">
                <Button
                  variant="outline"
                  onClick={() => setSelectedTemplate(null)}
                  className="flex-1 h-8 sm:h-10 text-xs sm:text-sm"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleUseTemplate}
                  className="flex-1 h-8 sm:h-10 text-xs sm:text-sm"
                >
                  Use Template
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </motion.div>
  );
};

// Template Creator Component
interface TemplateCreatorProps {
  onSave: (template: any) => void;
  onCancel: () => void;
}

const TemplateCreator: React.FC<TemplateCreatorProps> = ({ onSave, onCancel }) => {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [category, setCategory] = useState<MessageTemplate['category']>('custom');
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');
  const [variables, setVariables] = useState<string[]>([]);

  // Extract variables from content
  const extractVariables = useCallback((text: string) => {
    const matches = text.match(/\{([^}]+)\}/g);
    return matches ? matches.map(match => match.slice(1, -1)) : [];
  }, []);

  // Update variables when content changes
  React.useEffect(() => {
    const extractedVars = extractVariables(content);
    setVariables(extractedVars);
  }, [content, extractVariables]);

  const handleAddTag = useCallback(() => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags(prev => [...prev, newTag.trim()]);
      setNewTag('');
    }
  }, [newTag, tags]);

  const handleRemoveTag = useCallback((tagToRemove: string) => {
    setTags(prev => prev.filter(tag => tag !== tagToRemove));
  }, []);

  const handleSave = useCallback(() => {
    if (!title.trim() || !content.trim()) return;
    
    onSave({
      title: title.trim(),
      content: content.trim(),
      category,
      tags,
      variables,
      isStarred: false,
    });
    
    // Reset form
    setTitle('');
    setContent('');
    setCategory('custom');
    setTags([]);
    setNewTag('');
    setVariables([]);
  }, [title, content, category, tags, variables, onSave]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
    >
      <Card>
        <CardHeader className="p-3 sm:p-4">
          <CardTitle className="text-sm sm:text-lg">Create New Template</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 sm:space-y-4 p-3 sm:p-4">
        <div>
          <Label htmlFor="template-title" className="text-xs sm:text-sm">Title</Label>
          <Input
            id="template-title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Enter template title..."
            className="mt-1 h-7 sm:h-10 text-xs sm:text-sm"
          />
        </div>
        
        <div>
          <Label htmlFor="template-content" className="text-xs sm:text-sm">Content</Label>
          <Textarea
            id="template-content"
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="Enter template content... Use {variable_name} for variables."
            className="mt-1 min-h-[80px] sm:min-h-[120px] text-xs sm:text-sm"
          />
          {variables.length > 0 && (
            <div className="mt-2">
              <Label className="text-xs text-muted-foreground">Detected variables:</Label>
              <div className="flex flex-wrap gap-1 mt-1">
                {variables.map(variable => (
                  <Badge key={variable} variant="outline" className="text-xs h-4 px-1">
                    {variable}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>
        
        <div>
          <Label htmlFor="template-category" className="text-xs sm:text-sm">Category</Label>
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-1 sm:gap-2 mt-2">
            {Object.keys(categoryIcons).map(cat => {
              const Icon = categoryIcons[cat as keyof typeof categoryIcons];
              return (
                <Button
                  key={cat}
                  variant={category === cat ? 'default' : 'outline'}
                  size="sm"
                  className="h-7 sm:h-8 text-xs sm:text-sm justify-start px-2 sm:px-3"
                  onClick={() => setCategory(cat as MessageTemplate['category'])}
                >
                  <Icon className="w-2.5 h-2.5 sm:w-3 sm:h-3 mr-1 flex-shrink-0" />
                  <span className="truncate">{cat}</span>
                </Button>
              );
            })}
          </div>
        </div>
        
        <div>
          <Label htmlFor="template-tags" className="text-xs sm:text-sm">Tags</Label>
          <div className="flex gap-1 sm:gap-2 mt-1">
            <Input
              id="template-tags"
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  handleAddTag();
                }
              }}
              placeholder="Add tag..."
              className="flex-1 h-7 sm:h-10 text-xs sm:text-sm"
            />
            <Button 
              onClick={handleAddTag} 
              disabled={!newTag.trim()}
              size="sm"
              className="h-7 w-7 p-0 sm:h-10 sm:w-10"
            >
              <Plus className="w-3 h-3 sm:w-4 sm:h-4" />
            </Button>
          </div>
          
          {tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {tags.map(tag => (
                <Badge key={tag} variant="secondary" className="text-xs h-4 px-1">
                  <Tag className="w-2 h-2 mr-1" />
                  <span className="truncate max-w-[60px] sm:max-w-[80px]">{tag}</span>
                  <X
                    className="w-2 h-2 ml-1 cursor-pointer flex-shrink-0"
                    onClick={() => handleRemoveTag(tag)}
                  />
                </Badge>
              ))}
            </div>
          )}
        </div>
        
        <div className="flex flex-col sm:flex-row justify-end gap-2">
          <Button 
            variant="outline" 
            onClick={onCancel}
            size="sm"
            className="h-7 sm:h-10 text-xs sm:text-sm"
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSave} 
            disabled={!title.trim() || !content.trim()}
            size="sm"
            className="h-7 sm:h-10 text-xs sm:text-sm"
          >
            Save Template
          </Button>
        </div>
      </CardContent>
    </Card>
    </motion.div>
  );
};

export default MessageTemplates;
export type { MessageTemplate, MessageTemplatesProps };