import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  GitBranch,
  Plus,
  ArrowRight,
  ArrowLeft,
  Merge,
  X,
  MessageSquare,
  Clock,
  User,
  Bot,
  ChevronDown,
  ChevronRight,
  MoreVertical,
  Copy,
  Share2,
  Bookmark,
} from 'lucide-react';
import { cn } from '../../lib/utils';

interface ConversationBranch {
  id: string;
  parentMessageId: string;
  title: string;
  description?: string;
  messages: ChatMessage[];
  createdAt: Date;
  isActive: boolean;
  metadata?: {
    model?: string;
    temperature?: number;
    context?: string[];
  };
}

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  branchId?: string;
  parentId?: string;
}

interface ConversationBranchingProps {
  messages: ChatMessage[];
  currentBranchId?: string;
  onBranchCreate: (parentMessageId: string, title: string, description?: string) => void;
  onBranchSwitch: (branchId: string) => void;
  onBranchMerge: (sourceBranchId: string, targetBranchId: string) => void;
  onBranchDelete: (branchId: string) => void;
  onMessageSend: (content: string, branchId?: string) => void;
}

const ConversationBranching: React.FC<ConversationBranchingProps> = ({
  messages,
  currentBranchId,
  onBranchCreate,
  onBranchSwitch,
  onBranchMerge,
  onBranchDelete,
  onMessageSend,
}) => {
  const [branches, setBranches] = useState<ConversationBranch[]>([
    {
      id: 'main',
      parentMessageId: '',
      title: 'Main Conversation',
      description: 'Primary conversation thread',
      messages: messages.filter(m => !m.branchId || m.branchId === 'main'),
      createdAt: new Date(),
      isActive: true,
    },
  ]);
  
  const [showBranchPanel, setShowBranchPanel] = useState(false);
  const [selectedMessageId, setSelectedMessageId] = useState<string | null>(null);
  const [newBranchTitle, setNewBranchTitle] = useState('');
  const [newBranchDescription, setNewBranchDescription] = useState('');
  const [expandedBranches, setExpandedBranches] = useState<Set<string>>(new Set(['main']));

  // Get current active branch
  const activeBranch = branches.find(b => b.id === currentBranchId) || branches[0];

  // Handle branch creation
  const handleCreateBranch = useCallback((messageId: string) => {
    if (!newBranchTitle.trim()) return;

    const branchId = `branch-${Date.now()}`;
    const newBranch: ConversationBranch = {
      id: branchId,
      parentMessageId: messageId,
      title: newBranchTitle,
      description: newBranchDescription,
      messages: [],
      createdAt: new Date(),
      isActive: false,
    };

    setBranches(prev => [...prev, newBranch]);
    onBranchCreate(messageId, newBranchTitle, newBranchDescription);
    
    // Reset form
    setNewBranchTitle('');
    setNewBranchDescription('');
    setSelectedMessageId(null);
  }, [newBranchTitle, newBranchDescription, onBranchCreate]);

  // Handle branch switching
  const handleSwitchBranch = useCallback((branchId: string) => {
    setBranches(prev => prev.map(b => ({
      ...b,
      isActive: b.id === branchId,
    })));
    onBranchSwitch(branchId);
  }, [onBranchSwitch]);

  // Toggle branch expansion
  const toggleBranchExpansion = useCallback((branchId: string) => {
    setExpandedBranches(prev => {
      const newSet = new Set(prev);
      if (newSet.has(branchId)) {
        newSet.delete(branchId);
      } else {
        newSet.add(branchId);
      }
      return newSet;
    });
  }, []);

  // Get branch tree structure
  const getBranchTree = useCallback(() => {
    const tree: { [key: string]: ConversationBranch[] } = {};
    
    branches.forEach(branch => {
      const parentId = branch.parentMessageId || 'root';
      if (!tree[parentId]) {
        tree[parentId] = [];
      }
      tree[parentId].push(branch);
    });
    
    return tree;
  }, [branches]);

  const branchTree = getBranchTree();

  return (
    <div className="flex h-full">
      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Branch Header */}
        <div className="flex items-center justify-between p-3 border-b bg-muted/20">
          <div className="flex items-center gap-2">
            <GitBranch className="w-4 h-4 text-primary" />
            <span className="font-medium text-sm">{activeBranch.title}</span>
            {activeBranch.description && (
              <Badge variant="outline" className="text-xs">
                {activeBranch.description}
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              {activeBranch.messages.length} messages
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowBranchPanel(!showBranchPanel)}
              className={cn(showBranchPanel && "bg-primary/10")}
            >
              <GitBranch className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Messages with Branch Points */}
        <ScrollArea className="flex-1 p-4">
          <div className="space-y-4">
            {activeBranch.messages.map((message, index) => {
              const hasBranches = branches.some(b => b.parentMessageId === message.id);
              
              return (
                <div key={message.id} className="relative">
                  {/* Message */}
                  <div className={cn(
                    "flex gap-3",
                    message.role === 'user' ? "justify-end" : "justify-start"
                  )}>
                    <Card className={cn(
                      "max-w-[70%]",
                      message.role === 'user' ? "bg-primary text-primary-foreground" : ""
                    )}>
                      <CardContent className="p-3">
                        <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                        
                        {/* Message Actions */}
                        <div className="flex items-center justify-between mt-2 pt-2 border-t">
                          <div className="flex items-center gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                              onClick={() => setSelectedMessageId(message.id)}
                              title="Create branch from this message"
                            >
                              <GitBranch className="w-3 h-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                            >
                              <Copy className="w-3 h-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                            >
                              <Bookmark className="w-3 h-3" />
                            </Button>
                          </div>
                          
                          <div className="text-xs text-muted-foreground">
                            {message.timestamp.toLocaleTimeString()}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Branch Indicator */}
                  {hasBranches && (
                    <div className="flex items-center gap-2 mt-2 ml-12">
                      <GitBranch className="w-3 h-3 text-muted-foreground" />
                      <span className="text-xs text-muted-foreground">
                        {branches.filter(b => b.parentMessageId === message.id).length} branches
                      </span>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </ScrollArea>

        {/* Branch Creation Modal */}
        {selectedMessageId && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <Card className="w-96">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <GitBranch className="w-4 h-4" />
                  Create New Branch
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="branch-title">Branch Title</Label>
                  <Textarea
                    id="branch-title"
                    value={newBranchTitle}
                    onChange={(e) => setNewBranchTitle(e.target.value)}
                    placeholder="Enter branch title..."
                    className="mt-1"
                  />
                </div>
                
                <div>
                  <Label htmlFor="branch-description">Description (Optional)</Label>
                  <Textarea
                    id="branch-description"
                    value={newBranchDescription}
                    onChange={(e) => setNewBranchDescription(e.target.value)}
                    placeholder="Describe this conversation branch..."
                    className="mt-1"
                  />
                </div>
                
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSelectedMessageId(null);
                      setNewBranchTitle('');
                      setNewBranchDescription('');
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={() => handleCreateBranch(selectedMessageId)}
                    disabled={!newBranchTitle.trim()}
                  >
                    Create Branch
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>

      {/* Branch Panel */}
      {showBranchPanel && (
        <div className="w-80 border-l bg-muted/10">
          <div className="p-3 border-b">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-sm">Conversation Branches</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowBranchPanel(false)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
          
          <ScrollArea className="flex-1">
            <div className="p-3 space-y-2">
              {branches.map(branch => {
                const isExpanded = expandedBranches.has(branch.id);
                const isActive = branch.id === activeBranch.id;
                
                return (
                  <Card
                    key={branch.id}
                    className={cn(
                      "cursor-pointer transition-colors",
                      isActive && "ring-2 ring-primary"
                    )}
                    onClick={() => handleSwitchBranch(branch.id)}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-4 w-4 p-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleBranchExpansion(branch.id);
                            }}
                          >
                            {isExpanded ? (
                              <ChevronDown className="w-3 h-3" />
                            ) : (
                              <ChevronRight className="w-3 h-3" />
                            )}
                          </Button>
                          <GitBranch className="w-3 h-3 text-muted-foreground" />
                          <span className="text-sm font-medium">{branch.title}</span>
                        </div>
                        
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <MoreVertical className="w-3 h-3" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-40">
                            <div className="space-y-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="w-full justify-start"
                                onClick={() => {
                                  // Handle merge
                                }}
                              >
                                <Merge className="w-3 h-3 mr-2" />
                                Merge
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="w-full justify-start"
                                onClick={() => {
                                  // Handle copy
                                }}
                              >
                                <Copy className="w-3 h-3 mr-2" />
                                Copy
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="w-full justify-start text-destructive"
                                onClick={() => onBranchDelete(branch.id)}
                                disabled={branch.id === 'main'}
                              >
                                <X className="w-3 h-3 mr-2" />
                                Delete
                              </Button>
                            </div>
                          </PopoverContent>
                        </Popover>
                      </div>
                      
                      {branch.description && (
                        <p className="text-xs text-muted-foreground mt-1">
                          {branch.description}
                        </p>
                      )}
                      
                      <div className="flex items-center justify-between mt-2">
                        <Badge variant="outline" className="text-xs">
                          {branch.messages.length} messages
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {branch.createdAt.toLocaleDateString()}
                        </span>
                      </div>
                      
                      {/* Branch Preview */}
                      {isExpanded && branch.messages.length > 0 && (
                        <div className="mt-2 pt-2 border-t space-y-1">
                          {branch.messages.slice(-2).map(msg => (
                            <div key={msg.id} className="text-xs">
                              <div className="flex items-center gap-1">
                                {msg.role === 'user' ? (
                                  <User className="w-2 h-2" />
                                ) : (
                                  <Bot className="w-2 h-2" />
                                )}
                                <span className="text-muted-foreground truncate">
                                  {msg.content.slice(0, 50)}...
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </ScrollArea>
        </div>
      )}
    </div>
  );
};

export default ConversationBranching;