import React, { useState, useCallback, useEffect, useRef } from 'react';
import { useAuth } from '@/services/auth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  Users,
  Edit3,
  Eye,
  MessageCircle,
  Clock,
  Save,
  Share2,
  Lock,
  Unlock,
  UserPlus,
  Settings,
  History,
  Undo,
  Redo,
  <PERSON>py,
  Download,
  FileText,
  Zap,
  AlertCircle,
  CheckCircle,
  X,
  More<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>er,
} from 'lucide-react';
import { cn } from '../../lib/utils';

interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  color: string;
  isOnline: boolean;
  lastSeen?: Date;
}

interface Cursor {
  userId: string;
  position: number;
  selection?: { start: number; end: number };
  timestamp: Date;
}

interface Comment {
  id: string;
  userId: string;
  content: string;
  position: number;
  resolved: boolean;
  createdAt: Date;
  replies: CommentReply[];
}

interface CommentReply {
  id: string;
  userId: string;
  content: string;
  createdAt: Date;
}

interface Change {
  id: string;
  userId: string;
  type: 'insert' | 'delete' | 'replace';
  position: number;
  content: string;
  timestamp: Date;
}

interface Version {
  id: string;
  content: string;
  userId: string;
  timestamp: Date;
  changes: Change[];
  message?: string;
}

interface CollaborativeEditingProps {
  documentId: string;
  initialContent: string;
  currentUser: User;
  onContentChange: (content: string) => void;
  onSave: (content: string, message?: string) => void;
  onShare: (users: string[]) => void;
  readOnly?: boolean;
}

// Generate a consistent color for a user based on their ID
const getUserColor = (userId: string): string => {
  const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#F97316', '#84CC16'];
  let hash = 0;
  for (let i = 0; i < userId.length; i++) {
    hash = userId.charCodeAt(i) + ((hash << 5) - hash);
  }
  return colors[Math.abs(hash) % colors.length];
};

// Comments will be loaded from the backend or initialized as empty
const initialComments: Comment[] = [];

const CollaborativeEditing: React.FC<CollaborativeEditingProps> = ({
  documentId,
  initialContent,
  currentUser,
  onContentChange,
  onSave,
  onShare,
  readOnly = false,
}) => {
  const { currentUser: authUser } = useAuth();
  const [content, setContent] = useState(initialContent);
  
  // Initialize collaborators with the current authenticated user
  const [collaborators, setCollaborators] = useState<User[]>(() => {
    if (authUser) {
      return [{
        id: authUser.id,
        name: authUser.username,
        email: authUser.email,
        color: getUserColor(authUser.id),
        isOnline: true,
      }];
    }
    return [];
  });
  
  const [cursors, setCursors] = useState<Cursor[]>([]);
  const [comments, setComments] = useState<Comment[]>(initialComments);
  const [versions, setVersions] = useState<Version[]>([]);
  const [isLocked, setIsLocked] = useState(false);
  const [showComments, setShowComments] = useState(true);
  const [showCursors, setShowCursors] = useState(true);
  const [selectedComment, setSelectedComment] = useState<Comment | null>(null);
  const [newComment, setNewComment] = useState('');
  const [commentPosition, setCommentPosition] = useState<number | null>(null);
  const [saveMessage, setSaveMessage] = useState('');
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Auto-save functionality
  useEffect(() => {
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }
    
    autoSaveTimeoutRef.current = setTimeout(() => {
      if (content !== initialContent) {
        setIsAutoSaving(true);
        onSave(content);
        setLastSaved(new Date());
        setTimeout(() => setIsAutoSaving(false), 1000);
      }
    }, 2000);
    
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, [content, initialContent, onSave]);

  // Handle content changes
  const handleContentChange = useCallback((newContent: string) => {
    if (readOnly || isLocked) return;
    
    setContent(newContent);
    onContentChange(newContent);
    
    // Simulate cursor position update
    const position = textareaRef.current?.selectionStart || 0;
    setCursors(prev => prev.filter(c => c.userId !== currentUser.id).concat({
      userId: currentUser.id,
      position,
      timestamp: new Date(),
    }));
  }, [readOnly, isLocked, currentUser.id, onContentChange]);

  // Handle manual save
  const handleSave = useCallback(() => {
    onSave(content, saveMessage || undefined);
    setSaveMessage('');
    setLastSaved(new Date());
    
    // Create version
    const newVersion: Version = {
      id: `version-${Date.now()}`,
      content,
      userId: currentUser.id,
      timestamp: new Date(),
      changes: [],
      message: saveMessage || undefined,
    };
    
    setVersions(prev => [newVersion, ...prev]);
  }, [content, saveMessage, currentUser.id, onSave]);

  // Handle comment creation
  const handleAddComment = useCallback(() => {
    if (!newComment.trim() || commentPosition === null) return;
    
    const comment: Comment = {
      id: `comment-${Date.now()}`,
      userId: currentUser.id,
      content: newComment.trim(),
      position: commentPosition,
      resolved: false,
      createdAt: new Date(),
      replies: [],
    };
    
    setComments(prev => [...prev, comment]);
    setNewComment('');
    setCommentPosition(null);
  }, [newComment, commentPosition, currentUser.id]);

  // Handle comment reply
  const handleReplyToComment = useCallback((commentId: string, replyContent: string) => {
    const reply: CommentReply = {
      id: `reply-${Date.now()}`,
      userId: currentUser.id,
      content: replyContent,
      createdAt: new Date(),
    };
    
    setComments(prev => prev.map(comment => 
      comment.id === commentId 
        ? { ...comment, replies: [...comment.replies, reply] }
        : comment
    ));
  }, [currentUser.id]);

  // Handle comment resolution
  const handleResolveComment = useCallback((commentId: string) => {
    setComments(prev => prev.map(comment => 
      comment.id === commentId 
        ? { ...comment, resolved: !comment.resolved }
        : comment
    ));
  }, []);

  // Get user by ID
  const getUserById = useCallback((userId: string) => {
    return collaborators.find(user => user.id === userId) || currentUser;
  }, [collaborators, currentUser]);

  // Format time ago
  const formatTimeAgo = useCallback((date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (minutes < 1) return 'just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  }, []);

  return (
    <div className="w-full max-w-6xl mx-auto">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Edit3 className="w-5 h-5" />
              Collaborative Editor
              {isLocked && <Lock className="w-4 h-4 text-orange-500" />}
            </CardTitle>
            
            <div className="flex items-center gap-2">
              {/* Auto-save indicator */}
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                {isAutoSaving ? (
                  <>
                    <Zap className="w-3 h-3 animate-pulse" />
                    Saving...
                  </>
                ) : lastSaved ? (
                  <>
                    <CheckCircle className="w-3 h-3 text-green-500" />
                    Saved {formatTimeAgo(lastSaved)}
                  </>
                ) : (
                  <>
                    <AlertCircle className="w-3 h-3 text-orange-500" />
                    Unsaved changes
                  </>
                )}
              </div>
              
              {/* Collaborators */}
              <div className="flex items-center -space-x-2">
                {collaborators.slice(0, 3).map(user => (
                  <Avatar key={user.id} className="w-6 h-6 border-2 border-background">
                    <AvatarImage src={user.avatar} />
                    <AvatarFallback className="text-xs" style={{ backgroundColor: user.color + '20', color: user.color }}>
                      {user.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                ))}
                {collaborators.length > 3 && (
                  <div className="w-6 h-6 rounded-full bg-muted border-2 border-background flex items-center justify-center text-xs">
                    +{collaborators.length - 3}
                  </div>
                )}
              </div>
              
              {/* Actions */}
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowComments(!showComments)}
                  className={cn(showComments && "bg-muted")}
                >
                  <MessageCircle className="w-4 h-4" />
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowCursors(!showCursors)}
                  className={cn(showCursors && "bg-muted")}
                >
                  <MousePointer className="w-4 h-4" />
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsLocked(!isLocked)}
                  disabled={readOnly}
                >
                  {isLocked ? <Lock className="w-4 h-4" /> : <Unlock className="w-4 h-4" />}
                </Button>
                
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreVertical className="w-4 h-4" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-48">
                    <div className="space-y-1">
                      <Button variant="ghost" size="sm" className="w-full justify-start">
                        <History className="w-4 h-4 mr-2" />
                        Version History
                      </Button>
                      <Button variant="ghost" size="sm" className="w-full justify-start">
                        <Share2 className="w-4 h-4 mr-2" />
                        Share Document
                      </Button>
                      <Button variant="ghost" size="sm" className="w-full justify-start">
                        <Download className="w-4 h-4 mr-2" />
                        Export
                      </Button>
                      <Button variant="ghost" size="sm" className="w-full justify-start">
                        <Settings className="w-4 h-4 mr-2" />
                        Settings
                      </Button>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
            {/* Main Editor */}
            <div className="lg:col-span-3 space-y-4">
              <div className="relative">
                <Textarea
                  ref={textareaRef}
                  value={content}
                  onChange={(e) => handleContentChange(e.target.value)}
                  onSelect={(e) => {
                    const target = e.target as HTMLTextAreaElement;
                    const position = target.selectionStart;
                    setCommentPosition(position);
                  }}
                  placeholder="Start typing your content..."
                  className="min-h-[400px] font-mono text-sm leading-relaxed"
                  disabled={readOnly || isLocked}
                />
                
                {/* Cursors overlay */}
                {showCursors && (
                  <div className="absolute inset-0 pointer-events-none">
                    {cursors.filter(cursor => cursor.userId !== currentUser.id).map(cursor => {
                      const user = getUserById(cursor.userId);
                      return (
                        <div
                          key={cursor.userId}
                          className="absolute w-0.5 h-4 animate-pulse"
                          style={{
                            backgroundColor: user.color,
                            left: `${(cursor.position / content.length) * 100}%`,
                            top: '10px',
                          }}
                        >
                          <div
                            className="absolute -top-6 left-0 px-1 py-0.5 rounded text-xs text-white whitespace-nowrap"
                            style={{ backgroundColor: user.color }}
                          >
                            {user.name}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
              
              {/* Quick comment */}
              {commentPosition !== null && (
                <Card className="border-blue-200 bg-blue-50">
                  <CardContent className="pt-4">
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Add Comment</Label>
                      <Textarea
                        value={newComment}
                        onChange={(e) => setNewComment(e.target.value)}
                        placeholder="Add your comment..."
                        className="min-h-[80px]"
                      />
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setNewComment('');
                            setCommentPosition(null);
                          }}
                        >
                          Cancel
                        </Button>
                        <Button
                          size="sm"
                          onClick={handleAddComment}
                          disabled={!newComment.trim()}
                        >
                          Add Comment
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
              
              {/* Save controls */}
              <Card>
                <CardContent className="pt-4">
                  <div className="space-y-3">
                    <div>
                      <Label htmlFor="save-message" className="text-sm">Save Message (Optional)</Label>
                      <Textarea
                        id="save-message"
                        value={saveMessage}
                        onChange={(e) => setSaveMessage(e.target.value)}
                        placeholder="Describe your changes..."
                        className="mt-1 min-h-[60px]"
                      />
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                          <Undo className="w-4 h-4 mr-1" />
                          Undo
                        </Button>
                        <Button variant="outline" size="sm">
                          <Redo className="w-4 h-4 mr-1" />
                          Redo
                        </Button>
                      </div>
                      
                      <Button onClick={handleSave} disabled={readOnly || isLocked}>
                        <Save className="w-4 h-4 mr-2" />
                        Save Changes
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            {/* Sidebar */}
            <div className="space-y-4">
              <Tabs defaultValue="comments" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="comments">Comments</TabsTrigger>
                  <TabsTrigger value="collaborators">Users</TabsTrigger>
                </TabsList>
                
                <TabsContent value="comments" className="space-y-2">
                  <ScrollArea className="h-96">
                    {comments.length === 0 ? (
                      <div className="text-center text-muted-foreground py-8">
                        <MessageCircle className="w-8 h-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">No comments yet</p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {comments.map(comment => {
                          const user = getUserById(comment.userId);
                          return (
                            <Card key={comment.id} className={cn(
                              "cursor-pointer transition-colors",
                              comment.resolved && "opacity-60",
                              selectedComment?.id === comment.id && "ring-2 ring-blue-500"
                            )}>
                              <CardContent className="p-3">
                                <div className="space-y-2">
                                  <div className="flex items-start justify-between">
                                    <div className="flex items-center gap-2">
                                      <Avatar className="w-5 h-5">
                                        <AvatarImage src={user.avatar} />
                                        <AvatarFallback className="text-xs" style={{ backgroundColor: user.color + '20', color: user.color }}>
                                          {user.name.split(' ').map(n => n[0]).join('')}
                                        </AvatarFallback>
                                      </Avatar>
                                      <span className="text-xs font-medium">{user.name}</span>
                                      <span className="text-xs text-muted-foreground">
                                        {formatTimeAgo(comment.createdAt)}
                                      </span>
                                    </div>
                                    
                                    <div className="flex items-center gap-1">
                                      {comment.resolved && (
                                        <CheckCircle className="w-3 h-3 text-green-500" />
                                      )}
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-5 w-5 p-0"
                                        onClick={() => handleResolveComment(comment.id)}
                                      >
                                        {comment.resolved ? <X className="w-3 h-3" /> : <CheckCircle className="w-3 h-3" />}
                                      </Button>
                                    </div>
                                  </div>
                                  
                                  <p className="text-sm">{comment.content}</p>
                                  
                                  {comment.replies.length > 0 && (
                                    <div className="space-y-1 pl-4 border-l-2 border-muted">
                                      {comment.replies.map(reply => {
                                        const replyUser = getUserById(reply.userId);
                                        return (
                                          <div key={reply.id} className="space-y-1">
                                            <div className="flex items-center gap-2">
                                              <Avatar className="w-4 h-4">
                                                <AvatarImage src={replyUser.avatar} />
                                                <AvatarFallback className="text-xs" style={{ backgroundColor: replyUser.color + '20', color: replyUser.color }}>
                                                  {replyUser.name.split(' ').map(n => n[0]).join('')}
                                                </AvatarFallback>
                                              </Avatar>
                                              <span className="text-xs font-medium">{replyUser.name}</span>
                                              <span className="text-xs text-muted-foreground">
                                                {formatTimeAgo(reply.createdAt)}
                                              </span>
                                            </div>
                                            <p className="text-xs">{reply.content}</p>
                                          </div>
                                        );
                                      })}
                                    </div>
                                  )}
                                </div>
                              </CardContent>
                            </Card>
                          );
                        })}
                      </div>
                    )}
                  </ScrollArea>
                </TabsContent>
                
                <TabsContent value="collaborators" className="space-y-2">
                  <ScrollArea className="h-96">
                    <div className="space-y-2">
                      {collaborators.map(user => (
                        <Card key={user.id}>
                          <CardContent className="p-3">
                            <div className="flex items-center gap-3">
                              <div className="relative">
                                <Avatar className="w-8 h-8">
                                  <AvatarImage src={user.avatar} />
                                  <AvatarFallback style={{ backgroundColor: user.color + '20', color: user.color }}>
                                    {user.name.split(' ').map(n => n[0]).join('')}
                                  </AvatarFallback>
                                </Avatar>
                                <div className={cn(
                                  "absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-background",
                                  user.isOnline ? "bg-green-500" : "bg-gray-400"
                                )} />
                              </div>
                              
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium truncate">{user.name}</p>
                                <p className="text-xs text-muted-foreground truncate">
                                  {user.isOnline ? 'Online' : user.lastSeen ? `Last seen ${formatTimeAgo(user.lastSeen)}` : 'Offline'}
                                </p>
                              </div>
                              
                              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                                <MoreVertical className="w-3 h-3" />
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                      
                      <Button variant="outline" className="w-full" disabled={readOnly}>
                        <UserPlus className="w-4 h-4 mr-2" />
                        Invite Collaborator
                      </Button>
                    </div>
                  </ScrollArea>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CollaborativeEditing;
export type { User, Comment, Version, CollaborativeEditingProps };