import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Upload, FileText, Send } from 'lucide-react';

interface EmptyChatStateProps {
  onAddSources: () => void;
}

const EmptyChatState: React.FC<EmptyChatStateProps> = React.memo(({ onAddSources }) => {
  return (
    <div className="flex-1 flex flex-col items-center justify-center p-8 overflow-hidden" role="main" aria-label="Empty chat state">
      <div className="text-center mb-8">
        <div className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center bg-muted" aria-hidden="true">
          <Upload className="h-8 w-8 text-slate-600" aria-hidden="true" />
        </div>
        <h2 className="text-xl font-medium text-foreground mb-4">Add a source to get started</h2>
        <Button
          onClick={onAddSources}
          className="transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-blue-500/25 active:scale-95 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 group"
          aria-label="Upload a source to start chatting"
        >
          <Upload className="h-4 w-4 mr-2 transition-transform duration-300 group-hover:scale-110 group-hover:-rotate-12" aria-hidden="true" />
          <span className="transition-all duration-300 group-hover:tracking-wide">Upload a source</span>
        </Button>
      </div>

      {/* Bottom Input */}
      <div className="w-full max-w-2xl">
        <div className="flex space-x-4">
          <Input
            placeholder="Upload a source to get started"
            disabled
            className="flex-1"
            aria-label="Message input (disabled)"
          />
          <div className="flex items-center text-sm text-muted-foreground transition-colors duration-300 hover:text-foreground" aria-label="Source count">
            <FileText className="h-4 w-4 mr-1 opacity-60" aria-hidden="true" />
            <span>0 sources</span>
          </div>
          <Button
            disabled
            className="opacity-50 cursor-not-allowed transition-opacity duration-300"
            aria-label="Send message (disabled)"
          >
            <Send className="h-4 w-4 opacity-60" aria-hidden="true" />
          </Button>
        </div>
      </div>
    </div>
  );
});

EmptyChatState.displayName = 'EmptyChatState';

export default EmptyChatState;