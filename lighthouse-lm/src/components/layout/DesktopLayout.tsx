import React, { useState, useEffect } from 'react';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import { MessageSquare, BookOpen, Brain, Bell, Search, Settings, Keyboard, ArrowLeft, Database } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import ChatArea from '../chat/ChatArea';
import Sidebar from '../sidebar/Sidebar';
import { NotebookProvider } from '../../contexts/NotebookContext';
import SourceManagementDialog from '../source/SourceManagement';
import ConnectionIndicator from '../shared/ConnectionIndicator';
import SearchBar from '../search/SearchBar';
import NotificationCenter, { Notification } from '../notifications/NotificationCenter';
import KeyboardShortcuts from '../shortcuts/KeyboardShortcuts';
import QuickActions from '../quickactions/QuickActions';
import ThemeToggle from '../theme/ThemeToggle';
import ProgressTracker, { ProgressTask } from '../progress/ProgressTracker';
import { useNotebookLayout, NOTEBOOK_TABS } from '../../hooks/useNotebookLayout';
import { useToast } from '../../hooks/useToast';
import { useSources } from '../../hooks/useSources';
import { useChatMessages } from '../../hooks/useChatMessages';

interface DesktopLayoutProps {
  notebookId?: string;
  notebook: any;
  hasSource: boolean;
  onBack?: () => void;
}

export const DesktopLayout: React.FC<DesktopLayoutProps> = ({
  notebookId,
  notebook,
  hasSource,
  onBack,
}) => {
  const {
    activeTab,
    selectedCitation,
    setActiveTab,
    setSelectedCitation,
    handleCitationClick,
    handleCitationClose,
    isTabActive,
  } = useNotebookLayout('chat');
  
  const { toast } = useToast();
  const [showKeyboardShortcuts, setShowKeyboardShortcuts] = useState(false);
  const [showSourceManagement, setShowSourceManagement] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [progressTasks, setProgressTasks] = useState<ProgressTask[]>([]);
  
  // Get data
  const { sources } = useSources(notebookId);
  const { messages } = useChatMessages(notebookId);
  
  // Sample notifications for demo
  useEffect(() => {
    const sampleNotifications: Notification[] = [
      {
        id: '1',
        type: 'success',
        category: 'source',
        title: 'Source uploaded',
        message: 'Your document has been processed successfully',
        timestamp: new Date(),
        read: false,
      },
    ];
    setNotifications(sampleNotifications);
  }, []);
  
  // Handlers for search
  const handleSearch = (query: string, filters?: any) => {
    setSearchQuery(query);
    // In a real implementation, this would trigger a search across sources and messages
    // For now, we'll just show a toast and set the search query
    toast({
      title: 'Search initiated',
      description: `Searching for: ${query}`,
    });
    
    // Dispatch a custom event that other components can listen to
    const searchEvent = new CustomEvent('lighthouse-search', {
      detail: { query, filters }
    });
    window.dispatchEvent(searchEvent);
  };
  
  // Handlers for notifications
  const handleMarkNotificationRead = (id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    );
  };
  
  const handleMarkAllNotificationsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  };
  
  const handleClearNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };
  
  const handleClearAllNotifications = () => {
    setNotifications([]);
  };
  
  // Quick action handlers
  const handleCreateNotebook = () => {
    toast({
      title: 'Creating notebook...',
      description: 'This feature is coming soon',
    });
  };
  
  const handleUploadSource = () => {
    // Trigger file upload
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    fileInput?.click();
  };
  
  const handleStartChat = () => {
    setActiveTab(NOTEBOOK_TABS.CHAT.id);
  };
  
  const handleGenerateDiagram = () => {
    toast({
      title: 'Generating diagram...',
      description: 'AI is creating a visual representation',
    });
  };
  
  const handleOpenSearch = () => {
    setShowSearch(true);
    setTimeout(() => {
      document.getElementById('lighthouse-search')?.focus();
    }, 100);
  };
  
  const handleImportFromWeb = () => {
    toast({
      title: 'Import from web',
      description: 'This feature is coming soon',
    });
  };
  
  const handleOpenSourceManagement = () => {
    setShowSourceManagement(true);
  };
  
  const handleSourceSelect = (source: any) => {
    // Handle source selection from the dialog
    handleCitationClick({
      id: source.id,
      source_id: source.id,
      source_title: source.title,
      source: source.title,
      excerpt: source.content,
      page_number: 1,
    });
    setShowSourceManagement(false);
  };

  return (
    <NotebookProvider notebookId={notebookId}>
      <div className="h-screen bg-background text-foreground overflow-hidden flex flex-col">
      {/* Top Header Bar */}
      <div className="h-14 bg-card border-b border-border flex items-center justify-between px-4 flex-shrink-0">
        <div className="flex items-center gap-4">
          {onBack && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
          )}
          <div className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-primary" />
            <span className="font-semibold">{notebook?.title || 'Notebook'}</span>
          </div>
          <ConnectionIndicator />
        </div>
        
        <div className="flex items-center gap-4">
          {showSearch && (
            <SearchBar
              onSearch={handleSearch}
              className="w-96"
              placeholder="Search in notebook..."
            />
          )}
          {!showSearch && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleOpenSearch}
            >
              <Search className="h-4 w-4" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleOpenSourceManagement}
          >
            <Database className="h-4 w-4" />
          </Button>
          <NotificationCenter
            notifications={notifications}
            onMarkAsRead={handleMarkNotificationRead}
            onMarkAllAsRead={handleMarkAllNotificationsRead}
            onClear={handleClearNotification}
            onClearAll={handleClearAllNotifications}
          />
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowKeyboardShortcuts(true)}
          >
            <Keyboard className="h-4 w-4" />
          </Button>
          <ThemeToggle />
          <Button
            variant="ghost"
            size="sm"
          >
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      {/* Main Content Area */}
      <div className="flex-1 p-4 min-h-0">
        <PanelGroup direction="horizontal" className="h-full rounded-lg">
        {/* Left Sidebar */}
        <Panel defaultSize={20} minSize={15} maxSize={30} className="bg-card border border-border rounded-l-lg">
          <div className="flex flex-col h-full overflow-hidden">
            {/* Sidebar Header */}
            <div className="p-4 border-b border-border flex-shrink-0">
              <div className="text-xs font-medium text-muted-foreground uppercase tracking-wider">
                Navigation
              </div>
            </div>

            {/* Navigation Items */}
            <div className="flex-1 min-h-0 overflow-y-auto">
              <nav className="p-2">
                <button
                  onClick={() => setActiveTab(NOTEBOOK_TABS.CHAT.id)}
                  className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-colors mb-1 ${
                    isTabActive(NOTEBOOK_TABS.CHAT.id)
                      ? 'bg-accent text-accent-foreground'
                      : 'text-foreground hover:bg-accent hover:text-accent-foreground'
                  }`}
                >
                  <MessageSquare className="h-4 w-4 flex-shrink-0" />
                  <span className="text-sm truncate">{NOTEBOOK_TABS.CHAT.label}</span>
                </button>

                <button
                  onClick={() => setActiveTab(NOTEBOOK_TABS.SOURCES.id)}
                  className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-colors mb-1 ${
                    isTabActive(NOTEBOOK_TABS.SOURCES.id)
                      ? 'bg-accent text-accent-foreground'
                      : 'text-foreground hover:bg-accent hover:text-accent-foreground'
                  }`}
                >
                  <BookOpen className="h-4 w-4 flex-shrink-0" />
                  <span className="text-sm truncate">{NOTEBOOK_TABS.SOURCES.label}</span>
                </button>
              </nav>
            </div>
          </div>
        </Panel>

        <PanelResizeHandle className="resize-handle-horizontal" />

        {/* Main Content Area */}
        <Panel defaultSize={50} minSize={30} className="bg-card border-y border-border">
          <div className="flex flex-col h-full">
            <div className="flex-1 overflow-hidden">
              {isTabActive(NOTEBOOK_TABS.CHAT.id) ? (
                <ChatArea
                  notebookId={notebookId}
                  notebook={notebook}
                  onCitationClick={handleCitationClick}
                />
              ) : isTabActive(NOTEBOOK_TABS.SOURCES.id) ? (
                <div className="flex flex-col items-center justify-center h-full p-8">
                  <Database className="w-16 h-16 text-muted-foreground mb-4" />
                  <h2 className="text-2xl font-semibold mb-2">Source Management</h2>
                  <p className="text-muted-foreground text-center mb-6 max-w-md">
                    Organize, manage, and analyze all your knowledge sources in one place
                  </p>
                  <Button 
                    onClick={handleOpenSourceManagement}
                    size="lg"
                    className="gap-2"
                  >
                    <Database className="w-5 h-5" />
                    Open Source Manager
                  </Button>
                  <div className="grid grid-cols-3 gap-4 mt-8 text-center">
                    <div>
                      <div className="text-3xl font-bold">{sources?.length || 0}</div>
                      <div className="text-sm text-muted-foreground">Total Sources</div>
                    </div>
                    <div>
                      <div className="text-3xl font-bold">24</div>
                      <div className="text-sm text-muted-foreground">Recent Uploads</div>
                    </div>
                    <div>
                      <div className="text-3xl font-bold">2.4 GB</div>
                      <div className="text-sm text-muted-foreground">Storage Used</div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="p-8 text-center text-muted-foreground">
                  <p>Select a tab to view content</p>
                </div>
              )}
            </div>
          </div>
        </Panel>

        <PanelResizeHandle className="resize-handle-horizontal" />

        {/* Right Panel - Studio Sidebar */}
        <Panel defaultSize={30} minSize={20} maxSize={40} className="bg-card border border-border rounded-r-lg">
          <div className="flex flex-col h-full overflow-hidden">
            <div className="flex-1 overflow-y-auto">
              {selectedCitation && (
                <div className="p-4 border-b border-border">
                  <h3 className="text-sm font-medium mb-3">Citation Details</h3>
                  <div className="space-y-2 text-sm text-muted-foreground">
                    <p>{selectedCitation.excerpt || selectedCitation.source_title}</p>
                  </div>
                </div>
              )}

              <div className="flex-1">
                <Sidebar
                  notebookId={notebookId}
                  selectedCitation={selectedCitation}
                  onExport={(format, content) => {
                    toast({
                      title: 'Exporting',
                      description: `Exporting as ${format}...`,
                    });
                  }}
                  onPublish={(platform, content) => {
                    toast({
                      title: 'Publishing',
                      description: `Publishing to ${platform}...`,
                    });
                  }}
                  onCollaborate={(action, data) => {
                    toast({
                      title: 'Collaboration',
                      description: `${action} initiated`,
                    });
                  }}
                />
              </div>
            </div>
          </div>
        </Panel>
      </PanelGroup>
      </div>
      
      
      {/* Progress Tracker */}
      <ProgressTracker
        tasks={progressTasks}
        onCancel={(id) => {}}
        onPause={(id) => {}}
        onResume={(id) => {}}
        onRetry={(id) => {}}
        onClose={(id) => setProgressTasks(prev => prev.filter(t => t.id !== id))}
      />
      
      {/* Keyboard Shortcuts Modal */}
      <KeyboardShortcuts
        isOpen={showKeyboardShortcuts}
        onClose={() => setShowKeyboardShortcuts(false)}
      />
      
      {/* Quick Actions Floating Menu */}
      <QuickActions
        onCreateNotebook={handleCreateNotebook}
        onUploadSource={handleUploadSource}
        onStartChat={handleStartChat}
        onGenerateDiagram={handleGenerateDiagram}
        onOpenSearch={handleOpenSearch}
        onImportFromWeb={handleImportFromWeb}
      />
      
      {/* Source Management Dialog */}
      <SourceManagementDialog
        open={showSourceManagement}
        onOpenChange={setShowSourceManagement}
        notebookId={notebookId}
        onSourceSelect={handleSourceSelect}
      />
      </div>
    </NotebookProvider>
  );
};

export default DesktopLayout;