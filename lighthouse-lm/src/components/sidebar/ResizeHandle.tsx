import React from 'react';

interface ResizeHandleProps {
  onMouseDown: (e: React.MouseEvent) => void;
  orientation?: 'vertical' | 'horizontal';
  position?: 'left' | 'right' | 'top' | 'bottom';
}

const ResizeHandle: React.FC<ResizeHandleProps> = ({ 
  onMouseDown, 
  orientation = 'vertical',
  position = 'right'
}) => {
  const isVertical = orientation === 'vertical';
  
  const positionClasses = {
    left: 'left-0 top-0 bottom-0',
    right: 'right-0 top-0 bottom-0',
    top: 'top-0 left-0 right-0',
    bottom: 'bottom-0 left-0 right-0'
  };

  return (
    <div
      className={`absolute ${positionClasses[position]} ${
        isVertical ? 'w-1 cursor-col-resize' : 'h-1 cursor-row-resize'
      } group z-20 hover:bg-primary/10 transition-colors`}
      onMouseDown={onMouseDown}
    >
      <div className={`absolute ${
        isVertical 
          ? position === 'left' ? 'left-0' : 'right-0'
          : position === 'top' ? 'top-0' : 'bottom-0'
      } ${
        isVertical ? 'inset-y-0 w-px' : 'inset-x-0 h-px'
      } bg-border group-hover:bg-primary/50 transition-colors`} />
      
      {/* Visual indicator on hover */}
      <div className={`absolute ${
        isVertical 
          ? 'top-1/2 -translate-y-1/2 -translate-x-1/2 left-1/2' 
          : 'left-1/2 -translate-x-1/2 -translate-y-1/2 top-1/2'
      } opacity-0 group-hover:opacity-100 transition-opacity`}>
        <div className={`${
          isVertical ? 'w-0.5 h-8' : 'w-8 h-0.5'
        } bg-primary/50 rounded-full`} />
      </div>
    </div>
  );
};

export default ResizeHandle;