import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import { Sun, Moon, Monitor, Palette, Check } from 'lucide-react';
import { useTheme } from '@/contexts/ThemeContext';

type Theme = 'light' | 'dark' | 'system';
type ColorScheme = 'default' | 'blue' | 'green' | 'purple' | 'orange';

interface ThemeToggleProps {
  className?: string;
  showLabel?: boolean;
  expanded?: boolean;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({ 
  className = '',
  showLabel = false,
  expanded = false
}) => {
  const { theme: appTheme, setTheme: setAppTheme, isLoaded } = useTheme();
  
  // Convert app theme to local theme format
  const theme = appTheme === 'light' || appTheme === 'dark' || appTheme === 'system' 
    ? appTheme 
    : 'system'; // fallback to system for custom themes

  // Detect system theme
  const [systemTheme, setSystemTheme] = React.useState<'light' | 'dark'>('light');

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    setSystemTheme(mediaQuery.matches ? 'dark' : 'light');

    const handleChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? 'dark' : 'light');
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  const handleThemeChange = async (newTheme: Theme) => {
    try {
      await setAppTheme(newTheme);
    } catch (error) {
      console.error('Failed to change theme:', error);
    }
  };

  const getThemeIcon = () => {
    const effectiveTheme = theme === 'system' ? systemTheme : theme;
    
    if (theme === 'system') {
      return <Monitor className="h-4 w-4" />;
    }
    
    return effectiveTheme === 'dark' ? (
      <Moon className="h-4 w-4" />
    ) : (
      <Sun className="h-4 w-4" />
    );
  };

  const colorSchemes = [
    { id: 'default', label: 'Default', color: 'hsl(var(--primary))' },
    { id: 'blue', label: 'Ocean Blue', color: '#3b82f6' },
    { id: 'green', label: 'Forest Green', color: '#10b981' },
    { id: 'purple', label: 'Royal Purple', color: '#8b5cf6' },
    { id: 'orange', label: 'Sunset Orange', color: '#f97316' },
  ];

  if (expanded) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div>
          <label className="text-sm font-medium mb-2 block">Theme</label>
          <div className="grid grid-cols-3 gap-2">
            <Button
              variant={theme === 'light' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleThemeChange('light')}
              className="gap-2"
              disabled={!isLoaded}
            >
              <Sun className="h-4 w-4" />
              Light
            </Button>
            <Button
              variant={theme === 'dark' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleThemeChange('dark')}
              className="gap-2"
              disabled={!isLoaded}
            >
              <Moon className="h-4 w-4" />
              Dark
            </Button>
            <Button
              variant={theme === 'system' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleThemeChange('system')}
              className="gap-2"
              disabled={!isLoaded}
            >
              <Monitor className="h-4 w-4" />
              System
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={`gap-2 ${className}`}
          disabled={isLoading}
        >
          <motion.div
            key={theme + systemTheme}
            initial={{ rotate: -30, opacity: 0 }}
            animate={{ rotate: 0, opacity: 1 }}
            transition={{ duration: 0.2 }}
          >
            {getThemeIcon()}
          </motion.div>
          {showLabel && <span>Theme</span>}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuLabel>Appearance</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        <DropdownMenuItem
          onClick={() => handleThemeChange('light')}
          className="gap-2"
        >
          <Sun className="h-4 w-4" />
          Light
          {theme === 'light' && <Check className="h-4 w-4 ml-auto" />}
        </DropdownMenuItem>
        
        <DropdownMenuItem
          onClick={() => handleThemeChange('dark')}
          className="gap-2"
        >
          <Moon className="h-4 w-4" />
          Dark
          {theme === 'dark' && <Check className="h-4 w-4 ml-auto" />}
        </DropdownMenuItem>
        
        <DropdownMenuItem
          onClick={() => handleThemeChange('system')}
          className="gap-2"
        >
          <Monitor className="h-4 w-4" />
          System
          {theme === 'system' && <Check className="h-4 w-4 ml-auto" />}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ThemeToggle;