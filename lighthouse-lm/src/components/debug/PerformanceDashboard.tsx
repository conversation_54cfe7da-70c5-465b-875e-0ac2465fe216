import React, { useState, useEffect } from 'react';
import { PerformanceMonitor, BundleAnalyzer } from '../../utils/performance';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Ta<PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '../ui/tabs';
import { BarChart3, Clock, HardDrive, Zap, X } from 'lucide-react';

interface PerformanceDashboardProps {
  isOpen: boolean;
  onClose: () => void;
}

const PerformanceDashboard: React.FC<PerformanceDashboardProps> = ({ isOpen, onClose }) => {
  const [metrics, setMetrics] = useState<Record<string, { average: number; count: number; latest: number }>>({});
  const [memoryUsage, setMemoryUsage] = useState<any>(null);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (isOpen) {
      const updateMetrics = () => {
        const monitor = PerformanceMonitor.getInstance();
        setMetrics(monitor.getAllMetrics());
        setMemoryUsage(BundleAnalyzer.getMemoryUsage());
      };

      // Initial update
      updateMetrics();

      // Set up refresh interval
      const interval = setInterval(updateMetrics, 2000);
      setRefreshInterval(interval);

      return () => {
        if (interval) clearInterval(interval);
      };
    } else {
      if (refreshInterval) {
        clearInterval(refreshInterval);
        setRefreshInterval(null);
      }
    }
  }, [isOpen, refreshInterval]);

  if (!isOpen) return null;

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTime = (ms: number) => {
    return `${ms.toFixed(2)}ms`;
  };

  const getPerformanceColor = (time: number) => {
    if (time < 16) return 'bg-green-500';
    if (time < 50) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl max-h-[80vh] overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-2xl font-bold flex items-center gap-2">
            <BarChart3 className="h-6 w-6" />
            Performance Dashboard
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        <CardContent className="overflow-auto">
          <Tabs defaultValue="components" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="components" className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Components
              </TabsTrigger>
              <TabsTrigger value="memory" className="flex items-center gap-2">
                <HardDrive className="h-4 w-4" />
                Memory
              </TabsTrigger>
              <TabsTrigger value="tips" className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                Tips
              </TabsTrigger>
            </TabsList>

            <TabsContent value="components" className="space-y-4">
              <div className="grid gap-4">
                <h3 className="text-lg font-semibold">Component Render Times</h3>
                {Object.keys(metrics).length === 0 ? (
                  <p className="text-muted-foreground">No performance data available yet. Interact with the application to see metrics.</p>
                ) : (
                  <div className="space-y-2">
                    {Object.entries(metrics)
                      .sort(([, a], [, b]) => b.average - a.average)
                      .map(([componentName, data]) => (
                        <div key={componentName} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center gap-3">
                            <div className={`w-3 h-3 rounded-full ${getPerformanceColor(data.average)}`} />
                            <span className="font-medium">{componentName}</span>
                            <Badge variant="secondary">{data.count} renders</Badge>
                          </div>
                          <div className="flex items-center gap-4 text-sm">
                            <span>Avg: {formatTime(data.average)}</span>
                            <span>Latest: {formatTime(data.latest)}</span>
                          </div>
                        </div>
                      ))}
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="memory" className="space-y-4">
              <div className="grid gap-4">
                <h3 className="text-lg font-semibold">Memory Usage</h3>
                {memoryUsage ? (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Used Heap</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {formatBytes(memoryUsage.usedJSHeapSize)}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Currently allocated
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Total Heap</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {formatBytes(memoryUsage.totalJSHeapSize)}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Total heap size
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Heap Limit</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {formatBytes(memoryUsage.jsHeapSizeLimit)}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Maximum heap size
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                ) : (
                  <p className="text-muted-foreground">Memory usage information is not available in this browser.</p>
                )}
              </div>
            </TabsContent>

            <TabsContent value="tips" className="space-y-4">
              <div className="grid gap-4">
                <h3 className="text-lg font-semibold">Performance Optimization Tips</h3>
                <div className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Component Render Times</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-green-500" />
                        <span className="text-sm">Good (&lt; 16ms) - Smooth 60fps</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-yellow-500" />
                        <span className="text-sm">Acceptable (16-50ms) - May cause frame drops</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-red-500" />
                        <span className="text-sm">Poor (&gt; 50ms) - Noticeable lag</span>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Optimization Strategies</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2 text-sm">
                        <li>• Use React.memo() for components that re-render frequently</li>
                        <li>• Implement useMemo() and useCallback() for expensive calculations</li>
                        <li>• Consider lazy loading for large components</li>
                        <li>• Optimize images and use appropriate formats (WebP, AVIF)</li>
                        <li>• Minimize bundle size with code splitting</li>
                        <li>• Use virtualization for long lists</li>
                        <li>• Debounce user input handlers</li>
                      </ul>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default PerformanceDashboard;