import React from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Type, Layout, Palette, Image } from 'lucide-react';
import { Slide, SlideLayout, SlideTransition } from './types';

interface SlideEditorProps {
  isOpen: boolean;
  slide: Slide | null;
  slideTitle: string;
  slideContent: string;
  slideNotes: string;
  slideLayout: string;
  slideTransition: string;
  slideBackground: string;
  slideClass: string;
  showPresenterNotes: boolean;
  slideLayouts: SlideLayout[];
  slideTransitions: SlideTransition[];
  onTitleChange: (title: string) => void;
  onContentChange: (content: string) => void;
  onNotesChange: (notes: string) => void;
  onLayoutChange: (layout: string) => void;
  onTransitionChange: (transition: string) => void;
  onBackgroundChange: (background: string) => void;
  onClassChange: (className: string) => void;
  onShowNotesChange: (show: boolean) => void;
  onSave: () => void;
  onCancel: () => void;
}

export const SlideEditor: React.FC<SlideEditorProps> = ({
  isOpen,
  slide,
  slideTitle,
  slideContent,
  slideNotes,
  slideLayout,
  slideTransition,
  slideBackground,
  slideClass,
  showPresenterNotes,
  slideLayouts,
  slideTransitions,
  onTitleChange,
  onContentChange,
  onNotesChange,
  onLayoutChange,
  onTransitionChange,
  onBackgroundChange,
  onClassChange,
  onShowNotesChange,
  onSave,
  onCancel,
}) => {
  if (!isOpen) return null;

  const renderPreviewContent = () => {
    return slideContent.split('\n').map((line, i) => {
      if (line.startsWith('# ')) {
        return <h1 key={i} className="text-2xl font-bold my-2">{line.substring(2)}</h1>;
      } else if (line.startsWith('## ')) {
        return <h2 key={i} className="text-xl font-semibold my-2">{line.substring(3)}</h2>;
      } else if (line.startsWith('- ')) {
        return <li key={i} className="ml-4 my-1">{line.substring(2)}</li>;
      } else if (line.startsWith('```')) {
        return <pre key={i} className="bg-muted p-2 rounded my-2"><code>{line.substring(3)}</code></pre>;
      } else if (line) {
        return <p key={i} className="my-2">{line}</p>;
      }
      return null;
    });
  };

  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-3xl max-h-[90vh] overflow-hidden flex flex-col">
        <div className="p-4 border-b flex items-center justify-between">
          <h3 className="text-lg font-semibold">Edit Slide</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={onCancel}
            className="h-8 w-8 p-0"
          >
            ×
          </Button>
        </div>
        
        <div className="flex-1 overflow-hidden">
          <Tabs defaultValue="content" className="h-full flex flex-col">
            <TabsList className="mx-4 mt-4">
              <TabsTrigger value="content">Content</TabsTrigger>
              <TabsTrigger value="properties">Properties</TabsTrigger>
              <TabsTrigger value="preview">Preview</TabsTrigger>
            </TabsList>
            
            <TabsContent value="content" className="flex-1 overflow-y-auto p-4 space-y-4">
              <div>
                <Label htmlFor="slide-title">Slide Title</Label>
                <Input
                  id="slide-title"
                  value={slideTitle}
                  onChange={(e) => onTitleChange(e.target.value)}
                  placeholder="Enter slide title"
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="slide-content">Content (Markdown)</Label>
                <Textarea
                  id="slide-content"
                  value={slideContent}
                  onChange={(e) => onContentChange(e.target.value)}
                  className="min-h-[250px] font-mono text-sm mt-1"
                  placeholder="# Heading\n\n- Bullet point\n- Another point\n\n```code\nExample code\n```"
                />
              </div>
              
              <div>
                <Label htmlFor="slide-notes">Speaker Notes</Label>
                <Textarea
                  id="slide-notes"
                  value={slideNotes}
                  onChange={(e) => onNotesChange(e.target.value)}
                  className="min-h-[100px] mt-1"
                  placeholder="Add speaker notes (optional)..."
                />
              </div>
            </TabsContent>
            
            <TabsContent value="properties" className="flex-1 overflow-y-auto p-4 space-y-4">
              <div>
                <Label>Layout</Label>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {slideLayouts.map(layout => (
                    <Button
                      key={layout.name}
                      variant={slideLayout === layout.name ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => onLayoutChange(layout.name)}
                      className="justify-start"
                    >
                      {layout.icon}
                      <span className="ml-2 text-xs">{layout.label}</span>
                    </Button>
                  ))}
                </div>
              </div>
              
              <div>
                <Label>Transition</Label>
                <Select value={slideTransition} onValueChange={onTransitionChange}>
                  <SelectTrigger className="mt-2">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {slideTransitions.map(transition => (
                      <SelectItem key={transition.name} value={transition.name}>
                        <div>
                          <div className="font-medium">{transition.label}</div>
                          <div className="text-xs text-muted-foreground">{transition.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="slide-background">Background (URL or Color)</Label>
                <Input
                  id="slide-background"
                  value={slideBackground}
                  onChange={(e) => onBackgroundChange(e.target.value)}
                  placeholder="https://... or #hex or gradient"
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="slide-class">CSS Classes</Label>
                <Input
                  id="slide-class"
                  value={slideClass}
                  onChange={(e) => onClassChange(e.target.value)}
                  placeholder="text-center, text-left, etc."
                  className="mt-1"
                />
              </div>
            </TabsContent>
            
            <TabsContent value="preview" className="flex-1 overflow-y-auto p-4">
              <div className="h-full flex flex-col">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-sm font-medium">Live Preview</h4>
                  <div className="flex items-center gap-2">
                    <Switch
                      id="show-notes"
                      checked={showPresenterNotes}
                      onCheckedChange={onShowNotesChange}
                    />
                    <Label htmlFor="show-notes" className="text-xs">Show Notes</Label>
                  </div>
                </div>
                
                <div 
                  className={`flex-1 bg-gradient-to-br from-background to-muted rounded-lg border-2 border-dashed p-8 ${
                    slideLayout === 'center' ? 'flex items-center justify-center text-center' :
                    slideLayout === 'cover' ? 'flex flex-col items-center justify-center text-center' :
                    ''
                  }`}
                  style={{
                    backgroundImage: slideBackground?.startsWith('http') ? `url(${slideBackground})` : undefined,
                    backgroundColor: slideBackground?.startsWith('#') ? slideBackground : undefined,
                    background: slideBackground?.includes('gradient') ? slideBackground : undefined,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center'
                  }}
                >
                  <div className={`${slideClass} max-w-full`}>
                    <h1 className="text-3xl font-bold mb-4">{slideTitle || 'Untitled Slide'}</h1>
                    <div className="prose prose-lg dark:prose-invert max-w-none">
                      {renderPreviewContent()}
                    </div>
                  </div>
                </div>
                
                {showPresenterNotes && slideNotes && (
                  <div className="mt-4 p-3 bg-muted rounded-md">
                    <h5 className="text-xs font-medium mb-1">Speaker Notes</h5>
                    <p className="text-xs text-muted-foreground">{slideNotes}</p>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>
        
        <div className="p-4 border-t flex justify-end gap-2">
          <Button
            variant="outline"
            onClick={onCancel}
          >
            Cancel
          </Button>
          <Button
            onClick={onSave}
          >
            Save Changes
          </Button>
        </div>
      </Card>
    </div>
  );
};