export interface Slide {
  id: string;
  title: string;
  content: string;
  notes?: string;
  layout?: string;
  transition?: string;
  background?: string;
  class?: string;
  order: number;
  created_at: string;
  updated_at: string;
}

export interface SlideTransition {
  name: string;
  label: string;
  description: string;
}

export interface SlideLayout {
  name: string;
  label: string;
  description: string;
  icon?: React.ReactNode;
}

export interface Presentation {
  id: string;
  title: string;
  description?: string;
  author?: string;
  theme: string;
  slides: Slide[];
  frontmatter: SlidevFrontmatter;
  created_at: string;
  updated_at: string;
}

export interface SlidevFrontmatter {
  theme: string;
  background?: string;
  class?: string;
  highlighter?: string;
  lineNumbers?: boolean;
  info?: string;
  persist?: boolean;
  exportFilename?: string;
  title?: string;
}

export interface GenerateSlidesRequest {
  prompt: string;
  theme?: string;
  slide_count?: number;
  audience?: string;
  duration?: number;
  context?: string;
}

export interface GenerateSlidesResponse {
  presentation: Presentation;
  metadata: GenerationMetadata;
}

export interface GenerationMetadata {
  model: string;
  generation_time: number;
  token_usage: {
    input_tokens: number;
    output_tokens: number;
    total_tokens: number;
  };
  generated_at: string;
}

export interface PresentationSummary {
  id: string;
  title: string;
  description?: string;
  author?: string;
  slide_count: number;
  theme: string;
  created_at: string;
  updated_at: string;
}

export interface SlidevTabProps {
  notebookId?: string;
}

export type ViewMode = 'list' | 'grid' | 'compact';
export type GridSize = 'small' | 'medium' | 'large';