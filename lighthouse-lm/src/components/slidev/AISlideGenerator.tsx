import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { <PERSON><PERSON>, Sparkles, MessageCircle } from 'lucide-react';
import { GenerateSlidesRequest } from './types';

interface AISlideGeneratorProps {
  isVisible: boolean;
  isGenerating: boolean;
  onGenerate: (request: GenerateSlidesRequest) => void;
  onGenerateFromChat: () => void;
  onClose: () => void;
}

const themes = [
  'default',
  'seriph',
  'apple-basic',
  'bricks',
  'geist',
  'penguin',
  'shibainu',
  'unicorn'
];

const audiences = [
  'General',
  'Technical',
  'Business',
  'Academic',
  'Students',
  'Executives'
];

export const AISlideGenerator: React.FC<AISlideGeneratorProps> = ({
  isVisible,
  isGenerating,
  onGenerate,
  onGenerateFromChat,
  onClose,
}) => {
  const [prompt, setPrompt] = useState('');
  const [theme, setTheme] = useState('default');
  const [slideCount, setSlideCount] = useState([8]);
  const [audience, setAudience] = useState('General');
  const [duration, setDuration] = useState([15]);
  const [context, setContext] = useState('');

  const handleGenerate = () => {
    if (!prompt.trim()) return;

    const request: GenerateSlidesRequest = {
      prompt: prompt.trim(),
      theme,
      slide_count: slideCount[0],
      audience,
      duration: duration[0],
      context: context.trim() || undefined,
    };

    onGenerate(request);
  };

  const handleReset = () => {
    setPrompt('');
    setTheme('default');
    setSlideCount([8]);
    setAudience('General');
    setDuration([15]);
    setContext('');
  };

  if (!isVisible) return null;

  return (
    <Card className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Bot className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-semibold">AI Slide Generator</h3>
        </div>
        <Button variant="ghost" size="sm" onClick={onClose}>
          ×
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left Column */}
        <div className="space-y-4">
          <div>
            <Label htmlFor="ai-prompt">Presentation Topic *</Label>
            <Textarea
              id="ai-prompt"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="Describe your presentation topic, key points, or objectives..."
              className="min-h-[100px] mt-1"
            />
          </div>

          <div>
            <Label htmlFor="ai-context">Additional Context</Label>
            <Textarea
              id="ai-context"
              value={context}
              onChange={(e) => setContext(e.target.value)}
              placeholder="Any specific requirements, examples, or background information..."
              className="min-h-[80px] mt-1"
            />
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-4">
          <div>
            <Label>Theme</Label>
            <Select value={theme} onValueChange={setTheme}>
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {themes.map((themeOption) => (
                  <SelectItem key={themeOption} value={themeOption}>
                    {themeOption.charAt(0).toUpperCase() + themeOption.slice(1).replace('-', ' ')}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label>Target Audience</Label>
            <Select value={audience} onValueChange={setAudience}>
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {audiences.map((audienceOption) => (
                  <SelectItem key={audienceOption} value={audienceOption}>
                    {audienceOption}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label>Number of Slides: {slideCount[0]}</Label>
            <Slider
              value={slideCount}
              onValueChange={setSlideCount}
              max={20}
              min={3}
              step={1}
              className="mt-2"
            />
            <div className="flex justify-between text-xs text-muted-foreground mt-1">
              <span>3</span>
              <span>20</span>
            </div>
          </div>

          <div>
            <Label>Presentation Duration: {duration[0]} minutes</Label>
            <Slider
              value={duration}
              onValueChange={setDuration}
              max={60}
              min={5}
              step={5}
              className="mt-2"
            />
            <div className="flex justify-between text-xs text-muted-foreground mt-1">
              <span>5 min</span>
              <span>60 min</span>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between pt-4 border-t">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={onGenerateFromChat}
            className="flex items-center gap-2"
          >
            <MessageCircle className="h-4 w-4" />
            Generate from Chat
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleReset}>
            Reset
          </Button>
          <Button
            onClick={handleGenerate}
            disabled={!prompt.trim() || isGenerating}
            className="flex items-center gap-2"
          >
            {isGenerating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
                Generating...
              </>
            ) : (
              <>
                <Sparkles className="h-4 w-4" />
                Generate Slides
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Tips */}
      <div className="bg-muted/50 rounded-lg p-4">
        <h4 className="text-sm font-medium mb-2">💡 Tips for better results:</h4>
        <ul className="text-xs text-muted-foreground space-y-1">
          <li>• Be specific about your topic and key points</li>
          <li>• Mention your target audience and their level of expertise</li>
          <li>• Include any specific examples or case studies you want covered</li>
          <li>• Specify the presentation style (formal, casual, interactive, etc.)</li>
        </ul>
      </div>
    </Card>
  );
};