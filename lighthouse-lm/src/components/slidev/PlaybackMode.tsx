import React, { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Eye, EyeOff } from 'lucide-react';
import { Presentation } from './types';

interface PlaybackModeProps {
  isActive: boolean;
  presentation: Presentation;
  currentSlide: number;
  showPresenterNotes: boolean;
  onPreviousSlide: () => void;
  onNextSlide: () => void;
  onSlideChange: (slideIndex: number) => void;
  onToggleNotes: () => void;
  onExit: () => void;
}

export const PlaybackMode: React.FC<PlaybackModeProps> = ({
  isActive,
  presentation,
  currentSlide,
  showPresenterNotes,
  onPreviousSlide,
  onNextSlide,
  onSlideChange,
  onToggleNotes,
  onExit,
}) => {
  useEffect(() => {
    if (!isActive) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowLeft':
        case 'ArrowUp':
          e.preventDefault();
          onPreviousSlide();
          break;
        case 'ArrowRight':
        case 'ArrowDown':
        case ' ':
          e.preventDefault();
          onNextSlide();
          break;
        case 'Escape':
          e.preventDefault();
          onExit();
          break;
        case 'n':
        case 'N':
          e.preventDefault();
          onToggleNotes();
          break;
        default:
          // Handle number keys for direct slide navigation
          if (e.key >= '1' && e.key <= '9') {
            const slideIndex = parseInt(e.key) - 1;
            if (slideIndex < presentation.slides.length) {
              onSlideChange(slideIndex);
            }
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isActive, onPreviousSlide, onNextSlide, onExit, onToggleNotes, onSlideChange, presentation.slides.length]);

  if (!isActive) return null;

  const currentSlideData = presentation.slides[currentSlide];
  if (!currentSlideData) return null;

  const renderSlideContent = () => {
    return currentSlideData.content.split('\n').map((line, i) => {
      if (line.startsWith('# ')) {
        return <h1 key={i} className="text-4xl font-bold mb-6 text-white">{line.substring(2)}</h1>;
      } else if (line.startsWith('## ')) {
        return <h2 key={i} className="text-3xl font-semibold mb-4 text-white">{line.substring(3)}</h2>;
      } else if (line.startsWith('### ')) {
        return <h3 key={i} className="text-2xl font-medium mb-3 text-white">{line.substring(4)}</h3>;
      } else if (line.startsWith('- ')) {
        return <li key={i} className="text-xl mb-2 ml-6 text-white">{line.substring(2)}</li>;
      } else if (line.startsWith('```')) {
        return (
          <pre key={i} className="bg-black/30 p-4 rounded-lg my-4 text-white font-mono">
            <code>{line.substring(3)}</code>
          </pre>
        );
      } else if (line.trim()) {
        return <p key={i} className="text-xl mb-4 text-white leading-relaxed">{line}</p>;
      }
      return <br key={i} />;
    });
  };

  return (
    <div className="fixed inset-0 bg-black z-50 flex flex-col">
      {/* Main slide content */}
      <div className="flex-1 flex items-center justify-center p-8">
        <div 
          className={`max-w-6xl w-full h-full flex flex-col justify-center ${
            currentSlideData.layout === 'center' ? 'text-center items-center' :
            currentSlideData.layout === 'cover' ? 'text-center items-center' :
            'items-start'
          } ${currentSlideData.class || ''}`}
          style={{
            backgroundImage: currentSlideData.background?.startsWith('http') 
              ? `url(${currentSlideData.background})` 
              : undefined,
            backgroundColor: currentSlideData.background?.startsWith('#') 
              ? currentSlideData.background 
              : undefined,
            background: currentSlideData.background?.includes('gradient') 
              ? currentSlideData.background 
              : undefined,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            borderRadius: '12px',
            padding: '3rem'
          }}
        >
          {currentSlideData.title && (
            <h1 className="text-5xl font-bold mb-8 text-white">
              {currentSlideData.title}
            </h1>
          )}
          
          <div className="prose prose-xl prose-invert max-w-none">
            {renderSlideContent()}
          </div>
        </div>
      </div>
      
      {/* Navigation and controls */}
      <div className="absolute bottom-0 left-0 right-0 p-6">
        <div className="max-w-6xl mx-auto flex items-center justify-between">
          {/* Navigation buttons */}
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={onPreviousSlide}
              disabled={currentSlide === 0}
              className="text-white hover:bg-white/10 disabled:opacity-30"
            >
              <ChevronLeft className="h-4 w-4" />
              <span className="ml-1 text-xs">Previous</span>
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={onNextSlide}
              disabled={currentSlide === presentation.slides.length - 1}
              className="text-white hover:bg-white/10 disabled:opacity-30"
            >
              <span className="mr-1 text-xs">Next</span>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
          
          {/* Slide indicators */}
          <div className="flex items-center gap-1">
            {presentation.slides.map((_, index) => (
              <button
                key={index}
                onClick={() => onSlideChange(index)}
                className={`w-2 h-2 rounded-full transition-all ${
                  index === currentSlide 
                    ? 'bg-white scale-125' 
                    : 'bg-white/30 hover:bg-white/50'
                }`}
                title={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
          
          {/* Control buttons */}
          <div className="flex items-center gap-2">
            <span className="text-xs text-white/70 mr-2">
              {currentSlide + 1} / {presentation.slides.length}
            </span>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleNotes}
              className={`text-white hover:bg-white/10 ${
                showPresenterNotes ? 'bg-white/10' : ''
              }`}
            >
              {showPresenterNotes ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              <span className="ml-1 text-xs">Notes</span>
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={onExit}
              className="text-white hover:bg-white/10"
            >
              <span className="text-xs">Exit</span>
            </Button>
          </div>
        </div>
        
        {/* Keyboard hints */}
        <div className="max-w-6xl mx-auto mt-2">
          <p className="text-xs text-white/50 text-center">
            Use arrow keys or space to navigate • Press N to toggle notes • Press Esc to exit
          </p>
        </div>
      </div>
      
      {/* Presenter Notes */}
      {showPresenterNotes && currentSlideData.notes && (
        <div className="max-w-6xl mx-auto mt-4 p-3 bg-white/5 rounded-lg absolute bottom-20 left-6 right-6">
          <h5 className="text-xs font-medium text-white/50 mb-1">Speaker Notes</h5>
          <p className="text-sm text-white/80">
            {currentSlideData.notes}
          </p>
        </div>
      )}
    </div>
  );
};