import React from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Presentation, Edit, Trash2, Play } from 'lucide-react';
import { PresentationSummary } from './types';

interface PresentationListProps {
  presentations: PresentationSummary[];
  onPresentationClick: (presentation: PresentationSummary) => void;
  onDeletePresentation: (presentationId: string) => void;
}

export const PresentationList: React.FC<PresentationListProps> = ({
  presentations,
  onPresentationClick,
  onDeletePresentation,
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-3">
      {presentations.map((presentation) => (
        <Card key={presentation.id} className="p-4 hover:shadow-md transition-shadow">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-2">
                <Presentation className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                <h3 className="font-medium truncate">{presentation.title}</h3>
                <Badge variant="secondary" className="text-xs">
                  {presentation.slide_count} slides
                </Badge>
              </div>
              
              {presentation.description && (
                <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                  {presentation.description}
                </p>
              )}
              
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                <span>Theme: {presentation.theme}</span>
                {presentation.author && <span>By: {presentation.author}</span>}
                <span>Updated: {formatDate(presentation.updated_at)}</span>
              </div>
            </div>
            
            <div className="flex items-center gap-1 ml-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onPresentationClick(presentation)}
                className="h-8 w-8 p-0"
                title="Edit presentation"
              >
                <Edit className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDeletePresentation(presentation.id)}
                className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                title="Delete presentation"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};