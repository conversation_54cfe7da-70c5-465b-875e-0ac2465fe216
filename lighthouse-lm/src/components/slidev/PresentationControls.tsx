import React from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { 
  Play, 
  Plus, 
  Save, 
  Download, 
  List, 
  Grid, 
  LayoutGrid, 
  Maximize2, 
  Minimize2 
} from 'lucide-react';
import { ViewMode, GridSize } from './types';

interface PresentationControlsProps {
  // Presentation actions
  onPresent: () => void;
  onAddSlide: () => void;
  onSave: () => void;
  onExport: () => void;
  
  // View controls
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
  gridSize: GridSize;
  onGridSizeChange: (size: GridSize) => void;
  
  // State
  hasSlides: boolean;
  isSaving?: boolean;
  isExporting?: boolean;
}

const viewModeIcons = {
  list: List,
  grid: Grid,
  compact: LayoutGrid,
};

const gridSizeIcons = {
  small: Minimize2,
  medium: LayoutGrid,
  large: Maximize2,
};

export const PresentationControls: React.FC<PresentationControlsProps> = ({
  onPresent,
  onAddSlide,
  onSave,
  onExport,
  viewMode,
  onViewModeChange,
  gridSize,
  onGridSizeChange,
  hasSlides,
  isSaving = false,
  isExporting = false,
}) => {
  const ViewModeIcon = viewModeIcons[viewMode];
  const GridSizeIcon = gridSizeIcons[gridSize];

  return (
    <div className="flex items-center justify-between gap-4 p-4 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      {/* Left side - Main actions */}
      <div className="flex items-center gap-2">
        <Button
          onClick={onPresent}
          disabled={!hasSlides}
          className="flex items-center gap-2"
        >
          <Play className="h-4 w-4" />
          Present
        </Button>
        
        <Separator orientation="vertical" className="h-6" />
        
        <Button
          variant="outline"
          onClick={onAddSlide}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Add Slide
        </Button>
        
        <Button
          variant="outline"
          onClick={onSave}
          disabled={isSaving || !hasSlides}
          className="flex items-center gap-2"
        >
          {isSaving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
              Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4" />
              Save
            </>
          )}
        </Button>
        
        <Button
          variant="outline"
          onClick={onExport}
          disabled={isExporting || !hasSlides}
          className="flex items-center gap-2"
        >
          {isExporting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
              Exporting...
            </>
          ) : (
            <>
              <Download className="h-4 w-4" />
              Export
            </>
          )}
        </Button>
      </div>
      
      {/* Right side - View controls */}
      {hasSlides && (
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <span className="text-sm text-muted-foreground mr-2">View:</span>
            
            {/* View Mode Selector */}
            <Select value={viewMode} onValueChange={(value: ViewMode) => onViewModeChange(value)}>
              <SelectTrigger className="w-auto h-8 px-2">
                <div className="flex items-center gap-1">
                  <ViewModeIcon className="h-3 w-3" />
                  <span className="capitalize text-xs">{viewMode}</span>
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="list">
                  <div className="flex items-center gap-2">
                    <List className="h-4 w-4" />
                    <span>List</span>
                  </div>
                </SelectItem>
                <SelectItem value="grid">
                  <div className="flex items-center gap-2">
                    <Grid className="h-4 w-4" />
                    <span>Grid</span>
                  </div>
                </SelectItem>
                <SelectItem value="compact">
                  <div className="flex items-center gap-2">
                    <LayoutGrid className="h-4 w-4" />
                    <span>Compact</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            
            {/* Grid Size Selector - only show for grid and compact views */}
            {(viewMode === 'grid' || viewMode === 'compact') && (
              <>
                <Separator orientation="vertical" className="h-4 mx-1" />
                <Select value={gridSize} onValueChange={(value: GridSize) => onGridSizeChange(value)}>
                  <SelectTrigger className="w-auto h-8 px-2">
                    <div className="flex items-center gap-1">
                      <GridSizeIcon className="h-3 w-3" />
                      <span className="capitalize text-xs">{gridSize}</span>
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="small">
                      <div className="flex items-center gap-2">
                        <Minimize2 className="h-4 w-4" />
                        <span>Small</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="medium">
                      <div className="flex items-center gap-2">
                        <LayoutGrid className="h-4 w-4" />
                        <span>Medium</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="large">
                      <div className="flex items-center gap-2">
                        <Maximize2 className="h-4 w-4" />
                        <span>Large</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};