import React from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, Toolt<PERSON>Provider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  Edit, 
  Copy, 
  Trash2, 
  GripVertical, 
  FileText, 
  Image, 
  Palette, 
  Clock 
} from 'lucide-react';
import { Slide, ViewMode, GridSize } from './types';

interface SlideGridProps {
  slides: Slide[];
  viewMode: ViewMode;
  gridSize: GridSize;
  onSlideEdit: (slide: Slide) => void;
  onSlideDuplicate: (slideId: string) => void;
  onSlideDelete: (slideId: string) => void;
  onSlidesReorder: (startIndex: number, endIndex: number) => void;
}

const getGridSizeClass = (gridSize: GridSize): string => {
  switch (gridSize) {
    case 'small':
      return 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6';
    case 'medium':
      return 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4';
    case 'large':
      return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3';
    default:
      return 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4';
  }
};

const getSlideHeight = (gridSize: GridSize): string => {
  switch (gridSize) {
    case 'small':
      return 'h-24';
    case 'medium':
      return 'h-32';
    case 'large':
      return 'h-40';
    default:
      return 'h-32';
  }
};

const renderSlideContent = (slide: Slide): string => {
  // Simple markdown to text conversion for preview
  let content = slide.content || '';
  
  // Remove markdown syntax for preview
  content = content
    .replace(/#{1,6}\s+/g, '') // Remove headers
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
    .replace(/\*(.*?)\*/g, '$1') // Remove italic
    .replace(/`(.*?)`/g, '$1') // Remove inline code
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Remove links
    .replace(/^[-*+]\s+/gm, '• ') // Convert list items
    .replace(/\n{2,}/g, '\n') // Reduce multiple newlines
    .trim();
    
  return content.length > 100 ? content.substring(0, 100) + '...' : content;
};

const SlideCard: React.FC<{
  slide: Slide;
  index: number;
  viewMode: ViewMode;
  gridSize: GridSize;
  onEdit: () => void;
  onDuplicate: () => void;
  onDelete: () => void;
  onDragStart: (e: React.DragEvent) => void;
  onDragOver: (e: React.DragEvent) => void;
  onDrop: (e: React.DragEvent) => void;
}> = ({ 
  slide, 
  index, 
  viewMode, 
  gridSize, 
  onEdit, 
  onDuplicate, 
  onDelete, 
  onDragStart, 
  onDragOver, 
  onDrop 
}) => {
  const slideHeight = getSlideHeight(gridSize);
  const content = renderSlideContent(slide);
  
  if (viewMode === 'list') {
    return (
      <Card 
        className="p-4 cursor-move hover:shadow-md transition-shadow"
        draggable
        onDragStart={onDragStart}
        onDragOver={onDragOver}
        onDrop={onDrop}
      >
        <div className="flex items-start gap-4">
          <div className="flex items-center gap-2 text-muted-foreground">
            <GripVertical className="h-4 w-4" />
            <span className="text-sm font-medium">{index + 1}</span>
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-4">
              <div className="flex-1">
                <h4 className="font-medium text-sm mb-1 truncate">
                  {slide.title || `Slide ${index + 1}`}
                </h4>
                <p className="text-xs text-muted-foreground line-clamp-2">
                  {content || 'No content'}
                </p>
                
                <div className="flex items-center gap-2 mt-2">
                  {slide.layout && (
                    <Badge variant="outline" className="text-xs">
                      <FileText className="h-3 w-3 mr-1" />
                      {slide.layout}
                    </Badge>
                  )}
                  {slide.transition && (
                    <Badge variant="outline" className="text-xs">
                      <Clock className="h-3 w-3 mr-1" />
                      {typeof slide.transition === 'string' ? slide.transition : (slide.transition && typeof slide.transition === 'object' && 'type' in slide.transition ? slide.transition.type : 'fade')}
                    </Badge>
                  )}
                  {slide.background && (
                    <Badge variant="outline" className="text-xs">
                      <Palette className="h-3 w-3 mr-1" />
                      BG
                    </Badge>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-1">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="sm" onClick={onEdit}>
                        <Edit className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Edit slide</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="sm" onClick={onDuplicate}>
                        <Copy className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Duplicate slide</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="sm" onClick={onDelete}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Delete slide</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          </div>
        </div>
      </Card>
    );
  }
  
  // Grid and Compact views
  return (
    <Card 
      className={`p-3 cursor-move hover:shadow-md transition-shadow ${slideHeight}`}
      draggable
      onDragStart={onDragStart}
      onDragOver={onDragOver}
      onDrop={onDrop}
    >
      <div className="h-full flex flex-col">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-1 text-muted-foreground">
            <GripVertical className="h-3 w-3" />
            <span className="text-xs font-medium">{index + 1}</span>
          </div>
          
          <div className="flex items-center gap-1">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0" onClick={onEdit}>
                    <Edit className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Edit</TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0" onClick={onDuplicate}>
                    <Copy className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Duplicate</TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0" onClick={onDelete}>
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Delete</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
        
        <div className="flex-1 min-h-0">
          <h4 className="font-medium text-xs mb-1 truncate">
            {slide.title || `Slide ${index + 1}`}
          </h4>
          
          {viewMode !== 'compact' && (
            <p className="text-xs text-muted-foreground line-clamp-3">
              {content || 'No content'}
            </p>
          )}
        </div>
        
        {viewMode !== 'compact' && (
          <div className="flex items-center gap-1 mt-2">
            {slide.layout && (
              <Badge variant="outline" className="text-xs px-1 py-0">
                <FileText className="h-2 w-2" />
              </Badge>
            )}
            {slide.transition && (
              <Badge variant="outline" className="text-xs px-1 py-0">
                <Clock className="h-2 w-2" />
              </Badge>
            )}
            {slide.background && (
              <Badge variant="outline" className="text-xs px-1 py-0">
                <Palette className="h-2 w-2" />
              </Badge>
            )}
          </div>
        )}
      </div>
    </Card>
  );
};

export const SlideGrid: React.FC<SlideGridProps> = ({
  slides,
  viewMode,
  gridSize,
  onSlideEdit,
  onSlideDuplicate,
  onSlideDelete,
  onSlidesReorder,
}) => {
  const [draggedIndex, setDraggedIndex] = React.useState<number | null>(null);
  
  const handleDragStart = (index: number) => (e: React.DragEvent) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = 'move';
  };
  
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };
  
  const handleDrop = (dropIndex: number) => (e: React.DragEvent) => {
    e.preventDefault();
    
    if (draggedIndex !== null && draggedIndex !== dropIndex) {
      onSlidesReorder(draggedIndex, dropIndex);
    }
    
    setDraggedIndex(null);
  };
  
  if (slides.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <FileText className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium mb-2">No slides yet</h3>
        <p className="text-muted-foreground mb-4">Create your first slide to get started</p>
      </div>
    );
  }
  
  const gridClass = viewMode === 'list' 
    ? 'space-y-3' 
    : `grid gap-4 ${getGridSizeClass(gridSize)}`;
  
  return (
    <div className={gridClass}>
      {slides.map((slide, index) => (
        <SlideCard
          key={slide.id}
          slide={slide}
          index={index}
          viewMode={viewMode}
          gridSize={gridSize}
          onEdit={() => onSlideEdit(slide)}
          onDuplicate={() => onSlideDuplicate(slide.id)}
          onDelete={() => onSlideDelete(slide.id)}
          onDragStart={handleDragStart(index)}
          onDragOver={handleDragOver}
          onDrop={handleDrop(index)}
        />
      ))}
    </div>
  );
};