"use client"

import * as React from "react"
import { useNavigate } from "react-router-dom"
import {
  Command,
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
  CommandShortcut,
} from "@/components/ui/command"
import { useNotebooks } from "@/hooks/useNotebooks"
import { FileText, Search, Plus, Settings, HelpCircle, Zap } from "lucide-react"

interface CommandSearchProps {
  open: boolean
  setOpen: (open: boolean) => void
}

export function CommandSearch({ open, setOpen }: CommandSearchProps) {
  const navigate = useNavigate()
  const { notebooks } = useNotebooks()
  const [searchQuery, setSearchQuery] = React.useState("")

  React.useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault()
        setOpen(!open)
      }
    }

    document.addEventListener("keydown", down)
    return () => document.removeEventListener("keydown", down)
  }, [open, setOpen])

  const runCommand = React.useCallback((command: () => unknown) => {
    setOpen(false)
    command()
  }, [setOpen])

  const filteredNotebooks = React.useMemo(() => {
    if (!notebooks || !searchQuery) return notebooks?.slice(0, 5) || []
    
    return notebooks.filter((notebook) =>
      notebook.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      notebook.content?.toLowerCase().includes(searchQuery.toLowerCase())
    ).slice(0, 5)
  }, [notebooks, searchQuery])

  return (
    <CommandDialog open={open} onOpenChange={setOpen}>
      <Command>
        <CommandInput 
          placeholder="Type a command or search..." 
          value={searchQuery}
          onValueChange={setSearchQuery}
        />
        <CommandList>
          <CommandEmpty>No results found.</CommandEmpty>
          
          <CommandGroup heading="Quick Actions">
            <CommandItem
              onSelect={() => runCommand(() => navigate('/lighthouse-lm'))}
            >
              <Plus className="mr-2 h-4 w-4" />
              <span>New Notebook</span>
              <CommandShortcut>⌘N</CommandShortcut>
            </CommandItem>
            <CommandItem
              onSelect={() => runCommand(() => console.log('Open settings'))}
            >
              <Settings className="mr-2 h-4 w-4" />
              <span>Settings</span>
              <CommandShortcut>⌘,</CommandShortcut>
            </CommandItem>
            <CommandItem
              onSelect={() => runCommand(() => console.log('Open help'))}
            >
              <HelpCircle className="mr-2 h-4 w-4" />
              <span>Help & Support</span>
              <CommandShortcut>⌘?</CommandShortcut>
            </CommandItem>
          </CommandGroup>
          
          {filteredNotebooks && filteredNotebooks.length > 0 && (
            <>
              <CommandSeparator />
              <CommandGroup heading="Notebooks">
                {filteredNotebooks.map((notebook) => (
                  <CommandItem
                    key={notebook.id}
                    onSelect={() => runCommand(() => navigate(`/lighthouse-lm/notebook/${notebook.id}`))}
                  >
                    <FileText className="mr-2 h-4 w-4" />
                    <span>{notebook.title}</span>
                    <CommandShortcut>{new Date(notebook.updatedAt).toLocaleDateString()}</CommandShortcut>
                  </CommandItem>
                ))}
              </CommandGroup>
            </>
          )}
          
          <CommandSeparator />
          <CommandGroup heading="Search">
            <CommandItem
              onSelect={() => runCommand(() => {
                navigate('/lighthouse-lm')
                // Focus search bar after navigation
                setTimeout(() => {
                  const searchInput = document.querySelector('[data-search-input]') as HTMLInputElement
                  if (searchInput) {
                    searchInput.focus()
                    searchInput.value = searchQuery
                  }
                }, 100)
              })}
            >
              <Search className="mr-2 h-4 w-4" />
              <span>Search "{searchQuery}"</span>
            </CommandItem>
          </CommandGroup>
        </CommandList>
      </Command>
    </CommandDialog>
  )
}

export default CommandSearch