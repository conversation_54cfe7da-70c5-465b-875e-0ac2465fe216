import React, { useState, useCallback, useMemo } from 'react';
import { Search, Filter, Calendar, Tag, User, Clock, Star, X } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { useDebounce } from '@/hooks/useDebounce';
import { SearchFilters, SearchResult, SavedSearch } from './types';
import SearchResults from './SearchResults';
import SearchHistory from './SearchHistory';

interface AdvancedSearchDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSearch?: (query: string, filters: SearchFilters) => void;
  initialQuery?: string;
  savedSearches?: SavedSearch[];
  onSaveSearch?: (name: string, query: string, filters: SearchFilters) => void;
}

const defaultFilters: SearchFilters = {
  contentTypes: [],
  dateRange: {},
  tags: [],
  authors: [],
  sourceTypes: [],
  sortBy: 'relevance',
  sortOrder: 'desc',
};

export const AdvancedSearchDialog: React.FC<AdvancedSearchDialogProps> = ({
  open,
  onOpenChange,
  onSearch,
  initialQuery = '',
  savedSearches = [],
  onSaveSearch,
}) => {
  const [query, setQuery] = useState(initialQuery);
  const [filters, setFilters] = useState<SearchFilters>(defaultFilters);
  const [activeTab, setActiveTab] = useState('search');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  
  const debouncedQuery = useDebounce(query, 300);

  const handleSearch = useCallback(async () => {
    if (!debouncedQuery.trim() && filters === defaultFilters) return;
    
    setIsSearching(true);
    try {
      if (onSearch) {
        onSearch(debouncedQuery, filters);
      }
      // Simulate search results for demo
      const mockResults: SearchResult[] = [
        {
          id: '1',
          type: 'notebook',
          title: 'Machine Learning Fundamentals',
          excerpt: 'Introduction to supervised and unsupervised learning algorithms...',
          relevance: 0.95,
          date: new Date(),
          tags: ['ml', 'ai', 'tutorial'],
        },
        {
          id: '2',
          type: 'source',
          title: 'Research Paper: Neural Networks',
          excerpt: 'A comprehensive study on deep neural network architectures...',
          relevance: 0.87,
          date: new Date(),
          tags: ['research', 'neural-networks'],
        },
      ];
      setSearchResults(mockResults);
    } finally {
      setIsSearching(false);
    }
  }, [debouncedQuery, filters, onSearch]);

  const handleContentTypeToggle = (type: 'notebook' | 'source' | 'chat' | 'slide') => {
    setFilters(prev => ({
      ...prev,
      contentTypes: prev.contentTypes.includes(type)
        ? prev.contentTypes.filter(t => t !== type)
        : [...prev.contentTypes, type],
    }));
  };

  const handleTagToggle = (tag: string) => {
    setSelectedTags(prev =>
      prev.includes(tag) ? prev.filter(t => t !== tag) : [...prev, tag]
    );
    setFilters(prev => ({
      ...prev,
      tags: prev.tags.includes(tag)
        ? prev.tags.filter(t => t !== tag)
        : [...prev.tags, tag],
    }));
  };

  const handleResetFilters = () => {
    setFilters(defaultFilters);
    setSelectedTags([]);
  };

  const activeFiltersCount = useMemo(() => {
    let count = 0;
    if (filters.contentTypes.length > 0) count += filters.contentTypes.length;
    if (filters.tags.length > 0) count += filters.tags.length;
    if (filters.dateRange.from || filters.dateRange.to) count++;
    if (filters.authors.length > 0) count += filters.authors.length;
    return count;
  }, [filters]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] p-0">
        <DialogHeader className="px-6 pt-6">
          <DialogTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Advanced Search
          </DialogTitle>
          <DialogDescription>
            Search across all your notebooks, sources, chats, and slides
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
          <TabsList className="grid w-full grid-cols-4 px-6">
            <TabsTrigger value="search">Search</TabsTrigger>
            <TabsTrigger value="filters">
              Filters {activeFiltersCount > 0 && (
                <Badge variant="secondary" className="ml-2 h-5 px-1">
                  {activeFiltersCount}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="saved">Saved</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>

          <ScrollArea className="h-[500px]">
            <TabsContent value="search" className="px-6 pb-6 space-y-4">
              <div className="space-y-2">
                <Label htmlFor="search-input">Search Query</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search-input"
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                    placeholder="Type to search..."
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Content Types</Label>
                <div className="flex gap-2 flex-wrap">
                  {(['notebook', 'source', 'chat', 'slide'] as const).map(type => (
                    <Button
                      key={type}
                      variant={filters.contentTypes.includes(type) ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => handleContentTypeToggle(type)}
                    >
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </Button>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label>Quick Filters</Label>
                <div className="flex gap-2 flex-wrap">
                  <Button variant="outline" size="sm" className="gap-1">
                    <Calendar className="h-3 w-3" />
                    Last 7 days
                  </Button>
                  <Button variant="outline" size="sm" className="gap-1">
                    <Star className="h-3 w-3" />
                    Starred
                  </Button>
                  <Button variant="outline" size="sm" className="gap-1">
                    <User className="h-3 w-3" />
                    My content
                  </Button>
                  <Button variant="outline" size="sm" className="gap-1">
                    <Clock className="h-3 w-3" />
                    Recently viewed
                  </Button>
                </div>
              </div>

              <Separator />

              <SearchResults 
                results={searchResults}
                isLoading={isSearching}
                query={debouncedQuery}
              />
            </TabsContent>

            <TabsContent value="filters" className="px-6 pb-6 space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-sm font-medium">Active Filters</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleResetFilters}
                  disabled={activeFiltersCount === 0}
                >
                  Reset all
                </Button>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Tags</Label>
                  <div className="flex gap-2 flex-wrap">
                    {['research', 'tutorial', 'project', 'meeting', 'idea', 'todo'].map(tag => (
                      <Badge
                        key={tag}
                        variant={selectedTags.includes(tag) ? 'default' : 'outline'}
                        className="cursor-pointer"
                        onClick={() => handleTagToggle(tag)}
                      >
                        <Tag className="h-3 w-3 mr-1" />
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Sort By</Label>
                  <Select
                    value={filters.sortBy}
                    onValueChange={(value: any) => 
                      setFilters(prev => ({ ...prev, sortBy: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="relevance">Relevance</SelectItem>
                      <SelectItem value="date">Date</SelectItem>
                      <SelectItem value="title">Title</SelectItem>
                      <SelectItem value="modified">Last Modified</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Sort Order</Label>
                  <Select
                    value={filters.sortOrder}
                    onValueChange={(value: any) => 
                      setFilters(prev => ({ ...prev, sortOrder: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="asc">Ascending</SelectItem>
                      <SelectItem value="desc">Descending</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="saved" className="px-6 pb-6">
              <div className="space-y-2">
                {savedSearches.length > 0 ? (
                  savedSearches.map(search => (
                    <div
                      key={search.id}
                      className="p-3 border rounded-lg hover:bg-accent cursor-pointer"
                      onClick={() => {
                        setQuery(search.query);
                        setFilters(search.filters);
                        setActiveTab('search');
                      }}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium">{search.name}</h4>
                          {search.description && (
                            <p className="text-sm text-muted-foreground">{search.description}</p>
                          )}
                        </div>
                        <Star className="h-4 w-4 text-muted-foreground" />
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Star className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>No saved searches yet</p>
                    <p className="text-sm">Save your frequently used searches for quick access</p>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="history" className="px-6 pb-6">
              <SearchHistory
                onSelectSearch={(historyItem) => {
                  setQuery(historyItem.query);
                  setFilters(historyItem.filters);
                  setActiveTab('search');
                }}
              />
            </TabsContent>
          </ScrollArea>

          <div className="px-6 py-4 border-t flex justify-between items-center">
            <div className="text-sm text-muted-foreground">
              {searchResults.length > 0 && `${searchResults.length} results found`}
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button onClick={handleSearch} disabled={isSearching}>
                {isSearching ? 'Searching...' : 'Search'}
              </Button>
            </div>
          </div>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default AdvancedSearchDialog;