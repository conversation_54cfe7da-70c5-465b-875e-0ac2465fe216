import React from 'react';
import { Clock, Search, X } from 'lucide-react';
import { SearchHistory as SearchHistoryType } from './types';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';

interface SearchHistoryProps {
  history?: SearchHistoryType[];
  onSelectSearch: (item: SearchHistoryType) => void;
  onClearHistory?: () => void;
  onRemoveItem?: (id: string) => void;
}

const mockHistory: SearchHistoryType[] = [
  {
    id: '1',
    query: 'machine learning algorithms',
    filters: {
      contentTypes: ['notebook', 'source'],
      dateRange: {},
      tags: ['ml', 'ai'],
      authors: [],
      sourceTypes: [],
      sortBy: 'relevance',
      sortOrder: 'desc',
    },
    timestamp: new Date(Date.now() - 1000 * 60 * 30),
    resultCount: 42,
  },
  {
    id: '2',
    query: 'project roadmap',
    filters: {
      contentTypes: ['notebook'],
      dateRange: { from: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7) },
      tags: ['project'],
      authors: [],
      sourceTypes: [],
      sortBy: 'date',
      sortOrder: 'desc',
    },
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),
    resultCount: 15,
  },
];

const SearchHistory: React.FC<SearchHistoryProps> = ({
  history = mockHistory,
  onSelectSearch,
  onClearHistory,
  onRemoveItem,
}) => {
  const formatTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes} minutes ago`;
    if (hours < 24) return `${hours} hours ago`;
    return date.toLocaleDateString();
  };

  if (history.length === 0) {
    return (
      <div className="text-center py-12">
        <Clock className="h-12 w-12 mx-auto mb-2 opacity-50 text-muted-foreground" />
        <p className="text-muted-foreground">No search history</p>
        <p className="text-sm text-muted-foreground mt-2">
          Your recent searches will appear here
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-sm font-medium">Recent Searches</h3>
        {onClearHistory && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearHistory}
            className="text-muted-foreground hover:text-foreground"
          >
            Clear all
          </Button>
        )}
      </div>

      <ScrollArea className="h-[400px]">
        <div className="space-y-2">
          {history.map((item) => (
            <div
              key={item.id}
              className="group p-3 border rounded-lg hover:bg-accent cursor-pointer transition-colors"
              onClick={() => onSelectSearch(item)}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3 flex-1">
                  <Search className="h-4 w-4 text-muted-foreground mt-1" />
                  <div className="flex-1 space-y-1">
                    <p className="font-medium group-hover:text-primary transition-colors">
                      {item.query || 'All content'}
                    </p>
                    <div className="flex items-center gap-3 text-xs text-muted-foreground">
                      <span>{formatTime(item.timestamp)}</span>
                      <span>{item.resultCount} results</span>
                      {item.filters.contentTypes.length > 0 && (
                        <span>
                          {item.filters.contentTypes.join(', ')}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                {onRemoveItem && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={(e) => {
                      e.stopPropagation();
                      onRemoveItem(item.id);
                    }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
};

export default SearchHistory;