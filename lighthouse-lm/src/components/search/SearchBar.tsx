import React, { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  Search,
  X,
  Filter,
  Calendar,
  Tag,
  FileText,
  Clock,
  TrendingUp,
  Command,
  Brain,
  Sparkles,
  History,
  Zap,
  Target,
  BookOpen,
  Globe,
  Mic,
  Image,
  BarChart3,
  ArrowRight,
  Star
} from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
// Simple debounce implementation to avoid lodash dependency
const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

interface SearchFilter {
  dateRange?: 'today' | 'week' | 'month' | 'all';
  tags?: string[];
  sourceTypes?: string[];
  hasNotes?: boolean;
  hasDiagrams?: boolean;
  semanticSimilarity?: number;
  contentType?: 'all' | 'notebooks' | 'sources' | 'chats' | 'diagrams';
  language?: string;
  author?: string;
  minRelevance?: number;
}

interface SearchSuggestion {
  id: string;
  text: string;
  type: 'ai' | 'recent' | 'popular' | 'semantic' | 'completion';
  relevance: number;
  category?: string;
  icon?: React.ReactNode;
  description?: string;
  metadata?: {
    resultCount?: number;
    lastUsed?: Date;
    frequency?: number;
  };
}

interface SearchAnalytics {
  totalSearches: number;
  avgResultsPerSearch: number;
  topQueries: string[];
  searchTrends: { query: string; count: number; trend: 'up' | 'down' | 'stable' }[];
  semanticClusters: { topic: string; queries: string[]; relevance: number }[];
}

interface SearchBarProps {
  onSearch: (query: string, filters?: SearchFilter) => void;
  placeholder?: string;
  suggestions?: SearchSuggestion[];
  recentSearches?: string[];
  onClearRecent?: () => void;
  className?: string;
  enableAI?: boolean;
  enableSemanticSearch?: boolean;
  enableAnalytics?: boolean;
  onSuggestionGenerate?: (query: string) => Promise<SearchSuggestion[]>;
  onSemanticSearch?: (query: string) => Promise<SearchSuggestion[]>;
  analytics?: SearchAnalytics;
  maxSuggestions?: number;
  showSearchTips?: boolean;
}

const SearchBar: React.FC<SearchBarProps> = ({
  onSearch,
  placeholder = 'Search notebooks, sources, or content...',
  suggestions = [],
  recentSearches = [],
  onClearRecent,
  className = '',
  enableAI = true,
  enableSemanticSearch = true,
  enableAnalytics = false,
  onSuggestionGenerate,
  onSemanticSearch,
  analytics,
  maxSuggestions = 8,
  showSearchTips = true
}) => {
  const [query, setQuery] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<SearchFilter>({
    dateRange: 'all',
    tags: [],
    sourceTypes: [],
    hasNotes: false,
    hasDiagrams: false,
    contentType: 'all',
    semanticSimilarity: 0.7,
    minRelevance: 0.5
  });
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [aiSuggestions, setAiSuggestions] = useState<SearchSuggestion[]>([]);
  const [semanticSuggestions, setSemanticSuggestions] = useState<SearchSuggestion[]>([]);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  const [searchMode, setSearchMode] = useState<'standard' | 'semantic' | 'ai'>('standard');
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Debounced search
  const debouncedSearch = useCallback(
    debounce((query: string, filters: SearchFilter) => {
      onSearch(query, filters);
    }, 300),
    [onSearch]
  );

  // Generate AI suggestions
  const generateAISuggestions = useCallback(async (query: string): Promise<SearchSuggestion[]> => {
    if (!enableAI || !onSuggestionGenerate || query.length < 2) return [];
    
    try {
      setIsLoadingSuggestions(true);
      const aiSuggestions = await onSuggestionGenerate(query);
      return aiSuggestions.slice(0, maxSuggestions);
    } catch (error) {
      console.error('Failed to generate AI suggestions:', error);
      return [];
    } finally {
      setIsLoadingSuggestions(false);
    }
  }, [enableAI, onSuggestionGenerate, maxSuggestions]);

  // Generate semantic search suggestions
  const generateSemanticSuggestions = useCallback(async (query: string): Promise<SearchSuggestion[]> => {
    if (!enableSemanticSearch || !onSemanticSearch || query.length < 3) return [];
    
    try {
      const semanticSuggestions = await onSemanticSearch(query);
      return semanticSuggestions.slice(0, Math.floor(maxSuggestions / 2));
    } catch (error) {
      console.error('Failed to generate semantic suggestions:', error);
      return [];
    }
  }, [enableSemanticSearch, onSemanticSearch, maxSuggestions]);

  useEffect(() => {
    if (query.length > 0) {
      debouncedSearch(query, filters);
    }
  }, [query, filters, debouncedSearch]);

  // Generate suggestions when query changes
  useEffect(() => {
    if (query.length >= 2 && isFocused) {
      generateAISuggestions(query).then(setAiSuggestions);
    } else {
      setAiSuggestions([]);
    }
  }, [query, isFocused, generateAISuggestions]);

  useEffect(() => {
    if (query.length >= 3 && isFocused) {
      generateSemanticSuggestions(query).then(setSemanticSuggestions);
    } else {
      setSemanticSuggestions([]);
    }
  }, [query, isFocused, generateSemanticSuggestions]);

  const handleQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = e.target.value;
    setQuery(newQuery);
    setShowSuggestions(newQuery.length > 0);
  };

  const handleClear = () => {
    setQuery('');
    setShowSuggestions(false);
    onSearch('', filters);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setQuery(suggestion);
    setShowSuggestions(false);
    onSearch(suggestion, filters);
    
    // Add to search history
    if (!recentSearches.includes(suggestion)) {
      const updatedHistory = [suggestion, ...recentSearches.slice(0, 9)];
      // In a real app, you'd save this to localStorage or state management
    }
  };

  // Combine all suggestions
  const combinedSuggestions = useMemo(() => {
    const allSuggestions: SearchSuggestion[] = [];
    
    // Add provided suggestions
    allSuggestions.push(...suggestions);
    
    // Add AI suggestions
    allSuggestions.push(...aiSuggestions);
    
    // Add semantic suggestions
    allSuggestions.push(...semanticSuggestions);
    
    // Add recent searches as suggestions
    const recentAsSuggestions: SearchSuggestion[] = recentSearches
      .filter(search => search.toLowerCase().includes(query.toLowerCase()))
      .slice(0, 3)
      .map((search, index) => ({
        id: `recent-${index}`,
        text: search,
        type: 'recent' as const,
        relevance: 0.8,
        icon: <History className="h-3 w-3" />,
        description: 'Recent search'
      }));
    
    allSuggestions.push(...recentAsSuggestions);
    
    // Sort by relevance and limit
    return allSuggestions
      .sort((a, b) => b.relevance - a.relevance)
      .slice(0, maxSuggestions);
  }, [suggestions, aiSuggestions, semanticSuggestions, recentSearches, query, maxSuggestions]);

  const handleFilterChange = (newFilters: Partial<SearchFilter>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    if (query) {
      onSearch(query, updatedFilters);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsFocused(false);
      setShowSuggestions(false);
      setSelectedSuggestionIndex(-1);
    } else if (e.key === 'Enter') {
      if (selectedSuggestionIndex >= 0 && combinedSuggestions[selectedSuggestionIndex]) {
        handleSuggestionClick(combinedSuggestions[selectedSuggestionIndex].text);
      } else if (query.trim()) {
        onSearch(query, filters);
        setShowSuggestions(false);
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedSuggestionIndex(prev => 
        prev < combinedSuggestions.length - 1 ? prev + 1 : 0
      );
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedSuggestionIndex(prev => 
        prev > 0 ? prev - 1 : combinedSuggestions.length - 1
      );
    } else if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
      e.preventDefault();
      inputRef.current?.focus();
    }
  };

  // Keyboard shortcut for search focus
  useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        document.getElementById('lighthouse-search')?.focus();
      }
    };

    window.addEventListener('keydown', handleGlobalKeyDown);
    return () => window.removeEventListener('keydown', handleGlobalKeyDown);
  }, []);

  const activeFilterCount = [
    filters.dateRange !== 'all',
    (filters.tags?.length || 0) > 0,
    (filters.sourceTypes?.length || 0) > 0,
    filters.hasNotes,
    filters.hasDiagrams
  ].filter(Boolean).length;

  return (
    <div className={`relative ${className}`}>
      <div className="flex items-center gap-1 sm:gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-2 sm:left-3 top-1/2 -translate-y-1/2 h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
          <Input
            id="lighthouse-search"
            type="text"
            value={query}
            onChange={handleQueryChange}
            onKeyDown={handleKeyDown}
            onFocus={() => {
              setIsFocused(true);
              setShowSuggestions(query.length > 0 || recentSearches.length > 0);
            }}
            onBlur={() => {
              setIsFocused(false);
              setTimeout(() => setShowSuggestions(false), 200);
            }}
            placeholder={placeholder}
            className="pl-7 sm:pl-9 pr-16 sm:pr-20 text-sm sm:text-base h-9 sm:h-10"
          />
          <div className="absolute right-1 sm:right-2 top-1/2 -translate-y-1/2 flex items-center gap-0.5 sm:gap-1">
            {query && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClear}
                className="h-5 w-5 sm:h-6 sm:w-6 p-0"
              >
                <X className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
              </Button>
            )}
            <div className="hidden sm:flex items-center gap-1 text-xs text-muted-foreground">
              <Command className="h-3 w-3" />
              <span>K</span>
            </div>
          </div>
        </div>

        <Popover open={showFilters} onOpenChange={setShowFilters}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="relative h-9 sm:h-10 px-2 sm:px-3"
            >
              <Filter className="h-3 w-3 sm:h-4 sm:w-4 sm:mr-2" />
              <span className="hidden sm:inline">Filters</span>
              {activeFilterCount > 0 && (
                <Badge
                  variant="secondary"
                  className="absolute -top-1 sm:-top-2 -right-1 sm:-right-2 h-4 w-4 sm:h-5 sm:w-5 p-0 flex items-center justify-center text-xs"
                >
                  {activeFilterCount}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-72 sm:w-80" align="end">
            <div className="space-y-3 sm:space-y-4">
              <div>
                <h4 className="font-medium text-xs sm:text-sm mb-2">Date Range</h4>
                <div className="grid grid-cols-2 gap-1.5 sm:gap-2">
                  {['today', 'week', 'month', 'all'].map((range) => (
                    <Button
                      key={range}
                      variant={filters.dateRange === range ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => handleFilterChange({ dateRange: range as any })}
                      className="capitalize text-xs sm:text-sm h-7 sm:h-8 px-2 sm:px-3"
                    >
                      {range === 'all' ? 'All Time' : range}
                    </Button>
                  ))}
                </div>
              </div>

              <Separator />

              <div>
                <h4 className="font-medium text-xs sm:text-sm mb-2">Content Type</h4>
                <div className="space-y-1.5 sm:space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="has-notes"
                      checked={filters.hasNotes}
                      onCheckedChange={(checked) =>
                        handleFilterChange({ hasNotes: checked as boolean })
                      }
                      className="h-3 w-3 sm:h-4 sm:w-4"
                    />
                    <Label htmlFor="has-notes" className="text-xs sm:text-sm">
                      Has Notes
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="has-diagrams"
                      checked={filters.hasDiagrams}
                      onCheckedChange={(checked) =>
                        handleFilterChange({ hasDiagrams: checked as boolean })
                      }
                      className="h-3 w-3 sm:h-4 sm:w-4"
                    />
                    <Label htmlFor="has-diagrams" className="text-xs sm:text-sm">
                      Has Diagrams
                    </Label>
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <h4 className="font-medium text-xs sm:text-sm mb-2">Source Types</h4>
                <div className="flex flex-wrap gap-1.5 sm:gap-2">
                  {['PDF', 'Web', 'Audio', 'Text'].map((type) => (
                    <Badge
                      key={type}
                      variant={filters.sourceTypes?.includes(type) ? 'default' : 'outline'}
                      className="cursor-pointer text-xs h-6 sm:h-7 px-2 sm:px-3"
                      onClick={() => {
                        const current = filters.sourceTypes || [];
                        const updated = current.includes(type)
                          ? current.filter((t) => t !== type)
                          : [...current, type];
                        handleFilterChange({ sourceTypes: updated });
                      }}
                    >
                      {type}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* AI Search Options */}
              {enableAI && (
                <div>
                  <h4 className="font-medium text-xs sm:text-sm mb-2 flex items-center gap-1.5 sm:gap-2">
                    <Sparkles className="h-3 w-3 sm:h-4 sm:w-4 text-blue-500" />
                    AI Search
                  </h4>
                  <div className="space-y-1.5 sm:space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="ai-suggestions"
                        checked={true}
                        disabled
                        className="h-3 w-3 sm:h-4 sm:w-4"
                      />
                      <Label htmlFor="ai-suggestions" className="text-xs sm:text-sm">AI Suggestions</Label>
                    </div>
                  </div>
                </div>
              )}

              {/* Semantic Search Options */}
              {enableSemanticSearch && (
                <div>
                  <h4 className="font-medium text-xs sm:text-sm mb-2 flex items-center gap-1.5 sm:gap-2">
                    <Brain className="h-3 w-3 sm:h-4 sm:w-4 text-purple-500" />
                    Semantic Search
                  </h4>
                  <div className="space-y-1.5 sm:space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="semantic-enabled"
                        checked={true}
                        disabled
                        className="h-3 w-3 sm:h-4 sm:w-4"
                      />
                      <Label htmlFor="semantic-enabled" className="text-xs sm:text-sm">Semantic Matching</Label>
                    </div>
                    <div>
                      <Label className="text-xs text-muted-foreground">Similarity Threshold</Label>
                      <input
                        type="range"
                        min="0.1"
                        max="1"
                        step="0.1"
                        value={filters.semanticSimilarity || 0.7}
                        onChange={(e) => handleFilterChange({ semanticSimilarity: parseFloat(e.target.value) })}
                        className="w-full mt-1 h-1.5 sm:h-2"
                      />
                      <div className="text-xs text-muted-foreground mt-1">
                        {((filters.semanticSimilarity || 0.7) * 100).toFixed(0)}% similarity
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <Separator />

              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setFilters({
                    dateRange: 'all',
                    tags: [],
                    sourceTypes: [],
                    hasNotes: false,
                    hasDiagrams: false
                  });
                  if (query) {
                    onSearch(query, {});
                  }
                }}
                className="w-full text-xs sm:text-sm h-7 sm:h-8"
              >
                Clear All Filters
              </Button>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      {/* Suggestions Dropdown */}
      <AnimatePresence>
        {showSuggestions && (combinedSuggestions.length > 0 || recentSearches.length > 0) && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full left-0 right-0 mt-1 sm:mt-2 bg-popover border rounded-lg shadow-lg z-50 overflow-hidden max-h-80 sm:max-h-96 overflow-y-auto"
          >
            {recentSearches.length > 0 && !query && (
               <div className="p-1.5 sm:p-2">
                 <div className="flex items-center justify-between px-1.5 sm:px-2 py-1">
                   <span className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                     <Clock className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
                     Recent
                   </span>
                   {onClearRecent && (
                     <button
                       onClick={onClearRecent}
                       className="text-xs text-muted-foreground hover:text-foreground px-1 py-0.5 rounded"
                     >
                       Clear
                     </button>
                   )}
                 </div>
                 {recentSearches.slice(0, 5).map((search, index) => (
                   <button
                     key={index}
                     onClick={() => handleSuggestionClick(search)}
                     className="w-full text-left px-1.5 sm:px-2 py-1 sm:py-1.5 hover:bg-accent rounded text-xs sm:text-sm flex items-center gap-1.5 sm:gap-2 truncate"
                   >
                     <History className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-muted-foreground flex-shrink-0" />
                     <span className="truncate">{search}</span>
                   </button>
                 ))}
               </div>
             )}

             {/* Search Analytics */}
             {enableAnalytics && analytics && !query && (
               <div className="p-1.5 sm:p-2 border-t">
                 <div className="px-1.5 sm:px-2 py-1">
                   <span className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                     <BarChart3 className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
                     Analytics
                   </span>
                 </div>
                 <div className="space-y-0.5 sm:space-y-1">
                   <div className="text-xs text-muted-foreground px-1.5 sm:px-2">
                     {analytics.totalSearches} total searches
                   </div>
                   {analytics.topQueries.slice(0, 3).map((query, index) => (
                     <button
                       key={index}
                       onClick={() => handleSuggestionClick(query)}
                       className="w-full text-left px-1.5 sm:px-2 py-0.5 sm:py-1 hover:bg-accent rounded text-xs flex items-center gap-1.5 sm:gap-2 truncate"
                     >
                       <TrendingUp className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-green-500 flex-shrink-0" />
                       <span className="truncate">{query}</span>
                     </button>
                   ))}
                 </div>
               </div>
             )}

             {/* Search Tips */}
             {showSearchTips && !query && combinedSuggestions.length === 0 && (
               <div className="p-1.5 sm:p-2 border-t">
                 <div className="px-1.5 sm:px-2 py-1">
                   <span className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                     <Target className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
                     Search Tips
                   </span>
                 </div>
                 <div className="space-y-0.5 sm:space-y-1 text-xs text-muted-foreground px-1.5 sm:px-2">
                   <div className="flex items-center gap-1.5 sm:gap-2">
                     <Sparkles className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-blue-500 flex-shrink-0" />
                     <span className="text-xs">Use AI suggestions for better results</span>
                   </div>
                   <div className="flex items-center gap-1.5 sm:gap-2">
                     <Brain className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-purple-500 flex-shrink-0" />
                     <span className="text-xs">Try semantic search for related content</span>
                   </div>
                   <div className="hidden sm:flex items-center gap-2">
                     <Command className="h-3 w-3 flex-shrink-0" />
                     <span className="text-xs">Press Cmd+K to focus search</span>
                   </div>
                 </div>
               </div>
             )}

            {combinedSuggestions.length > 0 && query && (
              <div className="p-1.5 sm:p-2">
                <div className="px-1.5 sm:px-2 py-1">
                  <span className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                    <TrendingUp className="h-2.5 w-2.5 sm:h-3 sm:w-3" />
                    Suggestions
                    {isLoadingSuggestions && <div className="animate-spin h-2.5 w-2.5 sm:h-3 sm:w-3 border border-current border-t-transparent rounded-full" />}
                  </span>
                </div>
                {combinedSuggestions.map((suggestion, index) => (
                   <button
                     key={suggestion.id || index}
                     onClick={() => handleSuggestionClick(suggestion.text)}
                     className={`w-full text-left px-1.5 sm:px-2 py-1 sm:py-1.5 rounded text-xs sm:text-sm flex items-center gap-1.5 sm:gap-2 transition-colors ${
                       selectedSuggestionIndex === index 
                         ? 'bg-accent text-accent-foreground' 
                         : 'hover:bg-accent/50'
                     }`}
                   >
                     {suggestion.icon && <span className="text-muted-foreground flex-shrink-0">{suggestion.icon}</span>}
                     <div className="flex-1 min-w-0">
                       <div className="font-medium truncate">{suggestion.text}</div>
                       {suggestion.description && (
                         <div className="text-xs text-muted-foreground truncate hidden sm:block">{suggestion.description}</div>
                       )}
                       {suggestion.metadata?.resultCount && (
                         <div className="text-xs text-muted-foreground hidden sm:block">
                           {suggestion.metadata.resultCount} results
                         </div>
                       )}
                     </div>
                     <div className="flex items-center gap-0.5 sm:gap-1 flex-shrink-0">
                       {suggestion.type === 'ai' && <Sparkles className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-blue-500" />}
                       {suggestion.type === 'semantic' && <Brain className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-purple-500" />}
                       {suggestion.type === 'recent' && <History className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-gray-500" />}
                       {suggestion.type === 'popular' && <Star className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-yellow-500" />}
                     </div>
                   </button>
                 ))}
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SearchBar;