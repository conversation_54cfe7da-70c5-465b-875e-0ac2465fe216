export interface SearchFilters {
  contentTypes: ('notebook' | 'source' | 'chat' | 'slide')[];
  dateRange: { from?: Date; to?: Date };
  tags: string[];
  authors: string[];
  sourceTypes: string[];
  sortBy: 'relevance' | 'date' | 'title' | 'modified';
  sortOrder: 'asc' | 'desc';
}

export interface SearchResult {
  id: string;
  type: 'notebook' | 'source' | 'chat' | 'slide';
  title: string;
  excerpt: string;
  relevance: number;
  date: Date;
  author?: string;
  tags?: string[];
  highlights?: string[];
}

export interface SearchQuery {
  query: string;
  filters: SearchFilters;
  limit?: number;
  offset?: number;
}

export interface SearchHistory {
  id: string;
  query: string;
  filters: SearchFilters;
  timestamp: Date;
  resultCount: number;
}

export interface SavedSearch {
  id: string;
  name: string;
  description?: string;
  query: string;
  filters: SearchFilters;
  createdAt: Date;
  updatedAt: Date;
}