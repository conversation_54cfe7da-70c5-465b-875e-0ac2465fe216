import React from 'react';
import { FileText, Database, MessageSquare, Presentation } from 'lucide-react';
import { SearchResult } from './types';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

interface SearchResultsProps {
  results: SearchResult[];
  isLoading?: boolean;
  query?: string;
  onResultClick?: (result: SearchResult) => void;
}

const typeIcons = {
  notebook: FileText,
  source: Database,
  chat: MessageSquare,
  slide: Presentation,
};

const SearchResults: React.FC<SearchResultsProps> = ({
  results,
  isLoading = false,
  query = '',
  onResultClick,
}) => {
  if (isLoading) {
    return (
      <div className="space-y-3">
        {[1, 2, 3].map((i) => (
          <div key={i} className="p-4 border rounded-lg space-y-2">
            <Skeleton className="h-5 w-3/4" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
          </div>
        ))}
      </div>
    );
  }

  if (results.length === 0 && query) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">No results found for "{query}"</p>
        <p className="text-sm text-muted-foreground mt-2">
          Try adjusting your search terms or filters
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {results.map((result) => {
        const Icon = typeIcons[result.type];
        const relevanceColor = result.relevance > 0.8 ? 'text-green-500' : 
                               result.relevance > 0.5 ? 'text-yellow-500' : 'text-gray-500';
        
        return (
          <div
            key={result.id}
            className={cn(
              "p-4 border rounded-lg hover:bg-accent cursor-pointer transition-colors",
              "group relative"
            )}
            onClick={() => onResultClick?.(result)}
          >
            <div className="flex items-start gap-3">
              <div className="mt-1">
                <Icon className="h-5 w-5 text-muted-foreground" />
              </div>
              <div className="flex-1 space-y-1">
                <div className="flex items-start justify-between">
                  <h4 className="font-medium group-hover:text-primary transition-colors">
                    {highlightText(result.title, query)}
                  </h4>
                  <div className="flex items-center gap-2">
                    <span className={cn("text-xs", relevanceColor)}>
                      {Math.round(result.relevance * 100)}%
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {result.type}
                    </Badge>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground line-clamp-2">
                  {highlightText(result.excerpt, query)}
                </p>
                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <span>{formatDate(result.date)}</span>
                  {result.author && <span>by {result.author}</span>}
                  {result.tags && result.tags.length > 0 && (
                    <div className="flex gap-1">
                      {result.tags.slice(0, 3).map(tag => (
                        <Badge key={tag} variant="secondary" className="text-xs py-0 h-5">
                          {tag}
                        </Badge>
                      ))}
                      {result.tags.length > 3 && (
                        <span className="text-muted-foreground">+{result.tags.length - 3}</span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

function highlightText(text: string, query: string): React.ReactNode {
  if (!query) return text;
  
  const parts = text.split(new RegExp(`(${query})`, 'gi'));
  return (
    <>
      {parts.map((part, index) =>
        part.toLowerCase() === query.toLowerCase() ? (
          <mark key={index} className="bg-yellow-200 dark:bg-yellow-900 px-0.5">
            {part}
          </mark>
        ) : (
          part
        )
      )}
    </>
  );
}

function formatDate(date: Date): string {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (days === 0) return 'Today';
  if (days === 1) return 'Yesterday';
  if (days < 7) return `${days} days ago`;
  if (days < 30) return `${Math.floor(days / 7)} weeks ago`;
  if (days < 365) return `${Math.floor(days / 30)} months ago`;
  return `${Math.floor(days / 365)} years ago`;
}

export default SearchResults;