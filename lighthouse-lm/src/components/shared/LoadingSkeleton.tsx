import React from 'react';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { skeleton, staggerDelay } from '../../utils/styles';

interface LoadingSkeletonProps {
  variant?: 'card' | 'list' | 'grid' | 'text' | 'table' | 'metric';
  count?: number;
  className?: string;
  showHeader?: boolean;
  columns?: number; // For table variant
  animated?: boolean; // Control animation
  size?: 'sm' | 'md' | 'lg'; // Size variants
}

/**
 * Reusable loading skeleton component for consistent loading states
 */
const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({
  variant = 'card',
  count = 1,
  className = '',
  showHeader = false,
  columns = 4,
  animated = true,
  size = 'md',
}) => {
  const sizeClasses = {
    sm: { avatar: 'w-6 h-6', title: 'h-3', text: 'h-2', button: 'h-6 w-16' },
    md: { avatar: 'w-10 h-10', title: 'h-6', text: 'h-4', button: 'h-10 w-24' },
    lg: { avatar: 'w-12 h-12', title: 'h-8', text: 'h-5', button: 'h-12 w-32' },
  };
  
  const currentSize = sizeClasses[size];
  const renderSkeleton = (index: number) => {
    switch (variant) {
      case 'card':
        return (
          <Card 
            key={index}
            className={cn(skeleton.card, className)}
            style={animated ? staggerDelay(index) : undefined}
          >
            {/* Header skeleton */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className={cn(skeleton.base, currentSize.avatar, 'rounded-full')} />
                <div className="space-y-2">
                  <div className={cn(skeleton.base, currentSize.title, 'w-32 rounded')} />
                  <div className={cn(skeleton.base, currentSize.text, 'w-20 rounded')} />
                </div>
              </div>
              <div className={cn(skeleton.base, 'w-6 h-6 rounded')} />
            </div>
            
            {/* Content skeleton */}
            <div className="space-y-3 mb-4">
              <div className={cn(skeleton.base, currentSize.text, 'w-full rounded')} />
              <div className={cn(skeleton.base, currentSize.text, 'w-3/4 rounded')} />
            </div>
            
            {/* Footer skeleton */}
            <div className="flex items-center justify-between pt-4 border-t">
              <div className={cn(skeleton.base, currentSize.text, 'w-24 rounded')} />
              <div className={cn(skeleton.base, currentSize.button, 'rounded')} />
            </div>
          </Card>
        );
        
      case 'list':
        return (
          <div 
            key={index}
            className={cn('p-4 border-b', className)}
            style={animated ? staggerDelay(index) : undefined}
          >
            <div className="flex items-center space-x-3">
              <div className={cn(skeleton.base, currentSize.avatar, 'rounded-full')} />
              <div className="flex-1 space-y-2">
                <div className={cn(skeleton.base, currentSize.title, 'w-48 rounded')} />
                <div className={cn(skeleton.base, currentSize.text, 'w-32 rounded')} />
              </div>
            </div>
          </div>
        );
        
      case 'text':
        return (
          <div 
            key={index}
            className={cn('space-y-2', className)}
            style={animated ? staggerDelay(index) : undefined}
          >
            <div className={cn(skeleton.base, currentSize.title, 'w-3/4 rounded')} />
            <div className={cn(skeleton.base, currentSize.text, 'w-full rounded')} />
            <div className={cn(skeleton.base, currentSize.text, 'w-5/6 rounded')} />
          </div>
        );
        
      case 'grid':
        return (
          <div 
            key={index}
            className={cn('aspect-square rounded-lg', skeleton.base, className)}
            style={animated ? staggerDelay(index) : undefined}
          />
        );
        
      case 'table':
        return (
          <div 
            key={index}
            className={cn('flex items-center gap-4 p-3 border-b', className)}
            style={animated ? staggerDelay(index) : undefined}
          >
            {Array.from({ length: columns }, (_, colIndex) => (
              <div 
                key={colIndex}
                className={cn(
                  skeleton.base, 
                  currentSize.text, 
                  'rounded',
                  colIndex === 0 ? 'w-16' : colIndex === columns - 1 ? 'w-20' : 'flex-1'
                )}
              />
            ))}
          </div>
        );
        
      case 'metric':
        return (
          <Card 
            key={index}
            className={cn('p-6', className)}
            style={animated ? staggerDelay(index) : undefined}
          >
            <div className="flex items-center justify-between mb-4">
              <div className={cn(skeleton.base, currentSize.avatar, 'rounded-lg')} />
              <div className={cn(skeleton.base, 'w-6 h-6 rounded')} />
            </div>
            <div className="space-y-2">
              <div className={cn(skeleton.base, currentSize.title, 'w-20 rounded')} />
              <div className={cn(skeleton.base, 'h-8 w-24 rounded')} />
              <div className={cn(skeleton.base, currentSize.text, 'w-16 rounded')} />
            </div>
          </Card>
        );
        
      default:
        return null;
    }
  };
  
  return (
    <>
      {showHeader && (
        <div className="mb-8">
          <div className={cn(skeleton.base, 'h-12 rounded-lg w-96 mb-2')} />
        </div>
      )}
      
      {variant === 'grid' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: count }, (_, i) => renderSkeleton(i))}
        </div>
      ) : (
        <>
          {Array.from({ length: count }, (_, i) => renderSkeleton(i))}
        </>
      )}
    </>
  );
};

/**
 * Grid skeleton preset
 */
export const GridSkeleton: React.FC<{ 
  count?: number; 
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
}> = ({ 
  count = 6, 
  className,
  size = 'md',
  animated = true
}) => (
  <LoadingSkeleton variant="grid" count={count} className={className} size={size} animated={animated} />
);

/**
 * Card skeleton preset
 */
export const CardSkeleton: React.FC<{ 
  count?: number; 
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
}> = ({ 
  count = 1, 
  className,
  size = 'md',
  animated = true
}) => (
  <LoadingSkeleton variant="card" count={count} className={className} size={size} animated={animated} />
);

/**
 * List skeleton preset
 */
export const ListSkeleton: React.FC<{ 
  count?: number; 
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
}> = ({ 
  count = 5, 
  className,
  size = 'md',
  animated = true
}) => (
  <LoadingSkeleton variant="list" count={count} className={className} size={size} animated={animated} />
);

/**
 * Table skeleton preset
 */
export const TableSkeleton: React.FC<{ 
  rows?: number; 
  columns?: number;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
}> = ({ 
  rows = 5, 
  columns = 4,
  className,
  size = 'md',
  animated = true
}) => (
  <LoadingSkeleton variant="table" count={rows} columns={columns} className={className} size={size} animated={animated} />
);

/**
 * Metric skeleton preset
 */
export const MetricSkeleton: React.FC<{ 
  count?: number; 
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
}> = ({ 
  count = 3, 
  className,
  size = 'md',
  animated = true
}) => (
  <LoadingSkeleton variant="metric" count={count} className={className} size={size} animated={animated} />
);

export default LoadingSkeleton;