import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

export type DialogType = 'url' | 'text' | 'multiline' | 'rename';

interface UnifiedInputDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (value: string | { title: string; content: string } | string[]) => void | Promise<void>;
  type: DialogType;
  title: string;
  icon?: LucideIcon;
  iconClassName?: string;
  placeholder?: string;
  label?: string;
  submitLabel?: string;
  defaultValue?: string | { title: string; content: string };
  validator?: (value: string) => boolean;
  autoClipboard?: boolean;
  multipleInputs?: boolean;
  requireTitle?: boolean;
  className?: string;
  maxLength?: number;
}

/**
 * Unified dialog component for all input types
 * Consolidates WebsiteUrlInput, YouTubeUrlInput, PasteTextDialog, CopiedTextDialog, etc.
 */
const UnifiedInputDialog: React.FC<UnifiedInputDialogProps> = ({
  open,
  onOpenChange,
  onSubmit,
  type,
  title,
  icon: Icon,
  iconClassName = '',
  placeholder = '',
  label = '',
  submitLabel = 'Submit',
  defaultValue = '',
  validator,
  autoClipboard = false,
  multipleInputs = false,
  requireTitle = false,
  className = '',
  maxLength,
}) => {
  const [inputValue, setInputValue] = useState('');
  const [titleValue, setTitleValue] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize values when dialog opens
  useEffect(() => {
    if (open) {
      setError(null);
      
      if (typeof defaultValue === 'object') {
        setTitleValue(defaultValue.title || '');
        setInputValue(defaultValue.content || '');
      } else if (typeof defaultValue === 'string') {
        setInputValue(defaultValue);
      }
      
      // Auto-populate from clipboard if enabled
      if (autoClipboard && !defaultValue) {
        navigator.clipboard.readText()
          .then(text => {
            if (text && text.trim()) {
              setInputValue(text);
              if (requireTitle) {
                // Generate default title from content
                const words = text.trim().split(' ').slice(0, 8).join(' ');
                setTitleValue(words.length > 50 ? words.substring(0, 50) + '...' : words);
              }
            }
          })
          .catch(err => console.error('Failed to read clipboard:', err));
      }
    } else {
      // Reset when closing
      setInputValue('');
      setTitleValue('');
      setError(null);
    }
  }, [open, defaultValue, autoClipboard, requireTitle]);

  const handleSubmit = async (e?: React.FormEvent) => {
    e?.preventDefault();
    
    // Validation
    if (requireTitle && !titleValue.trim()) {
      setError('Title is required');
      return;
    }
    
    if (!inputValue.trim()) {
      setError(label || 'Input is required');
      return;
    }
    
    if (validator && !validator(inputValue)) {
      setError('Invalid input format');
      return;
    }
    
    setIsSubmitting(true);
    setError(null);
    
    try {
      let submitValue: any;
      
      if (multipleInputs) {
        // Parse multiple lines into array
        submitValue = inputValue
          .split('\n')
          .map(line => line.trim())
          .filter(line => line !== '');
      } else if (requireTitle) {
        submitValue = {
          title: titleValue.trim(),
          content: inputValue.trim()
        };
      } else {
        submitValue = inputValue.trim();
      }
      
      await onSubmit(submitValue);
      
      // Reset and close on success
      setInputValue('');
      setTitleValue('');
      onOpenChange(false);
    } catch (error) {
      setError('Failed to submit. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setInputValue('');
    setTitleValue('');
    setError(null);
    onOpenChange(false);
  };

  const isValid = requireTitle 
    ? titleValue.trim() !== '' && inputValue.trim() !== ''
    : inputValue.trim() !== '';

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={cn(
        type === 'text' || type === 'multiline' ? 'max-w-2xl' : 'max-w-md',
        'max-h-[80vh]',
        className
      )}>
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            {Icon && <Icon className={cn('h-5 w-5', iconClassName)} />}
            <span>{title}</span>
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {requireTitle && (
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={titleValue}
                onChange={(e) => setTitleValue(e.target.value)}
                placeholder="Enter a title..."
                maxLength={100}
              />
            </div>
          )}

          <div className="space-y-2">
            {label && <Label htmlFor="input">{label}</Label>}
            
            {type === 'url' || type === 'rename' ? (
              <Input
                id="input"
                type={type === 'url' ? 'url' : 'text'}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder={placeholder}
                maxLength={maxLength}
                autoFocus
              />
            ) : (
              <Textarea
                id="input"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder={placeholder}
                rows={type === 'multiline' ? 10 : 6}
                maxLength={maxLength}
                className="resize-none"
              />
            )}
            
            {multipleInputs && (
              <p className="text-xs text-muted-foreground">
                Enter one item per line
              </p>
            )}
          </div>

          {error && (
            <p className="text-sm text-destructive">{error}</p>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!isValid || isSubmitting}
            >
              {isSubmitting ? 'Submitting...' : submitLabel}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default UnifiedInputDialog;