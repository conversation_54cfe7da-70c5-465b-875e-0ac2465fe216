import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { FileText, MessageCircle, Edit } from 'lucide-react';
import ChatArea from '../chat/ChatArea';
import Sidebar from '../sidebar/Sidebar';
import { Citation } from '../../types/message';

interface MobileNotebookTabsProps {
  notebookId?: string;
  notebook?: any;
  hasSource: boolean;
  selectedCitation: Citation | null;
  onCitationClick: (citation: Citation) => void;
  onCitationClose: () => void;
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const MobileNotebookTabs: React.FC<MobileNotebookTabsProps> = ({
  notebookId,
  notebook,
  hasSource,
  selectedCitation,
  onCitationClick,
  onCitationClose,
  activeTab,
  onTabChange,
}) => {
  return (
    <Tabs value={activeTab} onValueChange={onTabChange} className="h-full flex flex-col">
      <TabsList className="grid w-full grid-cols-3 rounded-none transition-all duration-300 hover:shadow-md">
        <TabsTrigger value="sources" className="flex items-center space-x-1 transition-all duration-200 hover:scale-105 hover:bg-primary/10 hover:text-primary focus:ring-2 focus:ring-primary/20 active:scale-95 group data-[state=active]:bg-primary/10 data-[state=active]:text-primary">
          <FileText className="h-4 w-4 transition-all duration-200 group-hover:scale-110 group-hover:rotate-12" />
          <span className="transition-all duration-200 group-hover:tracking-wide">Sources</span>
        </TabsTrigger>
        <TabsTrigger value="chat" className="flex items-center space-x-1 transition-all duration-200 hover:scale-105 hover:bg-primary/10 hover:text-primary focus:ring-2 focus:ring-primary/20 active:scale-95 group data-[state=active]:bg-primary/10 data-[state=active]:text-primary">
          <MessageCircle className="h-4 w-4 transition-all duration-200 group-hover:scale-110 group-hover:bounce" />
          <span className="transition-all duration-200 group-hover:tracking-wide">Chat</span>
        </TabsTrigger>
        <TabsTrigger value="studio" className="flex items-center space-x-1 transition-all duration-200 hover:scale-105 hover:bg-primary/10 hover:text-primary focus:ring-2 focus:ring-primary/20 active:scale-95 group data-[state=active]:bg-primary/10 data-[state=active]:text-primary">
          <Edit className="h-4 w-4 transition-all duration-200 group-hover:scale-110 group-hover:-rotate-12" />
          <span className="transition-all duration-200 group-hover:tracking-wide">Studio</span>
        </TabsTrigger>
      </TabsList>
      
      <TabsContent value="sources" className="flex-1 mt-0 overflow-hidden transition-all duration-300 animate-in fade-in-0 slide-in-from-bottom-2">
        <div className="p-4 text-center text-muted-foreground">
          <p>Sources panel removed</p>
        </div>
      </TabsContent>
      
      <TabsContent value="chat" className="flex-1 mt-0 overflow-hidden transition-all duration-300 animate-in fade-in-0 slide-in-from-bottom-2">
        <ChatArea
          hasSource={hasSource}
          notebookId={notebookId}
          notebook={notebook}
          onCitationClick={onCitationClick}
        />
      </TabsContent>
      
      <TabsContent value="studio" className="flex-1 mt-0 overflow-hidden transition-all duration-300 animate-in fade-in-0 slide-in-from-bottom-2">
        <Sidebar
          notebookId={notebookId}
          selectedCitation={selectedCitation}
        />
      </TabsContent>
    </Tabs>
  );
};

export default MobileNotebookTabs;