import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { FileText, Clock, MoreVertical, Trash2, Edit, Download, BookOpen } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useNotebooks } from '@/hooks/useNotebooks';
import { useNotebookDelete } from '@/hooks/useNotebookDelete';
import InsightsAPI from '@/services/insights-api';
import { getStatusColor } from '@/utils/styles';
import { exportToFile } from '@/utils/common';

interface NotebookGridProps {
  onNotebookSelect?: (notebookId: string) => void;
}

const NotebookGrid: React.FC<NotebookGridProps> = ({ onNotebookSelect }) => {
  const { notebooks, isLoading } = useNotebooks();
  const { deleteNotebook } = useNotebookDelete();

  const handleNotebookClick = (notebookId: string) => {
    if (onNotebookSelect) {
      onNotebookSelect(notebookId);
    }
  };

  const handleExportNotebook = async (notebookId: string, notebookTitle: string) => {
    try {
      const exportData = await InsightsAPI.exportNotebook(notebookId);
      const dataStr = JSON.stringify(exportData, null, 2);
      const exportFileDefaultName = `${notebookTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_export.json`;
      
      exportToFile(dataStr, exportFileDefaultName, 'application/json');
    } catch (error) {
    }
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 sm:gap-6">
        {[...Array(8)].map((_, i) => (
          <Card key={i} className="p-4 sm:p-6 transition-all duration-300 hover:scale-105 hover:shadow-lg">
            <Skeleton className="h-10 w-10 sm:h-12 sm:w-12 rounded-lg mb-3 sm:mb-4" />
            <Skeleton className="h-5 sm:h-6 w-3/4 mb-2" />
            <Skeleton className="h-3 sm:h-4 w-full mb-3 sm:mb-4" />
            <div className="flex justify-between">
              <Skeleton className="h-3 sm:h-4 w-16 sm:w-20" />
              <Skeleton className="h-3 sm:h-4 w-20 sm:w-24" />
            </div>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 sm:gap-6">
      {notebooks.map((notebook) => (
        <Card
          key={notebook.id}
          className="p-4 sm:p-6 hover:shadow-xl transition-all duration-300 cursor-pointer group hover:scale-105 hover:border-primary/30 hover:bg-gradient-to-br hover:from-primary/5 hover:to-accent/20 focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2"
          onClick={() => handleNotebookClick(notebook.id)}
          tabIndex={0}
          role="button"
          aria-label={`Open notebook: ${notebook.title}`}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleNotebookClick(notebook.id);
            }
          }}
        >
          <div className="flex justify-between items-start mb-3 sm:mb-4">
            {notebook.icon ? (
              <div className="text-2xl sm:text-3xl transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12">{notebook.icon}</div>
            ) : (
              <BookOpen className="w-6 h-6 sm:w-8 sm:h-8 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12" />
            )}
            <DropdownMenu>
              <DropdownMenuTrigger
                onClick={(e) => e.stopPropagation()}
                className="opacity-0 group-hover:opacity-100 transition-all duration-300 p-1 rounded-lg hover:bg-accent focus:opacity-100 focus:ring-2 focus:ring-primary focus:ring-offset-2 hover:scale-110 active:scale-95"
                aria-label="Notebook options"
              >
                <MoreVertical className="w-5 h-5 text-muted-foreground hover:text-foreground transition-colors duration-200" />
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="transition-all duration-200 animate-in slide-in-from-top-2">
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    handleNotebookClick(notebook.id);
                  }}
                  className="transition-all duration-200 hover:bg-primary/10 hover:text-primary focus:bg-primary/10 focus:text-primary group/item"
                >
                  <Edit className="w-4 h-4 mr-2 transition-transform duration-200 group-hover/item:scale-110" />
                  Open
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    handleExportNotebook(notebook.id, notebook.title);
                  }}
                  className="transition-all duration-200 hover:bg-emerald-50 hover:text-emerald-600 focus:bg-emerald-50 focus:text-emerald-600 group/item"
                >
                  <Download className="w-4 h-4 mr-2 transition-transform duration-200 group-hover/item:scale-110 group-hover/item:-translate-y-0.5" />
                  Export
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    if (confirm('Are you sure you want to delete this notebook?')) {
                      deleteNotebook(notebook.id);
                    }
                  }}
                  className="text-destructive focus:text-destructive transition-all duration-200 hover:bg-destructive/10 focus:bg-destructive/10 group/item"
                >
                  <Trash2 className="w-4 h-4 mr-2 transition-transform duration-200 group-hover/item:scale-110 group-hover/item:shake" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <h3 className="font-semibold text-foreground mb-2 line-clamp-2 transition-colors duration-300 group-hover:text-primary">
            {notebook.title}
          </h3>
          
          {notebook.description && (
            <p className="text-sm text-muted-foreground mb-4 line-clamp-2 transition-colors duration-300 group-hover:text-foreground">
              {notebook.description}
            </p>
          )}

          {/* Status Badges */}
          <div className="flex items-center gap-2 mb-3">
            {notebook.source_count > 0 ? (
              <Badge variant="secondary" className="text-xs transition-all duration-300 hover:scale-105">
                Active
              </Badge>
            ) : (
              <Badge variant="outline" className="text-xs transition-all duration-300 hover:scale-105">
                Empty
              </Badge>
            )}
            {notebook.updated_at && new Date(notebook.updated_at) > new Date(Date.now() - 24 * 60 * 60 * 1000) && (
              <Badge variant="default" className="text-xs transition-all duration-300 hover:scale-105">
                Recent
              </Badge>
            )}
          </div>

          <div className="flex items-center justify-between text-xs text-muted-foreground transition-colors duration-300 group-hover:text-foreground">
              <div className="flex items-center space-x-1 group/sources transition-all duration-300 hover:text-primary">
              <FileText className="w-3 h-3 transition-transform duration-300 group-hover/sources:scale-110" />
              <span>{notebook.source_count || 0} sources</span>
            </div>
            
            <div className="flex items-center space-x-1 group/time transition-all duration-300 hover:text-purple-600">
              <Clock className="w-3 h-3 transition-transform duration-300 group-hover/time:scale-110" />
              <span>{new Date(notebook.updated_at).toLocaleDateString()}</span>
            </div>
          </div>

          {notebook.generation_status && (
            <div className="mt-3">
              <Badge className={`${getStatusColor(notebook.generation_status)} transition-all duration-300 hover:scale-105 hover:shadow-sm`}>
                {notebook.generation_status}
              </Badge>
            </div>
          )}
        </Card>
      ))}
    </div>
  );
};

export default NotebookGrid;