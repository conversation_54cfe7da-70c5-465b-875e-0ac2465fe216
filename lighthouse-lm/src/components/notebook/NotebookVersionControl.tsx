import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  GitBranch, 
  GitCommit, 
  GitMerge, 
  History, 
  Tag, 
  Clock, 
  User, 
  FileText, 
  Plus, 
  ArrowRight, 
  RotateCcw, 
  Download, 
  Upload, 
  Diff, 
  Eye, 
  Edit,
  Save,
  X,
  Check,
  AlertTriangle
} from 'lucide-react';
import { useToast } from '@/hooks/useToast';

interface Version {
  id: string;
  version: string;
  title: string;
  description?: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
  };
  createdAt: string;
  changes: {
    added: number;
    modified: number;
    deleted: number;
  };
  tags: string[];
  isCurrent: boolean;
  parentId?: string;
}

interface Branch {
  id: string;
  name: string;
  description?: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
  };
  createdAt: string;
  lastCommit: string;
  isActive: boolean;
  isMain: boolean;
  commitsAhead: number;
  commitsBehind: number;
}

interface NotebookVersionControlProps {
  notebookId: string;
  notebookTitle: string;
  currentUserId: string;
  onVersionRestore?: (versionId: string) => void;
  onBranchSwitch?: (branchId: string) => void;
  onVersionCreate?: (version: Omit<Version, 'id' | 'createdAt'>) => void;
}

const NotebookVersionControl: React.FC<NotebookVersionControlProps> = ({
  notebookId,
  notebookTitle,
  currentUserId,
  onVersionRestore,
  onBranchSwitch,
  onVersionCreate
}) => {
  const [versions, setVersions] = useState<Version[]>([
    {
      id: '1',
      version: 'v1.2.3',
      title: 'Added research methodology section',
      description: 'Comprehensive update including new research methods and data analysis techniques',
      author: {
        id: '1',
        name: 'Alice Johnson',
        avatar: '/avatars/alice.jpg'
      },
      createdAt: new Date().toISOString(),
      changes: { added: 15, modified: 8, deleted: 2 },
      tags: ['stable', 'research'],
      isCurrent: true
    },
    {
      id: '2',
      version: 'v1.2.2',
      title: 'Updated literature review',
      description: 'Added 12 new sources and updated citations',
      author: {
        id: '2',
        name: 'Bob Smith'
      },
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      changes: { added: 12, modified: 5, deleted: 1 },
      tags: ['draft'],
      isCurrent: false,
      parentId: '3'
    },
    {
      id: '3',
      version: 'v1.2.1',
      title: 'Initial draft completion',
      description: 'First complete draft with all major sections',
      author: {
        id: '1',
        name: 'Alice Johnson',
        avatar: '/avatars/alice.jpg'
      },
      createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      changes: { added: 45, modified: 0, deleted: 0 },
      tags: ['milestone', 'draft'],
      isCurrent: false,
      parentId: '4'
    }
  ]);

  const [branches, setBranches] = useState<Branch[]>([
    {
      id: '1',
      name: 'main',
      description: 'Main development branch',
      author: {
        id: '1',
        name: 'Alice Johnson',
        avatar: '/avatars/alice.jpg'
      },
      createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      lastCommit: new Date().toISOString(),
      isActive: true,
      isMain: true,
      commitsAhead: 0,
      commitsBehind: 0
    },
    {
      id: '2',
      name: 'feature/methodology',
      description: 'Working on new methodology section',
      author: {
        id: '2',
        name: 'Bob Smith'
      },
      createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      lastCommit: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
      isActive: false,
      isMain: false,
      commitsAhead: 3,
      commitsBehind: 1
    }
  ]);

  const [showCreateVersion, setShowCreateVersion] = useState(false);
  const [showCreateBranch, setShowCreateBranch] = useState(false);
  const [newVersionTitle, setNewVersionTitle] = useState('');
  const [newVersionDescription, setNewVersionDescription] = useState('');
  const [newVersionTags, setNewVersionTags] = useState('');
  const [newBranchName, setNewBranchName] = useState('');
  const [newBranchDescription, setNewBranchDescription] = useState('');
  const [selectedVersion, setSelectedVersion] = useState<Version | null>(null);
  const [showVersionDiff, setShowVersionDiff] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  const { toast } = useToast();

  const handleCreateVersion = async () => {
    if (!newVersionTitle.trim()) return;

    setIsCreating(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newVersion: Version = {
        id: Date.now().toString(),
        version: `v1.${versions.length + 1}.0`,
        title: newVersionTitle,
        description: newVersionDescription || undefined,
        author: {
          id: currentUserId,
          name: 'Current User'
        },
        createdAt: new Date().toISOString(),
        changes: { added: 0, modified: 0, deleted: 0 },
        tags: newVersionTags.split(',').map(tag => tag.trim()).filter(Boolean),
        isCurrent: false,
        parentId: versions.find(v => v.isCurrent)?.id
      };

      // Mark previous version as not current
      setVersions(prev => [
        newVersion,
        ...prev.map(v => ({ ...v, isCurrent: false }))
      ]);
      
      onVersionCreate?.(newVersion);
      
      setNewVersionTitle('');
      setNewVersionDescription('');
      setNewVersionTags('');
      setShowCreateVersion(false);
      
      toast({
        title: 'Version created',
        description: `Created ${newVersion.version}: ${newVersionTitle}`,
      });
    } catch (error) {
      toast({
        title: 'Failed to create version',
        description: 'Please try again later',
        variant: 'destructive'
      });
    } finally {
      setIsCreating(false);
    }
  };

  const handleCreateBranch = async () => {
    if (!newBranchName.trim()) return;

    setIsCreating(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newBranch: Branch = {
        id: Date.now().toString(),
        name: newBranchName,
        description: newBranchDescription || undefined,
        author: {
          id: currentUserId,
          name: 'Current User'
        },
        createdAt: new Date().toISOString(),
        lastCommit: new Date().toISOString(),
        isActive: false,
        isMain: false,
        commitsAhead: 0,
        commitsBehind: 0
      };

      setBranches(prev => [...prev, newBranch]);
      
      setNewBranchName('');
      setNewBranchDescription('');
      setShowCreateBranch(false);
      
      toast({
        title: 'Branch created',
        description: `Created branch: ${newBranchName}`,
      });
    } catch (error) {
      toast({
        title: 'Failed to create branch',
        description: 'Please try again later',
        variant: 'destructive'
      });
    } finally {
      setIsCreating(false);
    }
  };

  const handleRestoreVersion = async (versionId: string) => {
    const version = versions.find(v => v.id === versionId);
    if (!version) return;

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setVersions(prev => 
        prev.map(v => ({ 
          ...v, 
          isCurrent: v.id === versionId 
        }))
      );
      
      onVersionRestore?.(versionId);
      
      toast({
        title: 'Version restored',
        description: `Restored to ${version.version}: ${version.title}`,
      });
    } catch (error) {
      toast({
        title: 'Failed to restore version',
        description: 'Please try again later',
        variant: 'destructive'
      });
    }
  };

  const handleSwitchBranch = async (branchId: string) => {
    const branch = branches.find(b => b.id === branchId);
    if (!branch) return;

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setBranches(prev => 
        prev.map(b => ({ 
          ...b, 
          isActive: b.id === branchId 
        }))
      );
      
      onBranchSwitch?.(branchId);
      
      toast({
        title: 'Branch switched',
        description: `Switched to branch: ${branch.name}`,
      });
    } catch (error) {
      toast({
        title: 'Failed to switch branch',
        description: 'Please try again later',
        variant: 'destructive'
      });
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const getChangesSummary = (changes: Version['changes']) => {
    const total = changes.added + changes.modified + changes.deleted;
    if (total === 0) return 'No changes';
    
    const parts = [];
    if (changes.added > 0) parts.push(`+${changes.added}`);
    if (changes.modified > 0) parts.push(`~${changes.modified}`);
    if (changes.deleted > 0) parts.push(`-${changes.deleted}`);
    
    return parts.join(' ');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold flex items-center gap-2">
            <GitBranch className="h-6 w-6" />
            Version Control
          </h2>
          <p className="text-muted-foreground mt-1">
            Manage versions and branches for "{notebookTitle}"
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Dialog open={showCreateBranch} onOpenChange={setShowCreateBranch}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <GitBranch className="h-4 w-4 mr-2" />
                New Branch
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Branch</DialogTitle>
                <DialogDescription>
                  Create a new branch to work on features independently
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="branch-name">Branch name</Label>
                  <Input
                    id="branch-name"
                    placeholder="feature/new-section"
                    value={newBranchName}
                    onChange={(e) => setNewBranchName(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="branch-description">Description (optional)</Label>
                  <Textarea
                    id="branch-description"
                    placeholder="Describe what this branch is for..."
                    value={newBranchDescription}
                    onChange={(e) => setNewBranchDescription(e.target.value)}
                    rows={3}
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setShowCreateBranch(false)}>
                    Cancel
                  </Button>
                  <Button 
                    onClick={handleCreateBranch} 
                    disabled={!newBranchName.trim() || isCreating}
                  >
                    {isCreating ? 'Creating...' : 'Create Branch'}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
          
          <Dialog open={showCreateVersion} onOpenChange={setShowCreateVersion}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Tag className="h-4 w-4 mr-2" />
                Create Version
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Version</DialogTitle>
                <DialogDescription>
                  Save the current state as a new version
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="version-title">Version title</Label>
                  <Input
                    id="version-title"
                    placeholder="Brief description of changes"
                    value={newVersionTitle}
                    onChange={(e) => setNewVersionTitle(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="version-description">Description (optional)</Label>
                  <Textarea
                    id="version-description"
                    placeholder="Detailed description of changes..."
                    value={newVersionDescription}
                    onChange={(e) => setNewVersionDescription(e.target.value)}
                    rows={3}
                  />
                </div>
                <div>
                  <Label htmlFor="version-tags">Tags (optional)</Label>
                  <Input
                    id="version-tags"
                    placeholder="stable, draft, milestone (comma-separated)"
                    value={newVersionTags}
                    onChange={(e) => setNewVersionTags(e.target.value)}
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setShowCreateVersion(false)}>
                    Cancel
                  </Button>
                  <Button 
                    onClick={handleCreateVersion} 
                    disabled={!newVersionTitle.trim() || isCreating}
                  >
                    {isCreating ? 'Creating...' : 'Create Version'}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs defaultValue="versions" className="space-y-4">
        <TabsList>
          <TabsTrigger value="versions">Version History</TabsTrigger>
          <TabsTrigger value="branches">Branches</TabsTrigger>
          <TabsTrigger value="compare">Compare</TabsTrigger>
        </TabsList>

        <TabsContent value="versions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <History className="h-5 w-5" />
                Version History
              </CardTitle>
              <CardDescription>
                Track all changes and versions of this notebook
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <div className="space-y-4">
                  {versions.map((version, index) => (
                    <div key={version.id} className="relative">
                      {index < versions.length - 1 && (
                        <div className="absolute left-6 top-12 bottom-0 w-px bg-border" />
                      )}
                      <div className="flex items-start gap-4 p-4 rounded-lg border hover:bg-accent/50 transition-colors">
                        <div className="relative">
                          <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                            <GitCommit className="h-6 w-6 text-primary" />
                          </div>
                          {version.isCurrent && (
                            <div className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-green-500 flex items-center justify-center">
                              <Check className="h-3 w-3 text-white" />
                            </div>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant={version.isCurrent ? 'default' : 'secondary'}>
                              {version.version}
                            </Badge>
                            {version.isCurrent && (
                              <Badge variant="outline" className="text-green-600 border-green-600">
                                Current
                              </Badge>
                            )}
                            {version.tags.map(tag => (
                              <Badge key={tag} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                          <h3 className="font-medium text-foreground mb-1">
                            {version.title}
                          </h3>
                          {version.description && (
                            <p className="text-sm text-muted-foreground mb-2">
                              {version.description}
                            </p>
                          )}
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Avatar className="h-4 w-4">
                                <AvatarImage src={version.author.avatar} />
                                <AvatarFallback className="text-xs">
                                  {version.author.name.split(' ').map(n => n[0]).join('')}
                                </AvatarFallback>
                              </Avatar>
                              <span>{version.author.name}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              <span>{formatTimeAgo(version.createdAt)}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Diff className="h-3 w-3" />
                              <span>{getChangesSummary(version.changes)}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedVersion(version);
                              setShowVersionDiff(true);
                            }}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          {!version.isCurrent && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleRestoreVersion(version.id)}
                            >
                              <RotateCcw className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="branches" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <GitBranch className="h-5 w-5" />
                Branches
              </CardTitle>
              <CardDescription>
                Manage different branches of development
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {branches.map((branch) => (
                  <div key={branch.id} className="flex items-center justify-between p-4 rounded-lg border hover:bg-accent/50 transition-colors">
                    <div className="flex items-center gap-4">
                      <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                        <GitBranch className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium">{branch.name}</span>
                          {branch.isActive && (
                            <Badge variant="default" className="text-xs">
                              Active
                            </Badge>
                          )}
                          {branch.isMain && (
                            <Badge variant="outline" className="text-xs">
                              Main
                            </Badge>
                          )}
                        </div>
                        {branch.description && (
                          <p className="text-sm text-muted-foreground mb-1">
                            {branch.description}
                          </p>
                        )}
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Avatar className="h-4 w-4">
                              <AvatarImage src={branch.author.avatar} />
                              <AvatarFallback className="text-xs">
                                {branch.author.name.split(' ').map(n => n[0]).join('')}
                              </AvatarFallback>
                            </Avatar>
                            <span>{branch.author.name}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            <span>Updated {formatTimeAgo(branch.lastCommit)}</span>
                          </div>
                          {!branch.isMain && (
                            <div className="flex items-center gap-2">
                              {branch.commitsAhead > 0 && (
                                <span className="text-green-600">+{branch.commitsAhead}</span>
                              )}
                              {branch.commitsBehind > 0 && (
                                <span className="text-red-600">-{branch.commitsBehind}</span>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {!branch.isActive && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleSwitchBranch(branch.id)}
                        >
                          Switch
                        </Button>
                      )}
                      {!branch.isMain && branch.commitsAhead > 0 && (
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-green-600 border-green-600 hover:bg-green-50"
                        >
                          <GitMerge className="h-4 w-4 mr-1" />
                          Merge
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="compare" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Diff className="h-5 w-5" />
                Compare Versions
              </CardTitle>
              <CardDescription>
                Compare different versions to see what changed
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Diff className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Version Comparison</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Select two versions to compare their differences
                </p>
                <Button variant="outline">
                  <Eye className="h-4 w-4 mr-2" />
                  Select Versions to Compare
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Version Details Dialog */}
      <Dialog open={showVersionDiff} onOpenChange={setShowVersionDiff}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>
              {selectedVersion?.version}: {selectedVersion?.title}
            </DialogTitle>
            <DialogDescription>
              Version details and changes
            </DialogDescription>
          </DialogHeader>
          <ScrollArea className="h-96">
            <div className="space-y-4">
              {selectedVersion && (
                <>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Author</Label>
                      <div className="flex items-center gap-2 mt-1">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={selectedVersion.author.avatar} />
                          <AvatarFallback className="text-xs">
                            {selectedVersion.author.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-sm">{selectedVersion.author.name}</span>
                      </div>
                    </div>
                    <div>
                      <Label>Created</Label>
                      <p className="text-sm mt-1">{formatTimeAgo(selectedVersion.createdAt)}</p>
                    </div>
                  </div>
                  
                  {selectedVersion.description && (
                    <div>
                      <Label>Description</Label>
                      <p className="text-sm mt-1">{selectedVersion.description}</p>
                    </div>
                  )}
                  
                  <div>
                    <Label>Changes</Label>
                    <div className="flex items-center gap-4 mt-1 text-sm">
                      <span className="text-green-600">+{selectedVersion.changes.added} added</span>
                      <span className="text-blue-600">~{selectedVersion.changes.modified} modified</span>
                      <span className="text-red-600">-{selectedVersion.changes.deleted} deleted</span>
                    </div>
                  </div>
                  
                  {selectedVersion.tags.length > 0 && (
                    <div>
                      <Label>Tags</Label>
                      <div className="flex items-center gap-2 mt-1">
                        {selectedVersion.tags.map(tag => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  <Separator />
                  
                  <div>
                    <Label>Content Preview</Label>
                    <div className="mt-2 p-4 bg-muted rounded-lg">
                      <p className="text-sm text-muted-foreground">
                        Content diff and preview would be displayed here...
                      </p>
                    </div>
                  </div>
                </>
              )}
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default NotebookVersionControl;