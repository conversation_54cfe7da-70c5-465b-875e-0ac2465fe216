import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Users, 
  UserPlus, 
  Share2, 
  Settings, 
  Eye, 
  Edit, 
  Crown, 
  Clock, 
  MessageSquare,
  Bell,
  Link,
  Copy,
  Mail,
  Trash2,
  Shield,
  Activity
} from 'lucide-react';
import { useToast } from '@/hooks/useToast';

interface Collaborator {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'owner' | 'editor' | 'viewer';
  status: 'online' | 'offline' | 'away';
  lastActive: string;
  joinedAt: string;
}

interface ShareLink {
  id: string;
  url: string;
  permission: 'view' | 'edit';
  expiresAt?: string;
  createdAt: string;
  accessCount: number;
}

interface NotebookCollaborationProps {
  notebookId: string;
  notebookTitle: string;
  currentUserId: string;
  onCollaboratorAdded?: (collaborator: Collaborator) => void;
  onCollaboratorRemoved?: (collaboratorId: string) => void;
  onPermissionChanged?: (collaboratorId: string, newRole: string) => void;
}

const NotebookCollaboration: React.FC<NotebookCollaborationProps> = ({
  notebookId,
  notebookTitle,
  currentUserId,
  onCollaboratorAdded,
  onCollaboratorRemoved,
  onPermissionChanged
}) => {
  const [collaborators, setCollaborators] = useState<Collaborator[]>([
    {
      id: '1',
      name: 'Alice Johnson',
      email: '<EMAIL>',
      avatar: '/avatars/alice.jpg',
      role: 'owner',
      status: 'online',
      lastActive: new Date().toISOString(),
      joinedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: '2',
      name: 'Bob Smith',
      email: '<EMAIL>',
      role: 'editor',
      status: 'online',
      lastActive: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
      joinedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: '3',
      name: 'Carol Davis',
      email: '<EMAIL>',
      role: 'viewer',
      status: 'away',
      lastActive: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      joinedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
    }
  ]);

  const [shareLinks, setShareLinks] = useState<ShareLink[]>([
    {
      id: '1',
      url: 'https://app.lighthouse.com/share/abc123',
      permission: 'view',
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      accessCount: 12
    }
  ]);

  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState<'editor' | 'viewer'>('viewer');
  const [inviteMessage, setInviteMessage] = useState('');
  const [isInviting, setIsInviting] = useState(false);
  const [showShareDialog, setShowShareDialog] = useState(false);
  const [newLinkPermission, setNewLinkPermission] = useState<'view' | 'edit'>('view');
  const [linkExpiry, setLinkExpiry] = useState('7days');

  const { toast } = useToast();

  const handleInviteCollaborator = async () => {
    if (!inviteEmail.trim()) return;

    setIsInviting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newCollaborator: Collaborator = {
        id: Date.now().toString(),
        name: inviteEmail.split('@')[0],
        email: inviteEmail,
        role: inviteRole,
        status: 'offline',
        lastActive: new Date().toISOString(),
        joinedAt: new Date().toISOString()
      };

      setCollaborators(prev => [...prev, newCollaborator]);
      onCollaboratorAdded?.(newCollaborator);
      
      setInviteEmail('');
      setInviteMessage('');
      
      toast({
        title: 'Invitation sent',
        description: `Invited ${inviteEmail} as ${inviteRole}`,
      });
    } catch (error) {
      toast({
        title: 'Failed to send invitation',
        description: 'Please try again later',
        variant: 'destructive'
      });
    } finally {
      setIsInviting(false);
    }
  };

  const handleRemoveCollaborator = async (collaboratorId: string) => {
    const collaborator = collaborators.find(c => c.id === collaboratorId);
    if (!collaborator || collaborator.role === 'owner') return;

    setCollaborators(prev => prev.filter(c => c.id !== collaboratorId));
    onCollaboratorRemoved?.(collaboratorId);
    
    toast({
      title: 'Collaborator removed',
      description: `${collaborator.name} has been removed from the notebook`,
    });
  };

  const handleRoleChange = async (collaboratorId: string, newRole: 'owner' | 'editor' | 'viewer') => {
    setCollaborators(prev => 
      prev.map(c => c.id === collaboratorId ? { ...c, role: newRole } : c)
    );
    onPermissionChanged?.(collaboratorId, newRole);
    
    toast({
      title: 'Permission updated',
      description: `Role changed to ${newRole}`,
    });
  };

  const handleCreateShareLink = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const expiryDays = linkExpiry === 'never' ? null : parseInt(linkExpiry.replace('days', ''));
      const expiresAt = expiryDays ? new Date(Date.now() + expiryDays * 24 * 60 * 60 * 1000).toISOString() : undefined;
      
      const newLink: ShareLink = {
        id: Date.now().toString(),
        url: `https://app.lighthouse.com/share/${Math.random().toString(36).substr(2, 9)}`,
        permission: newLinkPermission,
        expiresAt,
        createdAt: new Date().toISOString(),
        accessCount: 0
      };

      setShareLinks(prev => [...prev, newLink]);
      
      toast({
        title: 'Share link created',
        description: 'Link copied to clipboard',
      });
      
      navigator.clipboard.writeText(newLink.url);
    } catch (error) {
      toast({
        title: 'Failed to create share link',
        description: 'Please try again later',
        variant: 'destructive'
      });
    }
  };

  const handleCopyLink = (url: string) => {
    navigator.clipboard.writeText(url);
    toast({
      title: 'Link copied',
      description: 'Share link copied to clipboard',
    });
  };

  const handleDeleteLink = (linkId: string) => {
    setShareLinks(prev => prev.filter(l => l.id !== linkId));
    toast({
      title: 'Share link deleted',
      description: 'The link is no longer accessible',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-500';
      case 'away': return 'bg-yellow-500';
      default: return 'bg-gray-400';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner': return <Crown className="h-4 w-4 text-yellow-500" />;
      case 'editor': return <Edit className="h-4 w-4 text-blue-500" />;
      default: return <Eye className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold flex items-center gap-2">
            <Users className="h-6 w-6" />
            Collaboration
          </h2>
          <p className="text-muted-foreground mt-1">
            Manage collaborators and sharing for "{notebookTitle}"
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Dialog open={showShareDialog} onOpenChange={setShowShareDialog}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Create Share Link</DialogTitle>
                <DialogDescription>
                  Generate a shareable link for this notebook
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="permission">Permission</Label>
                  <Select value={newLinkPermission} onValueChange={(value: 'view' | 'edit') => setNewLinkPermission(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="view">View only</SelectItem>
                      <SelectItem value="edit">Can edit</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="expiry">Expires</Label>
                  <Select value={linkExpiry} onValueChange={setLinkExpiry}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1days">1 day</SelectItem>
                      <SelectItem value="7days">7 days</SelectItem>
                      <SelectItem value="30days">30 days</SelectItem>
                      <SelectItem value="never">Never</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button onClick={handleCreateShareLink} className="w-full">
                  <Link className="h-4 w-4 mr-2" />
                  Create Link
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs defaultValue="collaborators" className="space-y-4">
        <TabsList>
          <TabsTrigger value="collaborators">Collaborators</TabsTrigger>
          <TabsTrigger value="share-links">Share Links</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="collaborators" className="space-y-4">
          {/* Invite Section */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <UserPlus className="h-5 w-5" />
                Invite Collaborators
              </CardTitle>
              <CardDescription>
                Invite people to collaborate on this notebook
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="email">Email address</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={inviteEmail}
                    onChange={(e) => setInviteEmail(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="role">Role</Label>
                  <Select value={inviteRole} onValueChange={(value: 'editor' | 'viewer') => setInviteRole(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="viewer">Viewer - Can view only</SelectItem>
                      <SelectItem value="editor">Editor - Can edit</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div>
                <Label htmlFor="message">Personal message (optional)</Label>
                <Textarea
                  id="message"
                  placeholder="Add a personal message to your invitation..."
                  value={inviteMessage}
                  onChange={(e) => setInviteMessage(e.target.value)}
                  rows={3}
                />
              </div>
              <Button 
                onClick={handleInviteCollaborator} 
                disabled={!inviteEmail.trim() || isInviting}
                className="w-full"
              >
                <Mail className="h-4 w-4 mr-2" />
                {isInviting ? 'Sending...' : 'Send Invitation'}
              </Button>
            </CardContent>
          </Card>

          {/* Collaborators List */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Current Collaborators</CardTitle>
              <CardDescription>
                {collaborators.length} {collaborators.length === 1 ? 'person has' : 'people have'} access to this notebook
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-64">
                <div className="space-y-3">
                  {collaborators.map((collaborator) => (
                    <div key={collaborator.id} className="flex items-center justify-between p-3 rounded-lg border">
                      <div className="flex items-center gap-3">
                        <div className="relative">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={collaborator.avatar} />
                            <AvatarFallback>
                              {collaborator.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div className={`absolute -bottom-1 -right-1 h-3 w-3 rounded-full border-2 border-background ${getStatusColor(collaborator.status)}`} />
                        </div>
                        <div>
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{collaborator.name}</span>
                            {getRoleIcon(collaborator.role)}
                            <Badge variant={collaborator.role === 'owner' ? 'default' : 'secondary'} className="text-xs">
                              {collaborator.role}
                            </Badge>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {collaborator.email} • {collaborator.status === 'online' ? 'Online' : `Last active ${formatTimeAgo(collaborator.lastActive)}`}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {collaborator.role !== 'owner' && (
                          <>
                            <Select 
                              value={collaborator.role} 
                              onValueChange={(value: 'owner' | 'editor' | 'viewer') => handleRoleChange(collaborator.id, value)}
                            >
                              <SelectTrigger className="w-24">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="viewer">Viewer</SelectItem>
                                <SelectItem value="editor">Editor</SelectItem>
                                <SelectItem value="owner">Owner</SelectItem>
                              </SelectContent>
                            </Select>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleRemoveCollaborator(collaborator.id)}
                              className="text-destructive hover:text-destructive"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="share-links" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Link className="h-5 w-5" />
                Share Links
              </CardTitle>
              <CardDescription>
                Manage shareable links for this notebook
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {shareLinks.map((link) => (
                  <div key={link.id} className="flex items-center justify-between p-4 rounded-lg border">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant={link.permission === 'edit' ? 'default' : 'secondary'}>
                          {link.permission === 'edit' ? 'Can edit' : 'View only'}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          {link.accessCount} {link.accessCount === 1 ? 'access' : 'accesses'}
                        </span>
                      </div>
                      <div className="text-sm font-mono bg-muted p-2 rounded truncate">
                        {link.url}
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        Created {formatTimeAgo(link.createdAt)}
                        {link.expiresAt && ` • Expires ${formatTimeAgo(link.expiresAt)}`}
                      </div>
                    </div>
                    <div className="flex items-center gap-2 ml-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleCopyLink(link.url)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteLink(link.id)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
                {shareLinks.length === 0 && (
                  <div className="text-center py-8">
                    <Link className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">No share links</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Create a share link to give others access to this notebook
                    </p>
                    <Button onClick={() => setShowShareDialog(true)}>
                      <Link className="h-4 w-4 mr-2" />
                      Create Share Link
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Recent Activity
              </CardTitle>
              <CardDescription>
                Track collaboration activity on this notebook
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-64">
                <div className="space-y-4">
                  {/* Mock activity data */}
                  <div className="flex items-start gap-3">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback>BS</AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <p className="text-sm">
                        <span className="font-medium">Bob Smith</span> edited the notebook
                      </p>
                      <p className="text-xs text-muted-foreground">5 minutes ago</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback>CD</AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <p className="text-sm">
                        <span className="font-medium">Carol Davis</span> joined the notebook
                      </p>
                      <p className="text-xs text-muted-foreground">1 day ago</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback>AJ</AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <p className="text-sm">
                        <span className="font-medium">Alice Johnson</span> created a share link
                      </p>
                      <p className="text-xs text-muted-foreground">2 days ago</p>
                    </div>
                  </div>
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default NotebookCollaboration;