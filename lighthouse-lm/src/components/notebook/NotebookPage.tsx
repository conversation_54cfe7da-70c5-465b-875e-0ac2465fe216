import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useNotebooks } from '../../hooks/useNotebooks';
import { useNotebookUpdate } from '../../hooks/useNotebookUpdate';
import { useSources } from '../../hooks/useSources';
import { useIsDesktop } from '../../hooks/useIsDesktop';
import InsightsAPI from '@/services/insights-api';
import '../../styles/resizable-panels.css';

// Import layout components directly to avoid barrel export issues
import LoadingSkeleton from '../layout/LoadingSkeleton';
import DesktopLayout from '../layout/DesktopLayout';
import MobileLayout from '../layout/MobileLayout';
import NotFound from '../layout/NotFound';

// Import new notebook features
import NotebookCollaboration from './NotebookCollaboration';
import NotebookVersionControl from './NotebookVersionControl';
import NotebookExport from './NotebookExport';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '../ui/tabs';
import { Button } from '../ui/button';
import { Users, GitBranch, Download, Settings } from 'lucide-react';

interface NotebookPageProps {
  notebookId?: string;
  onBack?: () => void;
}

const NotebookPage: React.FC<NotebookPageProps> = React.memo(({ notebookId: propId, onBack: propOnBack }) => {
  const params = useParams<{ id: string }>();
  const id = propId || params?.id;
  const navigate = useNavigate();
  const isDesktop = useIsDesktop();
  
  const [notebook, setNotebook] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [showNotebookFeatures, setShowNotebookFeatures] = useState(false);
  const [activeFeatureTab, setActiveFeatureTab] = useState('collaboration');
  
  const { notebooks } = useNotebooks();
  const { updateNotebook, isUpdating } = useNotebookUpdate();
  const { sources } = useSources(id);
  
  // Load notebook data
  useEffect(() => {
    const loadNotebook = async () => {
      if (!id) return;
      
      try {
        setLoading(true);
        const notebookData = await InsightsAPI.getNotebook(id);
        setNotebook(notebookData);
      } catch (error) {
      } finally {
        setLoading(false);
      }
    };
    
    loadNotebook();
  }, [id]);
  
  // Check if notebook has sources
  const hasSource = sources && sources.length > 0;

  const handleBackClick = () => {
    if (propOnBack) {
      propOnBack();
    } else {
      navigate('/lighthouse-lm');
    }
  };

  // Show loading skeleton
  if (loading) {
    return <LoadingSkeleton isDesktop={isDesktop} />;
  }

  // Show not found if notebook doesn't exist
  if (!notebook) {
    return <NotFound onBack={handleBackClick} />;
  }

  // Mobile layout
  if (!isDesktop) {
    return (
      <MobileLayout
        notebookId={id}
        notebook={notebook}
        hasSource={hasSource}
        onBack={handleBackClick}
      />
    );
  }

  // Desktop layout with resizable panels and notebook features
  return (
    <div className="flex flex-col h-full">
      {/* Notebook Features Toggle */}
      <div className="flex items-center justify-between p-4 border-b">
        <h1 className="text-xl font-semibold">{notebook.title}</h1>
        <div className="flex items-center gap-2">
          <Button
            variant={showNotebookFeatures ? "default" : "outline"}
            size="sm"
            onClick={() => setShowNotebookFeatures(!showNotebookFeatures)}
          >
            <Settings className="w-4 h-4 mr-2" />
            Notebook Features
          </Button>
        </div>
      </div>

      {/* Notebook Features Panel */}
      {showNotebookFeatures && (
        <div className="border-b bg-muted/30">
          <Tabs value={activeFeatureTab} onValueChange={setActiveFeatureTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="collaboration" className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                Collaboration
              </TabsTrigger>
              <TabsTrigger value="versions" className="flex items-center gap-2">
                <GitBranch className="w-4 h-4" />
                Versions
              </TabsTrigger>
              <TabsTrigger value="export" className="flex items-center gap-2">
                <Download className="w-4 h-4" />
                Export
              </TabsTrigger>
            </TabsList>
            <TabsContent value="collaboration" className="p-4">
              <NotebookCollaboration notebookId={id!} />
            </TabsContent>
            <TabsContent value="versions" className="p-4">
              <NotebookVersionControl notebookId={id!} />
            </TabsContent>
            <TabsContent value="export" className="p-4">
              <NotebookExport notebookId={id!} notebookTitle={notebook.title} />
            </TabsContent>
          </Tabs>
        </div>
      )}

      {/* Main Desktop Layout */}
      <div className="flex-1">
        <DesktopLayout
          notebookId={id}
          notebook={notebook}
          hasSource={hasSource}
          onBack={handleBackClick}
        />
      </div>
    </div>
  );
});

export default NotebookPage;