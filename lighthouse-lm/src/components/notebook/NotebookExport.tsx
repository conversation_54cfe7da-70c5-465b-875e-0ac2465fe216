import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Download, 
  FileText, 
  FileImage, 
  FileSpreadsheet, 
  File, 
  Globe, 
  Printer, 
  Settings, 
  Eye, 
  Share2, 
  Cloud, 
  HardDrive, 
  Mail, 
  Link, 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  X,
  RefreshCw,
  Archive,
  BookOpen,
  Code,
  Image,
  PieChart
} from 'lucide-react';
import { useToast } from '@/hooks/useToast';

interface ExportFormat {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  extension: string;
  category: 'document' | 'presentation' | 'data' | 'web' | 'archive';
  features: string[];
  premium?: boolean;
}

interface ExportTemplate {
  id: string;
  name: string;
  description: string;
  preview: string;
  category: string;
  customizable: boolean;
}

interface ExportJob {
  id: string;
  format: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  createdAt: string;
  completedAt?: string;
  downloadUrl?: string;
  error?: string;
  settings: ExportSettings;
}

interface ExportSettings {
  format: string;
  template?: string;
  includeImages: boolean;
  includeCitations: boolean;
  includeComments: boolean;
  includeMetadata: boolean;
  pageSize: 'A4' | 'Letter' | 'A3' | 'Legal';
  orientation: 'portrait' | 'landscape';
  fontSize: number;
  margins: 'narrow' | 'normal' | 'wide';
  headerFooter: boolean;
  tableOfContents: boolean;
  watermark?: string;
  password?: string;
  quality: 'low' | 'medium' | 'high';
  compression: boolean;
}

interface NotebookExportProps {
  notebookId: string;
  notebookTitle: string;
  notebookContent: any;
  onExportStart?: (settings: ExportSettings) => void;
  onExportComplete?: (job: ExportJob) => void;
}

const NotebookExport: React.FC<NotebookExportProps> = ({
  notebookId,
  notebookTitle,
  notebookContent,
  onExportStart,
  onExportComplete
}) => {
  const [exportFormats] = useState<ExportFormat[]>([
    {
      id: 'pdf',
      name: 'PDF Document',
      description: 'Portable Document Format with full formatting',
      icon: <FileText className="h-5 w-5" />,
      extension: 'pdf',
      category: 'document',
      features: ['Formatting preserved', 'Print-ready', 'Universal compatibility']
    },
    {
      id: 'docx',
      name: 'Word Document',
      description: 'Microsoft Word format for editing',
      icon: <FileText className="h-5 w-5" />,
      extension: 'docx',
      category: 'document',
      features: ['Editable', 'Comments preserved', 'Track changes']
    },
    {
      id: 'latex',
      name: 'LaTeX',
      description: 'Academic publishing format',
      icon: <Code className="h-5 w-5" />,
      extension: 'tex',
      category: 'document',
      features: ['Academic formatting', 'Mathematical equations', 'Bibliography']
    },
    {
      id: 'html',
      name: 'Web Page',
      description: 'HTML format for web publishing',
      icon: <Globe className="h-5 w-5" />,
      extension: 'html',
      category: 'web',
      features: ['Interactive elements', 'Responsive design', 'Web-ready']
    },
    {
      id: 'markdown',
      name: 'Markdown',
      description: 'Plain text with formatting syntax',
      icon: <FileText className="h-5 w-5" />,
      extension: 'md',
      category: 'document',
      features: ['Version control friendly', 'Platform independent', 'Lightweight']
    },
    {
      id: 'pptx',
      name: 'PowerPoint',
      description: 'Presentation slides',
      icon: <FileImage className="h-5 w-5" />,
      extension: 'pptx',
      category: 'presentation',
      features: ['Slide format', 'Visual presentation', 'Speaker notes'],
      premium: true
    },
    {
      id: 'epub',
      name: 'eBook (EPUB)',
      description: 'Electronic book format',
      icon: <BookOpen className="h-5 w-5" />,
      extension: 'epub',
      category: 'document',
      features: ['Reflowable text', 'Chapter navigation', 'Mobile-friendly'],
      premium: true
    },
    {
      id: 'csv',
      name: 'Data Export (CSV)',
      description: 'Structured data in CSV format',
      icon: <FileSpreadsheet className="h-5 w-5" />,
      extension: 'csv',
      category: 'data',
      features: ['Tabular data', 'Spreadsheet compatible', 'Data analysis']
    }
  ]);

  const [exportTemplates] = useState<ExportTemplate[]>([
    {
      id: 'academic',
      name: 'Academic Paper',
      description: 'Standard academic formatting with title page, abstract, and bibliography',
      preview: '/templates/academic-preview.png',
      category: 'Academic',
      customizable: true
    },
    {
      id: 'business',
      name: 'Business Report',
      description: 'Professional business document with executive summary',
      preview: '/templates/business-preview.png',
      category: 'Business',
      customizable: true
    },
    {
      id: 'minimal',
      name: 'Minimal',
      description: 'Clean, simple layout with minimal styling',
      preview: '/templates/minimal-preview.png',
      category: 'General',
      customizable: false
    },
    {
      id: 'magazine',
      name: 'Magazine Style',
      description: 'Multi-column layout with emphasis on visuals',
      preview: '/templates/magazine-preview.png',
      category: 'Creative',
      customizable: true
    }
  ]);

  const [exportJobs, setExportJobs] = useState<ExportJob[]>([
    {
      id: '1',
      format: 'pdf',
      status: 'completed',
      progress: 100,
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      completedAt: new Date(Date.now() - 2 * 60 * 60 * 1000 + 30000).toISOString(),
      downloadUrl: '/exports/notebook-v1.2.3.pdf',
      settings: {
        format: 'pdf',
        template: 'academic',
        includeImages: true,
        includeCitations: true,
        includeComments: false,
        includeMetadata: true,
        pageSize: 'A4',
        orientation: 'portrait',
        fontSize: 12,
        margins: 'normal',
        headerFooter: true,
        tableOfContents: true,
        quality: 'high',
        compression: false
      }
    },
    {
      id: '2',
      format: 'docx',
      status: 'processing',
      progress: 65,
      createdAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
      settings: {
        format: 'docx',
        includeImages: true,
        includeCitations: true,
        includeComments: true,
        includeMetadata: true,
        pageSize: 'A4',
        orientation: 'portrait',
        fontSize: 11,
        margins: 'normal',
        headerFooter: false,
        tableOfContents: false,
        quality: 'medium',
        compression: true
      }
    }
  ]);

  const [selectedFormat, setSelectedFormat] = useState<string>('pdf');
  const [selectedTemplate, setSelectedTemplate] = useState<string>('academic');
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);

  const [settings, setSettings] = useState<ExportSettings>({
    format: 'pdf',
    template: 'academic',
    includeImages: true,
    includeCitations: true,
    includeComments: false,
    includeMetadata: true,
    pageSize: 'A4',
    orientation: 'portrait',
    fontSize: 12,
    margins: 'normal',
    headerFooter: true,
    tableOfContents: true,
    quality: 'high',
    compression: false
  });

  const { toast } = useToast();

  useEffect(() => {
    setSettings(prev => ({ ...prev, format: selectedFormat }));
  }, [selectedFormat]);

  useEffect(() => {
    setSettings(prev => ({ ...prev, template: selectedTemplate }));
  }, [selectedTemplate]);

  // Simulate export job progress
  useEffect(() => {
    const interval = setInterval(() => {
      setExportJobs(prev => 
        prev.map(job => {
          if (job.status === 'processing' && job.progress < 100) {
            const newProgress = Math.min(job.progress + Math.random() * 10, 100);
            const newStatus = newProgress >= 100 ? 'completed' : 'processing';
            return {
              ...job,
              progress: newProgress,
              status: newStatus,
              completedAt: newStatus === 'completed' ? new Date().toISOString() : undefined,
              downloadUrl: newStatus === 'completed' ? `/exports/notebook-${job.id}.${getFormatExtension(job.format)}` : undefined
            };
          }
          return job;
        })
      );
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const getFormatExtension = (formatId: string) => {
    return exportFormats.find(f => f.id === formatId)?.extension || 'file';
  };

  const handleStartExport = async () => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      // Simulate export process
      const newJob: ExportJob = {
        id: Date.now().toString(),
        format: settings.format,
        status: 'processing',
        progress: 0,
        createdAt: new Date().toISOString(),
        settings: { ...settings }
      };

      setExportJobs(prev => [newJob, ...prev]);
      onExportStart?.(settings);

      // Simulate progress
      for (let i = 0; i <= 100; i += 10) {
        await new Promise(resolve => setTimeout(resolve, 200));
        setExportProgress(i);
      }

      // Complete the job
      const completedJob = {
        ...newJob,
        status: 'completed' as const,
        progress: 100,
        completedAt: new Date().toISOString(),
        downloadUrl: `/exports/notebook-${newJob.id}.${getFormatExtension(settings.format)}`
      };

      setExportJobs(prev => 
        prev.map(job => job.id === newJob.id ? completedJob : job)
      );

      onExportComplete?.(completedJob);
      setShowExportDialog(false);

      toast({
        title: 'Export completed',
        description: `Your notebook has been exported as ${settings.format.toUpperCase()}`,
      });
    } catch (error) {
      toast({
        title: 'Export failed',
        description: 'Please try again later',
        variant: 'destructive'
      });
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  const handleDownload = (job: ExportJob) => {
    if (job.downloadUrl) {
      // Simulate download
      const link = document.createElement('a');
      link.href = job.downloadUrl;
      link.download = `${notebookTitle}.${getFormatExtension(job.format)}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: 'Download started',
        description: `Downloading ${notebookTitle}.${getFormatExtension(job.format)}`,
      });
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const getStatusIcon = (status: ExportJob['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'processing':
        return <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-yellow-600" />;
    }
  };

  const getStatusColor = (status: ExportJob['status']) => {
    switch (status) {
      case 'completed':
        return 'text-green-600';
      case 'processing':
        return 'text-blue-600';
      case 'failed':
        return 'text-red-600';
      default:
        return 'text-yellow-600';
    }
  };

  const selectedFormatData = exportFormats.find(f => f.id === selectedFormat);
  const selectedTemplateData = exportTemplates.find(t => t.id === selectedTemplate);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold flex items-center gap-2">
            <Download className="h-6 w-6" />
            Export Notebook
          </h2>
          <p className="text-muted-foreground mt-1">
            Export "{notebookTitle}" in various formats
          </p>
        </div>
        <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
          <DialogTrigger asChild>
            <Button>
              <Download className="h-4 w-4 mr-2" />
              Export Now
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[80vh]">
            <DialogHeader>
              <DialogTitle>Export Notebook</DialogTitle>
              <DialogDescription>
                Configure export settings for "{notebookTitle}"
              </DialogDescription>
            </DialogHeader>
            <ScrollArea className="h-96">
              <div className="space-y-6 pr-4">
                {/* Format Selection */}
                <div>
                  <Label className="text-base font-medium">Export Format</Label>
                  <div className="grid grid-cols-2 gap-3 mt-3">
                    {exportFormats.map((format) => (
                      <div
                        key={format.id}
                        className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                          selectedFormat === format.id
                            ? 'border-primary bg-primary/5'
                            : 'border-border hover:bg-accent/50'
                        }`}
                        onClick={() => setSelectedFormat(format.id)}
                      >
                        <div className="flex items-center gap-2 mb-2">
                          {format.icon}
                          <span className="font-medium text-sm">{format.name}</span>
                          {format.premium && (
                            <Badge variant="secondary" className="text-xs">Pro</Badge>
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground mb-2">
                          {format.description}
                        </p>
                        <div className="flex flex-wrap gap-1">
                          {format.features.slice(0, 2).map((feature) => (
                            <Badge key={feature} variant="outline" className="text-xs">
                              {feature}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Template Selection */}
                {selectedFormatData?.category === 'document' && (
                  <div>
                    <Label className="text-base font-medium">Template</Label>
                    <div className="grid grid-cols-2 gap-3 mt-3">
                      {exportTemplates.map((template) => (
                        <div
                          key={template.id}
                          className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                            selectedTemplate === template.id
                              ? 'border-primary bg-primary/5'
                              : 'border-border hover:bg-accent/50'
                          }`}
                          onClick={() => setSelectedTemplate(template.id)}
                        >
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-medium text-sm">{template.name}</span>
                            <Badge variant="outline" className="text-xs">
                              {template.category}
                            </Badge>
                          </div>
                          <p className="text-xs text-muted-foreground">
                            {template.description}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Basic Settings */}
                <div className="space-y-4">
                  <Label className="text-base font-medium">Include Content</Label>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include-images"
                        checked={settings.includeImages}
                        onCheckedChange={(checked) => 
                          setSettings(prev => ({ ...prev, includeImages: !!checked }))
                        }
                      />
                      <Label htmlFor="include-images" className="text-sm">Images</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include-citations"
                        checked={settings.includeCitations}
                        onCheckedChange={(checked) => 
                          setSettings(prev => ({ ...prev, includeCitations: !!checked }))
                        }
                      />
                      <Label htmlFor="include-citations" className="text-sm">Citations</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include-comments"
                        checked={settings.includeComments}
                        onCheckedChange={(checked) => 
                          setSettings(prev => ({ ...prev, includeComments: !!checked }))
                        }
                      />
                      <Label htmlFor="include-comments" className="text-sm">Comments</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include-metadata"
                        checked={settings.includeMetadata}
                        onCheckedChange={(checked) => 
                          setSettings(prev => ({ ...prev, includeMetadata: !!checked }))
                        }
                      />
                      <Label htmlFor="include-metadata" className="text-sm">Metadata</Label>
                    </div>
                  </div>
                </div>

                {/* Advanced Settings Toggle */}
                <div>
                  <Button
                    variant="outline"
                    onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
                    className="w-full"
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    {showAdvancedSettings ? 'Hide' : 'Show'} Advanced Settings
                  </Button>
                </div>

                {/* Advanced Settings */}
                {showAdvancedSettings && (
                  <div className="space-y-4 p-4 bg-accent/20 rounded-lg">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="page-size">Page Size</Label>
                        <Select
                          value={settings.pageSize}
                          onValueChange={(value: any) => 
                            setSettings(prev => ({ ...prev, pageSize: value }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="A4">A4</SelectItem>
                            <SelectItem value="Letter">Letter</SelectItem>
                            <SelectItem value="A3">A3</SelectItem>
                            <SelectItem value="Legal">Legal</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="orientation">Orientation</Label>
                        <RadioGroup
                          value={settings.orientation}
                          onValueChange={(value: any) => 
                            setSettings(prev => ({ ...prev, orientation: value }))
                          }
                          className="flex gap-4 mt-2"
                        >
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="portrait" id="portrait" />
                            <Label htmlFor="portrait" className="text-sm">Portrait</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="landscape" id="landscape" />
                            <Label htmlFor="landscape" className="text-sm">Landscape</Label>
                          </div>
                        </RadioGroup>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="font-size">Font Size</Label>
                        <Input
                          id="font-size"
                          type="number"
                          min="8"
                          max="24"
                          value={settings.fontSize}
                          onChange={(e) => 
                            setSettings(prev => ({ ...prev, fontSize: parseInt(e.target.value) || 12 }))
                          }
                        />
                      </div>
                      <div>
                        <Label htmlFor="margins">Margins</Label>
                        <Select
                          value={settings.margins}
                          onValueChange={(value: any) => 
                            setSettings(prev => ({ ...prev, margins: value }))
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="narrow">Narrow</SelectItem>
                            <SelectItem value="normal">Normal</SelectItem>
                            <SelectItem value="wide">Wide</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="header-footer"
                          checked={settings.headerFooter}
                          onCheckedChange={(checked) => 
                            setSettings(prev => ({ ...prev, headerFooter: !!checked }))
                          }
                        />
                        <Label htmlFor="header-footer" className="text-sm">Header & Footer</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="table-of-contents"
                          checked={settings.tableOfContents}
                          onCheckedChange={(checked) => 
                            setSettings(prev => ({ ...prev, tableOfContents: !!checked }))
                          }
                        />
                        <Label htmlFor="table-of-contents" className="text-sm">Table of Contents</Label>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="watermark">Watermark (optional)</Label>
                      <Input
                        id="watermark"
                        placeholder="Enter watermark text"
                        value={settings.watermark || ''}
                        onChange={(e) => 
                          setSettings(prev => ({ ...prev, watermark: e.target.value || undefined }))
                        }
                      />
                    </div>

                    <div>
                      <Label htmlFor="quality">Export Quality</Label>
                      <RadioGroup
                        value={settings.quality}
                        onValueChange={(value: any) => 
                          setSettings(prev => ({ ...prev, quality: value }))
                        }
                        className="flex gap-4 mt-2"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="low" id="low" />
                          <Label htmlFor="low" className="text-sm">Low</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="medium" id="medium" />
                          <Label htmlFor="medium" className="text-sm">Medium</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="high" id="high" />
                          <Label htmlFor="high" className="text-sm">High</Label>
                        </div>
                      </RadioGroup>
                    </div>
                  </div>
                )}

                {/* Export Progress */}
                {isExporting && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label>Exporting...</Label>
                      <span className="text-sm text-muted-foreground">{exportProgress}%</span>
                    </div>
                    <Progress value={exportProgress} className="w-full" />
                  </div>
                )}
              </div>
            </ScrollArea>
            <div className="flex justify-end gap-2 pt-4 border-t">
              <Button variant="outline" onClick={() => setShowExportDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleStartExport} disabled={isExporting}>
                {isExporting ? 'Exporting...' : 'Start Export'}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <Tabs defaultValue="formats" className="space-y-4">
        <TabsList>
          <TabsTrigger value="formats">Export Formats</TabsTrigger>
          <TabsTrigger value="history">Export History</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="formats" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {exportFormats.map((format) => (
              <Card key={format.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {format.icon}
                      <CardTitle className="text-lg">{format.name}</CardTitle>
                    </div>
                    {format.premium && (
                      <Badge variant="secondary">Pro</Badge>
                    )}
                  </div>
                  <CardDescription>{format.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex flex-wrap gap-1">
                      {format.features.map((feature) => (
                        <Badge key={feature} variant="outline" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                    </div>
                    <Button 
                      className="w-full" 
                      variant="outline"
                      onClick={() => {
                        setSelectedFormat(format.id);
                        setShowExportDialog(true);
                      }}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Export as {format.extension.toUpperCase()}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Archive className="h-5 w-5" />
                Export History
              </CardTitle>
              <CardDescription>
                View and download previous exports
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {exportJobs.map((job) => (
                  <div key={job.id} className="flex items-center justify-between p-4 rounded-lg border">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(job.status)}
                        <span className={`text-sm font-medium ${getStatusColor(job.status)}`}>
                          {job.status.charAt(0).toUpperCase() + job.status.slice(1)}
                        </span>
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">
                            {notebookTitle}.{getFormatExtension(job.format)}
                          </span>
                          <Badge variant="outline" className="text-xs">
                            {job.format.toUpperCase()}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Created {formatTimeAgo(job.createdAt)}
                          {job.completedAt && ` • Completed ${formatTimeAgo(job.completedAt)}`}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {job.status === 'processing' && (
                        <div className="flex items-center gap-2">
                          <Progress value={job.progress} className="w-20" />
                          <span className="text-sm text-muted-foreground">{Math.round(job.progress)}%</span>
                        </div>
                      )}
                      {job.status === 'completed' && job.downloadUrl && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDownload(job)}
                        >
                          <Download className="h-4 w-4 mr-1" />
                          Download
                        </Button>
                      )}
                      {job.status === 'failed' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedFormat(job.format);
                            setShowExportDialog(true);
                          }}
                        >
                          <RefreshCw className="h-4 w-4 mr-1" />
                          Retry
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {exportTemplates.map((template) => (
              <Card key={template.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{template.name}</CardTitle>
                    <Badge variant="outline">{template.category}</Badge>
                  </div>
                  <CardDescription>{template.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="aspect-video bg-muted rounded-lg flex items-center justify-center">
                      <Image className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <div className="flex gap-2">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="flex-1"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        Preview
                      </Button>
                      <Button 
                        size="sm" 
                        className="flex-1"
                        onClick={() => {
                          setSelectedTemplate(template.id);
                          setShowExportDialog(true);
                        }}
                      >
                        Use Template
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default NotebookExport;