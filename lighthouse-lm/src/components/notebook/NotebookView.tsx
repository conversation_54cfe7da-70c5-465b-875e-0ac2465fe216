import React from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { ArrowLeft, BookOpen, Plus, FileText, MessageCircle, Edit3 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const NotebookView = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Enhanced Header with better visual hierarchy */}
      <header className="bg-background/95 backdrop-blur-sm border-b border-border/50 px-6 py-5 sticky top-0 z-50 shadow-sm">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/')}
              className="flex items-center space-x-2 text-muted-foreground hover:text-foreground transition-all duration-300 hover:scale-105 hover:bg-accent active:scale-95 focus:ring-2 focus:ring-ring focus:ring-offset-2 group"
            >
              <ArrowLeft className="h-4 w-4 transition-all duration-300 group-hover:-translate-x-1" />
              <span className="hidden sm:inline transition-all duration-300 group-hover:tracking-wide">Back to Dashboard</span>
            </Button>
            
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <BookOpen className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-semibold text-foreground tracking-tight">Notebook {id}</h1>
                <p className="text-sm text-muted-foreground">Research & Writing Workspace</p>
              </div>
            </div>
          </div>
          
          <Badge variant="secondary" className="hidden sm:flex">
            Getting Started
          </Badge>
        </div>
      </header>
      
      {/* Main Content with improved visual hierarchy */}
      <main className="max-w-7xl mx-auto px-6 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-2xl mb-6">
            <BookOpen className="h-8 w-8 text-primary" />
          </div>
          <h2 className="text-4xl font-bold text-foreground mb-4 tracking-tight">
            Welcome to Your Notebook
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
            Transform your research into insights with AI-powered analysis and writing assistance
          </p>
        </div>

        {/* Action Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          <Card className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-2 hover:border-primary/20">
            <CardHeader className="text-center pb-4">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-xl mb-4 group-hover:scale-110 transition-transform duration-300">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
              <CardTitle className="text-xl font-semibold">Add Sources</CardTitle>
              <CardDescription className="text-base">
                Upload documents, add web links, or paste text to get started
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full group-hover:bg-primary/90 transition-colors duration-300">
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Source
              </Button>
            </CardContent>
          </Card>

          <Card className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-2 hover:border-primary/20">
            <CardHeader className="text-center pb-4">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-xl mb-4 group-hover:scale-110 transition-transform duration-300">
                <MessageCircle className="h-6 w-6 text-green-600" />
              </div>
              <CardTitle className="text-xl font-semibold">Start Chatting</CardTitle>
              <CardDescription className="text-base">
                Ask questions about your sources and get AI-powered insights
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="outline" className="w-full group-hover:bg-accent transition-colors duration-300">
                <MessageCircle className="h-4 w-4 mr-2" />
                Open Chat Interface
              </Button>
            </CardContent>
          </Card>

          <Card className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-2 hover:border-primary/20">
            <CardHeader className="text-center pb-4">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-xl mb-4 group-hover:scale-110 transition-transform duration-300">
                <Edit3 className="h-6 w-6 text-purple-600" />
              </div>
              <CardTitle className="text-xl font-semibold">Writing Studio</CardTitle>
              <CardDescription className="text-base">
                Create structured notes and documents with AI assistance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="outline" className="w-full group-hover:bg-accent transition-colors duration-300">
                <Edit3 className="h-4 w-4 mr-2" />
                Open Writing Studio
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Getting Started Tips */}
        <Card className="bg-gradient-to-r from-primary/5 via-primary/3 to-transparent border-primary/20">
          <CardHeader>
            <CardTitle className="text-2xl font-semibold flex items-center space-x-2">
              <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                <span className="text-primary-foreground text-sm font-bold">!</span>
              </div>
              <span>Quick Start Tips</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-semibold text-foreground">Best Practices:</h4>
                <ul className="space-y-2 text-muted-foreground">
                  <li className="flex items-start space-x-2">
                    <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                    <span>Upload multiple sources for richer insights</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                    <span>Ask specific questions to get targeted answers</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                    <span>Use the studio to organize your findings</span>
                  </li>
                </ul>
              </div>
              <div className="space-y-4">
                <h4 className="font-semibold text-foreground">Supported Formats:</h4>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="secondary">PDF</Badge>
                  <Badge variant="secondary">DOCX</Badge>
                  <Badge variant="secondary">TXT</Badge>
                  <Badge variant="secondary">Web URLs</Badge>
                  <Badge variant="secondary">Plain Text</Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
};

export default NotebookView;