/**
 * Bulk Import Processor - Batch processing with progress tracking
 * Enterprise bulk import with parallel processing and real-time progress
 */

import React, { useState, useEffect, useCallback } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../ui/card';
import { Button } from '../ui/button';
import { Progress } from '../ui/progress';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { ScrollArea } from '../ui/scroll-area';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../ui/alert-dialog';
import {
  Play,
  Pause,
  Square,
  RefreshCw,
  AlertTriangle,
  CheckCircle2,
  XCircle,
  Clock,
  Activity,
  HardDrive,
  Zap,
  Users,
  FileText,
  Database,
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { toast } from 'sonner';
import {
  BulkImportProcessorProps,
  BulkImportSession,
  ImportJob,
  ImportStatus,
  ProcessingStats,
} from './types';
import { cn } from '../../lib/utils';

export const BulkImportProcessor: React.FC<BulkImportProcessorProps> = ({
  session,
  onSessionUpdate,
  onJobComplete,
  onSessionComplete,
  onError,
  className,
}) => {
  const [currentSession, setCurrentSession] = useState<BulkImportSession>(session);
  const [processingStats, setProcessingStats] = useState<ProcessingStats>({
    items_processed: 0,
    items_per_second: 0,
    estimated_completion: '',
    memory_usage: 0,
    disk_usage: 0,
    errors_count: 0,
    warnings_count: 0,
  });
  
  const [showRollbackDialog, setShowRollbackDialog] = useState(false);
  const [isRollingBack, setIsRollingBack] = useState(false);

  // Update session when prop changes
  useEffect(() => {
    setCurrentSession(session);
  }, [session]);

  // Listen for real-time updates
  useEffect(() => {
    const unlisteners: (() => void)[] = [];

    const setupEventListeners = async () => {
      // Session progress updates
      const unlistenProgress = await listen(`bulk-import-progress-${session.id}`, (event: any) => {
        const { sessionUpdate, stats } = event.payload;
        setCurrentSession(sessionUpdate);
        setProcessingStats(stats);
        onSessionUpdate(sessionUpdate);
      });

      // Job completion events
      const unlistenJobComplete = await listen(`import-job-complete-${session.id}`, (event: any) => {
        const { jobId } = event.payload;
        onJobComplete(jobId);
      });

      // Session completion event
      const unlistenSessionComplete = await listen(`bulk-import-complete-${session.id}`, (event: any) => {
        onSessionComplete();
        toast.success('Bulk import session completed');
      });

      // Error events
      const unlistenError = await listen(`bulk-import-error-${session.id}`, (event: any) => {
        const { error } = event.payload;
        onError(error);
        toast.error(`Import error: ${error}`);
      });

      unlisteners.push(unlistenProgress, unlistenJobComplete, unlistenSessionComplete, unlistenError);
    };

    setupEventListeners();

    return () => {
      unlisteners.forEach(unlisten => unlisten());
    };
  }, [session.id, onSessionUpdate, onJobComplete, onSessionComplete, onError]);

  const handlePause = async () => {
    try {
      await invoke('pause_bulk_import_session', { sessionId: currentSession.id });
      toast.success('Import session paused');
    } catch (error) {
      console.error('Failed to pause session:', error);
      toast.error('Failed to pause session');
    }
  };

  const handleResume = async () => {
    try {
      await invoke('resume_bulk_import_session', { sessionId: currentSession.id });
      toast.success('Import session resumed');
    } catch (error) {
      console.error('Failed to resume session:', error);
      toast.error('Failed to resume session');
    }
  };

  const handleCancel = async () => {
    try {
      await invoke('cancel_bulk_import_session', { sessionId: currentSession.id });
      toast.success('Import session cancelled');
    } catch (error) {
      console.error('Failed to cancel session:', error);
      toast.error('Failed to cancel session');
    }
  };

  const handleRollback = async () => {
    if (!currentSession.rollback_info?.is_available) {
      toast.error('Rollback is not available for this session');
      return;
    }

    setIsRollingBack(true);
    try {
      await invoke('rollback_bulk_import_session', { 
        sessionId: currentSession.id,
        rollbackId: currentSession.rollback_info.rollback_id 
      });
      setShowRollbackDialog(false);
      toast.success('Import session rolled back successfully');
    } catch (error) {
      console.error('Failed to rollback session:', error);
      toast.error('Failed to rollback session');
    } finally {
      setIsRollingBack(false);
    }
  };

  const getStatusIcon = (status: ImportStatus) => {
    switch (status) {
      case 'completed':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'processing':
      case 'validating':
      case 'transforming':
      case 'importing':
        return <Activity className="h-4 w-4 text-blue-500 animate-pulse" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-gray-500" />;
      case 'cancelled':
        return <Square className="h-4 w-4 text-gray-500" />;
      case 'partial':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: ImportStatus): string => {
    switch (status) {
      case 'completed':
        return 'bg-green-500';
      case 'failed':
        return 'bg-red-500';
      case 'processing':
      case 'validating':
      case 'transforming':
      case 'importing':
        return 'bg-blue-500';
      case 'pending':
        return 'bg-gray-300';
      case 'cancelled':
        return 'bg-gray-500';
      case 'partial':
        return 'bg-orange-500';
      default:
        return 'bg-gray-400';
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getOverallProgress = () => {
    return currentSession.total_jobs > 0 
      ? (currentSession.completed_jobs / currentSession.total_jobs) * 100 
      : 0;
  };

  const isProcessing = () => {
    return currentSession.status === 'processing';
  };

  const canPause = () => {
    return isProcessing();
  };

  const canResume = () => {
    return currentSession.status === 'paused';
  };

  const canCancel = () => {
    return ['preparing', 'processing', 'paused'].includes(currentSession.status);
  };

  const canRollback = () => {
    return currentSession.rollback_info?.is_available && 
           ['completed', 'failed', 'cancelled'].includes(currentSession.status);
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Database className="h-5 w-5" />
                <span>{currentSession.name}</span>
                <Badge variant="outline" className="capitalize">
                  {currentSession.status.replace('_', ' ')}
                </Badge>
              </CardTitle>
              <CardDescription>
                {currentSession.description || 'Bulk import processing session'}
              </CardDescription>
            </div>

            <div className="flex space-x-2">
              {canPause() && (
                <Button variant="outline" size="sm" onClick={handlePause}>
                  <Pause className="h-4 w-4" />
                </Button>
              )}
              
              {canResume() && (
                <Button variant="outline" size="sm" onClick={handleResume}>
                  <Play className="h-4 w-4" />
                </Button>
              )}
              
              {canCancel() && (
                <Button variant="outline" size="sm" onClick={handleCancel}>
                  <Square className="h-4 w-4" />
                </Button>
              )}
              
              {canRollback() && (
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => setShowRollbackDialog(true)}
                  className="text-orange-600 hover:text-orange-700"
                >
                  <RefreshCw className="h-4 w-4" />
                  Rollback
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Overall Progress */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Overall Progress</span>
              <span>{currentSession.completed_jobs} / {currentSession.total_jobs} jobs</span>
            </div>
            <Progress value={getOverallProgress()} className="h-2" />
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-lg font-semibold text-green-600">
                {currentSession.completed_jobs}
              </p>
              <p className="text-sm text-muted-foreground">Completed</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-semibold text-red-600">
                {currentSession.failed_jobs}
              </p>
              <p className="text-sm text-muted-foreground">Failed</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-semibold text-blue-600">
                {processingStats.items_per_second.toFixed(1)}
              </p>
              <p className="text-sm text-muted-foreground">Items/sec</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-semibold">
                {formatBytes(processingStats.memory_usage)}
              </p>
              <p className="text-sm text-muted-foreground">Memory</p>
            </div>
          </div>

          {/* Session Info */}
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>Created {formatDistanceToNow(new Date(currentSession.created_at), { addSuffix: true })}</span>
            <span>Updated {formatDistanceToNow(new Date(currentSession.updated_at), { addSuffix: true })}</span>
          </div>
        </CardContent>
      </Card>

      {/* Jobs List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Import Jobs</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <ScrollArea className="h-96">
            <div className="space-y-0">
              {currentSession.jobs.map((job, index) => (
                <div key={job.id}>
                  <div className="flex items-center space-x-4 p-4 hover:bg-muted/50">
                    <div className="flex-shrink-0">
                      {getStatusIcon(job.status)}
                    </div>

                    <div className="flex-1 min-w-0 space-y-1">
                      <div className="flex items-center space-x-2">
                        <p className="text-sm font-medium truncate">{job.name}</p>
                        <Badge variant="secondary" className="text-xs capitalize">
                          {job.format}
                        </Badge>
                      </div>
                      
                      {job.description && (
                        <p className="text-xs text-muted-foreground truncate">
                          {job.description}
                        </p>
                      )}

                      {/* Progress for active jobs */}
                      {job.status === 'processing' && (
                        <div className="space-y-1">
                          <Progress value={job.progress} className="h-1" />
                          <p className="text-xs text-muted-foreground">
                            {job.progress}% complete
                          </p>
                        </div>
                      )}

                      {/* Error message */}
                      {job.error_message && (
                        <p className="text-xs text-red-600 truncate">
                          {job.error_message}
                        </p>
                      )}

                      {/* Imported items count */}
                      {job.imported_items.length > 0 && (
                        <p className="text-xs text-muted-foreground">
                          {job.imported_items.length} items imported
                        </p>
                      )}
                    </div>

                    <div className="flex items-center space-x-2">
                      <div className={cn(
                        "w-2 h-2 rounded-full",
                        getStatusColor(job.status)
                      )} />
                      
                      <div className="text-right">
                        <p className="text-sm font-medium">{job.progress}%</p>
                        {job.started_at && (
                          <p className="text-xs text-muted-foreground">
                            {formatDistanceToNow(new Date(job.started_at), { addSuffix: true })}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {index < currentSession.jobs.length - 1 && <Separator />}
                </div>
              ))}

              {currentSession.jobs.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <p>No jobs in this session</p>
                </div>
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Processing Stats */}
      {isProcessing() && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Zap className="h-5 w-5" />
              <span>Real-time Statistics</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <p className="text-sm text-muted-foreground">Items Processed</p>
              <p className="text-lg font-semibold">{processingStats.items_processed.toLocaleString()}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Processing Speed</p>
              <p className="text-lg font-semibold">{processingStats.items_per_second.toFixed(1)}/s</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">ETA</p>
              <p className="text-lg font-semibold">{processingStats.estimated_completion}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Disk Usage</p>
              <p className="text-lg font-semibold">{formatBytes(processingStats.disk_usage)}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Rollback Dialog */}
      <AlertDialog open={showRollbackDialog} onOpenChange={setShowRollbackDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              <span>Rollback Import Session</span>
            </AlertDialogTitle>
            <AlertDialogDescription>
              This will undo all changes made during this import session and restore your workspace 
              to its previous state. This action cannot be undone.
              
              {currentSession.rollback_info && (
                <div className="mt-4 p-3 bg-muted rounded-md">
                  <p className="text-sm font-medium">Rollback Information:</p>
                  <p className="text-sm text-muted-foreground">
                    Restore point: {formatDistanceToNow(new Date(currentSession.rollback_info.created_at), { addSuffix: true })}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Affected items: {currentSession.rollback_info.affected_items.length}
                  </p>
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isRollingBack}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleRollback}
              disabled={isRollingBack}
              className="bg-orange-600 hover:bg-orange-700"
            >
              {isRollingBack ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Rolling Back...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Rollback Session
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};