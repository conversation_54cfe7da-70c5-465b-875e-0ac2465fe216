/**
 * Export Dialog - Comprehensive export options interface
 * Enterprise data export with format options, customization, and preview
 */

import React, { useState, useEffect } from 'react';
import { save } from '@tauri-apps/plugin-dialog';
import { invoke } from '@tauri-apps/api/core';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '../ui/tabs';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Switch } from '../ui/switch';
import { Badge } from '../ui/badge';
import { Checkbox } from '../ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { Separator } from '../ui/separator';
import { ScrollArea } from '../ui/scroll-area';
import { Slider } from '../ui/slider';
import {
  Download,
  FileText,
  Image,
  Settings,
  Eye,
  Package,
  Shield,
  Palette,
  Layout,
  Mail,
  Cloud,
  HardDrive,
  Clock,
  CheckCircle2,
  AlertTriangle,
} from 'lucide-react';
import { toast } from 'sonner';
import { useTauri } from '../../contexts/TauriContext';
import {
  ExportDialogProps,
  ExportJob,
  ExportFormat,
  ExportOptions,
  ExportPreview,
} from './types';
import { cn } from '../../lib/utils';

const EXPORT_FORMAT_INFO: Record<ExportFormat, { 
  name: string; 
  icon: React.ReactNode; 
  description: string;
  extensions: string[];
  category: 'document' | 'note' | 'data' | 'web' | 'academic';
  features: string[];
}> = {
  pdf: { 
    name: 'PDF', 
    icon: <FileText className="h-4 w-4" />, 
    description: 'Portable Document Format',
    extensions: ['.pdf'],
    category: 'document',
    features: ['pagination', 'formatting', 'images', 'toc']
  },
  docx: { 
    name: 'Word', 
    icon: <FileText className="h-4 w-4" />, 
    description: 'Microsoft Word document',
    extensions: ['.docx'],
    category: 'document',
    features: ['formatting', 'images', 'comments', 'toc']
  },
  html: { 
    name: 'HTML', 
    icon: <FileText className="h-4 w-4" />, 
    description: 'Web page format',
    extensions: ['.html'],
    category: 'web',
    features: ['formatting', 'images', 'links', 'css']
  },
  markdown: { 
    name: 'Markdown', 
    icon: <FileText className="h-4 w-4" />, 
    description: 'Markdown format',
    extensions: ['.md'],
    category: 'document',
    features: ['links', 'images', 'formatting']
  },
  csv: { 
    name: 'CSV', 
    icon: <FileText className="h-4 w-4" />, 
    description: 'Comma-separated values',
    extensions: ['.csv'],
    category: 'data',
    features: ['structured', 'metadata']
  },
  json: { 
    name: 'JSON', 
    icon: <FileText className="h-4 w-4" />, 
    description: 'JavaScript Object Notation',
    extensions: ['.json'],
    category: 'data',
    features: ['structured', 'metadata', 'relationships']
  },
  xml: { 
    name: 'XML', 
    icon: <FileText className="h-4 w-4" />, 
    description: 'Extensible Markup Language',
    extensions: ['.xml'],
    category: 'data',
    features: ['structured', 'metadata', 'schema']
  },
  epub: { 
    name: 'EPUB', 
    icon: <FileText className="h-4 w-4" />, 
    description: 'Electronic publication',
    extensions: ['.epub'],
    category: 'document',
    features: ['toc', 'images', 'chapters', 'formatting']
  },
  latex: { 
    name: 'LaTeX', 
    icon: <FileText className="h-4 w-4" />, 
    description: 'LaTeX document',
    extensions: ['.tex'],
    category: 'academic',
    features: ['formatting', 'math', 'citations', 'bibliography']
  },
  notion: { 
    name: 'Notion', 
    icon: <Package className="h-4 w-4" />, 
    description: 'Notion import format',
    extensions: ['.zip'],
    category: 'note',
    features: ['structured', 'metadata', 'relationships']
  },
  obsidian: { 
    name: 'Obsidian', 
    icon: <Package className="h-4 w-4" />, 
    description: 'Obsidian vault',
    extensions: ['.zip'],
    category: 'note',
    features: ['links', 'graph', 'metadata']
  },
  roam: { 
    name: 'Roam Research', 
    icon: <Package className="h-4 w-4" />, 
    description: 'Roam Research format',
    extensions: ['.json'],
    category: 'note',
    features: ['links', 'blocks', 'metadata']
  },
  bear: { 
    name: 'Bear', 
    icon: <Package className="h-4 w-4" />, 
    description: 'Bear notes format',
    extensions: ['.bearbk'],
    category: 'note',
    features: ['tags', 'formatting', 'images']
  },
  ulysses: { 
    name: 'Ulysses', 
    icon: <Package className="h-4 w-4" />, 
    description: 'Ulysses format',
    extensions: ['.ulysses'],
    category: 'note',
    features: ['sheets', 'goals', 'formatting']
  },
  devonthink: { 
    name: 'DEVONthink', 
    icon: <Package className="h-4 w-4" />, 
    description: 'DEVONthink format',
    extensions: ['.dtBase2'],
    category: 'note',
    features: ['ai', 'metadata', 'relationships']
  },
  evernote: { 
    name: 'Evernote', 
    icon: <Package className="h-4 w-4" />, 
    description: 'Evernote export',
    extensions: ['.enex'],
    category: 'note',
    features: ['attachments', 'formatting', 'metadata']
  },
  onenote: { 
    name: 'OneNote', 
    icon: <Package className="h-4 w-4" />, 
    description: 'Microsoft OneNote',
    extensions: ['.onepkg'],
    category: 'note',
    features: ['sections', 'formatting', 'images']
  },
  confluence: { 
    name: 'Confluence', 
    icon: <Package className="h-4 w-4" />, 
    description: 'Atlassian Confluence',
    extensions: ['.zip'],
    category: 'document',
    features: ['pages', 'attachments', 'comments']
  },
  google_docs: { 
    name: 'Google Docs', 
    icon: <Package className="h-4 w-4" />, 
    description: 'Google Docs format',
    extensions: ['.zip'],
    category: 'document',
    features: ['collaboration', 'comments', 'formatting']
  },
  dropbox_paper: { 
    name: 'Dropbox Paper', 
    icon: <Package className="h-4 w-4" />, 
    description: 'Dropbox Paper format',
    extensions: ['.zip'],
    category: 'document',
    features: ['collaboration', 'comments', 'formatting']
  },
  zotero: { 
    name: 'Zotero', 
    icon: <FileText className="h-4 w-4" />, 
    description: 'Zotero library',
    extensions: ['.rdf'],
    category: 'academic',
    features: ['citations', 'bibliography', 'attachments']
  },
  mendeley: { 
    name: 'Mendeley', 
    icon: <FileText className="h-4 w-4" />, 
    description: 'Mendeley library',
    extensions: ['.bib'],
    category: 'academic',
    features: ['citations', 'bibliography', 'annotations']
  },
  endnote: { 
    name: 'EndNote', 
    icon: <FileText className="h-4 w-4" />, 
    description: 'EndNote library',
    extensions: ['.enl'],
    category: 'academic',
    features: ['citations', 'bibliography', 'full-text']
  },
  bibtex: { 
    name: 'BibTeX', 
    icon: <FileText className="h-4 w-4" />, 
    description: 'BibTeX bibliography',
    extensions: ['.bib'],
    category: 'academic',
    features: ['citations', 'bibliography']
  },
  ris: { 
    name: 'RIS', 
    icon: <FileText className="h-4 w-4" />, 
    description: 'Research Information Systems',
    extensions: ['.ris'],
    category: 'academic',
    features: ['citations', 'metadata']
  },
};

export const ExportDialog: React.FC<ExportDialogProps> = ({
  isOpen,
  onClose,
  onExportStart,
  workspaceId,
  preSelectedNotebooks = [],
  preSelectedSources = [],
  supportedFormats,
  className,
}) => {
  const { isInitialized } = useTauri();
  
  const [step, setStep] = useState<'selection' | 'format' | 'options' | 'preview'>('selection');
  const [selectedFormat, setSelectedFormat] = useState<ExportFormat>('pdf');
  const [selectedNotebooks, setSelectedNotebooks] = useState<string[]>(preSelectedNotebooks);
  const [selectedSources, setSelectedSources] = useState<string[]>(preSelectedSources);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    include_notebooks: preSelectedNotebooks,
    include_sources: preSelectedSources,
    include_attachments: true,
    include_metadata: true,
    include_tags: true,
    include_comments: true,
    include_annotations: true,
    preserve_formatting: true,
    include_toc: true,
    include_cover_page: false,
    page_layout: 'portrait',
    font_family: 'Inter',
    font_size: 11,
    organization: 'hierarchical',
    separate_files: false,
    file_naming: 'original',
    compression: true,
    encryption: false,
    password_protection: false,
    image_quality: 'high',
    image_format: 'original',
    cloud_sync: false,
    email_when_ready: false,
  });
  
  const [preview, setPreview] = useState<ExportPreview | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [exportName, setExportName] = useState('');
  const [exportDescription, setExportDescription] = useState('');
  const [outputPath, setOutputPath] = useState('');

  const availableFormats = supportedFormats || Object.keys(EXPORT_FORMAT_INFO) as ExportFormat[];
  
  const formatsByCategory = availableFormats.reduce((acc, format) => {
    const category = EXPORT_FORMAT_INFO[format].category;
    if (!acc[category]) acc[category] = [];
    acc[category].push(format);
    return acc;
  }, {} as Record<string, ExportFormat[]>);

  // Load available content on mount
  const [availableNotebooks, setAvailableNotebooks] = useState<any[]>([]);
  const [availableSources, setAvailableSources] = useState<any[]>([]);

  useEffect(() => {
    if (isInitialized && isOpen) {
      loadAvailableContent();
    }
  }, [isInitialized, isOpen, workspaceId]);

  const loadAvailableContent = async () => {
    try {
      const [notebooks, sources] = await Promise.all([
        invoke('get_workspace_notebooks', { workspaceId }),
        invoke('get_workspace_sources', { workspaceId }),
      ]);
      
      setAvailableNotebooks(notebooks as any[]);
      setAvailableSources(sources as any[]);
    } catch (error) {
      console.error('Failed to load available content:', error);
      toast.error('Failed to load available content');
    }
  };

  const handleSelectOutputPath = async () => {
    if (!isInitialized) {
      toast.error('System not ready');
      return;
    }

    try {
      const formatInfo = EXPORT_FORMAT_INFO[selectedFormat];
      const extension = formatInfo.extensions[0];
      
      const selected = await save({
        defaultPath: `${exportName || 'export'}${extension}`,
        filters: [{
          name: formatInfo.name,
          extensions: formatInfo.extensions.map(ext => ext.slice(1)) // Remove dot
        }]
      });

      if (selected) {
        setOutputPath(selected);
      }
    } catch (error) {
      console.error('Failed to select output path:', error);
      toast.error('Failed to select output path');
    }
  };

  const generatePreview = async () => {
    if (selectedNotebooks.length === 0 && selectedSources.length === 0) {
      toast.error('Please select content to export');
      return;
    }

    setIsLoading(true);
    try {
      const previewData: ExportPreview = await invoke('generate_export_preview', {
        workspaceId,
        notebooks: selectedNotebooks,
        sources: selectedSources,
        format: selectedFormat,
        options: exportOptions,
      });

      setPreview(previewData);
      setStep('preview');
    } catch (error) {
      console.error('Failed to generate preview:', error);
      toast.error('Failed to generate preview');
    } finally {
      setIsLoading(false);
    }
  };

  const startExport = async () => {
    if (!preview) return;

    const exportJob: ExportJob = {
      id: `export_${Date.now()}`,
      workspace_id: workspaceId,
      name: exportName.trim() || 'Untitled Export',
      description: exportDescription.trim() || undefined,
      format: selectedFormat,
      status: 'pending',
      progress: 0,
      created_at: new Date().toISOString(),
      export_options: {
        ...exportOptions,
        include_notebooks: selectedNotebooks,
        include_sources: selectedSources,
        output_path: outputPath || undefined,
      },
      exported_items: [],
      output_info: {
        file_path: outputPath || '',
        file_size: 0,
        file_count: 0,
      },
      metadata: {
        preview: preview,
        user_agent: 'lighthouse-lm-export',
      },
    };

    try {
      await invoke('start_export_job', { job: exportJob });
      onExportStart(exportJob);
      onClose();
      toast.success('Export started successfully');
    } catch (error) {
      console.error('Failed to start export:', error);
      toast.error('Failed to start export');
    }
  };

  const resetDialog = () => {
    setStep('selection');
    setSelectedFormat('pdf');
    setSelectedNotebooks(preSelectedNotebooks);
    setSelectedSources(preSelectedSources);
    setPreview(null);
    setExportName('');
    setExportDescription('');
    setOutputPath('');
  };

  const handleClose = () => {
    resetDialog();
    onClose();
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m ${seconds % 60}s`;
    return `${Math.floor(seconds / 3600)}h ${Math.floor((seconds % 3600) / 60)}m`;
  };

  const getTotalSelectedItems = () => {
    return selectedNotebooks.length + selectedSources.length;
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className={cn("sm:max-w-4xl", className)}>
        <DialogHeader>
          <DialogTitle>Export Data</DialogTitle>
          <DialogDescription>
            Export your content to external formats
          </DialogDescription>
        </DialogHeader>

        <Tabs value={step} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="selection">Content</TabsTrigger>
            <TabsTrigger value="format" disabled={getTotalSelectedItems() === 0}>Format</TabsTrigger>
            <TabsTrigger value="options" disabled={step === 'selection' || step === 'format'}>Options</TabsTrigger>
            <TabsTrigger value="preview" disabled={!preview}>Preview</TabsTrigger>
          </TabsList>

          {/* Content Selection */}
          <TabsContent value="selection" className="space-y-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="export-name">Export name</Label>
                <Input
                  id="export-name"
                  value={exportName}
                  onChange={(e) => setExportName(e.target.value)}
                  placeholder="Enter export name"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="export-description">Description (optional)</Label>
                <Textarea
                  id="export-description"
                  value={exportDescription}
                  onChange={(e) => setExportDescription(e.target.value)}
                  placeholder="Describe what you're exporting"
                  rows={2}
                />
              </div>
            </div>

            <Separator />

            {/* Notebooks Selection */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-base font-medium">Notebooks</Label>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedNotebooks(availableNotebooks.map(n => n.id))}
                  >
                    Select All
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedNotebooks([])}
                  >
                    Clear
                  </Button>
                </div>
              </div>

              <ScrollArea className="h-48 border rounded-md p-4">
                <div className="space-y-2">
                  {availableNotebooks.map((notebook) => (
                    <div key={notebook.id} className="flex items-center space-x-2">
                      <Checkbox
                        checked={selectedNotebooks.includes(notebook.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedNotebooks(prev => [...prev, notebook.id]);
                          } else {
                            setSelectedNotebooks(prev => prev.filter(id => id !== notebook.id));
                          }
                        }}
                      />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{notebook.title}</p>
                        <p className="text-xs text-muted-foreground truncate">
                          {notebook.source_count} sources
                        </p>
                      </div>
                    </div>
                  ))}
                  
                  {availableNotebooks.length === 0 && (
                    <p className="text-sm text-muted-foreground text-center py-4">
                      No notebooks available
                    </p>
                  )}
                </div>
              </ScrollArea>
            </div>

            {/* Sources Selection */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-base font-medium">Individual Sources</Label>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedSources(availableSources.map(s => s.id))}
                  >
                    Select All
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedSources([])}
                  >
                    Clear
                  </Button>
                </div>
              </div>

              <ScrollArea className="h-48 border rounded-md p-4">
                <div className="space-y-2">
                  {availableSources.map((source) => (
                    <div key={source.id} className="flex items-center space-x-2">
                      <Checkbox
                        checked={selectedSources.includes(source.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedSources(prev => [...prev, source.id]);
                          } else {
                            setSelectedSources(prev => prev.filter(id => id !== source.id));
                          }
                        }}
                      />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{source.title}</p>
                        <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                          <Badge variant="outline" className="text-xs">
                            {source.source_type}
                          </Badge>
                          <span>{formatBytes(source.metadata?.size || 0)}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                  
                  {availableSources.length === 0 && (
                    <p className="text-sm text-muted-foreground text-center py-4">
                      No sources available
                    </p>
                  )}
                </div>
              </ScrollArea>
            </div>

            {getTotalSelectedItems() > 0 && (
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <CheckCircle2 className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium">
                      {getTotalSelectedItems()} items selected
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {selectedNotebooks.length} notebooks, {selectedSources.length} individual sources
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Format Selection */}
          <TabsContent value="format" className="space-y-6">
            <div className="space-y-4">
              <Label>Select export format</Label>
              <div className="space-y-4">
                {Object.entries(formatsByCategory).map(([category, formats]) => (
                  <div key={category} className="space-y-2">
                    <h4 className="text-sm font-medium text-muted-foreground capitalize">
                      {category.replace('_', ' ')} Formats
                    </h4>
                    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2">
                      {formats.map((format) => {
                        const info = EXPORT_FORMAT_INFO[format];
                        return (
                          <Card
                            key={format}
                            className={cn(
                              "cursor-pointer transition-all hover:shadow-md",
                              selectedFormat === format && "ring-2 ring-primary"
                            )}
                            onClick={() => setSelectedFormat(format)}
                          >
                            <CardContent className="p-3">
                              <div className="space-y-2">
                                <div className="flex items-center space-x-2">
                                  {info.icon}
                                  <div className="flex-1 min-w-0">
                                    <p className="text-sm font-medium truncate">{info.name}</p>
                                    <p className="text-xs text-muted-foreground truncate">
                                      {info.extensions.join(', ')}
                                    </p>
                                  </div>
                                </div>
                                <div className="flex flex-wrap gap-1">
                                  {info.features.slice(0, 2).map((feature) => (
                                    <Badge key={feature} variant="secondary" className="text-xs">
                                      {feature}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <Label>Output location</Label>
              <div className="flex space-x-2">
                <Input
                  value={outputPath}
                  onChange={(e) => setOutputPath(e.target.value)}
                  placeholder="Choose where to save the export"
                  readOnly
                />
                <Button variant="outline" onClick={handleSelectOutputPath}>
                  <HardDrive className="mr-2 h-4 w-4" />
                  Browse
                </Button>
              </div>
            </div>
          </TabsContent>

          {/* Export Options */}
          <TabsContent value="options" className="space-y-6">
            <ScrollArea className="h-[400px] pr-4">
              <div className="space-y-6">
                {/* Content Options */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Package className="h-5 w-5" />
                      <span>Content Options</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={exportOptions.include_attachments}
                        onCheckedChange={(checked) =>
                          setExportOptions(prev => ({ ...prev, include_attachments: checked }))
                        }
                      />
                      <Label>Include attachments</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={exportOptions.include_metadata}
                        onCheckedChange={(checked) =>
                          setExportOptions(prev => ({ ...prev, include_metadata: checked }))
                        }
                      />
                      <Label>Include metadata</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={exportOptions.include_tags}
                        onCheckedChange={(checked) =>
                          setExportOptions(prev => ({ ...prev, include_tags: checked }))
                        }
                      />
                      <Label>Include tags</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={exportOptions.include_comments}
                        onCheckedChange={(checked) =>
                          setExportOptions(prev => ({ ...prev, include_comments: checked }))
                        }
                      />
                      <Label>Include comments</Label>
                    </div>
                  </CardContent>
                </Card>

                {/* Formatting Options */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Palette className="h-5 w-5" />
                      <span>Formatting</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={exportOptions.preserve_formatting}
                          onCheckedChange={(checked) =>
                            setExportOptions(prev => ({ ...prev, preserve_formatting: checked }))
                          }
                        />
                        <Label>Preserve formatting</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={exportOptions.include_toc}
                          onCheckedChange={(checked) =>
                            setExportOptions(prev => ({ ...prev, include_toc: checked }))
                          }
                        />
                        <Label>Include table of contents</Label>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Font family</Label>
                      <Select
                        value={exportOptions.font_family}
                        onValueChange={(value) =>
                          setExportOptions(prev => ({ ...prev, font_family: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Inter">Inter</SelectItem>
                          <SelectItem value="Times New Roman">Times New Roman</SelectItem>
                          <SelectItem value="Arial">Arial</SelectItem>
                          <SelectItem value="Georgia">Georgia</SelectItem>
                          <SelectItem value="Roboto">Roboto</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label>Font size</Label>
                        <span className="text-sm text-muted-foreground">
                          {exportOptions.font_size}pt
                        </span>
                      </div>
                      <Slider
                        value={[exportOptions.font_size]}
                        onValueChange={([value]) =>
                          setExportOptions(prev => ({ ...prev, font_size: value }))
                        }
                        min={8}
                        max={18}
                        step={1}
                        className="w-full"
                      />
                    </div>
                  </CardContent>
                </Card>

                {/* Advanced Options */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Settings className="h-5 w-5" />
                      <span>Advanced Options</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={exportOptions.compression}
                          onCheckedChange={(checked) =>
                            setExportOptions(prev => ({ ...prev, compression: checked }))
                          }
                        />
                        <Label>Enable compression</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={exportOptions.encryption}
                          onCheckedChange={(checked) =>
                            setExportOptions(prev => ({ ...prev, encryption: checked }))
                          }
                        />
                        <Label>Enable encryption</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={exportOptions.cloud_sync}
                          onCheckedChange={(checked) =>
                            setExportOptions(prev => ({ ...prev, cloud_sync: checked }))
                          }
                        />
                        <Label>Cloud sync</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={exportOptions.email_when_ready}
                          onCheckedChange={(checked) =>
                            setExportOptions(prev => ({ ...prev, email_when_ready: checked }))
                          }
                        />
                        <Label>Email when ready</Label>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </ScrollArea>
          </TabsContent>

          {/* Preview */}
          <TabsContent value="preview" className="space-y-6">
            {preview && (
              <div className="space-y-6">
                {/* Summary */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Eye className="h-5 w-5" />
                      <span>Export Summary</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold">{preview.total_items}</p>
                      <p className="text-sm text-muted-foreground">Total Items</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">{formatBytes(preview.estimated_size)}</p>
                      <p className="text-sm text-muted-foreground">Estimated Size</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">{formatDuration(preview.estimated_duration)}</p>
                      <p className="text-sm text-muted-foreground">Estimated Time</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">{preview.file_structure.length}</p>
                      <p className="text-sm text-muted-foreground">Files</p>
                    </div>
                  </CardContent>
                </Card>

                {/* Item Breakdown */}
                <Card>
                  <CardHeader>
                    <CardTitle>Item Breakdown</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {Object.entries(preview.item_breakdown).map(([type, count]) => (
                        <div key={type} className="flex items-center justify-between">
                          <span className="text-sm capitalize">{type.replace('_', ' ')}</span>
                          <Badge variant="secondary">{count}</Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* File Structure */}
                <Card>
                  <CardHeader>
                    <CardTitle>File Structure</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-32">
                      <div className="space-y-1">
                        {preview.file_structure.map((file, index) => (
                          <p key={index} className="text-sm font-mono">{file}</p>
                        ))}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex justify-between">
          <div>
            {step === 'format' && (
              <Button variant="outline" onClick={() => setStep('selection')}>
                Back
              </Button>
            )}
            {step === 'options' && (
              <Button variant="outline" onClick={() => setStep('format')}>
                Back
              </Button>
            )}
            {step === 'preview' && (
              <Button variant="outline" onClick={() => setStep('options')}>
                Back
              </Button>
            )}
          </div>

          <div className="flex space-x-2">
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            
            {step === 'selection' && (
              <Button
                onClick={() => setStep('format')}
                disabled={getTotalSelectedItems() === 0 || !exportName.trim()}
              >
                Next
              </Button>
            )}
            
            {step === 'format' && (
              <Button
                onClick={() => setStep('options')}
                disabled={!selectedFormat}
              >
                Next
              </Button>
            )}
            
            {step === 'options' && (
              <Button
                onClick={generatePreview}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Clock className="mr-2 h-4 w-4 animate-spin" />
                    Generating Preview...
                  </>
                ) : (
                  <>
                    <Eye className="mr-2 h-4 w-4" />
                    Generate Preview
                  </>
                )}
              </Button>
            )}
            
            {step === 'preview' && (
              <Button onClick={startExport} disabled={!preview}>
                <Download className="mr-2 h-4 w-4" />
                Start Export
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};