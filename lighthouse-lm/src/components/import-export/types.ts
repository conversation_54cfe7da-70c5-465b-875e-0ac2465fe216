/**
 * Data Import/Export System Types
 * Enterprise-grade data transformation and migration system
 */

export interface ImportJob {
  id: string;
  workspace_id: string;
  name: string;
  description?: string;
  format: ImportFormat;
  status: ImportStatus;
  progress: number; // 0-100
  created_at: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  source_info: ImportSourceInfo;
  import_options: ImportOptions;
  validation_results?: ValidationResult[];
  imported_items: ImportedItem[];
  metadata: Record<string, any>;
}

export type ImportFormat = 
  | 'csv'
  | 'json'
  | 'excel'
  | 'pdf'
  | 'markdown'
  | 'notion'
  | 'obsidian'
  | 'roam'
  | 'evernote'
  | 'onenote'
  | 'confluence'
  | 'jira'
  | 'trello'
  | 'slack'
  | 'discord'
  | 'teams'
  | 'google_docs'
  | 'dropbox_paper'
  | 'bear'
  | 'ulysses'
  | 'devonthink'
  | 'zotero'
  | 'mendeley'
  | 'endnote';

export type ImportStatus = 
  | 'pending'
  | 'validating'
  | 'processing'
  | 'transforming'
  | 'importing'
  | 'completed'
  | 'failed'
  | 'cancelled'
  | 'partial';

export interface ImportSourceInfo {
  file_path?: string;
  file_size?: number;
  file_count?: number;
  url?: string;
  api_endpoint?: string;
  credentials?: Record<string, any>; // Encrypted
  last_sync_at?: string;
  sync_token?: string;
}

export interface ImportOptions {
  // General options
  merge_duplicates: boolean;
  preserve_timestamps: boolean;
  preserve_metadata: boolean;
  preserve_tags: boolean;
  preserve_links: boolean;
  
  // Content processing
  extract_images: boolean;
  convert_formats: boolean;
  clean_html: boolean;
  extract_citations: boolean;
  generate_summaries: boolean;
  
  // Organization
  create_folders: boolean;
  folder_structure: 'flat' | 'hierarchical' | 'by_type' | 'by_date' | 'custom';
  naming_convention: 'original' | 'sanitized' | 'timestamped' | 'sequential' | 'custom';
  
  // Validation
  validate_before_import: boolean;
  skip_invalid_items: boolean;
  require_manual_review: boolean;
  
  // Advanced
  batch_size: number;
  parallel_processing: boolean;
  enable_rollback: boolean;
  dry_run: boolean;
  
  // Encryption
  encrypt_sensitive_data: boolean;
  encryption_key?: string;
}

export interface ValidationResult {
  id: string;
  item_path: string;
  severity: 'error' | 'warning' | 'info';
  code: string;
  message: string;
  suggestion?: string;
  auto_fixable: boolean;
  metadata?: Record<string, any>;
}

export interface ImportedItem {
  id: string;
  source_path: string;
  source_id?: string;
  target_id?: string;
  item_type: ImportedItemType;
  title: string;
  status: 'imported' | 'skipped' | 'failed' | 'pending';
  error_message?: string;
  warnings?: string[];
  metadata: Record<string, any>;
  created_at: string;
  size_bytes?: number;
}

export type ImportedItemType = 
  | 'notebook'
  | 'note'
  | 'document'
  | 'image'
  | 'video'
  | 'audio'
  | 'file'
  | 'folder'
  | 'link'
  | 'tag'
  | 'comment'
  | 'annotation'
  | 'metadata';

// Export Types
export interface ExportJob {
  id: string;
  workspace_id: string;
  name: string;
  description?: string;
  format: ExportFormat;
  status: ExportStatus;
  progress: number; // 0-100
  created_at: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  export_options: ExportOptions;
  exported_items: ExportedItem[];
  output_info: ExportOutputInfo;
  metadata: Record<string, any>;
}

export type ExportFormat = 
  | 'pdf'
  | 'docx'
  | 'html'
  | 'markdown'
  | 'csv'
  | 'json'
  | 'xml'
  | 'epub'
  | 'latex'
  | 'notion'
  | 'obsidian'
  | 'roam'
  | 'bear'
  | 'ulysses'
  | 'devonthink'
  | 'evernote'
  | 'onenote'
  | 'confluence'
  | 'google_docs'
  | 'dropbox_paper'
  | 'zotero'
  | 'mendeley'
  | 'endnote'
  | 'bibtex'
  | 'ris';

export type ExportStatus = 
  | 'pending'
  | 'preparing'
  | 'exporting'
  | 'packaging'
  | 'completed'
  | 'failed'
  | 'cancelled';

export interface ExportOptions {
  // Content selection
  include_notebooks: string[]; // notebook IDs
  include_sources: string[]; // source IDs
  include_attachments: boolean;
  include_metadata: boolean;
  include_tags: boolean;
  include_comments: boolean;
  include_annotations: boolean;
  
  // Formatting
  preserve_formatting: boolean;
  include_toc: boolean;
  include_cover_page: boolean;
  page_layout: 'portrait' | 'landscape';
  font_family: string;
  font_size: number;
  
  // Structure
  organization: 'flat' | 'hierarchical' | 'by_type' | 'by_date' | 'custom';
  separate_files: boolean;
  file_naming: 'original' | 'sanitized' | 'timestamped' | 'sequential' | 'custom';
  
  // Advanced
  compression: boolean;
  encryption: boolean;
  password_protection: boolean;
  watermark?: string;
  
  // Quality
  image_quality: 'low' | 'medium' | 'high' | 'original';
  image_format: 'jpeg' | 'png' | 'webp' | 'original';
  
  // Destination
  output_path?: string;
  cloud_sync: boolean;
  email_when_ready: boolean;
}

export interface ExportedItem {
  id: string;
  source_id: string;
  item_type: ExportedItemType;
  title: string;
  file_path: string;
  file_size: number;
  status: 'exported' | 'skipped' | 'failed';
  error_message?: string;
  metadata: Record<string, any>;
}

export type ExportedItemType = ImportedItemType;

export interface ExportOutputInfo {
  file_path: string;
  file_size: number;
  file_count: number;
  download_url?: string;
  expires_at?: string;
  checksum?: string;
}

// Format Converter Types
export interface ConversionRule {
  id: string;
  name: string;
  source_format: string;
  target_format: string;
  transformation_rules: TransformationRule[];
  validation_rules: ValidationRule[];
  is_active: boolean;
  priority: number;
}

export interface TransformationRule {
  id: string;
  type: 'field_mapping' | 'content_transform' | 'structure_change' | 'metadata_merge';
  source_path: string;
  target_path: string;
  transform_function?: string;
  parameters?: Record<string, any>;
  conditions?: Condition[];
}

export interface ValidationRule {
  id: string;
  field_path: string;
  rule_type: 'required' | 'type_check' | 'format_check' | 'range_check' | 'custom';
  parameters: Record<string, any>;
  error_message: string;
  is_critical: boolean;
}

export interface Condition {
  field_path: string;
  operator: 'equals' | 'contains' | 'starts_with' | 'ends_with' | 'regex' | 'exists' | 'not_exists';
  value: any;
  case_sensitive?: boolean;
}

// Bulk Import Processor Types
export interface BulkImportSession {
  id: string;
  workspace_id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
  status: BulkImportStatus;
  total_jobs: number;
  completed_jobs: number;
  failed_jobs: number;
  jobs: ImportJob[];
  global_options: ImportOptions;
  rollback_info?: RollbackInfo;
}

export type BulkImportStatus = 
  | 'preparing'
  | 'processing'
  | 'paused'
  | 'completed'
  | 'failed'
  | 'cancelled'
  | 'rolled_back';

export interface RollbackInfo {
  rollback_id: string;
  created_at: string;
  rollback_point: string;
  affected_items: string[];
  is_available: boolean;
}

// Data Validator Types
export interface ValidationSchema {
  id: string;
  name: string;
  description?: string;
  format: ImportFormat;
  version: string;
  rules: ValidationRule[];
  field_definitions: FieldDefinition[];
  created_at: string;
  updated_at: string;
}

export interface FieldDefinition {
  path: string;
  name: string;
  type: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object' | 'file';
  required: boolean;
  description?: string;
  constraints?: FieldConstraints;
}

export interface FieldConstraints {
  min_length?: number;
  max_length?: number;
  min_value?: number;
  max_value?: number;
  pattern?: string;
  allowed_values?: any[];
  file_extensions?: string[];
  max_file_size?: number;
}

// React Component Props
export interface ImportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onImportStart: (job: ImportJob) => void;
  workspaceId: string;
  supportedFormats?: ImportFormat[];
  className?: string;
}

export interface ExportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onExportStart: (job: ExportJob) => void;
  workspaceId: string;
  preSelectedNotebooks?: string[];
  preSelectedSources?: string[];
  supportedFormats?: ExportFormat[];
  className?: string;
}

export interface BulkImportProcessorProps {
  session: BulkImportSession;
  onSessionUpdate: (session: BulkImportSession) => void;
  onJobComplete: (jobId: string) => void;
  onSessionComplete: () => void;
  onError: (error: string) => void;
  className?: string;
}

export interface FormatConvertersProps {
  availableConverters: ConversionRule[];
  onConverterCreate: (converter: ConversionRule) => void;
  onConverterUpdate: (converter: ConversionRule) => void;
  onConverterDelete: (converterId: string) => void;
  className?: string;
}

export interface DataValidatorProps {
  data: any[];
  schema: ValidationSchema;
  onValidationComplete: (results: ValidationResult[]) => void;
  onSchemaUpdate: (schema: ValidationSchema) => void;
  className?: string;
}

// Utility Types
export interface ImportPreview {
  total_items: number;
  item_breakdown: Record<ImportedItemType, number>;
  estimated_size: number;
  estimated_duration: number; // seconds
  potential_conflicts: string[];
  recommended_options: Partial<ImportOptions>;
}

export interface ExportPreview {
  total_items: number;
  item_breakdown: Record<ExportedItemType, number>;
  estimated_size: number;
  estimated_duration: number; // seconds
  file_structure: string[];
}

export interface ProcessingStats {
  items_processed: number;
  items_per_second: number;
  estimated_completion: string;
  memory_usage: number;
  disk_usage: number;
  errors_count: number;
  warnings_count: number;
}