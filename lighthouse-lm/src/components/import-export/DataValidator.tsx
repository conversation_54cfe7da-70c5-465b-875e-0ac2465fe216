/**
 * Data Validator - Validate imported data against schemas
 * Enterprise data validation with schema management and real-time feedback
 */

import React, { useState, useEffect, useMemo } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../ui/card';
import { Button } from '../ui/button';
import { Progress } from '../ui/progress';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { ScrollArea } from '../ui/scroll-area';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '../ui/tabs';
import {
  CheckCircle2,
  XCircle,
  AlertTriangle,
  Info,
  FileText,
  Database,
  Shield,
  Zap,
  Search,
  Filter,
  Download,
  RefreshCw,
} from 'lucide-react';
import { toast } from 'sonner';
import {
  DataValidatorProps,
  ValidationResult,
  ValidationSchema,
  FieldDefinition,
} from './types';
import { cn } from '../../lib/utils';

export const DataValidator: React.FC<DataValidatorProps> = ({
  data,
  schema,
  onValidationComplete,
  onSchemaUpdate,
  className,
}) => {
  const [validationResults, setValidationResults] = useState<ValidationResult[]>([]);
  const [isValidating, setIsValidating] = useState(false);
  const [validationProgress, setValidationProgress] = useState(0);
  const [filterSeverity, setFilterSeverity] = useState<'all' | 'error' | 'warning' | 'info'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Run validation when data or schema changes
  useEffect(() => {
    if (data.length > 0 && schema) {
      runValidation();
    }
  }, [data, schema]);

  const runValidation = async () => {
    setIsValidating(true);
    setValidationProgress(0);
    
    try {
      const results: ValidationResult[] = [];
      const totalItems = data.length;
      
      for (let i = 0; i < totalItems; i++) {
        const item = data[i];
        const itemResults = await validateItem(item, i, schema);
        results.push(...itemResults);
        
        // Update progress
        setValidationProgress(((i + 1) / totalItems) * 100);
        
        // Small delay to prevent blocking the UI
        if (i % 100 === 0) {
          await new Promise(resolve => setTimeout(resolve, 1));
        }
      }
      
      setValidationResults(results);
      onValidationComplete(results);
      
      const summary = getValidationSummary(results);
      toast.success(`Validation complete: ${summary.errors} errors, ${summary.warnings} warnings`);
      
    } catch (error) {
      console.error('Validation failed:', error);
      toast.error('Validation failed');
    } finally {
      setIsValidating(false);
    }
  };

  const validateItem = async (item: any, index: number, schema: ValidationSchema): Promise<ValidationResult[]> => {
    const results: ValidationResult[] = [];
    const itemPath = `item[${index}]`;

    // Validate each field definition
    for (const fieldDef of schema.field_definitions) {
      const fieldResults = validateField(item, fieldDef, itemPath);
      results.push(...fieldResults);
    }

    // Validate custom rules
    for (const rule of schema.rules) {
      const ruleResults = validateRule(item, rule, itemPath);
      results.push(...ruleResults);
    }

    return results;
  };

  const validateField = (item: any, fieldDef: FieldDefinition, itemPath: string): ValidationResult[] => {
    const results: ValidationResult[] = [];
    const fieldPath = `${itemPath}.${fieldDef.path}`;
    const value = getNestedValue(item, fieldDef.path);

    // Check if required field is missing
    if (fieldDef.required && (value === undefined || value === null || value === '')) {
      results.push({
        id: `${fieldPath}_required`,
        item_path: fieldPath,
        severity: 'error',
        code: 'FIELD_REQUIRED',
        message: `Required field '${fieldDef.name}' is missing`,
        suggestion: `Provide a value for ${fieldDef.name}`,
        auto_fixable: false,
      });
      return results; // Skip other validations if field is missing
    }

    // Skip further validation if field is optional and empty
    if (!fieldDef.required && (value === undefined || value === null || value === '')) {
      return results;
    }

    // Type validation
    const typeValid = validateFieldType(value, fieldDef.type);
    if (!typeValid) {
      results.push({
        id: `${fieldPath}_type`,
        item_path: fieldPath,
        severity: 'error',
        code: 'INVALID_TYPE',
        message: `Field '${fieldDef.name}' has invalid type. Expected ${fieldDef.type}, got ${typeof value}`,
        suggestion: `Convert value to ${fieldDef.type}`,
        auto_fixable: true,
      });
    }

    // Constraint validation
    if (fieldDef.constraints && typeValid) {
      const constraintResults = validateConstraints(value, fieldDef.constraints, fieldPath, fieldDef.name);
      results.push(...constraintResults);
    }

    return results;
  };

  const validateRule = (item: any, rule: ValidationRule, itemPath: string): ValidationResult[] => {
    const results: ValidationResult[] = [];
    const fieldPath = `${itemPath}.${rule.field_path}`;
    const value = getNestedValue(item, rule.field_path);

    let isValid = true;
    let message = rule.error_message;

    switch (rule.rule_type) {
      case 'required':
        isValid = value !== undefined && value !== null && value !== '';
        break;
        
      case 'type_check':
        const expectedType = rule.parameters.expected_type;
        isValid = typeof value === expectedType;
        break;
        
      case 'format_check':
        const pattern = new RegExp(rule.parameters.pattern);
        isValid = pattern.test(String(value));
        break;
        
      case 'range_check':
        const min = rule.parameters.min;
        const max = rule.parameters.max;
        const numValue = Number(value);
        isValid = !isNaN(numValue) && numValue >= min && numValue <= max;
        break;
        
      case 'custom':
        // Custom validation logic would be implemented here
        isValid = true; // Placeholder
        break;
    }

    if (!isValid) {
      results.push({
        id: `${fieldPath}_${rule.rule_type}`,
        item_path: fieldPath,
        severity: rule.is_critical ? 'error' : 'warning',
        code: rule.rule_type.toUpperCase(),
        message,
        suggestion: `Fix ${rule.field_path} to meet requirements`,
        auto_fixable: false,
        metadata: { rule_id: rule.id },
      });
    }

    return results;
  };

  const validateFieldType = (value: any, expectedType: FieldDefinition['type']): boolean => {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'date':
        return !isNaN(Date.parse(value));
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      case 'file':
        return typeof value === 'string' && value.length > 0;
      default:
        return true;
    }
  };

  const validateConstraints = (value: any, constraints: FieldConstraints, fieldPath: string, fieldName: string): ValidationResult[] => {
    const results: ValidationResult[] = [];

    if (typeof value === 'string') {
      if (constraints.min_length && value.length < constraints.min_length) {
        results.push({
          id: `${fieldPath}_min_length`,
          item_path: fieldPath,
          severity: 'error',
          code: 'MIN_LENGTH',
          message: `${fieldName} is too short (${value.length} < ${constraints.min_length})`,
          suggestion: `Minimum length is ${constraints.min_length} characters`,
          auto_fixable: false,
        });
      }

      if (constraints.max_length && value.length > constraints.max_length) {
        results.push({
          id: `${fieldPath}_max_length`,
          item_path: fieldPath,
          severity: 'warning',
          code: 'MAX_LENGTH',
          message: `${fieldName} is too long (${value.length} > ${constraints.max_length})`,
          suggestion: `Maximum length is ${constraints.max_length} characters`,
          auto_fixable: true,
        });
      }

      if (constraints.pattern) {
        const pattern = new RegExp(constraints.pattern);
        if (!pattern.test(value)) {
          results.push({
            id: `${fieldPath}_pattern`,
            item_path: fieldPath,
            severity: 'error',
            code: 'PATTERN_MISMATCH',
            message: `${fieldName} doesn't match required pattern`,
            suggestion: `Value should match pattern: ${constraints.pattern}`,
            auto_fixable: false,
          });
        }
      }
    }

    if (typeof value === 'number') {
      if (constraints.min_value !== undefined && value < constraints.min_value) {
        results.push({
          id: `${fieldPath}_min_value`,
          item_path: fieldPath,
          severity: 'error',
          code: 'MIN_VALUE',
          message: `${fieldName} is too small (${value} < ${constraints.min_value})`,
          suggestion: `Minimum value is ${constraints.min_value}`,
          auto_fixable: false,
        });
      }

      if (constraints.max_value !== undefined && value > constraints.max_value) {
        results.push({
          id: `${fieldPath}_max_value`,
          item_path: fieldPath,
          severity: 'error',
          code: 'MAX_VALUE',
          message: `${fieldName} is too large (${value} > ${constraints.max_value})`,
          suggestion: `Maximum value is ${constraints.max_value}`,
          auto_fixable: false,
        });
      }
    }

    if (constraints.allowed_values && !constraints.allowed_values.includes(value)) {
      results.push({
        id: `${fieldPath}_allowed_values`,
        item_path: fieldPath,
        severity: 'error',
        code: 'INVALID_VALUE',
        message: `${fieldName} has invalid value: ${value}`,
        suggestion: `Allowed values: ${constraints.allowed_values.join(', ')}`,
        auto_fixable: false,
      });
    }

    return results;
  };

  const getNestedValue = (obj: any, path: string): any => {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  };

  const getValidationSummary = (results: ValidationResult[]) => {
    return {
      total: results.length,
      errors: results.filter(r => r.severity === 'error').length,
      warnings: results.filter(r => r.severity === 'warning').length,
      info: results.filter(r => r.severity === 'info').length,
    };
  };

  const getSeverityIcon = (severity: ValidationResult['severity']) => {
    switch (severity) {
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'info':
        return <Info className="h-4 w-4 text-blue-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  const getSeverityColor = (severity: ValidationResult['severity']) => {
    switch (severity) {
      case 'error':
        return 'border-l-red-500 bg-red-50';
      case 'warning':
        return 'border-l-orange-500 bg-orange-50';
      case 'info':
        return 'border-l-blue-500 bg-blue-50';
      default:
        return 'border-l-gray-500 bg-gray-50';
    }
  };

  const filteredResults = useMemo(() => {
    return validationResults.filter(result => {
      const matchesSeverity = filterSeverity === 'all' || result.severity === filterSeverity;
      const matchesSearch = !searchTerm || 
        result.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
        result.item_path.toLowerCase().includes(searchTerm.toLowerCase()) ||
        result.code.toLowerCase().includes(searchTerm.toLowerCase());
      
      return matchesSeverity && matchesSearch;
    });
  }, [validationResults, filterSeverity, searchTerm]);

  const summary = getValidationSummary(validationResults);

  const exportResults = () => {
    const csvContent = [
      'Path,Severity,Code,Message,Suggestion,Auto Fixable',
      ...validationResults.map(result => 
        `"${result.item_path}","${result.severity}","${result.code}","${result.message}","${result.suggestion}","${result.auto_fixable}"`
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `validation-results-${Date.now()}.csv`;
    link.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Data Validator</span>
          </h3>
          <p className="text-sm text-muted-foreground">
            Validate {data.length.toLocaleString()} items against schema "{schema.name}"
          </p>
        </div>
        
        <div className="flex space-x-2">
          <Button variant="outline" onClick={runValidation} disabled={isValidating}>
            <RefreshCw className={cn("mr-2 h-4 w-4", isValidating && "animate-spin")} />
            {isValidating ? 'Validating...' : 'Re-validate'}
          </Button>
          
          {validationResults.length > 0 && (
            <Button variant="outline" onClick={exportResults}>
              <Download className="mr-2 h-4 w-4" />
              Export Results
            </Button>
          )}
        </div>
      </div>

      {/* Validation Progress */}
      {isValidating && (
        <Card>
          <CardContent className="p-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Validating data...</span>
                <span>{Math.round(validationProgress)}%</span>
              </div>
              <Progress value={validationProgress} className="h-2" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Summary */}
      {validationResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Validation Summary</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold">{summary.total}</p>
                <p className="text-sm text-muted-foreground">Total Issues</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-red-600">{summary.errors}</p>
                <p className="text-sm text-muted-foreground">Errors</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-orange-600">{summary.warnings}</p>
                <p className="text-sm text-muted-foreground">Warnings</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">{summary.info}</p>
                <p className="text-sm text-muted-foreground">Info</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="results" className="space-y-4">
        <TabsList>
          <TabsTrigger value="results">Validation Results</TabsTrigger>
          <TabsTrigger value="schema">Schema Details</TabsTrigger>
        </TabsList>

        <TabsContent value="results" className="space-y-4">
          {/* Filters */}
          <div className="flex space-x-2">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="Search issues..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            
            <select
              value={filterSeverity}
              onChange={(e) => setFilterSeverity(e.target.value as typeof filterSeverity)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Severities</option>
              <option value="error">Errors Only</option>
              <option value="warning">Warnings Only</option>
              <option value="info">Info Only</option>
            </select>
          </div>

          {/* Results List */}
          <Card>
            <CardContent className="p-0">
              <ScrollArea className="h-96">
                <div className="space-y-0">
                  {filteredResults.map((result, index) => (
                    <div key={result.id}>
                      <div className={cn(
                        "p-4 border-l-4 hover:bg-muted/50",
                        getSeverityColor(result.severity)
                      )}>
                        <div className="flex items-start space-x-3">
                          <div className="flex-shrink-0 mt-0.5">
                            {getSeverityIcon(result.severity)}
                          </div>
                          
                          <div className="flex-1 min-w-0 space-y-1">
                            <div className="flex items-center space-x-2">
                              <p className="text-sm font-medium">{result.message}</p>
                              <Badge variant="outline" className="text-xs">
                                {result.code}
                              </Badge>
                              {result.auto_fixable && (
                                <Badge variant="secondary" className="text-xs">
                                  <Zap className="mr-1 h-3 w-3" />
                                  Auto-fixable
                                </Badge>
                              )}
                            </div>
                            
                            <p className="text-xs text-muted-foreground font-mono">
                              {result.item_path}
                            </p>
                            
                            {result.suggestion && (
                              <p className="text-xs text-blue-600">
                                💡 {result.suggestion}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      {index < filteredResults.length - 1 && <Separator />}
                    </div>
                  ))}

                  {filteredResults.length === 0 && validationResults.length > 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      <p>No results match your filters</p>
                    </div>
                  )}

                  {validationResults.length === 0 && !isValidating && (
                    <div className="text-center py-8 text-muted-foreground">
                      <CheckCircle2 className="mx-auto h-12 w-12 mb-4 text-green-500" />
                      <p className="font-medium">All data passed validation!</p>
                      <p className="text-sm">No issues found in the dataset</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="schema" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Schema Information</CardTitle>
              <CardDescription>
                Validation schema "{schema.name}" version {schema.version}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {schema.description && (
                <p className="text-sm">{schema.description}</p>
              )}

              <Separator />

              <div>
                <h4 className="text-sm font-medium mb-2">Field Definitions</h4>
                <div className="space-y-2">
                  {schema.field_definitions.map((field) => (
                    <div key={field.path} className="flex items-center justify-between p-2 border rounded">
                      <div>
                        <p className="text-sm font-medium">{field.name}</p>
                        <p className="text-xs text-muted-foreground font-mono">{field.path}</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">{field.type}</Badge>
                        {field.required && (
                          <Badge variant="destructive" className="text-xs">Required</Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <Separator />

              <div>
                <h4 className="text-sm font-medium mb-2">Validation Rules</h4>
                <div className="space-y-2">
                  {schema.rules.map((rule) => (
                    <div key={rule.id} className="flex items-center justify-between p-2 border rounded">
                      <div>
                        <p className="text-sm font-medium capitalize">{rule.rule_type.replace('_', ' ')}</p>
                        <p className="text-xs text-muted-foreground font-mono">{rule.field_path}</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        {rule.is_critical && (
                          <Badge variant="destructive" className="text-xs">Critical</Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};