/**
 * Import Dialog - Multi-format import interface
 * Enterprise data import with validation, preview, and batch processing
 */

import React, { useState, useCallback } from 'react';
import { open } from '@tauri-apps/plugin-dialog';
import { invoke } from '@tauri-apps/api/core';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '../ui/tabs';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Switch } from '../ui/switch';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { Separator } from '../ui/separator';
import { ScrollArea } from '../ui/scroll-area';
import { Slider } from '../ui/slider';
import {
  Upload,
  FileText,
  Image,
  Video,
  Music,
  Archive,
  Globe,
  Database,
  Settings,
  Eye,
  AlertTriangle,
  CheckCircle2,
  Info,
  X,
  Plus,
  Zap,
  Shield,
  HardDrive,
  Clock,
} from 'lucide-react';
import { toast } from 'sonner';
import { useTauri } from '../../contexts/TauriContext';
import {
  ImportDialogProps,
  ImportJob,
  ImportFormat,
  ImportOptions,
  ImportPreview,
  ImportSourceInfo,
  ValidationResult,
} from './types';
import { cn } from '../../lib/utils';

const FORMAT_INFO: Record<ImportFormat, { 
  name: string; 
  icon: React.ReactNode; 
  description: string;
  extensions: string[];
  category: 'document' | 'note' | 'media' | 'data' | 'service';
}> = {
  csv: { name: 'CSV', icon: <FileText className="h-4 w-4" />, description: 'Comma-separated values', extensions: ['.csv'], category: 'data' },
  json: { name: 'JSON', icon: <Database className="h-4 w-4" />, description: 'JavaScript Object Notation', extensions: ['.json'], category: 'data' },
  excel: { name: 'Excel', icon: <FileText className="h-4 w-4" />, description: 'Microsoft Excel spreadsheet', extensions: ['.xlsx', '.xls'], category: 'data' },
  pdf: { name: 'PDF', icon: <FileText className="h-4 w-4" />, description: 'Portable Document Format', extensions: ['.pdf'], category: 'document' },
  markdown: { name: 'Markdown', icon: <FileText className="h-4 w-4" />, description: 'Markdown files', extensions: ['.md', '.markdown'], category: 'document' },
  notion: { name: 'Notion', icon: <Globe className="h-4 w-4" />, description: 'Notion workspace export', extensions: ['.zip'], category: 'service' },
  obsidian: { name: 'Obsidian', icon: <Globe className="h-4 w-4" />, description: 'Obsidian vault', extensions: ['.zip'], category: 'note' },
  roam: { name: 'Roam Research', icon: <Globe className="h-4 w-4" />, description: 'Roam Research export', extensions: ['.json'], category: 'note' },
  evernote: { name: 'Evernote', icon: <Globe className="h-4 w-4" />, description: 'Evernote export', extensions: ['.enex'], category: 'note' },
  onenote: { name: 'OneNote', icon: <Globe className="h-4 w-4" />, description: 'Microsoft OneNote', extensions: ['.onepkg'], category: 'note' },
  confluence: { name: 'Confluence', icon: <Globe className="h-4 w-4" />, description: 'Atlassian Confluence', extensions: ['.zip'], category: 'service' },
  jira: { name: 'Jira', icon: <Globe className="h-4 w-4" />, description: 'Atlassian Jira', extensions: ['.json'], category: 'service' },
  trello: { name: 'Trello', icon: <Globe className="h-4 w-4" />, description: 'Trello board export', extensions: ['.json'], category: 'service' },
  slack: { name: 'Slack', icon: <Globe className="h-4 w-4" />, description: 'Slack workspace export', extensions: ['.zip'], category: 'service' },
  discord: { name: 'Discord', icon: <Globe className="h-4 w-4" />, description: 'Discord chat export', extensions: ['.json'], category: 'service' },
  teams: { name: 'Microsoft Teams', icon: <Globe className="h-4 w-4" />, description: 'Teams chat export', extensions: ['.json'], category: 'service' },
  google_docs: { name: 'Google Docs', icon: <Globe className="h-4 w-4" />, description: 'Google Workspace export', extensions: ['.zip'], category: 'service' },
  dropbox_paper: { name: 'Dropbox Paper', icon: <Globe className="h-4 w-4" />, description: 'Dropbox Paper export', extensions: ['.zip'], category: 'service' },
  bear: { name: 'Bear', icon: <Globe className="h-4 w-4" />, description: 'Bear notes export', extensions: ['.bearbk'], category: 'note' },
  ulysses: { name: 'Ulysses', icon: <Globe className="h-4 w-4" />, description: 'Ulysses library export', extensions: ['.ulysses'], category: 'note' },
  devonthink: { name: 'DEVONthink', icon: <Globe className="h-4 w-4" />, description: 'DEVONthink database', extensions: ['.dtBase2'], category: 'note' },
  zotero: { name: 'Zotero', icon: <Database className="h-4 w-4" />, description: 'Zotero library', extensions: ['.rdf'], category: 'data' },
  mendeley: { name: 'Mendeley', icon: <Database className="h-4 w-4" />, description: 'Mendeley library', extensions: ['.bib'], category: 'data' },
  endnote: { name: 'EndNote', icon: <Database className="h-4 w-4" />, description: 'EndNote library', extensions: ['.enl'], category: 'data' },
};

export const ImportDialog: React.FC<ImportDialogProps> = ({
  isOpen,
  onClose,
  onImportStart,
  workspaceId,
  supportedFormats,
  className,
}) => {
  const { isInitialized } = useTauri();
  
  const [step, setStep] = useState<'source' | 'options' | 'preview' | 'processing'>('source');
  const [selectedFormat, setSelectedFormat] = useState<ImportFormat>('markdown');
  const [sourceInfo, setSourceInfo] = useState<ImportSourceInfo>({});
  const [importOptions, setImportOptions] = useState<ImportOptions>({
    merge_duplicates: true,
    preserve_timestamps: true,
    preserve_metadata: true,
    preserve_tags: true,
    preserve_links: true,
    extract_images: true,
    convert_formats: true,
    clean_html: true,
    extract_citations: false,
    generate_summaries: false,
    create_folders: true,
    folder_structure: 'hierarchical',
    naming_convention: 'original',
    validate_before_import: true,
    skip_invalid_items: true,
    require_manual_review: false,
    batch_size: 100,
    parallel_processing: true,
    enable_rollback: true,
    dry_run: false,
    encrypt_sensitive_data: false,
  });
  
  const [preview, setPreview] = useState<ImportPreview | null>(null);
  const [validationResults, setValidationResults] = useState<ValidationResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [importName, setImportName] = useState('');
  const [importDescription, setImportDescription] = useState('');

  const availableFormats = supportedFormats || Object.keys(FORMAT_INFO) as ImportFormat[];
  
  const formatsByCategory = availableFormats.reduce((acc, format) => {
    const category = FORMAT_INFO[format].category;
    if (!acc[category]) acc[category] = [];
    acc[category].push(format);
    return acc;
  }, {} as Record<string, ImportFormat[]>);

  const handleFileSelect = async () => {
    if (!isInitialized) {
      toast.error('System not ready');
      return;
    }

    try {
      const selected = await open({
        multiple: true,
        filters: [{
          name: 'All supported files',
          extensions: FORMAT_INFO[selectedFormat].extensions.map(ext => ext.slice(1)) // Remove dot
        }]
      });

      if (selected && selected.length > 0) {
        // For single file selection, selected is a string
        // For multiple files, it's an array
        const filePaths = Array.isArray(selected) ? selected : [selected];
        
        setSourceInfo({
          file_path: filePaths[0], // Primary file
          file_count: filePaths.length,
        });

        // Auto-generate import name from file
        const fileName = filePaths[0].split('/').pop()?.split('.').slice(0, -1).join('.') || 'Import';
        setImportName(fileName);
      }
    } catch (error) {
      console.error('Failed to select file:', error);
      toast.error('Failed to select file');
    }
  };

  const handleFolderSelect = async () => {
    if (!isInitialized) {
      toast.error('System not ready');
      return;
    }

    try {
      const selected = await open({
        directory: true,
      });

      if (selected) {
        setSourceInfo({
          file_path: selected as string,
        });

        // Auto-generate import name from folder
        const folderName = (selected as string).split('/').pop() || 'Import';
        setImportName(folderName);
      }
    } catch (error) {
      console.error('Failed to select folder:', error);
      toast.error('Failed to select folder');
    }
  };

  const generatePreview = async () => {
    if (!sourceInfo.file_path) {
      toast.error('Please select a source first');
      return;
    }

    setIsLoading(true);
    try {
      const previewData: ImportPreview = await invoke('generate_import_preview', {
        sourcePath: sourceInfo.file_path,
        format: selectedFormat,
        options: importOptions,
      });

      setPreview(previewData);
      
      // Run validation if enabled
      if (importOptions.validate_before_import) {
        const validation: ValidationResult[] = await invoke('validate_import_data', {
          sourcePath: sourceInfo.file_path,
          format: selectedFormat,
          options: importOptions,
        });
        setValidationResults(validation);
      }

      setStep('preview');
    } catch (error) {
      console.error('Failed to generate preview:', error);
      toast.error('Failed to generate preview');
    } finally {
      setIsLoading(false);
    }
  };

  const startImport = async () => {
    if (!preview || !sourceInfo.file_path) return;

    const importJob: ImportJob = {
      id: `import_${Date.now()}`,
      workspace_id: workspaceId,
      name: importName.trim() || 'Untitled Import',
      description: importDescription.trim() || undefined,
      format: selectedFormat,
      status: 'pending',
      progress: 0,
      created_at: new Date().toISOString(),
      source_info: sourceInfo,
      import_options: importOptions,
      validation_results: validationResults,
      imported_items: [],
      metadata: {
        preview: preview,
        user_agent: 'lighthouse-lm-import',
      },
    };

    try {
      await invoke('start_import_job', { job: importJob });
      onImportStart(importJob);
      onClose();
      toast.success('Import started successfully');
    } catch (error) {
      console.error('Failed to start import:', error);
      toast.error('Failed to start import');
    }
  };

  const resetDialog = () => {
    setStep('source');
    setSelectedFormat('markdown');
    setSourceInfo({});
    setPreview(null);
    setValidationResults([]);
    setImportName('');
    setImportDescription('');
  };

  const handleClose = () => {
    resetDialog();
    onClose();
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m ${seconds % 60}s`;
    return `${Math.floor(seconds / 3600)}h ${Math.floor((seconds % 3600) / 60)}m`;
  };

  const getValidationSummary = () => {
    const errors = validationResults.filter(r => r.severity === 'error').length;
    const warnings = validationResults.filter(r => r.severity === 'warning').length;
    return { errors, warnings };
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className={cn("sm:max-w-4xl", className)}>
        <DialogHeader>
          <DialogTitle>Import Data</DialogTitle>
          <DialogDescription>
            Import content from external sources and formats
          </DialogDescription>
        </DialogHeader>

        <Tabs value={step} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="source">Source & Format</TabsTrigger>
            <TabsTrigger value="options" disabled={step === 'source'}>Options</TabsTrigger>
            <TabsTrigger value="preview" disabled={!preview}>Preview</TabsTrigger>
          </TabsList>

          {/* Source Selection */}
          <TabsContent value="source" className="space-y-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="import-name">Import name</Label>
                <Input
                  id="import-name"
                  value={importName}
                  onChange={(e) => setImportName(e.target.value)}
                  placeholder="Enter import name"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="import-description">Description (optional)</Label>
                <Textarea
                  id="import-description"
                  value={importDescription}
                  onChange={(e) => setImportDescription(e.target.value)}
                  placeholder="Describe what you're importing"
                  rows={2}
                />
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <Label>Select format</Label>
              <div className="space-y-4">
                {Object.entries(formatsByCategory).map(([category, formats]) => (
                  <div key={category} className="space-y-2">
                    <h4 className="text-sm font-medium text-muted-foreground capitalize">
                      {category.replace('_', ' ')} Tools
                    </h4>
                    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2">
                      {formats.map((format) => {
                        const info = FORMAT_INFO[format];
                        return (
                          <Card
                            key={format}
                            className={cn(
                              "cursor-pointer transition-all hover:shadow-md",
                              selectedFormat === format && "ring-2 ring-primary"
                            )}
                            onClick={() => setSelectedFormat(format)}
                          >
                            <CardContent className="p-3">
                              <div className="flex items-center space-x-2">
                                {info.icon}
                                <div className="flex-1 min-w-0">
                                  <p className="text-sm font-medium truncate">{info.name}</p>
                                  <p className="text-xs text-muted-foreground truncate">
                                    {info.extensions.join(', ')}
                                  </p>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <Label>Select source</Label>
              <div className="grid grid-cols-2 gap-4">
                <Button
                  variant="outline"
                  className="h-24 flex-col"
                  onClick={handleFileSelect}
                >
                  <FileText className="h-8 w-8 mb-2" />
                  <span>Select Files</span>
                  <span className="text-xs text-muted-foreground">Choose individual files</span>
                </Button>
                
                <Button
                  variant="outline"
                  className="h-24 flex-col"
                  onClick={handleFolderSelect}
                >
                  <Archive className="h-8 w-8 mb-2" />
                  <span>Select Folder</span>
                  <span className="text-xs text-muted-foreground">Import entire folder</span>
                </Button>
              </div>

              {sourceInfo.file_path && (
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <CheckCircle2 className="h-4 w-4 text-green-600" />
                        <span className="text-sm font-medium">Source selected</span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSourceInfo({})}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    <p className="text-sm text-muted-foreground mt-2 font-mono">
                      {sourceInfo.file_path}
                    </p>
                    {sourceInfo.file_count && sourceInfo.file_count > 1 && (
                      <Badge variant="secondary" className="mt-2">
                        {sourceInfo.file_count} files
                      </Badge>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          {/* Import Options */}
          <TabsContent value="options" className="space-y-6">
            <ScrollArea className="h-[400px] pr-4">
              <div className="space-y-6">
                {/* Content Processing */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Settings className="h-5 w-5" />
                      <span>Content Processing</span>
                    </CardTitle>
                    <CardDescription>
                      Configure how content is processed during import
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={importOptions.merge_duplicates}
                          onCheckedChange={(checked) =>
                            setImportOptions(prev => ({ ...prev, merge_duplicates: checked }))
                          }
                        />
                        <Label>Merge duplicates</Label>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={importOptions.preserve_timestamps}
                          onCheckedChange={(checked) =>
                            setImportOptions(prev => ({ ...prev, preserve_timestamps: checked }))
                          }
                        />
                        <Label>Preserve timestamps</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={importOptions.preserve_metadata}
                          onCheckedChange={(checked) =>
                            setImportOptions(prev => ({ ...prev, preserve_metadata: checked }))
                          }
                        />
                        <Label>Preserve metadata</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={importOptions.preserve_tags}
                          onCheckedChange={(checked) =>
                            setImportOptions(prev => ({ ...prev, preserve_tags: checked }))
                          }
                        />
                        <Label>Preserve tags</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={importOptions.extract_images}
                          onCheckedChange={(checked) =>
                            setImportOptions(prev => ({ ...prev, extract_images: checked }))
                          }
                        />
                        <Label>Extract images</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={importOptions.convert_formats}
                          onCheckedChange={(checked) =>
                            setImportOptions(prev => ({ ...prev, convert_formats: checked }))
                          }
                        />
                        <Label>Convert formats</Label>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Organization */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <HardDrive className="h-5 w-5" />
                      <span>Organization</span>
                    </CardTitle>
                    <CardDescription>
                      Configure how imported content is organized
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label>Folder structure</Label>
                      <Select
                        value={importOptions.folder_structure}
                        onValueChange={(value: typeof importOptions.folder_structure) =>
                          setImportOptions(prev => ({ ...prev, folder_structure: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="flat">Flat (all in one folder)</SelectItem>
                          <SelectItem value="hierarchical">Hierarchical (preserve structure)</SelectItem>
                          <SelectItem value="by_type">By type (group by file type)</SelectItem>
                          <SelectItem value="by_date">By date (group by creation date)</SelectItem>
                          <SelectItem value="custom">Custom structure</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Naming convention</Label>
                      <Select
                        value={importOptions.naming_convention}
                        onValueChange={(value: typeof importOptions.naming_convention) =>
                          setImportOptions(prev => ({ ...prev, naming_convention: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="original">Original names</SelectItem>
                          <SelectItem value="sanitized">Sanitized names</SelectItem>
                          <SelectItem value="timestamped">Add timestamps</SelectItem>
                          <SelectItem value="sequential">Sequential numbering</SelectItem>
                          <SelectItem value="custom">Custom pattern</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>

                {/* Performance & Security */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Zap className="h-5 w-5" />
                      <span>Performance & Security</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label>Batch size</Label>
                        <span className="text-sm text-muted-foreground">
                          {importOptions.batch_size} items
                        </span>
                      </div>
                      <Slider
                        value={[importOptions.batch_size]}
                        onValueChange={([value]) =>
                          setImportOptions(prev => ({ ...prev, batch_size: value }))
                        }
                        min={10}
                        max={1000}
                        step={10}
                        className="w-full"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={importOptions.parallel_processing}
                          onCheckedChange={(checked) =>
                            setImportOptions(prev => ({ ...prev, parallel_processing: checked }))
                          }
                        />
                        <Label>Parallel processing</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={importOptions.enable_rollback}
                          onCheckedChange={(checked) =>
                            setImportOptions(prev => ({ ...prev, enable_rollback: checked }))
                          }
                        />
                        <Label>Enable rollback</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={importOptions.encrypt_sensitive_data}
                          onCheckedChange={(checked) =>
                            setImportOptions(prev => ({ ...prev, encrypt_sensitive_data: checked }))
                          }
                        />
                        <Label>Encrypt sensitive data</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={importOptions.dry_run}
                          onCheckedChange={(checked) =>
                            setImportOptions(prev => ({ ...prev, dry_run: checked }))
                          }
                        />
                        <Label>Dry run (preview only)</Label>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </ScrollArea>
          </TabsContent>

          {/* Preview */}
          <TabsContent value="preview" className="space-y-6">
            {preview && (
              <div className="space-y-6">
                {/* Summary */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Eye className="h-5 w-5" />
                      <span>Import Summary</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold">{preview.total_items}</p>
                      <p className="text-sm text-muted-foreground">Total Items</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">{formatBytes(preview.estimated_size)}</p>
                      <p className="text-sm text-muted-foreground">Estimated Size</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">{formatDuration(preview.estimated_duration)}</p>
                      <p className="text-sm text-muted-foreground">Estimated Time</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">{preview.potential_conflicts.length}</p>
                      <p className="text-sm text-muted-foreground">Potential Conflicts</p>
                    </div>
                  </CardContent>
                </Card>

                {/* Item Breakdown */}
                <Card>
                  <CardHeader>
                    <CardTitle>Item Breakdown</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {Object.entries(preview.item_breakdown).map(([type, count]) => (
                        <div key={type} className="flex items-center justify-between">
                          <span className="text-sm capitalize">{type.replace('_', ' ')}</span>
                          <Badge variant="secondary">{count}</Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Validation Results */}
                {validationResults.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <AlertTriangle className="h-5 w-5" />
                        <span>Validation Results</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {(() => {
                          const { errors, warnings } = getValidationSummary();
                          return (
                            <div className="flex space-x-4 mb-4">
                              {errors > 0 && (
                                <Badge variant="destructive">{errors} errors</Badge>
                              )}
                              {warnings > 0 && (
                                <Badge variant="secondary">{warnings} warnings</Badge>
                              )}
                            </div>
                          );
                        })()}
                        
                        <ScrollArea className="h-32">
                          {validationResults.slice(0, 10).map((result) => (
                            <div key={result.id} className="flex items-start space-x-2 py-2">
                              {result.severity === 'error' ? (
                                <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5" />
                              ) : (
                                <Info className="h-4 w-4 text-yellow-500 mt-0.5" />
                              )}
                              <div className="flex-1">
                                <p className="text-sm">{result.message}</p>
                                <p className="text-xs text-muted-foreground">{result.item_path}</p>
                              </div>
                            </div>
                          ))}
                          {validationResults.length > 10 && (
                            <p className="text-sm text-muted-foreground text-center py-2">
                              And {validationResults.length - 10} more...
                            </p>
                          )}
                        </ScrollArea>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Conflicts */}
                {preview.potential_conflicts.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <AlertTriangle className="h-5 w-5 text-orange-500" />
                        <span>Potential Conflicts</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ScrollArea className="h-24">
                        {preview.potential_conflicts.map((conflict, index) => (
                          <p key={index} className="text-sm py-1">{conflict}</p>
                        ))}
                      </ScrollArea>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex justify-between">
          <div>
            {step === 'options' && (
              <Button variant="outline" onClick={() => setStep('source')}>
                Back
              </Button>
            )}
            {step === 'preview' && (
              <Button variant="outline" onClick={() => setStep('options')}>
                Back
              </Button>
            )}
          </div>

          <div className="flex space-x-2">
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            
            {step === 'source' && (
              <Button
                onClick={() => setStep('options')}
                disabled={!sourceInfo.file_path || !importName.trim()}
              >
                Next
              </Button>
            )}
            
            {step === 'options' && (
              <Button
                onClick={generatePreview}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Clock className="mr-2 h-4 w-4 animate-spin" />
                    Generating Preview...
                  </>
                ) : (
                  <>
                    <Eye className="mr-2 h-4 w-4" />
                    Generate Preview
                  </>
                )}
              </Button>
            )}
            
            {step === 'preview' && (
              <Button
                onClick={startImport}
                disabled={
                  !preview ||
                  (validationResults.some(r => r.severity === 'error') && !importOptions.skip_invalid_items)
                }
              >
                <Upload className="mr-2 h-4 w-4" />
                {importOptions.dry_run ? 'Run Dry Run' : 'Start Import'}
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};