import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import {
  Plus,
  Upload,
  FileText,
  MessageSquare,
  Workflow,
  Search,
  Command,
  BookOpen,
  Brain,
  Sparkles,
  Link,
  Mic,
  Camera,
  Zap,
  X
} from 'lucide-react';
import { useKeyboardShortcut } from '../shortcuts/KeyboardShortcuts';
import NotebookContext from '../../contexts/NotebookContext';

interface QuickAction {
  id: string;
  label: string;
  description?: string;
  icon: React.ReactNode;
  shortcut?: string[];
  action: () => void;
  category: 'create' | 'import' | 'generate' | 'navigate';
}

interface QuickActionsProps {
  onCreateNotebook: () => void;
  onUploadSource: () => void;
  onStartChat: () => void;
  onGenerateDiagram: () => void;
  onOpenSearch: () => void;
  onImportFromWeb: () => void;
  className?: string;
}

const QuickActions: React.FC<QuickActionsProps> = ({
  onCreateNotebook,
  onUploadSource,
  onStartChat,
  onGenerateDiagram,
  onOpenSearch,
  onImportFromWeb,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  
  // Safely use context - provide defaults if not available
  const context = React.useContext(NotebookContext);
  const requestAIAssistance = context?.requestAIAssistance || (() => Promise.resolve(''));
  const selectedSources = context?.selectedSources || [];

  const actions: QuickAction[] = [
    {
      id: 'new-notebook',
      label: 'New Notebook',
      description: 'Create a new research notebook',
      icon: <BookOpen className="h-4 w-4" />,
      shortcut: ['⌘', 'N'],
      action: () => {
        onCreateNotebook();
        setIsOpen(false);
      },
      category: 'create'
    },
    {
      id: 'upload-source',
      label: 'Upload Source',
      description: 'Add documents or files',
      icon: <Upload className="h-4 w-4" />,
      shortcut: ['⌘', 'U'],
      action: () => {
        onUploadSource();
        setIsOpen(false);
      },
      category: 'import'
    },
    {
      id: 'import-web',
      label: 'Import from Web',
      description: 'Add content from URL',
      icon: <Link className="h-4 w-4" />,
      action: () => {
        onImportFromWeb();
        setIsOpen(false);
      },
      category: 'import'
    },
    {
      id: 'start-chat',
      label: 'Start AI Chat',
      description: 'Ask questions about your sources',
      icon: <MessageSquare className="h-4 w-4" />,
      action: () => {
        onStartChat();
        setIsOpen(false);
      },
      category: 'generate'
    },
    {
      id: 'generate-diagram',
      label: 'Generate Diagram',
      description: 'Create visual representations',
      icon: <Workflow className="h-4 w-4" />,
      shortcut: ['⌘', 'G'],
      action: () => {
        onGenerateDiagram();
        setIsOpen(false);
      },
      category: 'generate'
    },
    {
      id: 'ai-insights',
      label: 'AI Insights',
      description: 'Get smart analysis',
      icon: <Brain className="h-4 w-4" />,
      action: async () => {
        try {
          const prompt = selectedSources.length > 0 
            ? `Provide insights and analysis for the ${selectedSources.length} selected sources.`
            : 'Provide general insights about the current notebook content.';
          
          await requestAIAssistance(prompt);
        } catch (error) {
        }
        setIsOpen(false);
      },
      category: 'generate'
    },
    {
      id: 'search',
      label: 'Search Everything',
      description: 'Find across all content',
      icon: <Search className="h-4 w-4" />,
      shortcut: ['⌘', 'K'],
      action: () => {
        onOpenSearch();
        setIsOpen(false);
      },
      category: 'navigate'
    }
  ];

  const filteredActions = actions.filter(action =>
    action.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
    action.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const groupedActions = filteredActions.reduce((acc, action) => {
    if (!acc[action.category]) acc[action.category] = [];
    acc[action.category].push(action);
    return acc;
  }, {} as Record<string, QuickAction[]>);

  const categoryLabels = {
    create: 'Create',
    import: 'Import',
    generate: 'Generate',
    navigate: 'Navigate'
  };

  // Keyboard shortcut to open quick actions
  useKeyboardShortcut(['⌘', 'k'], () => setIsOpen(!isOpen));

  // Handle keyboard navigation
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex((prev) => 
            prev < filteredActions.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex((prev) => 
            prev > 0 ? prev - 1 : filteredActions.length - 1
          );
          break;
        case 'Enter':
          e.preventDefault();
          if (filteredActions[selectedIndex]) {
            filteredActions[selectedIndex].action();
          }
          break;
        case 'Escape':
          setIsOpen(false);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, selectedIndex, filteredActions]);

  // Reset selection when search changes
  useEffect(() => {
    setSelectedIndex(0);
  }, [searchQuery]);

  return (
    <>
      {/* Floating Action Button */}
      <motion.div
        className={`fixed bottom-6 right-6 z-40 ${className}`}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <Button
          onClick={() => setIsOpen(!isOpen)}
          size="lg"
          className="rounded-full h-14 w-14 shadow-lg"
        >
          <AnimatePresence mode="wait">
            {isOpen ? (
              <motion.div
                key="close"
                initial={{ rotate: 0 }}
                animate={{ rotate: 45 }}
                exit={{ rotate: 0 }}
              >
                <Plus className="h-6 w-6" />
              </motion.div>
            ) : (
              <motion.div
                key="open"
                initial={{ rotate: 45 }}
                animate={{ rotate: 0 }}
                exit={{ rotate: 45 }}
              >
                <Zap className="h-6 w-6" />
              </motion.div>
            )}
          </AnimatePresence>
        </Button>
      </motion.div>

      {/* Quick Actions Modal */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50"
              onClick={() => setIsOpen(false)}
            />

            {/* Modal */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50 w-full max-w-2xl"
            >
              <Card className="p-0 overflow-hidden">
                <div className="p-4 border-b">
                  <div className="relative">
                    <Command className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      autoFocus
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      placeholder="Type a command or search..."
                      className="pl-9 pr-9"
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsOpen(false)}
                      className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7 p-0"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="max-h-[400px] overflow-y-auto p-2">
                  {Object.entries(groupedActions).map(([category, categoryActions], categoryIndex) => (
                    <div key={category}>
                      {categoryIndex > 0 && <Separator className="my-2" />}
                      <div className="px-2 py-1">
                        <span className="text-xs font-medium text-muted-foreground">
                          {categoryLabels[category as keyof typeof categoryLabels]}
                        </span>
                      </div>
                      <div className="space-y-1">
                        {categoryActions.map((action, index) => {
                          const globalIndex = filteredActions.indexOf(action);
                          const isSelected = globalIndex === selectedIndex;
                          
                          return (
                            <button
                              key={action.id}
                              onClick={action.action}
                              onMouseEnter={() => setSelectedIndex(globalIndex)}
                              className={`
                                w-full flex items-center justify-between p-2 rounded-lg
                                transition-colors text-left
                                ${isSelected ? 'bg-accent' : 'hover:bg-accent/50'}
                              `}
                            >
                              <div className="flex items-center gap-3">
                                <div className="p-1.5 rounded-md bg-primary/10 text-primary">
                                  {action.icon}
                                </div>
                                <div>
                                  <div className="text-sm font-medium">{action.label}</div>
                                  {action.description && (
                                    <div className="text-xs text-muted-foreground">
                                      {action.description}
                                    </div>
                                  )}
                                </div>
                              </div>
                              {action.shortcut && (
                                <div className="flex items-center gap-1">
                                  {action.shortcut.map((key, keyIndex) => (
                                    <React.Fragment key={keyIndex}>
                                      <kbd className="px-1.5 py-0.5 text-xs bg-muted rounded">
                                        {key}
                                      </kbd>
                                    </React.Fragment>
                                  ))}
                                </div>
                              )}
                            </button>
                          );
                        })}
                      </div>
                    </div>
                  ))}

                  {filteredActions.length === 0 && (
                    <div className="py-12 text-center">
                      <Sparkles className="h-12 w-12 text-muted-foreground/20 mx-auto mb-3" />
                      <p className="text-sm text-muted-foreground">No matching commands</p>
                      <p className="text-xs text-muted-foreground mt-1">
                        Try a different search term
                      </p>
                    </div>
                  )}
                </div>

                <div className="p-3 border-t bg-muted/30">
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <div className="flex items-center gap-4">
                      <span className="flex items-center gap-1">
                        <kbd className="px-1 py-0.5 bg-background rounded text-xs">↑↓</kbd>
                        Navigate
                      </span>
                      <span className="flex items-center gap-1">
                        <kbd className="px-1 py-0.5 bg-background rounded text-xs">↵</kbd>
                        Select
                      </span>
                      <span className="flex items-center gap-1">
                        <kbd className="px-1 py-0.5 bg-background rounded text-xs">esc</kbd>
                        Close
                      </span>
                    </div>
                    <span>Quick Actions</span>
                  </div>
                </div>
              </Card>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};

export default QuickActions;