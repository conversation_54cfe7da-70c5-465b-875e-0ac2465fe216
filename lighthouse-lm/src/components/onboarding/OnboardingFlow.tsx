import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import {
  BookOpen,
  FileText,
  Workflow,
  MessageSquare,
  ChevronRight,
  ChevronLeft,
  Check,
  Sparkles,
  Upload,
  Brain
} from 'lucide-react';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  content: React.ReactNode;
  action?: () => void;
}

interface OnboardingFlowProps {
  onComplete: () => void;
  onSkip?: () => void;
}

const OnboardingFlow: React.FC<OnboardingFlowProps> = ({ onComplete, onSkip }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());

  const steps: OnboardingStep[] = [
    {
      id: 'welcome',
      title: 'Welcome to Lighthouse LM',
      description: 'Your AI-powered research and knowledge management assistant',
      icon: <Sparkles className="h-6 w-6" />,
      content: (
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Lighthouse LM helps you organize, analyze, and visualize your research materials
            with the power of AI.
          </p>
          <div className="grid grid-cols-2 gap-3">
            <div className="flex items-start gap-2">
              <Check className="h-4 w-4 text-primary mt-0.5" />
              <span className="text-xs">Smart document processing</span>
            </div>
            <div className="flex items-start gap-2">
              <Check className="h-4 w-4 text-primary mt-0.5" />
              <span className="text-xs">AI-powered insights</span>
            </div>
            <div className="flex items-start gap-2">
              <Check className="h-4 w-4 text-primary mt-0.5" />
              <span className="text-xs">Interactive diagrams</span>
            </div>
            <div className="flex items-start gap-2">
              <Check className="h-4 w-4 text-primary mt-0.5" />
              <span className="text-xs">Knowledge graphs</span>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'notebooks',
      title: 'Create Your First Notebook',
      description: 'Notebooks help you organize related research and sources',
      icon: <BookOpen className="h-6 w-6" />,
      content: (
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Notebooks are containers for your research projects. Each notebook can contain:
          </p>
          <ul className="space-y-2">
            <li className="flex items-start gap-2">
              <FileText className="h-4 w-4 text-primary mt-0.5" />
              <div>
                <span className="text-sm font-medium">Sources</span>
                <p className="text-xs text-muted-foreground">PDFs, documents, web pages, and more</p>
              </div>
            </li>
            <li className="flex items-start gap-2">
              <MessageSquare className="h-4 w-4 text-primary mt-0.5" />
              <div>
                <span className="text-sm font-medium">AI Chat</span>
                <p className="text-xs text-muted-foreground">Ask questions about your sources</p>
              </div>
            </li>
            <li className="flex items-start gap-2">
              <Workflow className="h-4 w-4 text-primary mt-0.5" />
              <div>
                <span className="text-sm font-medium">Diagrams</span>
                <p className="text-xs text-muted-foreground">Visualize concepts and relationships</p>
              </div>
            </li>
          </ul>
        </div>
      )
    },
    {
      id: 'sources',
      title: 'Add Your Sources',
      description: 'Upload documents or paste content to get started',
      icon: <Upload className="h-6 w-6" />,
      content: (
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Lighthouse LM supports various source types:
          </p>
          <div className="grid grid-cols-1 gap-2">
            <div className="p-3 border rounded-lg">
              <div className="font-medium text-sm mb-1">Documents</div>
              <p className="text-xs text-muted-foreground">PDF, Word, Markdown, Text files</p>
            </div>
            <div className="p-3 border rounded-lg">
              <div className="font-medium text-sm mb-1">Web Content</div>
              <p className="text-xs text-muted-foreground">Articles, blog posts, documentation</p>
            </div>
            <div className="p-3 border rounded-lg">
              <div className="font-medium text-sm mb-1">Media</div>
              <p className="text-xs text-muted-foreground">Audio transcripts, video summaries</p>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'ai-features',
      title: 'Leverage AI Intelligence',
      description: 'Use AI to analyze and understand your content',
      icon: <Brain className="h-6 w-6" />,
      content: (
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Our AI assistant can help you:
          </p>
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                <span className="text-xs font-bold">1</span>
              </div>
              <div>
                <div className="font-medium text-sm">Ask Questions</div>
                <p className="text-xs text-muted-foreground">Get answers from your sources with citations</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                <span className="text-xs font-bold">2</span>
              </div>
              <div>
                <div className="font-medium text-sm">Generate Diagrams</div>
                <p className="text-xs text-muted-foreground">Create visual representations automatically</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                <span className="text-xs font-bold">3</span>
              </div>
              <div>
                <div className="font-medium text-sm">Extract Insights</div>
                <p className="text-xs text-muted-foreground">Discover patterns and connections</p>
              </div>
            </div>
          </div>
        </div>
      )
    }
  ];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCompletedSteps(new Set([...completedSteps, currentStep]));
      setCurrentStep(currentStep + 1);
    } else {
      onComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const progress = ((currentStep + 1) / steps.length) * 100;

  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="w-full max-w-2xl"
      >
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                {steps[currentStep].icon}
                <span className="text-xs text-muted-foreground">
                  Step {currentStep + 1} of {steps.length}
                </span>
              </div>
              {onSkip && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onSkip}
                  className="text-xs"
                >
                  Skip Tour
                </Button>
              )}
            </div>
            <Progress value={progress} className="mb-4" />
            <CardTitle>{steps[currentStep].title}</CardTitle>
            <CardDescription>{steps[currentStep].description}</CardDescription>
          </CardHeader>
          <CardContent>
            <AnimatePresence mode="wait">
              <motion.div
                key={steps[currentStep].id}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
              >
                {steps[currentStep].content}
              </motion.div>
            </AnimatePresence>

            <div className="flex items-center justify-between mt-6">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrevious}
                disabled={currentStep === 0}
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>

              <div className="flex items-center gap-1">
                {steps.map((_, index) => (
                  <div
                    key={index}
                    className={`h-1.5 w-1.5 rounded-full transition-colors ${
                      index === currentStep
                        ? 'bg-primary w-6'
                        : completedSteps.has(index)
                        ? 'bg-primary/50'
                        : 'bg-muted'
                    }`}
                  />
                ))}
              </div>

              <Button
                size="sm"
                onClick={handleNext}
              >
                {currentStep === steps.length - 1 ? (
                  <>
                    Get Started
                    <Check className="h-4 w-4 ml-1" />
                  </>
                ) : (
                  <>
                    Next
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default OnboardingFlow;