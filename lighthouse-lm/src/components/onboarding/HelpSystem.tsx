import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Input } from '../ui/input';
import { ScrollArea } from '../ui/scroll-area';
import { Badge } from '../ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '../ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { 
  HelpCircle, 
  Search, 
  Book, 
  Video, 
  MessageCircle, 
  ExternalLink,
  ChevronRight,
  Star,
  Clock,
  Tag
} from 'lucide-react';

interface HelpArticle {
  id: string;
  title: string;
  description: string;
  content: string;
  category: string;
  tags: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: string;
  rating: number;
  lastUpdated: string;
}

interface VideoTutorial {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  duration: string;
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  url: string;
}

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: string;
  helpful: number;
}

interface HelpSystemProps {
  isOpen: boolean;
  onClose: () => void;
}

const HelpSystem: React.FC<HelpSystemProps> = ({ isOpen, onClose }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedArticle, setSelectedArticle] = useState<HelpArticle | null>(null);
  const [activeTab, setActiveTab] = useState('articles');

  // Mock data - in a real app, this would come from an API
  const helpArticles: HelpArticle[] = [
    {
      id: '1',
      title: 'Getting Started with Lighthouse LM',
      description: 'Learn the basics of creating and managing notebooks',
      content: 'This comprehensive guide will walk you through...',
      category: 'Getting Started',
      tags: ['basics', 'notebooks', 'beginner'],
      difficulty: 'beginner',
      estimatedTime: '5 min',
      rating: 4.8,
      lastUpdated: '2024-01-15'
    },
    {
      id: '2',
      title: 'Advanced Search Techniques',
      description: 'Master semantic search and AI-powered queries',
      content: 'Advanced search features allow you to...',
      category: 'Search',
      tags: ['search', 'AI', 'advanced'],
      difficulty: 'advanced',
      estimatedTime: '10 min',
      rating: 4.6,
      lastUpdated: '2024-01-12'
    },
    {
      id: '3',
      title: 'Collaboration Features',
      description: 'Share notebooks and collaborate with your team',
      content: 'Collaboration in Lighthouse LM enables...',
      category: 'Collaboration',
      tags: ['sharing', 'team', 'collaboration'],
      difficulty: 'intermediate',
      estimatedTime: '7 min',
      rating: 4.7,
      lastUpdated: '2024-01-10'
    }
  ];

  const videoTutorials: VideoTutorial[] = [
    {
      id: '1',
      title: 'Quick Start Guide',
      description: 'Get up and running in 5 minutes',
      thumbnail: '/api/placeholder/320/180',
      duration: '5:23',
      category: 'Getting Started',
      difficulty: 'beginner',
      url: '#'
    },
    {
      id: '2',
      title: 'Advanced Notebook Features',
      description: 'Explore version control and export options',
      thumbnail: '/api/placeholder/320/180',
      duration: '12:45',
      category: 'Notebooks',
      difficulty: 'intermediate',
      url: '#'
    }
  ];

  const faqs: FAQ[] = [
    {
      id: '1',
      question: 'How do I create my first notebook?',
      answer: 'Click the "New Notebook" button on the dashboard or use the keyboard shortcut Cmd+N.',
      category: 'Getting Started',
      helpful: 45
    },
    {
      id: '2',
      question: 'Can I collaborate with others on notebooks?',
      answer: 'Yes! Use the collaboration tab in any notebook to invite team members and manage permissions.',
      category: 'Collaboration',
      helpful: 32
    },
    {
      id: '3',
      question: 'How do I export my notebooks?',
      answer: 'Open the notebook and click the Export tab to choose from various formats including PDF, Word, and LaTeX.',
      category: 'Export',
      helpful: 28
    }
  ];

  const categories = ['all', 'Getting Started', 'Search', 'Notebooks', 'Collaboration', 'Export'];

  const filteredArticles = helpArticles.filter(article => {
    const matchesSearch = article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         article.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         article.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesCategory = selectedCategory === 'all' || article.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const filteredFAQs = faqs.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <HelpCircle className="h-5 w-5" />
            Help & Support
          </DialogTitle>
          <DialogDescription>
            Find answers, tutorials, and get help with Lighthouse LM
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col h-[70vh]">
          {/* Search and Filters */}
          <div className="flex gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search help articles, FAQs, and tutorials..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border rounded-md bg-white"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category}
                </option>
              ))}
            </select>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="articles" className="flex items-center gap-2">
                <Book className="h-4 w-4" />
                Articles
              </TabsTrigger>
              <TabsTrigger value="videos" className="flex items-center gap-2">
                <Video className="h-4 w-4" />
                Videos
              </TabsTrigger>
              <TabsTrigger value="faq" className="flex items-center gap-2">
                <MessageCircle className="h-4 w-4" />
                FAQ
              </TabsTrigger>
            </TabsList>

            <TabsContent value="articles" className="flex-1">
              <ScrollArea className="h-full">
                <div className="grid gap-4">
                  {filteredArticles.map(article => (
                    <Card key={article.id} className="cursor-pointer hover:shadow-md transition-shadow"
                          onClick={() => setSelectedArticle(article)}>
                      <CardHeader>
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <CardTitle className="text-lg flex items-center gap-2">
                              {article.title}
                              <ChevronRight className="h-4 w-4" />
                            </CardTitle>
                            <CardDescription className="mt-1">
                              {article.description}
                            </CardDescription>
                          </div>
                          <div className="flex flex-col items-end gap-2">
                            <Badge className={getDifficultyColor(article.difficulty)}>
                              {article.difficulty}
                            </Badge>
                            <div className="flex items-center gap-1 text-sm text-gray-500">
                              <Clock className="h-3 w-3" />
                              {article.estimatedTime}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center justify-between mt-2">
                          <div className="flex gap-1">
                            {article.tags.map(tag => (
                              <Badge key={tag} variant="outline" className="text-xs">
                                <Tag className="h-3 w-3 mr-1" />
                                {tag}
                              </Badge>
                            ))}
                          </div>
                          <div className="flex items-center gap-1 text-sm text-gray-500">
                            <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                            {article.rating}
                          </div>
                        </div>
                      </CardHeader>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="videos" className="flex-1">
              <ScrollArea className="h-full">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {videoTutorials.map(video => (
                    <Card key={video.id} className="cursor-pointer hover:shadow-md transition-shadow">
                      <div className="relative">
                        <img 
                          src={video.thumbnail} 
                          alt={video.title}
                          className="w-full h-40 object-cover rounded-t-lg"
                        />
                        <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-sm">
                          {video.duration}
                        </div>
                      </div>
                      <CardHeader>
                        <CardTitle className="text-base flex items-center justify-between">
                          {video.title}
                          <ExternalLink className="h-4 w-4" />
                        </CardTitle>
                        <CardDescription>{video.description}</CardDescription>
                        <div className="flex items-center justify-between mt-2">
                          <Badge className={getDifficultyColor(video.difficulty)}>
                            {video.difficulty}
                          </Badge>
                          <span className="text-sm text-gray-500">{video.category}</span>
                        </div>
                      </CardHeader>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="faq" className="flex-1">
              <ScrollArea className="h-full">
                <div className="space-y-4">
                  {filteredFAQs.map(faq => (
                    <Card key={faq.id}>
                      <CardHeader>
                        <CardTitle className="text-base">{faq.question}</CardTitle>
                        <CardDescription className="text-sm">
                          {faq.answer}
                        </CardDescription>
                        <div className="flex items-center justify-between mt-2">
                          <Badge variant="outline">{faq.category}</Badge>
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-gray-500">
                              {faq.helpful} people found this helpful
                            </span>
                            <Button variant="outline" size="sm">
                              Helpful
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </div>

        {/* Article Detail Modal */}
        {selectedArticle && (
          <Dialog open={!!selectedArticle} onOpenChange={() => setSelectedArticle(null)}>
            <DialogContent className="max-w-4xl max-h-[90vh]">
              <DialogHeader>
                <DialogTitle>{selectedArticle.title}</DialogTitle>
                <DialogDescription>
                  <div className="flex items-center gap-4 mt-2">
                    <Badge className={getDifficultyColor(selectedArticle.difficulty)}>
                      {selectedArticle.difficulty}
                    </Badge>
                    <span className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {selectedArticle.estimatedTime}
                    </span>
                    <span className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      {selectedArticle.rating}
                    </span>
                  </div>
                </DialogDescription>
              </DialogHeader>
              <ScrollArea className="h-[60vh]">
                <div className="prose max-w-none">
                  <p>{selectedArticle.content}</p>
                  {/* In a real app, this would be rich content */}
                </div>
              </ScrollArea>
            </DialogContent>
          </Dialog>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default HelpSystem;