import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  X,
  ChevronLeft,
  ChevronRight,
  Play,
  Pause,
  RotateCcw,
  Check,
  Lightbulb,
  Target,
  MousePointer,
  Keyboard,
  Eye
} from 'lucide-react';

interface TutorialStep {
  id: string;
  title: string;
  description: string;
  target?: string; // CSS selector for element to highlight
  position?: 'top' | 'bottom' | 'left' | 'right';
  action?: 'click' | 'type' | 'hover' | 'scroll';
  content: React.ReactNode;
  validation?: () => boolean;
  autoAdvance?: boolean;
  delay?: number;
}

interface InteractiveTutorialProps {
  steps: TutorialStep[];
  onComplete: () => void;
  onSkip?: () => void;
  tutorialId: string;
  title: string;
  description: string;
}

const InteractiveTutorial: React.FC<InteractiveTutorialProps> = ({
  steps,
  onComplete,
  onSkip,
  tutorialId,
  title,
  description
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const [highlightedElement, setHighlightedElement] = useState<Element | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const overlayRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  const currentStepData = steps[currentStep];

  // Highlight target element
  useEffect(() => {
    if (currentStepData?.target) {
      const element = document.querySelector(currentStepData.target);
      if (element) {
        setHighlightedElement(element);
        
        // Calculate tooltip position
        const rect = element.getBoundingClientRect();
        const position = currentStepData.position || 'bottom';
        
        let x = rect.left + rect.width / 2;
        let y = rect.bottom + 10;
        
        switch (position) {
          case 'top':
            y = rect.top - 10;
            break;
          case 'left':
            x = rect.left - 10;
            y = rect.top + rect.height / 2;
            break;
          case 'right':
            x = rect.right + 10;
            y = rect.top + rect.height / 2;
            break;
        }
        
        setTooltipPosition({ x, y });
        
        // Scroll element into view
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    } else {
      setHighlightedElement(null);
    }
  }, [currentStep, currentStepData]);

  // Auto-advance logic
  useEffect(() => {
    if (isPlaying && currentStepData?.autoAdvance) {
      const delay = currentStepData.delay || 3000;
      const timer = setTimeout(() => {
        handleNext();
      }, delay);
      return () => clearTimeout(timer);
    }
  }, [currentStep, isPlaying, currentStepData]);

  // Validation check
  useEffect(() => {
    if (currentStepData?.validation) {
      const checkValidation = () => {
        if (currentStepData.validation!()) {
          setCompletedSteps(prev => new Set([...prev, currentStep]));
          if (isPlaying) {
            setTimeout(() => handleNext(), 1000);
          }
        }
      };
      
      const interval = setInterval(checkValidation, 500);
      return () => clearInterval(interval);
    }
  }, [currentStep, currentStepData, isPlaying]);

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCompletedSteps(prev => new Set([...prev, currentStep]));
      setCurrentStep(currentStep + 1);
    } else {
      onComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handlePlay = () => {
    setIsPlaying(!isPlaying);
  };

  const handleReset = () => {
    setCurrentStep(0);
    setCompletedSteps(new Set());
    setIsPlaying(false);
  };

  const progress = ((currentStep + 1) / steps.length) * 100;
  const isStepCompleted = completedSteps.has(currentStep);

  return (
    <>
      {/* Overlay for highlighting */}
      {highlightedElement && (
        <div
          ref={overlayRef}
          className="fixed inset-0 z-40 pointer-events-none"
          style={{
            background: `radial-gradient(circle at ${tooltipPosition.x}px ${tooltipPosition.y}px, transparent 100px, rgba(0,0,0,0.5) 100px)`
          }}
        />
      )}

      {/* Tutorial Tooltip */}
      {highlightedElement && (
        <motion.div
          ref={tooltipRef}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="fixed z-50 max-w-sm"
          style={{
            left: tooltipPosition.x - 150,
            top: tooltipPosition.y
          }}
        >
          <Card className="shadow-lg border-2 border-primary">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <Badge variant="secondary" className="text-xs">
                  Step {currentStep + 1} of {steps.length}
                </Badge>
                <div className="flex items-center gap-1">
                  {currentStepData.action && (
                    <Badge variant="outline" className="text-xs flex items-center gap-1">
                      {currentStepData.action === 'click' && <MousePointer className="w-3 h-3" />}
                      {currentStepData.action === 'type' && <Keyboard className="w-3 h-3" />}
                      {currentStepData.action === 'hover' && <Eye className="w-3 h-3" />}
                      {currentStepData.action}
                    </Badge>
                  )}
                  {isStepCompleted && (
                    <Check className="w-4 h-4 text-green-500" />
                  )}
                </div>
              </div>
              <CardTitle className="text-sm">{currentStepData.title}</CardTitle>
              <CardDescription className="text-xs">
                {currentStepData.description}
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="text-xs mb-3">
                {currentStepData.content}
              </div>
              <div className="flex items-center justify-between">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handlePrevious}
                  disabled={currentStep === 0}
                  className="text-xs"
                >
                  <ChevronLeft className="w-3 h-3 mr-1" />
                  Back
                </Button>
                <Button
                  size="sm"
                  onClick={handleNext}
                  className="text-xs"
                >
                  {currentStep === steps.length - 1 ? 'Finish' : 'Next'}
                  <ChevronRight className="w-3 h-3 ml-1" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Main Tutorial Panel */}
      <div className="fixed bottom-4 right-4 z-50">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="w-80"
        >
          <Card className="shadow-xl">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Lightbulb className="w-4 h-4 text-primary" />
                  <span className="text-sm font-medium">{title}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handlePlay}
                    className="h-6 w-6 p-0"
                  >
                    {isPlaying ? (
                      <Pause className="w-3 h-3" />
                    ) : (
                      <Play className="w-3 h-3" />
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleReset}
                    className="h-6 w-6 p-0"
                  >
                    <RotateCcw className="w-3 h-3" />
                  </Button>
                  {onSkip && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={onSkip}
                      className="h-6 w-6 p-0"
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  )}
                </div>
              </div>
              <Progress value={progress} className="h-1" />
              <p className="text-xs text-muted-foreground mt-1">{description}</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-xs">
                  <span>Progress: {currentStep + 1} / {steps.length}</span>
                  <span className="text-muted-foreground">
                    {Math.round(progress)}% complete
                  </span>
                </div>
                
                {/* Step indicators */}
                <div className="flex items-center gap-1">
                  {steps.map((_, index) => (
                    <div
                      key={index}
                      className={`h-1 flex-1 rounded-full transition-colors ${
                        index === currentStep
                          ? 'bg-primary'
                          : completedSteps.has(index)
                          ? 'bg-green-500'
                          : 'bg-muted'
                      }`}
                    />
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </>
  );
};

export default InteractiveTutorial;
export type { TutorialStep };