import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Play,
  Pause,
  SkipForward,
  RotateCcw,
  CheckCircle,
  Circle,
  BookOpen,
  Lightbulb,
  Target,
  Users,
  Zap,
  Gift,
  Settings,
  X
} from 'lucide-react';
import InteractiveTutorial, { type TutorialStep } from './InteractiveTutorial';
import FeatureDiscovery from './FeatureDiscovery';
import { tauriService } from '@/services/tauriService';

interface OnboardingProgress {
  completedSteps: string[];
  currentStep?: string;
  tutorialsCompleted: string[];
  featuresDiscovered: string[];
  onboardingCompleted: boolean;
  lastActiveDate: string;
}

interface OnboardingSystemProps {
  onComplete?: () => void;
  onSkip?: () => void;
  autoStart?: boolean;
  showFeatureDiscovery?: boolean;
}

const OnboardingSystem: React.FC<OnboardingSystemProps> = ({
  onComplete,
  onSkip,
  autoStart = false,
  showFeatureDiscovery = true
}) => {
  const [isActive, setIsActive] = useState(false);
  const [activeTab, setActiveTab] = useState('tutorial');
  const [progress, setProgress] = useState<OnboardingProgress>({
    completedSteps: [],
    tutorialsCompleted: [],
    featuresDiscovered: [],
    onboardingCompleted: false,
    lastActiveDate: new Date().toISOString()
  });
  const [activeTutorial, setActiveTutorial] = useState<string | null>(null);

  // Tutorial definitions
  const tutorials = {
    'getting-started': {
      id: 'getting-started',
      title: 'Getting Started with Lighthouse',
      description: 'Learn the basics of creating and managing your research projects',
      category: 'essential' as const,
      estimatedTime: 5,
      steps: [
        {
          id: 'welcome',
          title: 'Welcome to Lighthouse',
          description: 'Lighthouse is your AI-powered research assistant. Let\'s explore the main features together.',
          content: 'Lighthouse is your AI-powered research assistant. Let\'s explore the main features together.',
          target: '.dashboard-header',
          position: 'bottom' as const,
          action: 'hover' as const
        },
        {
          id: 'create-notebook',
          title: 'Create Your First Notebook',
          description: 'Notebooks are containers for your research projects. Click the "New Notebook" button to get started.',
          content: 'Notebooks are containers for your research projects. Click the "New Notebook" button to get started.',
          target: '[data-testid="new-notebook-button"]',
          position: 'bottom' as const,
          action: 'click' as const
        },
        {
          id: 'add-sources',
          title: 'Add Research Sources',
          description: 'Add documents, websites, or other sources to your notebook for AI analysis.',
          content: 'Add documents, websites, or other sources to your notebook for AI analysis.',
          target: '.source-upload-area',
          position: 'right' as const,
          action: 'hover' as const
        },
        {
          id: 'chat-with-ai',
          title: 'Chat with AI',
          description: 'Ask questions about your sources and get intelligent insights.',
          content: 'Ask questions about your sources and get intelligent insights.',
          target: '.chat-input',
          position: 'top' as const,
          action: 'click' as const
        }
      ] as TutorialStep[]
    },
    'advanced-features': {
      id: 'advanced-features',
      title: 'Advanced Features',
      description: 'Discover powerful features for collaboration and productivity',
      category: 'advanced' as const,
      estimatedTime: 8,
      steps: [
        {
          id: 'collaboration',
          title: 'Collaborate with Others',
          description: 'Share your notebooks and work together with your team.',
          content: 'Share your notebooks and work together with your team.',
          target: '[data-testid="collaboration-tab"]',
          position: 'bottom' as const,
          action: 'click' as const
        },
        {
          id: 'version-control',
          title: 'Version Control',
          description: 'Track changes and manage different versions of your research.',
          content: 'Track changes and manage different versions of your research.',
          target: '[data-testid="versions-tab"]',
          position: 'bottom' as const,
          action: 'click' as const
        },
        {
          id: 'export-options',
          title: 'Export Your Work',
          description: 'Export your research in various formats including PDF, Word, and LaTeX.',
          content: 'Export your research in various formats including PDF, Word, and LaTeX.',
          target: '[data-testid="export-tab"]',
          position: 'bottom' as const,
          action: 'click' as const
        }
      ] as TutorialStep[]
    },
    'productivity-tips': {
      id: 'productivity-tips',
      title: 'Productivity Tips',
      description: 'Learn shortcuts and best practices to work more efficiently',
      category: 'tips' as const,
      estimatedTime: 6,
      steps: [
        {
          id: 'keyboard-shortcuts',
          title: 'Keyboard Shortcuts',
          description: 'Use Cmd+K to quickly search, Cmd+N for new notebook, and Cmd+/ for help.',
          content: 'Use Cmd+K to quickly search, Cmd+N for new notebook, and Cmd+/ for help.',
          target: '.search-bar',
          position: 'bottom' as const,
          action: 'hover' as const
        },
        {
          id: 'quick-actions',
          title: 'Quick Actions',
          description: 'Right-click on sources and notes for quick actions and context menus.',
          content: 'Right-click on sources and notes for quick actions and context menus.',
          target: '.source-list',
          position: 'right' as const,
          action: 'hover' as const
        },
        {
          id: 'templates',
          title: 'Use Templates',
          description: 'Save time with pre-built templates for common research tasks.',
          content: 'Save time with pre-built templates for common research tasks.',
          target: '[data-testid="templates-button"]',
          position: 'bottom' as const,
          action: 'click' as const
        }
      ] as TutorialStep[]
    }
  };

  // Feature discovery definitions
  const features = [
    {
      id: 'new-ai-models',
      title: 'New AI Models Available',
      description: 'Try our latest GPT-4 and Claude models for better research insights.',
      category: 'new' as const,
      priority: 'high' as const,
      target: '.model-selector',
      position: 'bottom' as const,
      icon: <Zap className="w-4 h-4" />,
      action: {
        label: 'Explore Models',
        onClick: () => {
          // Navigate to model settings
          console.log('Navigate to model settings');
        }
      }
    },
    {
      id: 'collaboration-update',
      title: 'Enhanced Collaboration',
      description: 'Real-time editing and commenting are now available in shared notebooks.',
      category: 'updated' as const,
      priority: 'medium' as const,
      conditions: {
        path: '/notebook'
      },
      icon: <Users className="w-4 h-4" />,
      action: {
        label: 'Try Collaboration',
        onClick: () => {
          // Open collaboration panel
          console.log('Open collaboration panel');
        }
      }
    },
    {
      id: 'search-tip',
      title: 'Pro Tip: Semantic Search',
      description: 'Use natural language to search across all your notebooks and sources.',
      category: 'tip' as const,
      priority: 'low' as const,
      target: '.search-bar',
      position: 'bottom' as const,
      icon: <Lightbulb className="w-4 h-4" />
    },
    {
      id: 'keyboard-shortcut',
      title: 'Quick Search Shortcut',
      description: 'Press Cmd+K anywhere to open the quick search dialog.',
      category: 'shortcut' as const,
      priority: 'medium' as const,
      icon: <Target className="w-4 h-4" />
    }
  ];

  // Load progress from backend state
  useEffect(() => {
    const loadProgress = async () => {
      try {
        const savedProgress = await tauriService.getAppState('lighthouse-onboarding-progress');
        if (savedProgress) {
          setProgress(JSON.parse(savedProgress));
        }
      } catch (error) {
        console.warn('Failed to load onboarding progress from backend, using localStorage fallback:', error);
        // Fallback to localStorage for backward compatibility
        const localProgress = localStorage.getItem('lighthouse-onboarding-progress');
        if (localProgress) {
          setProgress(JSON.parse(localProgress));
        }
      }
    };
    loadProgress();
  }, []);

  // Save progress to backend state
  useEffect(() => {
    const saveProgress = async () => {
      try {
        await tauriService.setAppState('lighthouse-onboarding-progress', JSON.stringify(progress));
      } catch (error) {
        console.warn('Failed to save onboarding progress to backend, falling back to localStorage:', error);
        // Fallback to localStorage for backward compatibility
        localStorage.setItem('lighthouse-onboarding-progress', JSON.stringify(progress));
      }
    };
    saveProgress();
  }, [progress]);

  // Auto-start onboarding for new users
  useEffect(() => {
    if (autoStart && !progress.onboardingCompleted && progress.completedSteps.length === 0) {
      setIsActive(true);
    }
  }, [autoStart, progress]);

  const handleTutorialComplete = (tutorialId: string) => {
    setProgress(prev => ({
      ...prev,
      tutorialsCompleted: [...prev.tutorialsCompleted, tutorialId],
      lastActiveDate: new Date().toISOString()
    }));
    setActiveTutorial(null);
  };

  const handleTutorialStep = (stepId: string) => {
    setProgress(prev => ({
      ...prev,
      completedSteps: [...prev.completedSteps, stepId],
      currentStep: stepId,
      lastActiveDate: new Date().toISOString()
    }));
  };

  const handleFeatureDiscovered = (featureId: string) => {
    setProgress(prev => ({
      ...prev,
      featuresDiscovered: [...prev.featuresDiscovered, featureId],
      lastActiveDate: new Date().toISOString()
    }));
  };

  const handleCompleteOnboarding = () => {
    setProgress(prev => ({
      ...prev,
      onboardingCompleted: true,
      lastActiveDate: new Date().toISOString()
    }));
    setIsActive(false);
    if (onComplete) {
      onComplete();
    }
  };

  const handleSkipOnboarding = () => {
    setIsActive(false);
    if (onSkip) {
      onSkip();
    }
  };

  const getOverallProgress = () => {
    const totalSteps = Object.values(tutorials).reduce((acc, tutorial) => acc + tutorial.steps.length, 0);
    const completedSteps = progress.completedSteps.length;
    return Math.round((completedSteps / totalSteps) * 100);
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'essential':
        return <BookOpen className="w-4 h-4" />;
      case 'advanced':
        return <Target className="w-4 h-4" />;
      case 'tips':
        return <Lightbulb className="w-4 h-4" />;
      default:
        return <Circle className="w-4 h-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'essential':
        return 'bg-blue-500';
      case 'advanced':
        return 'bg-purple-500';
      case 'tips':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  if (!isActive) {
    return showFeatureDiscovery ? (
      <FeatureDiscovery
        features={features}
        onFeatureDismiss={handleFeatureDiscovered}
      />
    ) : null;
  }

  return (
    <>
      {/* Main Onboarding Interface */}
      <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="w-full max-w-4xl max-h-[90vh] overflow-hidden"
        >
          <Card className="h-full">
            <CardHeader className="border-b">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Gift className="w-5 h-5 text-primary" />
                    Welcome to Lighthouse
                  </CardTitle>
                  <CardDescription>
                    Let's get you started with a quick tour of the features
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <div className="text-sm text-muted-foreground">
                    {getOverallProgress()}% Complete
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleSkipOnboarding}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              <Progress value={getOverallProgress()} className="mt-2" />
            </CardHeader>
            
            <CardContent className="p-6">
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="tutorial">Interactive Tutorials</TabsTrigger>
                  <TabsTrigger value="overview">Quick Overview</TabsTrigger>
                </TabsList>
                
                <TabsContent value="tutorial" className="mt-6">
                  <div className="space-y-4">
                    {Object.values(tutorials).map(tutorial => {
                      const isCompleted = progress.tutorialsCompleted.includes(tutorial.id);
                      const isActive = activeTutorial === tutorial.id;
                      
                      return (
                        <Card key={tutorial.id} className={`cursor-pointer transition-all ${
                          isActive ? 'ring-2 ring-primary' : 'hover:shadow-md'
                        }`}>
                          <CardHeader className="pb-3">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className={`p-2 rounded-full ${getCategoryColor(tutorial.category)} text-white`}>
                                  {getCategoryIcon(tutorial.category)}
                                </div>
                                <div>
                                  <div className="flex items-center gap-2">
                                    <CardTitle className="text-base">{tutorial.title}</CardTitle>
                                    {isCompleted && <CheckCircle className="w-4 h-4 text-green-500" />}
                                  </div>
                                  <CardDescription>{tutorial.description}</CardDescription>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Badge variant="secondary" className="text-xs">
                                  {tutorial.estimatedTime} min
                                </Badge>
                                <Badge variant="outline" className="text-xs capitalize">
                                  {tutorial.category}
                                </Badge>
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent className="pt-0">
                            <div className="flex items-center justify-between">
                              <div className="text-sm text-muted-foreground">
                                {tutorial.steps.length} steps
                              </div>
                              <Button
                                size="sm"
                                variant={isActive ? "secondary" : "default"}
                                onClick={() => setActiveTutorial(isActive ? null : tutorial.id)}
                                disabled={isCompleted}
                              >
                                {isCompleted ? (
                                  <>Completed <CheckCircle className="w-4 h-4 ml-1" /></>
                                ) : isActive ? (
                                  <>Stop <Pause className="w-4 h-4 ml-1" /></>
                                ) : (
                                  <>Start <Play className="w-4 h-4 ml-1" /></>
                                )}
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                  
                  <div className="mt-6 flex items-center justify-between">
                    <Button variant="outline" onClick={handleSkipOnboarding}>
                      Skip for Now
                    </Button>
                    <Button onClick={handleCompleteOnboarding}>
                      Complete Onboarding
                    </Button>
                  </div>
                </TabsContent>
                
                <TabsContent value="overview" className="mt-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-base flex items-center gap-2">
                          <BookOpen className="w-4 h-4" />
                          Create & Organize
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-2 text-sm text-muted-foreground">
                          <li>• Create notebooks for your research projects</li>
                          <li>• Upload documents, PDFs, and web sources</li>
                          <li>• Organize with tags and categories</li>
                        </ul>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-base flex items-center gap-2">
                          <Zap className="w-4 h-4" />
                          AI-Powered Analysis
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-2 text-sm text-muted-foreground">
                          <li>• Chat with your documents using AI</li>
                          <li>• Get summaries and insights</li>
                          <li>• Generate citations and references</li>
                        </ul>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-base flex items-center gap-2">
                          <Users className="w-4 h-4" />
                          Collaborate
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-2 text-sm text-muted-foreground">
                          <li>• Share notebooks with team members</li>
                          <li>• Real-time editing and comments</li>
                          <li>• Version control and history</li>
                        </ul>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-base flex items-center gap-2">
                          <Settings className="w-4 h-4" />
                          Export & Share
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-2 text-sm text-muted-foreground">
                          <li>• Export to PDF, Word, LaTeX</li>
                          <li>• Generate presentations</li>
                          <li>• Share public links</li>
                        </ul>
                      </CardContent>
                    </Card>
                  </div>
                  
                  <div className="mt-6 flex items-center justify-between">
                    <Button variant="outline" onClick={handleSkipOnboarding}>
                      Skip for Now
                    </Button>
                    <Button onClick={() => setActiveTab('tutorial')}>
                      Start Interactive Tutorial
                    </Button>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </motion.div>
      </div>
      
      {/* Active Tutorial */}
      {activeTutorial && tutorials[activeTutorial as keyof typeof tutorials] && (
        <InteractiveTutorial
          steps={tutorials[activeTutorial as keyof typeof tutorials].steps}
          onComplete={() => handleTutorialComplete(activeTutorial)}
          tutorialId={activeTutorial}
          title={tutorials[activeTutorial as keyof typeof tutorials].title}
          description={tutorials[activeTutorial as keyof typeof tutorials].description}
        />
      )}
      
      {/* Feature Discovery */}
      {showFeatureDiscovery && (
        <FeatureDiscovery
          features={features}
          onFeatureDismiss={handleFeatureDiscovered}
        />
      )}
    </>
  );
};

export default OnboardingSystem;