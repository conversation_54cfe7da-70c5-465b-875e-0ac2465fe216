import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { tauriService } from '@/services/tauriService';
import {
  X,
  Sparkles,
  ArrowRight,
  Lightbulb,
  Star,
  Zap,
  Gift,
  TrendingUp,
  Clock,
  Users,
  Target
} from 'lucide-react';

interface Feature {
  id: string;
  title: string;
  description: string;
  category: 'new' | 'updated' | 'tip' | 'shortcut';
  target?: string; // CSS selector
  position?: 'top' | 'bottom' | 'left' | 'right';
  priority: 'high' | 'medium' | 'low';
  icon?: React.ReactNode;
  action?: {
    label: string;
    onClick: () => void;
  };
  dismissible?: boolean;
  autoShow?: boolean;
  showAfter?: number; // milliseconds
  conditions?: {
    path?: string;
    element?: string;
    userAction?: string;
  };
}

interface FeatureDiscoveryProps {
  features: Feature[];
  onFeatureDismiss?: (featureId: string) => void;
  onAllDismiss?: () => void;
}

const FeatureDiscovery: React.FC<FeatureDiscoveryProps> = ({
  features,
  onFeatureDismiss,
  onAllDismiss
}) => {
  const [activeFeatures, setActiveFeatures] = useState<Feature[]>([]);
  const [dismissedFeatures, setDismissedFeatures] = useState<Set<string>>(new Set());
  const [positions, setPositions] = useState<Record<string, { x: number; y: number }>>({});
  const observerRef = useRef<MutationObserver | null>(null);

  // Load dismissed features from backend state
  useEffect(() => {
    const loadDismissedFeatures = async () => {
      try {
        const dismissed = await tauriService.getAppState('lighthouse-dismissed-features');
        if (dismissed) {
          setDismissedFeatures(new Set(JSON.parse(dismissed)));
        }
      } catch (error) {
        console.warn('Failed to load dismissed features from backend, using localStorage fallback:', error);
        // Fallback to localStorage for backward compatibility
        const localDismissed = localStorage.getItem('lighthouse-dismissed-features');
        if (localDismissed) {
          setDismissedFeatures(new Set(JSON.parse(localDismissed)));
        }
      }
    };
    loadDismissedFeatures();
  }, []);

  // Check which features should be shown
  useEffect(() => {
    const checkFeatures = () => {
      const currentPath = window.location.pathname;
      const eligibleFeatures = features.filter(feature => {
        // Skip dismissed features
        if (dismissedFeatures.has(feature.id)) return false;
        
        // Check path condition
        if (feature.conditions?.path && !currentPath.includes(feature.conditions.path)) {
          return false;
        }
        
        // Check element condition
        if (feature.conditions?.element) {
          const element = document.querySelector(feature.conditions.element);
          if (!element) return false;
        }
        
        return true;
      });
      
      setActiveFeatures(eligibleFeatures);
    };
    
    checkFeatures();
    
    // Set up mutation observer to watch for DOM changes
    observerRef.current = new MutationObserver(checkFeatures);
    observerRef.current.observe(document.body, {
      childList: true,
      subtree: true
    });
    
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [features, dismissedFeatures]);

  // Calculate positions for features with targets
  useEffect(() => {
    const newPositions: Record<string, { x: number; y: number }> = {};
    
    activeFeatures.forEach(feature => {
      if (feature.target) {
        const element = document.querySelector(feature.target);
        if (element) {
          const rect = element.getBoundingClientRect();
          const position = feature.position || 'bottom';
          
          let x = rect.left + rect.width / 2;
          let y = rect.bottom + 10;
          
          switch (position) {
            case 'top':
              y = rect.top - 10;
              break;
            case 'left':
              x = rect.left - 10;
              y = rect.top + rect.height / 2;
              break;
            case 'right':
              x = rect.right + 10;
              y = rect.top + rect.height / 2;
              break;
          }
          
          newPositions[feature.id] = { x, y };
        }
      }
    });
    
    setPositions(newPositions);
  }, [activeFeatures]);

  const handleDismiss = async (featureId: string) => {
    const newDismissed = new Set([...dismissedFeatures, featureId]);
    setDismissedFeatures(newDismissed);
    
    try {
      await tauriService.setAppState('lighthouse-dismissed-features', JSON.stringify([...newDismissed]));
    } catch (error) {
      console.warn('Failed to save dismissed features to backend, falling back to localStorage:', error);
      // Fallback to localStorage for backward compatibility
      localStorage.setItem('lighthouse-dismissed-features', JSON.stringify([...newDismissed]));
    }
    
    if (onFeatureDismiss) {
      onFeatureDismiss(featureId);
    }
  };

  const handleDismissAll = async () => {
    const allFeatureIds = new Set(features.map(f => f.id));
    setDismissedFeatures(allFeatureIds);
    
    try {
      await tauriService.setAppState('lighthouse-dismissed-features', JSON.stringify([...allFeatureIds]));
    } catch (error) {
      console.warn('Failed to save dismissed features to backend, falling back to localStorage:', error);
      // Fallback to localStorage for backward compatibility
      localStorage.setItem('lighthouse-dismissed-features', JSON.stringify([...allFeatureIds]));
    }
    
    if (onAllDismiss) {
      onAllDismiss();
    }
  };

  const getCategoryIcon = (category: Feature['category']) => {
    switch (category) {
      case 'new':
        return <Sparkles className="w-4 h-4" />;
      case 'updated':
        return <TrendingUp className="w-4 h-4" />;
      case 'tip':
        return <Lightbulb className="w-4 h-4" />;
      case 'shortcut':
        return <Zap className="w-4 h-4" />;
      default:
        return <Star className="w-4 h-4" />;
    }
  };

  const getCategoryColor = (category: Feature['category']) => {
    switch (category) {
      case 'new':
        return 'bg-green-500';
      case 'updated':
        return 'bg-blue-500';
      case 'tip':
        return 'bg-yellow-500';
      case 'shortcut':
        return 'bg-purple-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getPriorityOrder = (priority: Feature['priority']) => {
    switch (priority) {
      case 'high': return 0;
      case 'medium': return 1;
      case 'low': return 2;
      default: return 3;
    }
  };

  // Sort features by priority
  const sortedFeatures = [...activeFeatures].sort((a, b) => 
    getPriorityOrder(a.priority) - getPriorityOrder(b.priority)
  );

  // Features with targets (positioned tooltips)
  const targetedFeatures = sortedFeatures.filter(f => f.target && positions[f.id]);
  
  // Features without targets (floating notifications)
  const floatingFeatures = sortedFeatures.filter(f => !f.target).slice(0, 3); // Limit to 3

  return (
    <>
      {/* Targeted Feature Tooltips */}
      <AnimatePresence>
        {targetedFeatures.map(feature => {
          const position = positions[feature.id];
          if (!position) return null;
          
          return (
            <motion.div
              key={feature.id}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="fixed z-50 max-w-[280px] sm:max-w-xs"
              style={{
                left: Math.max(10, Math.min(position.x - 140, window.innerWidth - 300)),
                top: position.y
              }}
            >
              <Card className="shadow-lg border-2 border-primary/20">
                <CardHeader className="p-3 sm:pb-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1.5 sm:gap-2">
                      <div className={`p-1 rounded-full ${getCategoryColor(feature.category)} text-white`}>
                        {feature.icon || getCategoryIcon(feature.category)}
                      </div>
                      <Badge variant="secondary" className="text-[10px] sm:text-xs capitalize px-1.5 py-0.5">
                        {feature.category}
                      </Badge>
                    </div>
                    {feature.dismissible !== false && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDismiss(feature.id)}
                        className="h-5 w-5 sm:h-6 sm:w-6 p-0"
                      >
                        <X className="w-2.5 h-2.5 sm:w-3 sm:h-3" />
                      </Button>
                    )}
                  </div>
                  <CardTitle className="text-xs sm:text-sm leading-tight">{feature.title}</CardTitle>
                  <CardDescription className="text-[10px] sm:text-xs leading-tight">
                    {feature.description}
                  </CardDescription>
                </CardHeader>
                {feature.action && (
                  <CardContent className="pt-0 p-3">
                    <Button
                      size="sm"
                      onClick={feature.action.onClick}
                      className="w-full text-[10px] sm:text-xs h-7 sm:h-8"
                    >
                      {feature.action.label}
                      <ArrowRight className="w-2.5 h-2.5 sm:w-3 sm:h-3 ml-1" />
                    </Button>
                  </CardContent>
                )}
              </Card>
            </motion.div>
          );
        })}
      </AnimatePresence>

      {/* Floating Feature Notifications */}
      {floatingFeatures.length > 0 && (
        <div className="fixed top-2 sm:top-4 right-2 sm:right-4 z-50 space-y-1.5 sm:space-y-2">
          <AnimatePresence>
            {floatingFeatures.map((feature, index) => (
              <motion.div
                key={feature.id}
                initial={{ opacity: 0, x: 300 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 300 }}
                transition={{ delay: index * 0.1 }}
                className="w-[calc(100vw-16px)] max-w-[320px] sm:w-80"
              >
                <Card className="shadow-lg">
                  <CardHeader className="p-3 sm:pb-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1.5 sm:gap-2 flex-wrap">
                        <div className={`p-1 rounded-full ${getCategoryColor(feature.category)} text-white flex-shrink-0`}>
                          {feature.icon || getCategoryIcon(feature.category)}
                        </div>
                        <Badge variant="secondary" className="text-[10px] sm:text-xs capitalize px-1.5 py-0.5">
                          {feature.category}
                        </Badge>
                        {feature.priority === 'high' && (
                          <Badge variant="destructive" className="text-[10px] sm:text-xs px-1.5 py-0.5">
                            Important
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-1 flex-shrink-0">
                        {feature.dismissible !== false && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDismiss(feature.id)}
                            className="h-5 w-5 sm:h-6 sm:w-6 p-0"
                          >
                            <X className="w-2.5 h-2.5 sm:w-3 sm:h-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                    <CardTitle className="text-xs sm:text-sm leading-tight pr-2">{feature.title}</CardTitle>
                    <CardDescription className="text-[10px] sm:text-xs leading-tight">
                      {feature.description}
                    </CardDescription>
                  </CardHeader>
                  {feature.action && (
                    <CardContent className="pt-0 p-3">
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          onClick={feature.action.onClick}
                          className="text-[10px] sm:text-xs h-7 sm:h-8"
                        >
                          {feature.action.label}
                          <ArrowRight className="w-2.5 h-2.5 sm:w-3 sm:h-3 ml-1" />
                        </Button>
                      </div>
                    </CardContent>
                  )}
                </Card>
              </motion.div>
            ))}
          </AnimatePresence>
          
          {/* Dismiss All Button */}
          {floatingFeatures.length > 1 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: floatingFeatures.length * 0.1 + 0.5 }}
            >
              <Button
                variant="outline"
                size="sm"
                onClick={handleDismissAll}
                className="w-full text-[10px] sm:text-xs h-7 sm:h-8"
              >
                Dismiss All
              </Button>
            </motion.div>
          )}
        </div>
      )}
    </>
  );
};

export default FeatureDiscovery;