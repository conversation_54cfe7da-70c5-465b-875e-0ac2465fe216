/**
 * Workspace Provider - State management and context for workspace operations
 * Integrates with React Query, Tauri, and offline sync capabilities
 */

import React, { createContext, useContext, useEffect, useState, ReactNode, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { invoke } from '@tauri-apps/api/core';
import { listen, emit } from '@tauri-apps/api/event';
import {
  Workspace,
  WorkspaceContextValue,
  WorkspaceMember,
  WorkspaceInvitation,
  WorkspaceActivity,
  CreateWorkspaceRequest,
  WorkspaceFilter,
  Permission,
  MemberRole,
  SyncStatus
} from './types';
import { toast } from 'sonner';
import { useTauri } from '../../contexts/TauriContext';

const WorkspaceContext = createContext<WorkspaceContextValue | undefined>(undefined);

interface WorkspaceProviderProps {
  children: ReactNode;
}

export const WorkspaceProvider: React.FC<WorkspaceProviderProps> = ({ children }) => {
  const { isInitialized } = useTauri();
  const queryClient = useQueryClient();
  
  const [currentWorkspace, setCurrentWorkspace] = useState<Workspace | null>(null);
  const [syncQueue, setSyncQueue] = useState<Array<{ workspaceId: string; operation: string; data: any }>>([]);
  const [isOffline, setIsOffline] = useState(!navigator.onLine);

  // Query: Get all workspaces
  const {
    data: workspaces = [],
    isLoading: workspacesLoading,
    error: workspacesError,
  } = useQuery({
    queryKey: ['workspaces'],
    queryFn: async (): Promise<Workspace[]> => {
      if (!isInitialized) return [];
      return await invoke('get_workspaces');
    },
    enabled: isInitialized,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Query: Get current workspace details
  const {
    data: currentWorkspaceData,
    isLoading: currentWorkspaceLoading,
  } = useQuery({
    queryKey: ['workspace', currentWorkspace?.id],
    queryFn: async (): Promise<Workspace> => {
      if (!currentWorkspace?.id || !isInitialized) throw new Error('No workspace selected');
      return await invoke('get_workspace_details', { workspaceId: currentWorkspace.id });
    },
    enabled: !!currentWorkspace?.id && isInitialized,
  });

  // Mutation: Create workspace
  const createWorkspaceMutation = useMutation({
    mutationFn: async (request: CreateWorkspaceRequest): Promise<Workspace> => {
      if (!isInitialized) throw new Error('Tauri not initialized');
      return await invoke('create_workspace', { request });
    },
    onSuccess: (newWorkspace) => {
      queryClient.invalidateQueries({ queryKey: ['workspaces'] });
      toast.success(`Workspace "${newWorkspace.name}" created successfully`);
    },
    onError: (error) => {
      console.error('Failed to create workspace:', error);
      toast.error('Failed to create workspace');
    },
  });

  // Mutation: Update workspace
  const updateWorkspaceMutation = useMutation({
    mutationFn: async ({ workspaceId, updates }: { workspaceId: string; updates: Partial<Workspace> }): Promise<void> => {
      if (!isInitialized) {
        // Queue for offline sync
        setSyncQueue(prev => [...prev, { workspaceId, operation: 'update', data: updates }]);
        return;
      }
      await invoke('update_workspace', { workspaceId, updates });
    },
    onSuccess: (_, { workspaceId }) => {
      queryClient.invalidateQueries({ queryKey: ['workspaces'] });
      queryClient.invalidateQueries({ queryKey: ['workspace', workspaceId] });
      toast.success('Workspace updated successfully');
    },
    onError: (error) => {
      console.error('Failed to update workspace:', error);
      toast.error('Failed to update workspace');
    },
  });

  // Mutation: Delete workspace
  const deleteWorkspaceMutation = useMutation({
    mutationFn: async (workspaceId: string): Promise<void> => {
      if (!isInitialized) throw new Error('Tauri not initialized');
      await invoke('delete_workspace', { workspaceId });
    },
    onSuccess: (_, workspaceId) => {
      queryClient.invalidateQueries({ queryKey: ['workspaces'] });
      if (currentWorkspace?.id === workspaceId) {
        setCurrentWorkspace(null);
      }
      toast.success('Workspace deleted successfully');
    },
    onError: (error) => {
      console.error('Failed to delete workspace:', error);
      toast.error('Failed to delete workspace');
    },
  });

  // Mutation: Invite member
  const inviteMemberMutation = useMutation({
    mutationFn: async (invitation: Omit<WorkspaceInvitation, 'id' | 'created_at'>): Promise<void> => {
      if (!isInitialized) throw new Error('Tauri not initialized');
      await invoke('invite_workspace_member', { invitation });
    },
    onSuccess: () => {
      if (currentWorkspace) {
        queryClient.invalidateQueries({ queryKey: ['workspace', currentWorkspace.id] });
      }
      toast.success('Invitation sent successfully');
    },
    onError: (error) => {
      console.error('Failed to send invitation:', error);
      toast.error('Failed to send invitation');
    },
  });

  // Mutation: Remove member
  const removeMemberMutation = useMutation({
    mutationFn: async ({ workspaceId, userId }: { workspaceId: string; userId: string }): Promise<void> => {
      if (!isInitialized) throw new Error('Tauri not initialized');
      await invoke('remove_workspace_member', { workspaceId, userId });
    },
    onSuccess: (_, { workspaceId }) => {
      queryClient.invalidateQueries({ queryKey: ['workspace', workspaceId] });
      toast.success('Member removed successfully');
    },
    onError: (error) => {
      console.error('Failed to remove member:', error);
      toast.error('Failed to remove member');
    },
  });

  // Mutation: Update member role
  const updateMemberRoleMutation = useMutation({
    mutationFn: async ({ workspaceId, userId, role }: { workspaceId: string; userId: string; role: MemberRole }): Promise<void> => {
      if (!isInitialized) throw new Error('Tauri not initialized');
      await invoke('update_member_role', { workspaceId, userId, role });
    },
    onSuccess: (_, { workspaceId }) => {
      queryClient.invalidateQueries({ queryKey: ['workspace', workspaceId] });
      toast.success('Member role updated successfully');
    },
    onError: (error) => {
      console.error('Failed to update member role:', error);
      toast.error('Failed to update member role');
    },
  });

  // Mutation: Sync workspace
  const syncWorkspaceMutation = useMutation({
    mutationFn: async (workspaceId: string): Promise<void> => {
      if (!isInitialized) throw new Error('Tauri not initialized');
      await invoke('sync_workspace', { workspaceId });
    },
    onSuccess: (_, workspaceId) => {
      queryClient.invalidateQueries({ queryKey: ['workspace', workspaceId] });
      toast.success('Workspace synced successfully');
    },
    onError: (error) => {
      console.error('Failed to sync workspace:', error);
      toast.error('Failed to sync workspace');
    },
  });

  // Helper function to check permissions
  const canPerformAction = (action: Permission, workspaceId?: string): boolean => {
    const workspace = workspaceId 
      ? workspaces.find(w => w.id === workspaceId)
      : currentWorkspace;
    
    if (!workspace) return false;
    
    // Get current user's role and permissions
    const currentUserId = 'current-user-id'; // TODO: Get from auth context
    const member = workspace.members.find(m => m.user_id === currentUserId);
    
    if (!member) return false;
    
    // Check if user has specific permission
    if (member.permissions.includes(action)) return true;
    
    // Check role-based permissions
    const rolePermissions: Record<MemberRole, Permission[]> = {
      owner: ['read_workspace', 'write_workspace', 'delete_workspace', 'manage_members', 'manage_settings', 'create_notebooks', 'edit_notebooks', 'delete_notebooks', 'share_notebooks', 'export_data', 'import_data', 'manage_integrations'],
      admin: ['read_workspace', 'write_workspace', 'manage_members', 'create_notebooks', 'edit_notebooks', 'delete_notebooks', 'share_notebooks', 'export_data', 'import_data'],
      editor: ['read_workspace', 'write_workspace', 'create_notebooks', 'edit_notebooks', 'share_notebooks', 'export_data'],
      viewer: ['read_workspace', 'export_data'],
      guest: ['read_workspace'],
    };
    
    return rolePermissions[member.role]?.includes(action) || false;
  };

  // Function to get workspace activity
  const getWorkspaceActivity = async (workspaceId: string, limit = 50): Promise<WorkspaceActivity[]> => {
    if (!isInitialized) return [];
    return await invoke('get_workspace_activity', { workspaceId, limit });
  };

  // Function to get storage usage
  const getStorageUsage = async (workspaceId: string): Promise<{ used: number; limit: number }> => {
    if (!isInitialized) return { used: 0, limit: 0 };
    return await invoke('get_workspace_storage_usage', { workspaceId });
  };

  // Function to resolve sync conflicts
  const resolveSyncConflict = async (workspaceId: string, resolution: 'local' | 'remote' | 'merge'): Promise<void> => {
    if (!isInitialized) throw new Error('Tauri not initialized');
    await invoke('resolve_sync_conflict', { workspaceId, resolution });
    queryClient.invalidateQueries({ queryKey: ['workspace', workspaceId] });
  };

  // Handle offline/online status changes
  useEffect(() => {
    const handleOnline = () => {
      setIsOffline(false);
      // Process sync queue when coming back online
      if (syncQueue.length > 0) {
        syncQueue.forEach(async ({ workspaceId, operation, data }) => {
          try {
            if (operation === 'update') {
              await invoke('update_workspace', { workspaceId, updates: data });
            }
            // Add other operations as needed
          } catch (error) {
            console.error('Failed to sync queued operation:', error);
          }
        });
        setSyncQueue([]);
      }
    };

    const handleOffline = () => {
      setIsOffline(true);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [syncQueue, invoke]);

  // Listen for workspace events from backend
  useEffect(() => {
    if (!isInitialized) return;

    let unlisteners: (() => void)[] = [];

    const setupEventListeners = async () => {
      // Workspace updated event
      const unlistenWorkspaceUpdated = await listen('workspace-updated', (event: any) => {
        const { workspaceId } = event.payload;
        queryClient.invalidateQueries({ queryKey: ['workspace', workspaceId] });
        queryClient.invalidateQueries({ queryKey: ['workspaces'] });
      });

      // Member joined event
      const unlistenMemberJoined = await listen('workspace-member-joined', (event: any) => {
        const { workspaceId, member } = event.payload;
        queryClient.invalidateQueries({ queryKey: ['workspace', workspaceId] });
        toast.success(`${member.display_name || member.username} joined the workspace`);
      });

      // Sync status changed event
      const unlistenSyncStatusChanged = await listen('workspace-sync-status-changed', (event: any) => {
        const { workspaceId, status } = event.payload;
        queryClient.invalidateQueries({ queryKey: ['workspace', workspaceId] });
        
        if (status === 'conflict') {
          toast.error('Sync conflict detected. Please resolve conflicts.');
        } else if (status === 'error') {
          toast.error('Sync error occurred.');
        }
      });

      unlisteners = [unlistenWorkspaceUpdated, unlistenMemberJoined, unlistenSyncStatusChanged];
    };

    setupEventListeners();

    return () => {
      unlisteners.forEach(unlisten => unlisten());
    };
  }, [isInitialized, queryClient]);

  // Update current workspace when data changes
  useEffect(() => {
    if (currentWorkspaceData) {
      setCurrentWorkspace(currentWorkspaceData);
    }
  }, [currentWorkspaceData]);

  const contextValue: WorkspaceContextValue = useMemo(() => ({
    currentWorkspace,
    workspaces,
    isLoading: workspacesLoading || currentWorkspaceLoading,
    error: workspacesError?.message || null,

    selectWorkspace: async (workspaceId: string) => {
      const workspace = workspaces.find(w => w.id === workspaceId);
      if (workspace) {
        setCurrentWorkspace(workspace);
        // Store in local storage for persistence
        localStorage.setItem('currentWorkspaceId', workspaceId);
      }
    },

    createWorkspace: async (request: CreateWorkspaceRequest) => {
      const result = await createWorkspaceMutation.mutateAsync(request);
      return result;
    },

    updateWorkspace: async (workspaceId: string, updates: Partial<Workspace>) => {
      await updateWorkspaceMutation.mutateAsync({ workspaceId, updates });
    },

    deleteWorkspace: async (workspaceId: string) => {
      await deleteWorkspaceMutation.mutateAsync(workspaceId);
    },

    inviteMember: async (invitation: Omit<WorkspaceInvitation, 'id' | 'created_at'>) => {
      await inviteMemberMutation.mutateAsync(invitation);
    },

    removeMember: async (workspaceId: string, userId: string) => {
      await removeMemberMutation.mutateAsync({ workspaceId, userId });
    },

    updateMemberRole: async (workspaceId: string, userId: string, role: MemberRole) => {
      await updateMemberRoleMutation.mutateAsync({ workspaceId, userId, role });
    },

    syncWorkspace: async (workspaceId: string) => {
      await syncWorkspaceMutation.mutateAsync(workspaceId);
    },

    resolveSyncConflict,
    getWorkspaceActivity,
    canPerformAction,
    getStorageUsage,
  }), [
    currentWorkspace,
    workspaces,
    workspacesLoading,
    currentWorkspaceLoading,
    workspacesError,
    createWorkspaceMutation,
    updateWorkspaceMutation,
    deleteWorkspaceMutation,
    inviteMemberMutation,
    removeMemberMutation,
    updateMemberRoleMutation,
    syncWorkspaceMutation,
  ]);

  // Restore current workspace from localStorage on mount
  useEffect(() => {
    const storedWorkspaceId = localStorage.getItem('currentWorkspaceId');
    if (storedWorkspaceId && workspaces.length > 0) {
      const workspace = workspaces.find(w => w.id === storedWorkspaceId);
      if (workspace) {
        setCurrentWorkspace(workspace);
      }
    }
  }, [workspaces]);

  return (
    <WorkspaceContext.Provider value={contextValue}>
      {children}
    </WorkspaceContext.Provider>
  );
};

export const useWorkspace = (): WorkspaceContextValue => {
  const context = useContext(WorkspaceContext);
  if (context === undefined) {
    throw new Error('useWorkspace must be used within a WorkspaceProvider');
  }
  return context;
};

// Additional utility hooks

export const useCurrentWorkspace = () => {
  const { currentWorkspace } = useWorkspace();
  return currentWorkspace;
};

export const useWorkspacePermissions = (workspaceId?: string) => {
  const { canPerformAction } = useWorkspace();
  
  return {
    canRead: canPerformAction('read_workspace', workspaceId),
    canWrite: canPerformAction('write_workspace', workspaceId),
    canDelete: canPerformAction('delete_workspace', workspaceId),
    canManageMembers: canPerformAction('manage_members', workspaceId),
    canManageSettings: canPerformAction('manage_settings', workspaceId),
    canCreateNotebooks: canPerformAction('create_notebooks', workspaceId),
    canEditNotebooks: canPerformAction('edit_notebooks', workspaceId),
    canDeleteNotebooks: canPerformAction('delete_notebooks', workspaceId),
    canShareNotebooks: canPerformAction('share_notebooks', workspaceId),
    canExportData: canPerformAction('export_data', workspaceId),
    canImportData: canPerformAction('import_data', workspaceId),
    canManageIntegrations: canPerformAction('manage_integrations', workspaceId),
  };
};

export default WorkspaceProvider;