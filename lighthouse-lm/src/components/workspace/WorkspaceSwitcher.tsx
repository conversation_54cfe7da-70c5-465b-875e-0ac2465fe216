/**
 * Workspace Switcher - Context switching between workspaces
 * Compact dropdown for quick workspace navigation
 */

import React, { useState } from 'react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '../ui/command';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Avatar, AvatarFallback } from '../ui/avatar';
import { Separator } from '../ui/separator';
import {
  Check,
  ChevronsUpDown,
  Plus,
  Users,
  User,
  HardDrive,
  Wifi,
  WifiOff,
  AlertTriangle,
  CheckCircle2,
  Clock,
  RotateCw,
} from 'lucide-react';
import { useWorkspace, useCurrentWorkspace, useWorkspacePermissions } from './WorkspaceProvider';
import { WorkspaceSwitcherProps, Workspace, SyncStatus } from './types';
import { cn } from '../../lib/utils';

export const WorkspaceSwitcher: React.FC<WorkspaceSwitcherProps> = ({
  currentWorkspace: propCurrentWorkspace,
  onWorkspaceChange,
  compact = false,
  showCreateButton = true,
  className,
}) => {
  const {
    workspaces,
    isLoading,
    selectWorkspace,
  } = useWorkspace();
  
  const contextCurrentWorkspace = useCurrentWorkspace();
  const currentWorkspace = propCurrentWorkspace || contextCurrentWorkspace;
  
  const [open, setOpen] = useState(false);

  const getSyncStatusIcon = (status: SyncStatus) => {
    switch (status) {
      case 'synced':
        return <CheckCircle2 className="h-3 w-3 text-green-500" />;
      case 'syncing':
        return <RotateCw className="h-3 w-3 text-blue-500 animate-spin" />;
      case 'pending':
        return <Clock className="h-3 w-3 text-yellow-500" />;
      case 'conflict':
        return <AlertTriangle className="h-3 w-3 text-orange-500" />;
      case 'error':
        return <AlertTriangle className="h-3 w-3 text-red-500" />;
      case 'offline':
        return <WifiOff className="h-3 w-3 text-gray-500" />;
      default:
        return <Wifi className="h-3 w-3 text-gray-400" />;
    }
  };

  const getWorkspaceInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .slice(0, 2)
      .toUpperCase();
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const handleWorkspaceSelect = async (workspace: Workspace) => {
    try {
      await selectWorkspace(workspace.id);
      onWorkspaceChange(workspace);
      setOpen(false);
    } catch (error) {
      console.error('Failed to select workspace:', error);
    }
  };

  const personalWorkspaces = workspaces.filter(w => w.is_personal);
  const teamWorkspaces = workspaces.filter(w => !w.is_personal);

  if (compact) {
    return (
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            role="combobox"
            aria-expanded={open}
            className={cn("w-8 h-8 p-0", className)}
          >
            <Avatar className="h-6 w-6">
              <AvatarFallback className="text-xs">
                {currentWorkspace ? getWorkspaceInitials(currentWorkspace.name) : '?'}
              </AvatarFallback>
            </Avatar>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-0" align="start">
          <Command>
            <CommandInput placeholder="Search workspaces..." />
            <CommandEmpty>No workspaces found.</CommandEmpty>
            
            {personalWorkspaces.length > 0 && (
              <CommandGroup heading="Personal">
                {personalWorkspaces.map((workspace) => (
                  <CommandItem
                    key={workspace.id}
                    value={workspace.name}
                    onSelect={() => handleWorkspaceSelect(workspace)}
                  >
                    <div className="flex items-center space-x-2 flex-1">
                      <Avatar className="h-6 w-6">
                        <AvatarFallback className="text-xs">
                          {getWorkspaceInitials(workspace.name)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          {workspace.name}
                        </p>
                      </div>
                      <div className="flex items-center space-x-1">
                        {getSyncStatusIcon(workspace.sync_status)}
                        {currentWorkspace?.id === workspace.id && (
                          <Check className="h-4 w-4" />
                        )}
                      </div>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            )}

            {teamWorkspaces.length > 0 && (
              <CommandGroup heading="Team">
                {teamWorkspaces.map((workspace) => (
                  <CommandItem
                    key={workspace.id}
                    value={workspace.name}
                    onSelect={() => handleWorkspaceSelect(workspace)}
                  >
                    <div className="flex items-center space-x-2 flex-1">
                      <Avatar className="h-6 w-6">
                        <AvatarFallback className="text-xs">
                          {getWorkspaceInitials(workspace.name)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          {workspace.name}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {workspace.members.length} member{workspace.members.length !== 1 ? 's' : ''}
                        </p>
                      </div>
                      <div className="flex items-center space-x-1">
                        {getSyncStatusIcon(workspace.sync_status)}
                        {currentWorkspace?.id === workspace.id && (
                          <Check className="h-4 w-4" />
                        )}
                      </div>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            )}

            {showCreateButton && (
              <>
                <Separator />
                <CommandGroup>
                  <CommandItem onSelect={() => {
                    setOpen(false);
                    // TODO: Trigger create workspace dialog
                  }}>
                    <Plus className="mr-2 h-4 w-4" />
                    Create Workspace
                  </CommandItem>
                </CommandGroup>
              </>
            )}
          </Command>
        </PopoverContent>
      </Popover>
    );
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("w-full justify-between", className)}
          disabled={isLoading}
        >
          {currentWorkspace ? (
            <div className="flex items-center space-x-2 flex-1 min-w-0">
              <Avatar className="h-6 w-6">
                <AvatarFallback className="text-xs">
                  {getWorkspaceInitials(currentWorkspace.name)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0 text-left">
                <p className="text-sm font-medium truncate">
                  {currentWorkspace.name}
                </p>
                <div className="flex items-center space-x-2">
                  {currentWorkspace.is_personal ? (
                    <Badge variant="secondary" className="text-xs h-4">
                      Personal
                    </Badge>
                  ) : (
                    <Badge variant="default" className="text-xs h-4">
                      Team
                    </Badge>
                  )}
                  {getSyncStatusIcon(currentWorkspace.sync_status)}
                </div>
              </div>
            </div>
          ) : (
            <span className="text-muted-foreground">Select workspace...</span>
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      
      <PopoverContent className="w-96 p-0" align="start">
        <Command>
          <CommandInput placeholder="Search workspaces..." />
          <CommandEmpty>No workspaces found.</CommandEmpty>
          
          {personalWorkspaces.length > 0 && (
            <CommandGroup heading="Personal Workspaces">
              {personalWorkspaces.map((workspace) => (
                <CommandItem
                  key={workspace.id}
                  value={workspace.name}
                  onSelect={() => handleWorkspaceSelect(workspace)}
                  className="p-3"
                >
                  <div className="flex items-center space-x-3 w-full">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback>
                        {getWorkspaceInitials(workspace.name)}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <p className="text-sm font-medium truncate">
                          {workspace.name}
                        </p>
                        {getSyncStatusIcon(workspace.sync_status)}
                        {currentWorkspace?.id === workspace.id && (
                          <Check className="h-4 w-4 text-green-600" />
                        )}
                      </div>
                      
                      {workspace.description && (
                        <p className="text-xs text-muted-foreground truncate mt-1">
                          {workspace.description}
                        </p>
                      )}
                      
                      <div className="flex items-center space-x-3 mt-2 text-xs text-muted-foreground">
                        <div className="flex items-center space-x-1">
                          <User className="h-3 w-3" />
                          <span>Personal</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <HardDrive className="h-3 w-3" />
                          <span>{formatBytes(workspace.storage_usage)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          )}

          {teamWorkspaces.length > 0 && (
            <CommandGroup heading="Team Workspaces">
              {teamWorkspaces.map((workspace) => (
                <CommandItem
                  key={workspace.id}
                  value={workspace.name}
                  onSelect={() => handleWorkspaceSelect(workspace)}
                  className="p-3"
                >
                  <div className="flex items-center space-x-3 w-full">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback>
                        {getWorkspaceInitials(workspace.name)}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <p className="text-sm font-medium truncate">
                          {workspace.name}
                        </p>
                        {getSyncStatusIcon(workspace.sync_status)}
                        {currentWorkspace?.id === workspace.id && (
                          <Check className="h-4 w-4 text-green-600" />
                        )}
                      </div>
                      
                      {workspace.description && (
                        <p className="text-xs text-muted-foreground truncate mt-1">
                          {workspace.description}
                        </p>
                      )}
                      
                      <div className="flex items-center space-x-3 mt-2 text-xs text-muted-foreground">
                        <div className="flex items-center space-x-1">
                          <Users className="h-3 w-3" />
                          <span>{workspace.members.length} member{workspace.members.length !== 1 ? 's' : ''}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <HardDrive className="h-3 w-3" />
                          <span>{formatBytes(workspace.storage_usage)}</span>
                        </div>
                      </div>
                      
                      {workspace.tags.length > 0 && (
                        <div className="flex space-x-1 mt-1">
                          {workspace.tags.slice(0, 2).map((tag) => (
                            <Badge key={tag} variant="outline" className="text-xs h-4">
                              {tag}
                            </Badge>
                          ))}
                          {workspace.tags.length > 2 && (
                            <Badge variant="outline" className="text-xs h-4">
                              +{workspace.tags.length - 2}
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          )}

          {showCreateButton && (
            <>
              <Separator />
              <CommandGroup>
                <CommandItem
                  onSelect={() => {
                    setOpen(false);
                    // TODO: Trigger create workspace dialog or emit event
                  }}
                  className="p-3"
                >
                  <div className="flex items-center space-x-3">
                    <div className="h-8 w-8 rounded-md bg-muted flex items-center justify-center">
                      <Plus className="h-4 w-4" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">Create Workspace</p>
                      <p className="text-xs text-muted-foreground">
                        Start a new collaboration space
                      </p>
                    </div>
                  </div>
                </CommandItem>
              </CommandGroup>
            </>
          )}
        </Command>
      </PopoverContent>
    </Popover>
  );
};