/**
 * Collaboration Panel - Team member management for workspaces
 * Enterprise member management with roles, permissions, and invitations
 */

import React, { useState, useMemo } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Badge } from '../ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../ui/alert-dialog';
import { Separator } from '../ui/separator';
import { Progress } from '../ui/progress';
import { Skeleton } from '../ui/skeleton';
import {
  Users,
  UserPlus,
  MoreHorizontal,
  Send,
  Copy,
  Crown,
  Shield,
  Edit,
  Eye,
  UserX,
  Clock,
  CheckCircle2,
  AlertTriangle,
  Search,
  Filter,
  Mail,
  Calendar,
  Activity,
  Globe,
  Settings,
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { toast } from 'sonner';
import {
  CollaborationPanelProps,
  WorkspaceMember,
  WorkspaceInvitation,
  MemberRole,
  MemberStatus,
  Permission,
} from './types';
import { useWorkspace, useWorkspacePermissions } from './WorkspaceProvider';
import { cn } from '../../lib/utils';

export const CollaborationPanel: React.FC<CollaborationPanelProps> = ({
  workspace,
  onMemberAdd,
  onMemberRemove,
  onMemberRoleChange,
  onInvitationSend,
  className,
}) => {
  const {
    inviteMember,
    removeMember,
    updateMemberRole,
  } = useWorkspace();
  
  const { canManageMembers, canManageSettings } = useWorkspacePermissions(workspace.id);

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<MemberStatus | 'all'>('all');
  const [roleFilter, setRoleFilter] = useState<MemberRole | 'all'>('all');
  
  // Dialog states
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [isRemoveDialogOpen, setIsRemoveDialogOpen] = useState(false);
  const [selectedMember, setSelectedMember] = useState<WorkspaceMember | null>(null);
  
  // Form states
  const [inviteForm, setInviteForm] = useState({
    email: '',
    role: 'viewer' as MemberRole,
    message: '',
  });
  
  // Loading states
  const [loadingMembers, setLoadingMembers] = useState<Set<string>>(new Set());

  // Filter and sort members
  const filteredMembers = useMemo(() => {
    return workspace.members.filter(member => {
      const matchesSearch = !searchTerm || 
        member.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.display_name?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = statusFilter === 'all' || member.status === statusFilter;
      const matchesRole = roleFilter === 'all' || member.role === roleFilter;
      
      return matchesSearch && matchesStatus && matchesRole;
    }).sort((a, b) => {
      // Sort by role priority, then by last active
      const rolePriority = { owner: 0, admin: 1, editor: 2, viewer: 3, guest: 4 };
      const aPriority = rolePriority[a.role];
      const bPriority = rolePriority[b.role];
      
      if (aPriority !== bPriority) {
        return aPriority - bPriority;
      }
      
      // Sort by last active (most recent first)
      const aTime = a.last_active_at ? new Date(a.last_active_at).getTime() : 0;
      const bTime = b.last_active_at ? new Date(b.last_active_at).getTime() : 0;
      return bTime - aTime;
    });
  }, [workspace.members, searchTerm, statusFilter, roleFilter]);

  const handleSendInvitation = async () => {
    if (!inviteForm.email.trim()) {
      toast.error('Email address is required');
      return;
    }

    const invitation: Omit<WorkspaceInvitation, 'id' | 'created_at'> = {
      workspace_id: workspace.id,
      inviter_id: 'current-user-id', // TODO: Get from auth context
      invitee_email: inviteForm.email.trim(),
      role: inviteForm.role,
      message: inviteForm.message.trim() || undefined,
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
      status: 'pending',
    };

    try {
      await inviteMember(invitation);
      onInvitationSend?.(invitation);
      
      setIsInviteDialogOpen(false);
      setInviteForm({ email: '', role: 'viewer', message: '' });
      
      toast.success(`Invitation sent to ${invitation.invitee_email}`);
    } catch (error) {
      console.error('Failed to send invitation:', error);
      toast.error('Failed to send invitation');
    }
  };

  const handleRoleChange = async (member: WorkspaceMember, newRole: MemberRole) => {
    if (member.role === newRole) return;

    setLoadingMembers(prev => new Set(prev).add(member.user_id));

    try {
      await updateMemberRole(workspace.id, member.user_id, newRole);
      onMemberRoleChange?.(member.user_id, newRole);
      
      toast.success(`${member.display_name || member.username}'s role updated to ${newRole}`);
    } catch (error) {
      console.error('Failed to update member role:', error);
      toast.error('Failed to update member role');
    } finally {
      setLoadingMembers(prev => {
        const next = new Set(prev);
        next.delete(member.user_id);
        return next;
      });
    }
  };

  const handleRemoveMember = async () => {
    if (!selectedMember) return;

    setLoadingMembers(prev => new Set(prev).add(selectedMember.user_id));

    try {
      await removeMember(workspace.id, selectedMember.user_id);
      onMemberRemove?.(selectedMember.user_id);
      
      setIsRemoveDialogOpen(false);
      setSelectedMember(null);
      
      toast.success(`${selectedMember.display_name || selectedMember.username} removed from workspace`);
    } catch (error) {
      console.error('Failed to remove member:', error);
      toast.error('Failed to remove member');
    } finally {
      setLoadingMembers(prev => {
        const next = new Set(prev);
        next.delete(selectedMember.user_id);
        return next;
      });
    }
  };

  const getRoleIcon = (role: MemberRole) => {
    switch (role) {
      case 'owner':
        return <Crown className="h-4 w-4 text-yellow-500" />;
      case 'admin':
        return <Shield className="h-4 w-4 text-blue-500" />;
      case 'editor':
        return <Edit className="h-4 w-4 text-green-500" />;
      case 'viewer':
        return <Eye className="h-4 w-4 text-gray-500" />;
      case 'guest':
        return <Globe className="h-4 w-4 text-purple-500" />;
      default:
        return <Users className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: MemberStatus) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="text-xs">Active</Badge>;
      case 'pending':
        return <Badge variant="secondary" className="text-xs">Pending</Badge>;
      case 'suspended':
        return <Badge variant="destructive" className="text-xs">Suspended</Badge>;
      case 'removed':
        return <Badge variant="outline" className="text-xs">Removed</Badge>;
      default:
        return null;
    }
  };

  const canEditMember = (member: WorkspaceMember) => {
    // Can't edit owner, can only edit if user has manage members permission
    return member.role !== 'owner' && canManageMembers;
  };

  const copyInviteLink = async () => {
    try {
      // TODO: Generate actual invite link
      const inviteLink = `https://lighthouse-lm.com/invite/${workspace.id}`;
      await navigator.clipboard.writeText(inviteLink);
      toast.success('Invite link copied to clipboard');
    } catch (error) {
      console.error('Failed to copy invite link:', error);
      toast.error('Failed to copy invite link');
    }
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div>
          <h3 className="text-lg font-semibold flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Team Members</span>
          </h3>
          <p className="text-sm text-muted-foreground">
            Manage workspace members and permissions
          </p>
        </div>
        
        {canManageMembers && (
          <div className="flex space-x-2">
            <Button variant="outline" onClick={copyInviteLink}>
              <Copy className="mr-2 h-4 w-4" />
              Copy Link
            </Button>
            <Button onClick={() => setIsInviteDialogOpen(true)}>
              <UserPlus className="mr-2 h-4 w-4" />
              Invite Member
            </Button>
          </div>
        )}
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search members..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        
        <Select
          value={statusFilter}
          onValueChange={(value: MemberStatus | 'all') => setStatusFilter(value)}
        >
          <SelectTrigger className="w-32">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="suspended">Suspended</SelectItem>
          </SelectContent>
        </Select>

        <Select
          value={roleFilter}
          onValueChange={(value: MemberRole | 'all') => setRoleFilter(value)}
        >
          <SelectTrigger className="w-32">
            <SelectValue placeholder="Role" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Roles</SelectItem>
            <SelectItem value="owner">Owner</SelectItem>
            <SelectItem value="admin">Admin</SelectItem>
            <SelectItem value="editor">Editor</SelectItem>
            <SelectItem value="viewer">Viewer</SelectItem>
            <SelectItem value="guest">Guest</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Members List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-base">
              {filteredMembers.length} member{filteredMembers.length !== 1 ? 's' : ''}
            </CardTitle>
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Activity className="h-4 w-4" />
              <span>{workspace.members.filter(m => m.is_online).length} online</span>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="space-y-0">
            {filteredMembers.map((member, index) => (
              <div key={member.user_id}>
                <div className="flex items-center space-x-4 p-4 hover:bg-muted/50 transition-colors">
                  <div className="relative">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={member.avatar_url} alt={member.display_name || member.username} />
                      <AvatarFallback>
                        {(member.display_name || member.username).slice(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    {member.is_online && (
                      <div className="absolute -bottom-1 -right-1 h-3 w-3 bg-green-500 rounded-full border-2 border-white" />
                    )}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <p className="text-sm font-medium truncate">
                        {member.display_name || member.username}
                      </p>
                      {getRoleIcon(member.role)}
                      {getStatusBadge(member.status)}
                      {member.is_online && (
                        <Badge variant="outline" className="text-xs text-green-600">
                          Online
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground truncate">
                      {member.email}
                    </p>
                    <div className="flex items-center space-x-4 mt-1 text-xs text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3" />
                        <span>Joined {formatDistanceToNow(new Date(member.joined_at), { addSuffix: true })}</span>
                      </div>
                      {member.last_active_at && (
                        <div className="flex items-center space-x-1">
                          <Clock className="h-3 w-3" />
                          <span>Active {formatDistanceToNow(new Date(member.last_active_at), { addSuffix: true })}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="capitalize">
                      {member.role}
                    </Badge>
                    
                    {canEditMember(member) && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button 
                            variant="ghost" 
                            className="h-8 w-8 p-0"
                            disabled={loadingMembers.has(member.user_id)}
                          >
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => handleRoleChange(member, 'admin')}
                            disabled={member.role === 'admin'}
                          >
                            <Shield className="mr-2 h-4 w-4" />
                            Make Admin
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleRoleChange(member, 'editor')}
                            disabled={member.role === 'editor'}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Make Editor
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleRoleChange(member, 'viewer')}
                            disabled={member.role === 'viewer'}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            Make Viewer
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedMember(member);
                              setIsRemoveDialogOpen(true);
                            }}
                            className="text-red-600"
                          >
                            <UserX className="mr-2 h-4 w-4" />
                            Remove Member
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </div>
                </div>
                
                {index < filteredMembers.length - 1 && <Separator />}
              </div>
            ))}

            {filteredMembers.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                {searchTerm || statusFilter !== 'all' || roleFilter !== 'all' ? (
                  <p>No members match your filters</p>
                ) : (
                  <p>No members in this workspace</p>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Invite Member Dialog */}
      <Dialog open={isInviteDialogOpen} onOpenChange={setIsInviteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Invite Member</DialogTitle>
            <DialogDescription>
              Invite someone to collaborate in this workspace
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email address *</Label>
              <Input
                id="email"
                type="email"
                value={inviteForm.email}
                onChange={(e) => setInviteForm(prev => ({ ...prev, email: e.target.value }))}
                placeholder="<EMAIL>"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="role">Role</Label>
              <Select
                value={inviteForm.role}
                onValueChange={(value: MemberRole) => setInviteForm(prev => ({ ...prev, role: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="viewer">
                    <div className="flex items-center space-x-2">
                      <Eye className="h-4 w-4" />
                      <div>
                        <p>Viewer</p>
                        <p className="text-xs text-muted-foreground">Can view and comment</p>
                      </div>
                    </div>
                  </SelectItem>
                  <SelectItem value="editor">
                    <div className="flex items-center space-x-2">
                      <Edit className="h-4 w-4" />
                      <div>
                        <p>Editor</p>
                        <p className="text-xs text-muted-foreground">Can edit and share</p>
                      </div>
                    </div>
                  </SelectItem>
                  <SelectItem value="admin">
                    <div className="flex items-center space-x-2">
                      <Shield className="h-4 w-4" />
                      <div>
                        <p>Admin</p>
                        <p className="text-xs text-muted-foreground">Can manage members</p>
                      </div>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="message">Personal message (optional)</Label>
              <Textarea
                id="message"
                value={inviteForm.message}
                onChange={(e) => setInviteForm(prev => ({ ...prev, message: e.target.value }))}
                placeholder="Add a personal message to the invitation"
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsInviteDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSendInvitation} disabled={!inviteForm.email.trim()}>
              <Send className="mr-2 h-4 w-4" />
              Send Invitation
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Remove Member Dialog */}
      <AlertDialog open={isRemoveDialogOpen} onOpenChange={setIsRemoveDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove Member</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove "{selectedMember?.display_name || selectedMember?.username}" 
              from this workspace? They will lose access to all workspace content.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleRemoveMember}
              className="bg-red-600 hover:bg-red-700"
            >
              Remove Member
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};