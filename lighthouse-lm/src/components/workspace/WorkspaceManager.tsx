/**
 * Workspace Manager - Main CRUD operations for workspaces
 * Enterprise dashboard for managing multiple workspaces
 */

import React, { useState, useMemo } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Badge } from '../ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import { Progress } from '../ui/progress';
import { Skeleton } from '../ui/skeleton';
import { Separator } from '../ui/separator';
import {
  Users,
  Settings,
  MoreHorizontal,
  Plus,
  Search,
  Filter,
  Calendar,
  HardDrive,
  Trash2,
  Edit,
  Eye,
  Share2,
  Download,
  Upload,
  Sync,
  AlertTriangle,
  CheckCircle2,
  Clock,
  Wifi,
  WifiOff,
} from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';
import { toast } from 'sonner';
import { useWorkspace, useWorkspacePermissions } from './WorkspaceProvider';
import {
  Workspace,
  WorkspaceManagerProps,
  CreateWorkspaceRequest,
  WorkspaceFilter,
  SyncStatus,
} from './types';
import { cn } from '../../lib/utils';

export const WorkspaceManager: React.FC<WorkspaceManagerProps> = ({
  onWorkspaceSelect,
  onWorkspaceCreate,
  onWorkspaceUpdate,
  onWorkspaceDelete,
  className,
}) => {
  const {
    workspaces,
    isLoading,
    error,
    createWorkspace,
    updateWorkspace,
    deleteWorkspace,
    syncWorkspace,
    getStorageUsage,
  } = useWorkspace();

  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState<Partial<WorkspaceFilter>>({});
  const [sortBy, setSortBy] = useState<'name' | 'created_at' | 'updated_at' | 'storage_usage'>('updated_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  
  // Dialog states
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedWorkspace, setSelectedWorkspace] = useState<Workspace | null>(null);
  
  // Form states
  const [formData, setFormData] = useState<Partial<CreateWorkspaceRequest>>({
    name: '',
    description: '',
    is_personal: true,
  });
  
  // Loading states
  const [syncingWorkspaces, setSyncingWorkspaces] = useState<Set<string>>(new Set());
  const [storageUsage, setStorageUsage] = useState<Record<string, { used: number; limit: number }>>({});

  // Filter and sort workspaces
  const filteredAndSortedWorkspaces = useMemo(() => {
    let filtered = workspaces.filter(workspace => {
      const matchesSearch = !searchTerm || 
        workspace.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        workspace.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        workspace.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesPersonal = filter.is_personal === undefined || workspace.is_personal === filter.is_personal;
      
      return matchesSearch && matchesPersonal;
    });

    // Sort workspaces
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'created_at':
          aValue = new Date(a.created_at);
          bValue = new Date(b.created_at);
          break;
        case 'updated_at':
          aValue = new Date(a.updated_at);
          bValue = new Date(b.updated_at);
          break;
        case 'storage_usage':
          aValue = a.storage_usage;
          bValue = b.storage_usage;
          break;
        default:
          return 0;
      }
      
      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [workspaces, searchTerm, filter, sortBy, sortOrder]);

  // Load storage usage for visible workspaces
  React.useEffect(() => {
    const loadStorageUsage = async () => {
      const usage: Record<string, { used: number; limit: number }> = {};
      
      for (const workspace of filteredAndSortedWorkspaces.slice(0, 20)) {
        try {
          usage[workspace.id] = await getStorageUsage(workspace.id);
        } catch (error) {
          console.error(`Failed to load storage usage for workspace ${workspace.id}:`, error);
        }
      }
      
      setStorageUsage(usage);
    };

    if (filteredAndSortedWorkspaces.length > 0) {
      loadStorageUsage();
    }
  }, [filteredAndSortedWorkspaces, getStorageUsage]);

  const handleCreateWorkspace = async () => {
    if (!formData.name?.trim()) {
      toast.error('Workspace name is required');
      return;
    }

    try {
      const workspace = await createWorkspace({
        name: formData.name.trim(),
        description: formData.description?.trim(),
        is_personal: formData.is_personal || false,
        settings: formData.settings,
        initial_members: formData.initial_members,
      });
      
      setIsCreateDialogOpen(false);
      setFormData({ name: '', description: '', is_personal: true });
      onWorkspaceCreate?.(workspace);
      
      toast.success(`Workspace "${workspace.name}" created successfully`);
    } catch (error) {
      console.error('Failed to create workspace:', error);
      toast.error('Failed to create workspace');
    }
  };

  const handleUpdateWorkspace = async () => {
    if (!selectedWorkspace || !formData.name?.trim()) {
      toast.error('Workspace name is required');
      return;
    }

    try {
      await updateWorkspace(selectedWorkspace.id, {
        name: formData.name.trim(),
        description: formData.description?.trim(),
      });
      
      setIsEditDialogOpen(false);
      setSelectedWorkspace(null);
      setFormData({ name: '', description: '', is_personal: true });
      onWorkspaceUpdate?.(selectedWorkspace);
      
      toast.success('Workspace updated successfully');
    } catch (error) {
      console.error('Failed to update workspace:', error);
      toast.error('Failed to update workspace');
    }
  };

  const handleDeleteWorkspace = async () => {
    if (!selectedWorkspace) return;

    try {
      await deleteWorkspace(selectedWorkspace.id);
      setIsDeleteDialogOpen(false);
      setSelectedWorkspace(null);
      onWorkspaceDelete?.(selectedWorkspace.id);
      
      toast.success(`Workspace "${selectedWorkspace.name}" deleted successfully`);
    } catch (error) {
      console.error('Failed to delete workspace:', error);
      toast.error('Failed to delete workspace');
    }
  };

  const handleSyncWorkspace = async (workspace: Workspace) => {
    setSyncingWorkspaces(prev => new Set(prev).add(workspace.id));
    
    try {
      await syncWorkspace(workspace.id);
      toast.success(`Workspace "${workspace.name}" synced successfully`);
    } catch (error) {
      console.error('Failed to sync workspace:', error);
      toast.error(`Failed to sync workspace "${workspace.name}"`);
    } finally {
      setSyncingWorkspaces(prev => {
        const next = new Set(prev);
        next.delete(workspace.id);
        return next;
      });
    }
  };

  const openEditDialog = (workspace: Workspace) => {
    setSelectedWorkspace(workspace);
    setFormData({
      name: workspace.name,
      description: workspace.description,
      is_personal: workspace.is_personal,
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (workspace: Workspace) => {
    setSelectedWorkspace(workspace);
    setIsDeleteDialogOpen(true);
  };

  const getSyncStatusIcon = (status: SyncStatus) => {
    switch (status) {
      case 'synced':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case 'syncing':
        return <Sync className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'conflict':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'offline':
        return <WifiOff className="h-4 w-4 text-gray-500" />;
      default:
        return <Wifi className="h-4 w-4 text-gray-400" />;
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (error) {
    return (
      <Card className={cn('w-full', className)}>
        <CardContent className="pt-6">
          <div className="text-center text-red-600">
            Error loading workspaces: {error}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Workspaces</h2>
          <p className="text-muted-foreground">
            Manage your collaboration spaces and projects
          </p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Create Workspace
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search workspaces..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        
        <Select
          value={filter.is_personal?.toString() || 'all'}
          onValueChange={(value) => 
            setFilter(prev => ({ 
              ...prev, 
              is_personal: value === 'all' ? undefined : value === 'true' 
            }))
          }
        >
          <SelectTrigger className="w-48">
            <Filter className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Filter workspaces" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Workspaces</SelectItem>
            <SelectItem value="true">Personal</SelectItem>
            <SelectItem value="false">Team</SelectItem>
          </SelectContent>
        </Select>

        <Select
          value={`${sortBy}-${sortOrder}`}
          onValueChange={(value) => {
            const [field, order] = value.split('-') as [typeof sortBy, typeof sortOrder];
            setSortBy(field);
            setSortOrder(order);
          }}
        >
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="name-asc">Name (A-Z)</SelectItem>
            <SelectItem value="name-desc">Name (Z-A)</SelectItem>
            <SelectItem value="updated_at-desc">Recently Updated</SelectItem>
            <SelectItem value="updated_at-asc">Least Recently Updated</SelectItem>
            <SelectItem value="created_at-desc">Recently Created</SelectItem>
            <SelectItem value="created_at-asc">Oldest</SelectItem>
            <SelectItem value="storage_usage-desc">Storage Usage (High)</SelectItem>
            <SelectItem value="storage_usage-asc">Storage Usage (Low)</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Workspace Grid */}
      {isLoading ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Skeleton className="h-3 w-full" />
                  <Skeleton className="h-3 w-2/3" />
                  <Skeleton className="h-8 w-full" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredAndSortedWorkspaces.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-muted-foreground">No workspaces found</p>
              {searchTerm && (
                <p className="text-sm text-muted-foreground mt-2">
                  Try adjusting your search terms or filters
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredAndSortedWorkspaces.map((workspace) => {
            const usage = storageUsage[workspace.id];
            const storagePercentage = usage ? (usage.used / usage.limit) * 100 : 0;
            const isSyncing = syncingWorkspaces.has(workspace.id);

            return (
              <Card key={workspace.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="space-y-1 flex-1">
                      <CardTitle className="text-lg">{workspace.name}</CardTitle>
                      <div className="flex items-center space-x-2">
                        {workspace.is_personal ? (
                          <Badge variant="secondary">Personal</Badge>
                        ) : (
                          <Badge variant="default">Team</Badge>
                        )}
                        {getSyncStatusIcon(workspace.sync_status)}
                      </div>
                    </div>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => onWorkspaceSelect?.(workspace)}
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          Open
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => openEditDialog(workspace)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleSyncWorkspace(workspace)}
                          disabled={isSyncing}
                        >
                          <Sync className={cn("mr-2 h-4 w-4", isSyncing && "animate-spin")} />
                          {isSyncing ? 'Syncing...' : 'Sync'}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => openDeleteDialog(workspace)}
                          className="text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  
                  {workspace.description && (
                    <CardDescription className="line-clamp-2">
                      {workspace.description}
                    </CardDescription>
                  )}
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Stats */}
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <Users className="h-4 w-4" />
                      <span>{workspace.members.length} member{workspace.members.length !== 1 ? 's' : ''}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>{formatDistanceToNow(new Date(workspace.updated_at), { addSuffix: true })}</span>
                    </div>
                  </div>

                  {/* Storage Usage */}
                  {usage && (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center space-x-1">
                          <HardDrive className="h-4 w-4" />
                          <span>Storage</span>
                        </div>
                        <span className="text-muted-foreground">
                          {formatBytes(usage.used)} / {formatBytes(usage.limit)}
                        </span>
                      </div>
                      <Progress 
                        value={storagePercentage} 
                        className={cn(
                          "h-2",
                          storagePercentage > 90 && "bg-red-100",
                          storagePercentage > 75 && storagePercentage <= 90 && "bg-yellow-100"
                        )}
                      />
                    </div>
                  )}

                  {/* Tags */}
                  {workspace.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {workspace.tags.slice(0, 3).map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {workspace.tags.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{workspace.tags.length - 3}
                        </Badge>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* Create Workspace Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Create Workspace</DialogTitle>
            <DialogDescription>
              Create a new workspace to organize your projects and collaborate with others.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name *</Label>
              <Input
                id="name"
                value={formData.name || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter workspace name"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe this workspace (optional)"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label>Type</Label>
              <Select
                value={formData.is_personal ? 'personal' : 'team'}
                onValueChange={(value) => setFormData(prev => ({ ...prev, is_personal: value === 'personal' }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="personal">Personal Workspace</SelectItem>
                  <SelectItem value="team">Team Workspace</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateWorkspace} disabled={!formData.name?.trim()}>
              Create Workspace
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Workspace Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Workspace</DialogTitle>
            <DialogDescription>
              Update workspace information and settings.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name">Name *</Label>
              <Input
                id="edit-name"
                value={formData.name || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter workspace name"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-description">Description</Label>
              <Textarea
                id="edit-description"
                value={formData.description || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe this workspace (optional)"
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateWorkspace} disabled={!formData.name?.trim()}>
              Update Workspace
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Workspace Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Delete Workspace</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedWorkspace?.name}"?
              This action cannot be undone and will permanently remove all data.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDeleteWorkspace}
            >
              Delete Workspace
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};