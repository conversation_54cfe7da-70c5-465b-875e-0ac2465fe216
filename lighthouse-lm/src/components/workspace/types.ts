/**
 * Workspace Management System Types
 * Enterprise-grade workspace collaboration and management
 */

export interface Workspace {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
  owner_id: string;
  is_personal: boolean;
  settings: WorkspaceSettings;
  members: WorkspaceMember[];
  notebooks: string[]; // notebook IDs
  tags: string[];
  metadata: Record<string, any>;
  encrypted_data?: string; // Encrypted sensitive data
  sync_status: SyncStatus;
  storage_usage: number; // bytes
  storage_limit: number; // bytes
}

export interface WorkspaceSettings {
  theme: 'light' | 'dark' | 'system';
  default_sharing: 'private' | 'team' | 'public';
  auto_save_interval: number; // seconds
  enable_real_time_sync: boolean;
  enable_version_history: boolean;
  max_file_size: number; // bytes
  allowed_file_types: string[];
  collaboration_settings: CollaborationSettings;
  security_settings: SecuritySettings;
  notification_preferences: NotificationPreferences;
}

export interface CollaborationSettings {
  allow_guest_access: boolean;
  require_approval_for_joins: boolean;
  default_member_role: MemberRole;
  enable_comments: boolean;
  enable_mentions: boolean;
  enable_activity_feed: boolean;
  max_concurrent_editors: number;
}

export interface SecuritySettings {
  require_2fa: boolean;
  enable_data_encryption: boolean;
  session_timeout: number; // minutes
  ip_whitelist?: string[];
  audit_logging: boolean;
  data_retention_days: number;
}

export interface NotificationPreferences {
  email_notifications: boolean;
  push_notifications: boolean;
  digest_frequency: 'immediate' | 'hourly' | 'daily' | 'weekly';
  notify_on_mentions: boolean;
  notify_on_comments: boolean;
  notify_on_shares: boolean;
}

export interface WorkspaceMember {
  user_id: string;
  username: string;
  email: string;
  display_name?: string;
  avatar_url?: string;
  role: MemberRole;
  permissions: Permission[];
  joined_at: string;
  last_active_at?: string;
  is_online: boolean;
  status: MemberStatus;
}

export type MemberRole = 'owner' | 'admin' | 'editor' | 'viewer' | 'guest';

export type Permission = 
  | 'read_workspace'
  | 'write_workspace' 
  | 'delete_workspace'
  | 'manage_members'
  | 'manage_settings'
  | 'create_notebooks'
  | 'edit_notebooks'
  | 'delete_notebooks'
  | 'share_notebooks'
  | 'export_data'
  | 'import_data'
  | 'manage_integrations';

export type MemberStatus = 'active' | 'pending' | 'suspended' | 'removed';

export type SyncStatus = 'synced' | 'pending' | 'syncing' | 'conflict' | 'error' | 'offline';

export interface WorkspaceInvitation {
  id: string;
  workspace_id: string;
  inviter_id: string;
  invitee_email: string;
  role: MemberRole;
  permissions?: Permission[];
  message?: string;
  expires_at: string;
  created_at: string;
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
}

export interface WorkspaceActivity {
  id: string;
  workspace_id: string;
  user_id: string;
  action: ActivityAction;
  entity_type: 'workspace' | 'notebook' | 'source' | 'member';
  entity_id?: string;
  metadata: Record<string, any>;
  created_at: string;
}

export type ActivityAction = 
  | 'created'
  | 'updated'
  | 'deleted'
  | 'shared'
  | 'joined'
  | 'left'
  | 'invited'
  | 'promoted'
  | 'demoted'
  | 'exported'
  | 'imported';

// React component prop types
export interface WorkspaceManagerProps {
  onWorkspaceSelect?: (workspace: Workspace) => void;
  onWorkspaceCreate?: (workspace: Workspace) => void;
  onWorkspaceUpdate?: (workspace: Workspace) => void;
  onWorkspaceDelete?: (workspaceId: string) => void;
  className?: string;
}

export interface WorkspaceSwitcherProps {
  currentWorkspace?: Workspace;
  onWorkspaceChange: (workspace: Workspace) => void;
  compact?: boolean;
  showCreateButton?: boolean;
  className?: string;
}

export interface WorkspaceSettingsProps {
  workspace: Workspace;
  onSave: (settings: WorkspaceSettings) => Promise<void>;
  onCancel: () => void;
  readOnly?: boolean;
  className?: string;
}

export interface CollaborationPanelProps {
  workspace: Workspace;
  onMemberAdd?: (member: WorkspaceMember) => void;
  onMemberRemove?: (userId: string) => void;
  onMemberRoleChange?: (userId: string, role: MemberRole) => void;
  onInvitationSend?: (invitation: Omit<WorkspaceInvitation, 'id' | 'created_at'>) => void;
  className?: string;
}

export interface WorkspaceContextValue {
  // Current workspace state
  currentWorkspace: Workspace | null;
  workspaces: Workspace[];
  isLoading: boolean;
  error: string | null;

  // Workspace operations
  selectWorkspace: (workspaceId: string) => Promise<void>;
  createWorkspace: (workspace: Omit<Workspace, 'id' | 'created_at' | 'updated_at'>) => Promise<Workspace>;
  updateWorkspace: (workspaceId: string, updates: Partial<Workspace>) => Promise<void>;
  deleteWorkspace: (workspaceId: string) => Promise<void>;
  
  // Member operations
  inviteMember: (invitation: Omit<WorkspaceInvitation, 'id' | 'created_at'>) => Promise<void>;
  removeMember: (workspaceId: string, userId: string) => Promise<void>;
  updateMemberRole: (workspaceId: string, userId: string, role: MemberRole) => Promise<void>;
  
  // Sync operations
  syncWorkspace: (workspaceId: string) => Promise<void>;
  resolveSyncConflict: (workspaceId: string, resolution: 'local' | 'remote' | 'merge') => Promise<void>;
  
  // Activity tracking
  getWorkspaceActivity: (workspaceId: string, limit?: number) => Promise<WorkspaceActivity[]>;
  
  // Utility functions
  canPerformAction: (action: Permission, workspaceId?: string) => boolean;
  getStorageUsage: (workspaceId: string) => Promise<{ used: number; limit: number }>;
}

// Utility types
export interface CreateWorkspaceRequest {
  name: string;
  description?: string;
  is_personal: boolean;
  settings?: Partial<WorkspaceSettings>;
  initial_members?: Array<{
    email: string;
    role: MemberRole;
  }>;
}

export interface WorkspaceFilter {
  search?: string;
  owner_id?: string;
  is_personal?: boolean;
  created_after?: string;
  created_before?: string;
  tags?: string[];
  sort_by?: 'name' | 'created_at' | 'updated_at' | 'storage_usage';
  sort_order?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

export interface WorkspaceStats {
  total_workspaces: number;
  personal_workspaces: number;
  team_workspaces: number;
  total_notebooks: number;
  total_sources: number;
  total_storage_used: number;
  total_storage_limit: number;
  active_collaborators: number;
}