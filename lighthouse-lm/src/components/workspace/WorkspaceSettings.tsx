/**
 * Workspace Settings - Comprehensive workspace configuration interface
 * Enterprise-grade settings with security, collaboration, and notification controls
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../ui/card';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from '../ui/tabs';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Switch } from '../ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { Slider } from '../ui/slider';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../ui/alert-dialog';
import {
  Settings,
  Users,
  Shield,
  Bell,
  Palette,
  HardDrive,
  Clock,
  Globe,
  Lock,
  Save,
  AlertTriangle,
  Info,
  Trash2,
  Plus,
  X,
} from 'lucide-react';
import { toast } from 'sonner';
import {
  WorkspaceSettings as WorkspaceSettingsType,
  WorkspaceSettingsProps,
  MemberRole,
} from './types';
import { cn } from '../../lib/utils';

export const WorkspaceSettings: React.FC<WorkspaceSettingsProps> = ({
  workspace,
  onSave,
  onCancel,
  readOnly = false,
  className,
}) => {
  const [settings, setSettings] = useState<WorkspaceSettingsType>(workspace.settings);
  const [hasChanges, setHasChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [newFileType, setNewFileType] = useState('');
  const [newIpAddress, setNewIpAddress] = useState('');

  // Track changes
  useEffect(() => {
    const settingsChanged = JSON.stringify(settings) !== JSON.stringify(workspace.settings);
    setHasChanges(settingsChanged);
  }, [settings, workspace.settings]);

  const handleSave = async () => {
    if (!hasChanges) return;

    setIsSaving(true);
    try {
      await onSave(settings);
      setHasChanges(false);
      toast.success('Workspace settings updated successfully');
    } catch (error) {
      console.error('Failed to save settings:', error);
      toast.error('Failed to save settings');
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    setSettings(workspace.settings);
    setHasChanges(false);
  };

  const updateSettings = (updates: Partial<WorkspaceSettingsType>) => {
    setSettings(prev => ({ ...prev, ...updates }));
  };

  const updateCollaborationSettings = (updates: Partial<WorkspaceSettingsType['collaboration_settings']>) => {
    setSettings(prev => ({
      ...prev,
      collaboration_settings: { ...prev.collaboration_settings, ...updates }
    }));
  };

  const updateSecuritySettings = (updates: Partial<WorkspaceSettingsType['security_settings']>) => {
    setSettings(prev => ({
      ...prev,
      security_settings: { ...prev.security_settings, ...updates }
    }));
  };

  const updateNotificationSettings = (updates: Partial<WorkspaceSettingsType['notification_preferences']>) => {
    setSettings(prev => ({
      ...prev,
      notification_preferences: { ...prev.notification_preferences, ...updates }
    }));
  };

  const addFileType = () => {
    if (!newFileType.trim()) return;
    const fileType = newFileType.trim().toLowerCase();
    if (!settings.allowed_file_types.includes(fileType)) {
      updateSettings({
        allowed_file_types: [...settings.allowed_file_types, fileType]
      });
    }
    setNewFileType('');
  };

  const removeFileType = (fileType: string) => {
    updateSettings({
      allowed_file_types: settings.allowed_file_types.filter(type => type !== fileType)
    });
  };

  const addIpAddress = () => {
    if (!newIpAddress.trim()) return;
    const ip = newIpAddress.trim();
    const currentList = settings.security_settings.ip_whitelist || [];
    if (!currentList.includes(ip)) {
      updateSecuritySettings({
        ip_whitelist: [...currentList, ip]
      });
    }
    setNewIpAddress('');
  };

  const removeIpAddress = (ip: string) => {
    const currentList = settings.security_settings.ip_whitelist || [];
    updateSecuritySettings({
      ip_whitelist: currentList.filter(address => address !== ip)
    });
  };

  const formatBytes = (bytes: number): string => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Workspace Settings</h2>
          <p className="text-muted-foreground">
            Configure your workspace preferences and policies
          </p>
        </div>
        
        {!readOnly && (
          <div className="flex space-x-2">
            {hasChanges && (
              <Button variant="outline" onClick={handleReset}>
                Reset Changes
              </Button>
            )}
            <Button onClick={onCancel} variant="ghost">
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={!hasChanges || isSaving}
            >
              {isSaving ? (
                <>
                  <Clock className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        )}
      </div>

      {hasChanges && (
        <div className="flex items-center space-x-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <Info className="h-4 w-4 text-blue-600" />
          <span className="text-sm text-blue-800">You have unsaved changes</span>
        </div>
      )}

      <Tabs defaultValue="general" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="general" className="flex items-center space-x-1">
            <Settings className="h-4 w-4" />
            <span className="hidden sm:inline">General</span>
          </TabsTrigger>
          <TabsTrigger value="collaboration" className="flex items-center space-x-1">
            <Users className="h-4 w-4" />
            <span className="hidden sm:inline">Collaboration</span>
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center space-x-1">
            <Shield className="h-4 w-4" />
            <span className="hidden sm:inline">Security</span>
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center space-x-1">
            <Bell className="h-4 w-4" />
            <span className="hidden sm:inline">Notifications</span>
          </TabsTrigger>
          <TabsTrigger value="advanced" className="flex items-center space-x-1">
            <HardDrive className="h-4 w-4" />
            <span className="hidden sm:inline">Advanced</span>
          </TabsTrigger>
        </TabsList>

        {/* General Settings */}
        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Palette className="h-5 w-5" />
                <span>Appearance</span>
              </CardTitle>
              <CardDescription>
                Customize the look and feel of your workspace
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Theme</Label>
                <Select
                  value={settings.theme}
                  onValueChange={(value: 'light' | 'dark' | 'system') => updateSettings({ theme: value })}
                  disabled={readOnly}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="light">Light</SelectItem>
                    <SelectItem value="dark">Dark</SelectItem>
                    <SelectItem value="system">System</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Globe className="h-5 w-5" />
                <span>Default Sharing</span>
              </CardTitle>
              <CardDescription>
                Set default visibility for new content
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Default sharing level</Label>
                <Select
                  value={settings.default_sharing}
                  onValueChange={(value: 'private' | 'team' | 'public') => updateSettings({ default_sharing: value })}
                  disabled={readOnly}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="private">
                      <div className="flex items-center space-x-2">
                        <Lock className="h-4 w-4" />
                        <span>Private</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="team">
                      <div className="flex items-center space-x-2">
                        <Users className="h-4 w-4" />
                        <span>Team</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="public">
                      <div className="flex items-center space-x-2">
                        <Globe className="h-4 w-4" />
                        <span>Public</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Clock className="h-5 w-5" />
                <span>Auto-save</span>
              </CardTitle>
              <CardDescription>
                Configure automatic saving behavior
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Auto-save interval</Label>
                  <span className="text-sm text-muted-foreground">
                    {settings.auto_save_interval}s
                  </span>
                </div>
                <Slider
                  value={[settings.auto_save_interval]}
                  onValueChange={([value]) => updateSettings({ auto_save_interval: value })}
                  min={10}
                  max={300}
                  step={10}
                  disabled={readOnly}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>10s</span>
                  <span>5min</span>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={settings.enable_real_time_sync}
                  onCheckedChange={(checked) => updateSettings({ enable_real_time_sync: checked })}
                  disabled={readOnly}
                />
                <Label>Enable real-time sync</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={settings.enable_version_history}
                  onCheckedChange={(checked) => updateSettings({ enable_version_history: checked })}
                  disabled={readOnly}
                />
                <Label>Enable version history</Label>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <HardDrive className="h-5 w-5" />
                <span>File Management</span>
              </CardTitle>
              <CardDescription>
                Configure file upload and storage settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Maximum file size</Label>
                  <span className="text-sm text-muted-foreground">
                    {formatBytes(settings.max_file_size)}
                  </span>
                </div>
                <Slider
                  value={[settings.max_file_size]}
                  onValueChange={([value]) => updateSettings({ max_file_size: value })}
                  min={1024 * 1024} // 1MB
                  max={1024 * 1024 * 1024} // 1GB
                  step={1024 * 1024} // 1MB steps
                  disabled={readOnly}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>1MB</span>
                  <span>1GB</span>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Allowed file types</Label>
                <div className="flex flex-wrap gap-2 mb-2">
                  {settings.allowed_file_types.map((type) => (
                    <Badge key={type} variant="secondary" className="flex items-center space-x-1">
                      <span>{type}</span>
                      {!readOnly && (
                        <button
                          onClick={() => removeFileType(type)}
                          className="ml-1 hover:text-red-500"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      )}
                    </Badge>
                  ))}
                </div>
                {!readOnly && (
                  <div className="flex space-x-2">
                    <Input
                      placeholder="e.g., .pdf, .docx"
                      value={newFileType}
                      onChange={(e) => setNewFileType(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addFileType()}
                    />
                    <Button onClick={addFileType} size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Collaboration Settings */}
        <TabsContent value="collaboration" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>Team Access</span>
              </CardTitle>
              <CardDescription>
                Control how team members can join and collaborate
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  checked={settings.collaboration_settings.allow_guest_access}
                  onCheckedChange={(checked) => 
                    updateCollaborationSettings({ allow_guest_access: checked })
                  }
                  disabled={readOnly}
                />
                <Label>Allow guest access</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={settings.collaboration_settings.require_approval_for_joins}
                  onCheckedChange={(checked) => 
                    updateCollaborationSettings({ require_approval_for_joins: checked })
                  }
                  disabled={readOnly}
                />
                <Label>Require approval for new members</Label>
              </div>

              <div className="space-y-2">
                <Label>Default member role</Label>
                <Select
                  value={settings.collaboration_settings.default_member_role}
                  onValueChange={(value: MemberRole) => 
                    updateCollaborationSettings({ default_member_role: value })
                  }
                  disabled={readOnly}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="viewer">Viewer</SelectItem>
                    <SelectItem value="editor">Editor</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Maximum concurrent editors</Label>
                  <span className="text-sm text-muted-foreground">
                    {settings.collaboration_settings.max_concurrent_editors}
                  </span>
                </div>
                <Slider
                  value={[settings.collaboration_settings.max_concurrent_editors]}
                  onValueChange={([value]) => 
                    updateCollaborationSettings({ max_concurrent_editors: value })
                  }
                  min={1}
                  max={50}
                  step={1}
                  disabled={readOnly}
                  className="w-full"
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Collaboration Features</CardTitle>
              <CardDescription>
                Enable or disable specific collaboration features
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  checked={settings.collaboration_settings.enable_comments}
                  onCheckedChange={(checked) => 
                    updateCollaborationSettings({ enable_comments: checked })
                  }
                  disabled={readOnly}
                />
                <Label>Enable comments</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={settings.collaboration_settings.enable_mentions}
                  onCheckedChange={(checked) => 
                    updateCollaborationSettings({ enable_mentions: checked })
                  }
                  disabled={readOnly}
                />
                <Label>Enable @mentions</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={settings.collaboration_settings.enable_activity_feed}
                  onCheckedChange={(checked) => 
                    updateCollaborationSettings({ enable_activity_feed: checked })
                  }
                  disabled={readOnly}
                />
                <Label>Enable activity feed</Label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Settings */}
        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5" />
                <span>Authentication & Access</span>
              </CardTitle>
              <CardDescription>
                Configure security policies and access controls
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  checked={settings.security_settings.require_2fa}
                  onCheckedChange={(checked) => 
                    updateSecuritySettings({ require_2fa: checked })
                  }
                  disabled={readOnly}
                />
                <Label>Require two-factor authentication</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={settings.security_settings.enable_data_encryption}
                  onCheckedChange={(checked) => 
                    updateSecuritySettings({ enable_data_encryption: checked })
                  }
                  disabled={readOnly}
                />
                <Label>Enable data encryption at rest</Label>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Session timeout</Label>
                  <span className="text-sm text-muted-foreground">
                    {settings.security_settings.session_timeout} minutes
                  </span>
                </div>
                <Slider
                  value={[settings.security_settings.session_timeout]}
                  onValueChange={([value]) => 
                    updateSecuritySettings({ session_timeout: value })
                  }
                  min={15}
                  max={1440} // 24 hours
                  step={15}
                  disabled={readOnly}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>15min</span>
                  <span>24h</span>
                </div>
              </div>

              <div className="space-y-2">
                <Label>IP Whitelist</Label>
                <div className="space-y-2">
                  {settings.security_settings.ip_whitelist?.map((ip) => (
                    <div key={ip} className="flex items-center justify-between p-2 bg-muted rounded">
                      <span className="font-mono text-sm">{ip}</span>
                      {!readOnly && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeIpAddress(ip)}
                          className="text-red-600 hover:text-red-800"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
                {!readOnly && (
                  <div className="flex space-x-2">
                    <Input
                      placeholder="IP address or CIDR (e.g., ***********/24)"
                      value={newIpAddress}
                      onChange={(e) => setNewIpAddress(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addIpAddress()}
                    />
                    <Button onClick={addIpAddress} size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Audit & Compliance</CardTitle>
              <CardDescription>
                Configure audit logging and data retention policies
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  checked={settings.security_settings.audit_logging}
                  onCheckedChange={(checked) => 
                    updateSecuritySettings({ audit_logging: checked })
                  }
                  disabled={readOnly}
                />
                <Label>Enable audit logging</Label>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Data retention period</Label>
                  <span className="text-sm text-muted-foreground">
                    {settings.security_settings.data_retention_days} days
                  </span>
                </div>
                <Slider
                  value={[settings.security_settings.data_retention_days]}
                  onValueChange={([value]) => 
                    updateSecuritySettings({ data_retention_days: value })
                  }
                  min={30}
                  max={2555} // ~7 years
                  step={30}
                  disabled={readOnly}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>30 days</span>
                  <span>7 years</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notification Settings */}
        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Bell className="h-5 w-5" />
                <span>Notification Preferences</span>
              </CardTitle>
              <CardDescription>
                Configure how and when you receive notifications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  checked={settings.notification_preferences.email_notifications}
                  onCheckedChange={(checked) => 
                    updateNotificationSettings({ email_notifications: checked })
                  }
                  disabled={readOnly}
                />
                <Label>Email notifications</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={settings.notification_preferences.push_notifications}
                  onCheckedChange={(checked) => 
                    updateNotificationSettings({ push_notifications: checked })
                  }
                  disabled={readOnly}
                />
                <Label>Push notifications</Label>
              </div>

              <div className="space-y-2">
                <Label>Digest frequency</Label>
                <Select
                  value={settings.notification_preferences.digest_frequency}
                  onValueChange={(value: 'immediate' | 'hourly' | 'daily' | 'weekly') => 
                    updateNotificationSettings({ digest_frequency: value })
                  }
                  disabled={readOnly}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="immediate">Immediate</SelectItem>
                    <SelectItem value="hourly">Hourly</SelectItem>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Notification Types</CardTitle>
              <CardDescription>
                Choose which activities trigger notifications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  checked={settings.notification_preferences.notify_on_mentions}
                  onCheckedChange={(checked) => 
                    updateNotificationSettings({ notify_on_mentions: checked })
                  }
                  disabled={readOnly}
                />
                <Label>Mentions</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={settings.notification_preferences.notify_on_comments}
                  onCheckedChange={(checked) => 
                    updateNotificationSettings({ notify_on_comments: checked })
                  }
                  disabled={readOnly}
                />
                <Label>Comments</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={settings.notification_preferences.notify_on_shares}
                  onCheckedChange={(checked) => 
                    updateNotificationSettings({ notify_on_shares: checked })
                  }
                  disabled={readOnly}
                />
                <Label>Shares</Label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Advanced Settings */}
        <TabsContent value="advanced" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-red-600">
                <AlertTriangle className="h-5 w-5" />
                <span>Danger Zone</span>
              </CardTitle>
              <CardDescription>
                Irreversible and destructive actions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {!readOnly && (
                <Button
                  variant="destructive"
                  onClick={() => setShowDeleteDialog(true)}
                  className="w-full"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Workspace
                </Button>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Workspace</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the workspace
              "{workspace.name}" and all associated data, including notebooks, sources,
              and member access.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-600 hover:bg-red-700"
              onClick={() => {
                // TODO: Implement workspace deletion
                setShowDeleteDialog(false);
              }}
            >
              Delete Workspace
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};