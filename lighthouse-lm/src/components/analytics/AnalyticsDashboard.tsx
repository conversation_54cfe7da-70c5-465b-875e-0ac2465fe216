import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { UsageMetrics } from './UsageMetrics';
import { ProductivityInsights } from './ProductivityInsights';
import { ContentAnalytics } from './ContentAnalytics';
import { useAnalytics } from '@/hooks/useAnalytics';
import { AnalyticsTimeRange, ExportOptions } from '@/types/analytics';
import { 
  Calendar,
  Download,
  Settings,
  TrendingUp,
  BarChart3,
  PieChart,
  Activity,
  RefreshCw,
  AlertCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { format, subDays, startOfToday, endOfToday, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from 'date-fns';

interface AnalyticsDashboardProps {
  className?: string;
}

const TIME_RANGES = {
  'today': {
    label: 'Today',
    start: startOfToday(),
    end: endOfToday(),
    preset: 'today' as const
  },
  '7days': {
    label: 'Last 7 Days',
    start: subDays(new Date(), 7),
    end: new Date(),
    preset: '7days' as const
  },
  '30days': {
    label: 'Last 30 Days',
    start: subDays(new Date(), 30),
    end: new Date(),
    preset: '30days' as const
  },
  '90days': {
    label: 'Last 90 Days',
    start: subDays(new Date(), 90),
    end: new Date(),
    preset: '90days' as const
  },
  'thisWeek': {
    label: 'This Week',
    start: startOfWeek(new Date()),
    end: endOfWeek(new Date()),
    preset: '7days' as const
  },
  'thisMonth': {
    label: 'This Month',
    start: startOfMonth(new Date()),
    end: endOfMonth(new Date()),
    preset: '30days' as const
  }
};

export const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({
  className
}) => {
  const [selectedTimeRange, setSelectedTimeRange] = React.useState<keyof typeof TIME_RANGES>('7days');
  const [isExporting, setIsExporting] = React.useState(false);

  const timeRange: AnalyticsTimeRange = React.useMemo(() => ({
    ...TIME_RANGES[selectedTimeRange],
    preset: TIME_RANGES[selectedTimeRange].preset
  }), [selectedTimeRange]);

  const {
    data,
    isLoading,
    isError,
    error,
    refetch,
    updateGoalProgress,
    handleInsightAction,
    exportData
  } = useAnalytics({
    timeRange,
    refreshInterval: 30000 // Refresh every 30 seconds
  });

  const handleExport = async (format: 'pdf' | 'csv' | 'json' = 'csv') => {
    try {
      setIsExporting(true);
      
      const exportOptions: ExportOptions = {
        format,
        includeCharts: format === 'pdf',
        includeRawData: true,
        dateRange: timeRange,
        sections: ['sessions', 'content', 'productivity', 'insights']
      };

      const blob = await exportData(exportOptions);
      
      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `analytics-${format}-${format(new Date(), 'yyyy-MM-dd')}.${format === 'json' ? 'json' : 'csv'}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  // Calculate summary stats
  const summaryStats = React.useMemo(() => {
    if (!data) return null;

    const totalSessions = data.sessions.length;
    const totalActiveTime = data.sessions.reduce((sum, s) => sum + s.activeTime, 0);
    const totalContent = data.contentMetrics.reduce((sum, m) => 
      sum + m.notesCreated + m.sourcesAdded + m.chatMessages, 0);
    const avgProductivityScore = data.productivityMetrics.productivityScore;

    return {
      totalSessions,
      totalActiveTime,
      totalContent,
      avgProductivityScore,
      insights: data.insights.length,
      activeGoals: data.goals.filter(g => g.status === 'active').length
    };
  }, [data]);

  const formatTime = (minutes: number): string => {
    if (minutes < 60) return `${Math.round(minutes)}m`;
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
  };

  if (isError) {
    return (
      <div className={cn("space-y-6", className)}>
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center space-y-4">
              <AlertCircle className="h-12 w-12 mx-auto text-destructive" />
              <div>
                <h3 className="text-lg font-semibold">Failed to Load Analytics</h3>
                <p className="text-muted-foreground">
                  {error?.message || 'An error occurred while loading your analytics data.'}
                </p>
              </div>
              <Button onClick={() => refetch()} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h1>
          <p className="text-muted-foreground">
            Insights into your productivity and usage patterns
          </p>
        </div>

        <div className="flex items-center gap-3">
          {/* Time Range Selector */}
          <Select 
            value={selectedTimeRange} 
            onValueChange={(value: keyof typeof TIME_RANGES) => setSelectedTimeRange(value)}
          >
            <SelectTrigger className="w-[180px]">
              <Calendar className="h-4 w-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(TIME_RANGES).map(([key, range]) => (
                <SelectItem key={key} value={key}>
                  {range.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Export Button */}
          <Button
            variant="outline"
            onClick={() => handleExport('csv')}
            disabled={isExporting || isLoading}
          >
            <Download className="h-4 w-4 mr-2" />
            {isExporting ? 'Exporting...' : 'Export'}
          </Button>

          {/* Refresh Button */}
          <Button
            variant="outline"
            size="icon"
            onClick={() => refetch()}
            disabled={isLoading}
          >
            <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      {summaryStats && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Sessions</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summaryStats.totalSessions}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Time</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatTime(summaryStats.totalActiveTime)}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Content Items</CardTitle>
              <PieChart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summaryStats.totalContent}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Productivity</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{Math.round(summaryStats.avgProductivityScore)}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Insights</CardTitle>
              <AlertCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summaryStats.insights}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Goals</CardTitle>
              <Settings className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summaryStats.activeGoals}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Time Range Display */}
      <Card>
        <CardContent className="py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Data Range:</span>
              <Badge variant="outline">
                {format(timeRange.start, 'MMM dd, yyyy')} - {format(timeRange.end, 'MMM dd, yyyy')}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <div className={cn(
                "h-2 w-2 rounded-full",
                isLoading ? "bg-yellow-500 animate-pulse" : "bg-green-500"
              )} />
              <span className="text-xs text-muted-foreground">
                {isLoading ? 'Loading...' : `Last updated: ${format(new Date(), 'HH:mm')}`}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Analytics Tabs */}
      <Tabs defaultValue="usage" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="usage" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Usage
          </TabsTrigger>
          <TabsTrigger value="productivity" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Productivity
          </TabsTrigger>
          <TabsTrigger value="content" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Content
          </TabsTrigger>
        </TabsList>

        <TabsContent value="usage" className="space-y-6">
          {data && (
            <UsageMetrics
              sessions={data.sessions}
              timeRange={timeRange}
              isLoading={isLoading}
            />
          )}
        </TabsContent>

        <TabsContent value="productivity" className="space-y-6">
          {data && (
            <ProductivityInsights
              productivityMetrics={data.productivityMetrics}
              insights={data.insights}
              goals={data.goals}
              timeRange={timeRange}
              onGoalUpdate={updateGoalProgress}
              onInsightAction={handleInsightAction}
            />
          )}
        </TabsContent>

        <TabsContent value="content" className="space-y-6">
          {data && (
            <ContentAnalytics
              contentMetrics={data.contentMetrics}
              searchAnalytics={data.searchAnalytics}
              timeRange={timeRange}
              isLoading={isLoading}
            />
          )}
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common analytics operations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport('json')}
              disabled={isExporting || isLoading}
            >
              Export JSON
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport('csv')}
              disabled={isExporting || isLoading}
            >
              Export CSV
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedTimeRange('30days')}
            >
              View Last Month
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedTimeRange('90days')}
            >
              View Last Quarter
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Footer */}
      <div className="text-center py-4">
        <p className="text-xs text-muted-foreground">
          Analytics data is updated in real-time. Export functionality preserves data privacy.
        </p>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;