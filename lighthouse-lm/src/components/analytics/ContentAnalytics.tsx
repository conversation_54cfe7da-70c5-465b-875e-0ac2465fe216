import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { TimeSeriesChart } from './charts/TimeSeriesChart';
import { Bar<PERSON>hart } from './charts/BarChart';
import { Pie<PERSON><PERSON> } from './charts/PieChart';
import { ContentMetrics, SearchAnalytics, AnalyticsTimeRange } from '@/types/analytics';
import { 
  FileText, 
  MessageSquare, 
  Search, 
  BookOpen,
  PlusCircle,
  Edit3,
  Eye,
  TrendingUp,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieChartIcon
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { format, subDays } from 'date-fns';

interface ContentAnalyticsProps {
  contentMetrics: ContentMetrics[];
  searchAnalytics: SearchAnalytics[];
  timeRange: AnalyticsTimeRange;
  isLoading?: boolean;
  className?: string;
}

const formatNumber = (num: number): string => {
  if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
  if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
  return num.toString();
};

const getContentTypeIcon = (type: string) => {
  switch (type.toLowerCase()) {
    case 'note': return <FileText className="h-4 w-4" />;
    case 'chat': return <MessageSquare className="h-4 w-4" />;
    case 'source': return <BookOpen className="h-4 w-4" />;
    case 'document': return <FileText className="h-4 w-4" />;
    default: return <FileText className="h-4 w-4" />;
  }
};

export const ContentAnalytics: React.FC<ContentAnalyticsProps> = ({
  contentMetrics,
  searchAnalytics,
  timeRange,
  isLoading = false,
  className
}) => {
  // Aggregate content metrics across all notebooks
  const aggregatedMetrics = React.useMemo(() => {
    return contentMetrics.reduce((acc, metric) => ({
      notesCreated: acc.notesCreated + metric.notesCreated,
      notesModified: acc.notesModified + metric.notesModified,
      sourcesAdded: acc.sourcesAdded + metric.sourcesAdded,
      sourcesAnalyzed: acc.sourcesAnalyzed + metric.sourcesAnalyzed,
      chatMessages: acc.chatMessages + metric.chatMessages,
      studioDocuments: acc.studioDocuments + metric.studioDocuments,
      totalWords: acc.totalWords + metric.totalWords,
      totalCharacters: acc.totalCharacters + metric.totalCharacters,
      contentTypes: {
        ...acc.contentTypes,
        ...Object.entries(metric.contentTypes).reduce((types, [type, count]) => ({
          ...types,
          [type]: (types[type] || 0) + count
        }), {} as Record<string, number>)
      }
    }), {
      notesCreated: 0,
      notesModified: 0,
      sourcesAdded: 0,
      sourcesAnalyzed: 0,
      chatMessages: 0,
      studioDocuments: 0,
      totalWords: 0,
      totalCharacters: 0,
      contentTypes: {} as Record<string, number>
    });
  }, [contentMetrics]);

  // Generate content creation trends (mock data for demonstration)
  const contentTrendData = React.useMemo(() => {
    const days = 14;
    const data = [];
    const baseCreation = Math.max(1, aggregatedMetrics.notesCreated / days);
    
    for (let i = days - 1; i >= 0; i--) {
      const date = subDays(new Date(), i);
      const variation = (Math.random() - 0.5) * baseCreation;
      const value = Math.max(0, Math.round(baseCreation + variation));
      
      data.push({
        timestamp: date,
        value,
        label: format(date, 'MMM dd')
      });
    }
    
    return data;
  }, [aggregatedMetrics.notesCreated]);

  // Content type distribution for pie chart
  const contentTypeData = React.useMemo(() => {
    return Object.entries(aggregatedMetrics.contentTypes).map(([type, count]) => ({
      name: type.charAt(0).toUpperCase() + type.slice(1),
      value: count
    }));
  }, [aggregatedMetrics.contentTypes]);

  // Notebook productivity ranking
  const notebookRanking = React.useMemo(() => {
    return contentMetrics
      .map(metric => ({
        name: metric.notebookId.length > 15 
          ? metric.notebookId.substring(0, 15) + '...' 
          : metric.notebookId,
        value: metric.notesCreated + metric.sourcesAdded + metric.chatMessages,
        metadata: {
          fullName: metric.notebookId,
          notes: metric.notesCreated,
          sources: metric.sourcesAdded,
          messages: metric.chatMessages,
          words: metric.totalWords
        }
      }))
      .sort((a, b) => b.value - a.value);
  }, [contentMetrics]);

  // Search analytics processing
  const searchStats = React.useMemo(() => {
    const totalSearches = searchAnalytics.length;
    const totalResults = searchAnalytics.reduce((sum, search) => sum + search.resultCount, 0);
    const totalClicks = searchAnalytics.reduce((sum, search) => sum + search.clickedResults, 0);
    const avgClickThrough = totalResults > 0 ? (totalClicks / totalResults) * 100 : 0;
    
    const searchTypes = searchAnalytics.reduce((acc, search) => {
      acc[search.searchType] = (acc[search.searchType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const popularQueries = searchAnalytics
      .reduce((acc, search) => {
        const query = search.query.toLowerCase();
        acc[query] = (acc[query] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

    const topQueries = Object.entries(popularQueries)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([query, count]) => ({ name: query, value: count }));

    return {
      totalSearches,
      totalResults,
      totalClicks,
      avgClickThrough,
      searchTypes: Object.entries(searchTypes).map(([type, count]) => ({
        name: type.charAt(0).toUpperCase() + type.slice(1),
        value: count
      })),
      topQueries
    };
  }, [searchAnalytics]);

  // Calculate writing productivity
  const writingMetrics = React.useMemo(() => {
    const totalContent = aggregatedMetrics.totalWords + aggregatedMetrics.totalCharacters;
    const daysDiff = Math.max(1, Math.ceil((timeRange.end.getTime() - timeRange.start.getTime()) / (1000 * 60 * 60 * 24)));
    
    return {
      avgWordsPerDay: Math.round(aggregatedMetrics.totalWords / daysDiff),
      avgCharactersPerDay: Math.round(aggregatedMetrics.totalCharacters / daysDiff),
      avgWordsPerNote: aggregatedMetrics.notesCreated > 0 ? Math.round(aggregatedMetrics.totalWords / aggregatedMetrics.notesCreated) : 0,
      contentVelocity: Math.round(totalContent / daysDiff)
    };
  }, [aggregatedMetrics, timeRange]);

  if (isLoading) {
    return (
      <div className={cn("space-y-6", className)}>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="space-y-0 pb-2">
                <div className="h-4 w-20 bg-muted animate-pulse rounded" />
              </CardHeader>
              <CardContent>
                <div className="h-8 w-16 bg-muted animate-pulse rounded mb-2" />
                <div className="h-3 w-24 bg-muted animate-pulse rounded" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Content Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Notes Created</CardTitle>
            <PlusCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {formatNumber(aggregatedMetrics.notesCreated)}
            </div>
            <p className="text-xs text-muted-foreground">
              {writingMetrics.avgWordsPerNote} words per note avg
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Words</CardTitle>
            <Edit3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatNumber(aggregatedMetrics.totalWords)}
            </div>
            <p className="text-xs text-muted-foreground">
              {writingMetrics.avgWordsPerDay} words per day avg
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Chat Messages</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {formatNumber(aggregatedMetrics.chatMessages)}
            </div>
            <p className="text-xs text-muted-foreground">
              {aggregatedMetrics.sourcesAnalyzed} sources analyzed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Searches</CardTitle>
            <Search className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {formatNumber(searchStats.totalSearches)}
            </div>
            <p className="text-xs text-muted-foreground">
              {searchStats.avgClickThrough.toFixed(1)}% click-through rate
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Content Analytics Tabs */}
      <Tabs defaultValue="trends" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="content">Content</TabsTrigger>
          <TabsTrigger value="search">Search</TabsTrigger>
          <TabsTrigger value="productivity">Productivity</TabsTrigger>
        </TabsList>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Content Creation Trend
              </CardTitle>
              <CardDescription>
                Daily content creation over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TimeSeriesChart
                data={contentTrendData}
                height={300}
                color="#8884d8"
                type="area"
                showAverage
                formatValue={(value) => `${value} items`}
              />
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Content Type Distribution</CardTitle>
                <CardDescription>
                  Breakdown of content by type
                </CardDescription>
              </CardHeader>
              <CardContent>
                <PieChart
                  data={contentTypeData}
                  height={250}
                  showPercentages
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Writing Statistics</CardTitle>
                <CardDescription>
                  Your writing productivity metrics
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {writingMetrics.avgWordsPerDay}
                    </div>
                    <div className="text-xs text-muted-foreground">Words/Day</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {writingMetrics.avgWordsPerNote}
                    </div>
                    <div className="text-xs text-muted-foreground">Words/Note</div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Notes Modified</span>
                    <span className="font-medium">{aggregatedMetrics.notesModified}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Sources Added</span>
                    <span className="font-medium">{aggregatedMetrics.sourcesAdded}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Studio Documents</span>
                    <span className="font-medium">{aggregatedMetrics.studioDocuments}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="content" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Most Active Notebooks
              </CardTitle>
              <CardDescription>
                Notebooks ranked by total content activity
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BarChart
                data={notebookRanking}
                height={300}
                color="#82ca9d"
                formatValue={(value) => `${value} items`}
                maxBars={10}
                sortBy="value"
                sortOrder="desc"
              />
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Content Activity Breakdown</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { label: 'Notes Created', value: aggregatedMetrics.notesCreated, icon: PlusCircle, color: 'text-blue-600' },
                    { label: 'Notes Modified', value: aggregatedMetrics.notesModified, icon: Edit3, color: 'text-green-600' },
                    { label: 'Sources Added', value: aggregatedMetrics.sourcesAdded, icon: BookOpen, color: 'text-purple-600' },
                    { label: 'Chat Messages', value: aggregatedMetrics.chatMessages, icon: MessageSquare, color: 'text-orange-600' }
                  ].map((item) => {
                    const Icon = item.icon;
                    const total = aggregatedMetrics.notesCreated + aggregatedMetrics.notesModified + 
                                 aggregatedMetrics.sourcesAdded + aggregatedMetrics.chatMessages;
                    const percentage = total > 0 ? (item.value / total) * 100 : 0;

                    return (
                      <div key={item.label} className="flex items-center gap-3">
                        <Icon className={cn("h-4 w-4", item.color)} />
                        <div className="flex-1">
                          <div className="flex justify-between text-sm mb-1">
                            <span>{item.label}</span>
                            <span className="font-medium">{item.value}</span>
                          </div>
                          <div className="w-full bg-muted rounded-full h-2">
                            <div 
                              className={cn("h-2 rounded-full", item.color.replace('text-', 'bg-'))}
                              style={{ width: `${percentage}%` }}
                            />
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Content Quality Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 rounded-lg bg-muted/50">
                    <div className="text-lg font-bold">{formatNumber(aggregatedMetrics.totalWords)}</div>
                    <div className="text-xs text-muted-foreground">Total Words</div>
                  </div>
                  <div className="text-center p-3 rounded-lg bg-muted/50">
                    <div className="text-lg font-bold">{formatNumber(aggregatedMetrics.totalCharacters)}</div>
                    <div className="text-xs text-muted-foreground">Total Characters</div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="text-sm font-medium">Engagement Metrics</div>
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Sources Analyzed</span>
                      <Badge variant="outline">{aggregatedMetrics.sourcesAnalyzed}</Badge>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Studio Documents</span>
                      <Badge variant="outline">{aggregatedMetrics.studioDocuments}</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="search" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Search className="h-5 w-5" />
                  Search Type Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <PieChart
                  data={searchStats.searchTypes}
                  height={250}
                  showPercentages
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Search Performance</CardTitle>
                <CardDescription>
                  How effective are your searches
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {searchStats.avgClickThrough.toFixed(1)}%
                    </div>
                    <div className="text-xs text-muted-foreground">Click-through Rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {searchStats.totalResults > 0 ? (searchStats.totalResults / searchStats.totalSearches).toFixed(1) : '0'}
                    </div>
                    <div className="text-xs text-muted-foreground">Avg Results</div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Total Searches</span>
                    <span className="font-medium">{searchStats.totalSearches}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Total Results</span>
                    <span className="font-medium">{searchStats.totalResults}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Total Clicks</span>
                    <span className="font-medium">{searchStats.totalClicks}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {searchStats.topQueries.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Popular Search Queries</CardTitle>
                <CardDescription>
                  Most frequently searched terms
                </CardDescription>
              </CardHeader>
              <CardContent>
                <BarChart
                  data={searchStats.topQueries}
                  height={250}
                  color="#ffc658"
                  horizontal
                  maxBars={10}
                  formatValue={(value) => `${value} searches`}
                />
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="productivity" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Content Velocity</CardTitle>
                <CardDescription>Daily content creation rate</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {writingMetrics.contentVelocity}
                </div>
                <p className="text-sm text-muted-foreground">
                  units per day
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Writing Efficiency</CardTitle>
                <CardDescription>Words per content item</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-green-600 mb-2">
                  {writingMetrics.avgWordsPerNote}
                </div>
                <p className="text-sm text-muted-foreground">
                  words per note
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Research Ratio</CardTitle>
                <CardDescription>Sources to notes ratio</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-purple-600 mb-2">
                  {aggregatedMetrics.notesCreated > 0 
                    ? (aggregatedMetrics.sourcesAdded / aggregatedMetrics.notesCreated).toFixed(1)
                    : '0'
                  }:1
                </div>
                <p className="text-sm text-muted-foreground">
                  sources per note
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Content Creation Insights</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-3">
                  <h4 className="text-sm font-medium">Strengths</h4>
                  <div className="space-y-2">
                    {aggregatedMetrics.totalWords > 1000 && (
                      <div className="flex items-center gap-2 text-sm text-green-700 dark:text-green-400">
                        <div className="w-2 h-2 rounded-full bg-green-500" />
                        High word count production
                      </div>
                    )}
                    {searchStats.avgClickThrough > 50 && (
                      <div className="flex items-center gap-2 text-sm text-green-700 dark:text-green-400">
                        <div className="w-2 h-2 rounded-full bg-green-500" />
                        Effective search usage
                      </div>
                    )}
                    {aggregatedMetrics.sourcesAnalyzed > aggregatedMetrics.sourcesAdded * 0.5 && (
                      <div className="flex items-center gap-2 text-sm text-green-700 dark:text-green-400">
                        <div className="w-2 h-2 rounded-full bg-green-500" />
                        Good source analysis rate
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="text-sm font-medium">Opportunities</h4>
                  <div className="space-y-2">
                    {writingMetrics.avgWordsPerNote < 100 && (
                      <div className="flex items-center gap-2 text-sm text-amber-700 dark:text-amber-400">
                        <div className="w-2 h-2 rounded-full bg-amber-500" />
                        Consider writing longer notes
                      </div>
                    )}
                    {searchStats.avgClickThrough < 25 && searchStats.totalSearches > 0 && (
                      <div className="flex items-center gap-2 text-sm text-amber-700 dark:text-amber-400">
                        <div className="w-2 h-2 rounded-full bg-amber-500" />
                        Improve search query specificity
                      </div>
                    )}
                    {aggregatedMetrics.sourcesAdded > 0 && aggregatedMetrics.sourcesAnalyzed < aggregatedMetrics.sourcesAdded * 0.3 && (
                      <div className="flex items-center gap-2 text-sm text-amber-700 dark:text-amber-400">
                        <div className="w-2 h-2 rounded-full bg-amber-500" />
                        Analyze more of your sources
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ContentAnalytics;