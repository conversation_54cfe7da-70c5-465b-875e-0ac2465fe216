# Analytics Dashboard System

A comprehensive analytics dashboard for the lighthouse-lm project that provides real-time insights into user activity, productivity metrics, and content creation patterns.

## Overview

The Analytics Dashboard is a complete analytics solution that includes:

- **Real-time usage tracking** with customizable time ranges
- **Productivity metrics and scoring** with AI-powered insights
- **Content analytics** with creation trends and search analysis
- **Goal setting and tracking** system
- **Export functionality** for data portability
- **Mobile-responsive design** with dark/light theme support

## Components

### Core Components

#### `AnalyticsDashboard`
Main dashboard component that orchestrates all analytics views.

```tsx
import { AnalyticsDashboard } from '@/components/analytics';

<AnalyticsDashboard className="max-w-7xl mx-auto" />
```

#### `UsageMetrics`
Displays user activity patterns, session tracking, and usage trends.

- Session count and duration tracking
- Activity heatmaps showing usage patterns throughout the week
- Focus mode distribution analysis
- Notebook usage rankings

#### `ProductivityInsights` 
Provides AI-powered productivity analysis and goal management.

- Focus and productivity scoring (0-100 scale)
- Deep work vs shallow work analysis
- Goal creation and progress tracking
- Actionable insights and recommendations

#### `ContentAnalytics`
Analyzes content creation patterns and search behavior.

- Content creation trends over time
- Writing productivity metrics (words/day, words/note)
- Search analytics with click-through rates
- Content type distribution and notebook rankings

### Chart Components

Located in `./charts/`:

#### `TimeSeriesChart`
```tsx
<TimeSeriesChart
  data={chartData}
  height={300}
  color="#8884d8"
  type="area" // or "line"
  showAverage={true}
  formatValue={(value) => `${value} sessions`}
/>
```

#### `BarChart`
```tsx
<BarChart
  data={barData}
  height={300}
  horizontal={false}
  sortBy="value"
  sortOrder="desc"
  maxBars={10}
  formatValue={(value) => `${value} items`}
/>
```

#### `PieChart`
```tsx
<PieChart
  data={pieData}
  height={300}
  showPercentages={true}
  showLegend={true}
  formatValue={(value) => `${value}%`}
/>
```

#### `HeatmapChart`
```tsx
<HeatmapChart
  data={heatmapData}
  height={250}
  showTooltip={true}
  formatValue={(value) => formatTime(value)}
/>
```

## Services

### `analyticsService`
Core service that handles data collection, processing, and storage.

```tsx
import { analyticsService } from '@/services/analyticsService';

// Track events
analyticsService.trackEvent('custom_action', { data: 'value' });
analyticsService.trackContentCreation('note', 'notebook-123');
analyticsService.trackFeatureUsage('search', 30, 4);

// Goal management
const goalId = await analyticsService.createGoal({
  title: 'Write 1000 words daily',
  category: 'productivity',
  targetValue: 1000,
  unit: 'words'
});

await analyticsService.updateGoalProgress(goalId, 750);

// Export data
const blob = await analyticsService.exportData({
  format: 'csv',
  dateRange: timeRange,
  sections: ['sessions', 'content', 'productivity']
});
```

### `useAnalytics` Hook
React hook that provides analytics data and operations.

```tsx
import { useAnalytics } from '@/hooks/useAnalytics';

const {
  data,
  isLoading,
  refetch,
  updateGoalProgress,
  handleInsightAction,
  exportData,
  trackEvent
} = useAnalytics({
  timeRange: { start: startDate, end: endDate },
  refreshInterval: 30000
});
```

## Data Types

### Core Types

```typescript
interface AnalyticsDashboardData {
  timeRange: AnalyticsTimeRange;
  sessions: SessionMetrics[];
  contentMetrics: ContentMetrics[];
  productivityMetrics: ProductivityMetrics;
  featureUsage: FeatureUsage[];
  searchAnalytics: SearchAnalytics[];
  collaborationMetrics: CollaborationMetrics;
  goals: GoalMetrics[];
  insights: InsightData[];
  config: AnalyticsConfig;
}

interface SessionMetrics {
  sessionId: string;
  startTime: Date;
  endTime?: Date;
  duration: number; // minutes
  activeTime: number; // minutes
  focusMode: 'chat' | 'sources' | 'studio' | 'split';
  notebookId: string;
}

interface ProductivityMetrics {
  focusScore: number; // 0-100
  productivityScore: number; // 0-100
  deepWorkMinutes: number;
  shallowWorkMinutes: number;
  contextSwitches: number;
  completedTasks: number;
  efficiency: number; // tasks per hour
}
```

## Features

### Real-time Tracking
- Automatic session tracking with focus/blur detection
- User activity monitoring (clicks, keyboard, scroll)
- Content creation events (notes, sources, messages)
- Feature usage tracking with duration and satisfaction

### Productivity Analysis
- **Focus Score**: Calculated from distraction count and context switches
- **Productivity Score**: Based on task completion rates and consistency
- **Deep Work Detection**: Identifies periods of sustained focus
- **Goal Management**: Set, track, and achieve productivity goals

### AI-Powered Insights
```typescript
interface InsightData {
  type: 'suggestion' | 'warning' | 'achievement' | 'trend';
  title: string;
  description: string;
  impact: 'low' | 'medium' | 'high';
  confidence: number; // 0-100
  actionable: boolean;
  actions?: InsightAction[];
}
```

Insights are automatically generated based on:
- Low focus scores (suggests focus mode)
- Inactive days (suggests daily goals)
- Achievement milestones (celebrates completions)
- Usage patterns (identifies optimization opportunities)

### Search Analytics
- Query tracking with result counts
- Click-through rate analysis
- Popular query identification
- Search type distribution (source, chat, global)

### Export Options
- **CSV**: Tabular data for spreadsheet analysis
- **JSON**: Complete structured data
- **PDF**: Formatted reports with charts (planned)

Export includes:
- Session data with timestamps
- Content metrics by notebook
- Productivity trends
- Goal progress history

## Integration

### Basic Setup

```tsx
import React from 'react';
import { AnalyticsDashboard } from '@/components/analytics';
import { ThemeProvider } from '@/contexts/ThemeContext';

function App() {
  return (
    <ThemeProvider>
      <AnalyticsDashboard />
    </ThemeProvider>
  );
}
```

### With Routing

```tsx
import { Routes, Route } from 'react-router-dom';
import { AnalyticsDashboard } from '@/components/analytics';

function AppRoutes() {
  return (
    <Routes>
      <Route path="/analytics" element={<AnalyticsDashboard />} />
    </Routes>
  );
}
```

### Custom Integration

```tsx
import { 
  UsageMetrics, 
  ProductivityInsights, 
  ContentAnalytics 
} from '@/components/analytics';
import { useAnalytics } from '@/hooks/useAnalytics';

function CustomAnalytics() {
  const { data, isLoading } = useAnalytics({
    timeRange: { start: lastWeek, end: today },
    notebooks: ['important-notebook-id']
  });

  if (isLoading) return <div>Loading...</div>;

  return (
    <div className="space-y-6">
      <UsageMetrics sessions={data.sessions} timeRange={data.timeRange} />
      <ProductivityInsights 
        productivityMetrics={data.productivityMetrics}
        insights={data.insights}
        goals={data.goals}
      />
      <ContentAnalytics 
        contentMetrics={data.contentMetrics}
        searchAnalytics={data.searchAnalytics}
      />
    </div>
  );
}
```

## Configuration

### Analytics Configuration

```typescript
interface AnalyticsConfig {
  trackingEnabled: boolean;
  anonymousMode: boolean;
  dataRetentionDays: number;
  reportingFrequency: 'daily' | 'weekly' | 'monthly';
  goalReminders: boolean;
  insightNotifications: boolean;
  performanceAlerts: boolean;
}
```

### Service Configuration

```tsx
// Configure tracking behavior
analyticsService.updateConfig({
  trackingEnabled: true,
  dataRetentionDays: 365,
  reportingFrequency: 'weekly'
});

// Privacy-focused setup
analyticsService.updateConfig({
  anonymousMode: true,
  trackingEnabled: true,
  dataRetentionDays: 90
});
```

## Theming

All components support dark/light theme switching through the existing ThemeContext:

```tsx
import { useTheme } from '@/contexts/ThemeContext';

// Theme is automatically applied to:
// - Chart colors and backgrounds
// - Card and component styling
// - Text and border colors
// - Hover and active states
```

## Performance

### Optimizations
- **Lazy Loading**: Charts render only when visible
- **Data Virtualization**: Large datasets handled efficiently  
- **Memoization**: React.memo and useMemo prevent unnecessary re-renders
- **Query Caching**: 5-minute stale time with background refetching
- **Debounced Updates**: Activity tracking batched for performance

### Bundle Size
- **Recharts**: ~90KB (charts and visualizations)
- **Analytics Service**: ~15KB (data processing and storage)
- **Components**: ~25KB (UI components and hooks)
- **Total Addition**: ~130KB to bundle size

## Privacy & Security

### Data Handling
- All data stored locally using Tauri backend
- No external analytics services or tracking
- User has full control over data retention
- Export functionality for data portability

### Privacy Features
- Anonymous mode option
- Configurable data retention periods
- Local-only data storage
- Optional tracking disable

## Browser Compatibility

- **Chrome/Edge**: Full support with all features
- **Firefox**: Full support with all features  
- **Safari**: Full support with all features
- **Mobile**: Responsive design optimized for touch

## Dependencies

- **recharts**: Data visualization library
- **@tanstack/react-query**: Data fetching and caching
- **date-fns**: Date manipulation and formatting
- **lucide-react**: Icons (already in project)
- **Existing UI components**: Cards, buttons, tabs, etc.

## Development

### Running Examples

```bash
# Start development server
npm run dev

# View analytics demo
# Navigate to /src/components/demo/AnalyticsDemoIntegration.tsx
```

### Testing

```bash
# Run component tests
npm test src/components/analytics

# Run service tests  
npm test src/services/analyticsService
```

### Building

The analytics system is included in the main build process. No additional build steps required.

## Troubleshooting

### Common Issues

**Data not appearing**
- Verify analytics service is initialized
- Check browser console for errors
- Ensure sufficient data for selected time range

**Charts not rendering**
- Check recharts dependency installation
- Verify chart data format matches expected structure
- Check for JavaScript errors in browser console

**Export not working**
- Verify Tauri backend permissions
- Check available disk space
- Ensure browser allows file downloads

**Performance issues**
- Reduce refresh interval for real-time updates
- Limit time range for large datasets
- Consider using data aggregation for historical analysis

### Debug Mode

```typescript
// Enable debug logging
localStorage.setItem('analytics_debug', 'true');

// Check service status
console.log(analyticsService.getStatus());
```

## Roadmap

### Planned Features
- **PDF Export**: Formatted reports with embedded charts
- **Custom Dashboards**: User-defined dashboard layouts
- **Advanced Insights**: Machine learning-powered recommendations
- **Team Analytics**: Multi-user productivity insights
- **API Integration**: External data source connections
- **Scheduled Reports**: Automated report generation

### Performance Improvements
- **Web Workers**: Background data processing
- **Streaming Data**: Real-time updates without polling
- **Chart Virtualization**: Handle very large datasets
- **Progressive Loading**: Incremental data loading

## Support

For issues and questions:
1. Check this documentation
2. Review component source code
3. Check browser console for errors
4. Verify dependencies are installed correctly