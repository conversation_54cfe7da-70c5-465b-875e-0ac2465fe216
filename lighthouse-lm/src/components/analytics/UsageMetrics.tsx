import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { TimeSeriesChart } from './charts/TimeSeriesChart';
import { BarChart } from './charts/BarChart';
import { HeatmapChart } from './charts/HeatmapChart';
import { SessionMetrics, AnalyticsTimeRange, HeatmapDataPoint, ChartDataPoint } from '@/types/analytics';
import { Clock, Users, TrendingUp, Calendar, Activity, Target } from 'lucide-react';
import { cn } from '@/lib/utils';
import { format, formatDuration, intervalToDuration } from 'date-fns';

interface UsageMetricsProps {
  sessions: SessionMetrics[];
  timeRange: AnalyticsTimeRange;
  isLoading?: boolean;
  className?: string;
}

const formatTime = (minutes: number): string => {
  if (minutes < 60) {
    return `${Math.round(minutes)}m`;
  }
  const hours = Math.floor(minutes / 60);
  const mins = Math.round(minutes % 60);
  return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
};

const formatSessionDuration = (minutes: number): string => {
  const duration = intervalToDuration({ start: 0, end: minutes * 60 * 1000 });
  return formatDuration(duration, { format: ['hours', 'minutes'] }) || '0 minutes';
};

export const UsageMetrics: React.FC<UsageMetricsProps> = ({
  sessions,
  timeRange,
  isLoading = false,
  className
}) => {
  // Calculate aggregated metrics
  const metrics = React.useMemo(() => {
    const totalSessions = sessions.length;
    const totalActiveTime = sessions.reduce((sum, s) => sum + s.activeTime, 0);
    const totalDuration = sessions.reduce((sum, s) => sum + s.duration, 0);
    const uniqueNotebooks = new Set(sessions.map(s => s.notebookId)).size;
    const avgSessionDuration = totalSessions > 0 ? totalDuration / totalSessions : 0;
    const avgActiveTime = totalSessions > 0 ? totalActiveTime / totalSessions : 0;
    const activityRatio = totalDuration > 0 ? (totalActiveTime / totalDuration) * 100 : 0;

    // Calculate daily averages
    const daysDiff = Math.max(1, Math.ceil((timeRange.end.getTime() - timeRange.start.getTime()) / (1000 * 60 * 60 * 24)));
    const avgSessionsPerDay = totalSessions / daysDiff;
    const avgActiveTimePerDay = totalActiveTime / daysDiff;

    return {
      totalSessions,
      totalActiveTime,
      totalDuration,
      uniqueNotebooks,
      avgSessionDuration,
      avgActiveTime,
      activityRatio,
      avgSessionsPerDay,
      avgActiveTimePerDay
    };
  }, [sessions, timeRange]);

  // Generate time series data for session count and duration
  const timeSeriesData = React.useMemo(() => {
    const dataMap = new Map<string, { sessions: number; activeTime: number; duration: number }>();
    
    sessions.forEach(session => {
      const dateKey = format(session.startTime, 'yyyy-MM-dd');
      const existing = dataMap.get(dateKey) || { sessions: 0, activeTime: 0, duration: 0 };
      
      dataMap.set(dateKey, {
        sessions: existing.sessions + 1,
        activeTime: existing.activeTime + session.activeTime,
        duration: existing.duration + session.duration
      });
    });

    return Array.from(dataMap.entries()).map(([date, data]) => ({
      timestamp: new Date(date),
      value: data.sessions,
      activeTime: data.activeTime,
      duration: data.duration,
      metadata: data
    }));
  }, [sessions]);

  // Generate activity time series
  const activityTimeSeriesData: ChartDataPoint[] = timeSeriesData.map(point => ({
    timestamp: point.timestamp,
    value: point.activeTime,
    metadata: point.metadata
  }));

  // Generate focus mode distribution
  const focusModeData = React.useMemo(() => {
    const distribution = sessions.reduce((acc, session) => {
      acc[session.focusMode] = (acc[session.focusMode] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(distribution).map(([mode, count]) => ({
      name: mode.charAt(0).toUpperCase() + mode.slice(1),
      value: count
    }));
  }, [sessions]);

  // Generate notebook usage data
  const notebookUsageData = React.useMemo(() => {
    const usage = sessions.reduce((acc, session) => {
      const notebook = session.notebookId || 'Unknown';
      if (!acc[notebook]) {
        acc[notebook] = { sessions: 0, activeTime: 0 };
      }
      acc[notebook].sessions += 1;
      acc[notebook].activeTime += session.activeTime;
      return acc;
    }, {} as Record<string, { sessions: number; activeTime: number }>);

    return Object.entries(usage)
      .map(([notebook, data]) => ({
        name: notebook.length > 20 ? notebook.substring(0, 20) + '...' : notebook,
        value: data.activeTime,
        sessions: data.sessions,
        metadata: { fullName: notebook, ...data }
      }))
      .sort((a, b) => b.value - a.value);
  }, [sessions]);

  // Generate heatmap data
  const heatmapData = React.useMemo((): HeatmapDataPoint[] => {
    const heatmap: HeatmapDataPoint[] = [];
    
    // Initialize all hours/days with 0
    for (let day = 0; day < 7; day++) {
      for (let hour = 0; hour < 24; hour++) {
        heatmap.push({ day, hour, value: 0, intensity: 'low' });
      }
    }

    // Aggregate session data into heatmap
    sessions.forEach(session => {
      const startTime = new Date(session.startTime);
      const day = startTime.getDay();
      const hour = startTime.getHours();
      
      const index = day * 24 + hour;
      if (index < heatmap.length) {
        heatmap[index].value += session.activeTime;
      }
    });

    // Calculate intensity levels
    const maxValue = Math.max(...heatmap.map(h => h.value));
    return heatmap.map(point => ({
      ...point,
      intensity: point.value === 0 ? 'low' : 
                 point.value > maxValue * 0.7 ? 'high' :
                 point.value > maxValue * 0.3 ? 'medium' : 'low'
    }));
  }, [sessions]);

  if (isLoading) {
    return (
      <div className={cn("space-y-6", className)}>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 w-20 bg-muted animate-pulse rounded" />
                <div className="h-4 w-4 bg-muted animate-pulse rounded" />
              </CardHeader>
              <CardContent>
                <div className="h-8 w-16 bg-muted animate-pulse rounded mb-2" />
                <div className="h-3 w-24 bg-muted animate-pulse rounded" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Key Metrics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sessions</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalSessions}</div>
            <p className="text-xs text-muted-foreground">
              {metrics.avgSessionsPerDay.toFixed(1)} per day average
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatTime(metrics.totalActiveTime)}</div>
            <p className="text-xs text-muted-foreground">
              {formatTime(metrics.avgActiveTimePerDay)} per day average
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Session</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatTime(metrics.avgSessionDuration)}</div>
            <p className="text-xs text-muted-foreground">
              {metrics.activityRatio.toFixed(1)}% active time ratio
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Notebooks Used</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.uniqueNotebooks}</div>
            <p className="text-xs text-muted-foreground">
              Across all sessions
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Usage Trends */}
      <Tabs defaultValue="sessions" className="space-y-4">
        <TabsList>
          <TabsTrigger value="sessions">Session Trends</TabsTrigger>
          <TabsTrigger value="activity">Activity Time</TabsTrigger>
          <TabsTrigger value="heatmap">Activity Heatmap</TabsTrigger>
          <TabsTrigger value="distribution">Usage Distribution</TabsTrigger>
        </TabsList>

        <TabsContent value="sessions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Daily Session Count</CardTitle>
              <CardDescription>
                Number of sessions per day over the selected time range
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TimeSeriesChart
                data={timeSeriesData}
                height={300}
                color="#8884d8"
                showAverage
                formatValue={(value) => `${value} sessions`}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Daily Active Time</CardTitle>
              <CardDescription>
                Total active time spent per day
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TimeSeriesChart
                data={activityTimeSeriesData}
                height={300}
                color="#82ca9d"
                type="area"
                showAverage
                formatValue={(value) => formatTime(value)}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="heatmap" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Activity Heatmap</CardTitle>
              <CardDescription>
                Your activity patterns throughout the week
              </CardDescription>
            </CardHeader>
            <CardContent>
              <HeatmapChart
                data={heatmapData}
                height={250}
                formatValue={(value) => formatTime(value)}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="distribution" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Focus Mode Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Focus Mode Usage</CardTitle>
                <CardDescription>
                  Distribution of focus modes across sessions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {focusModeData.map((mode, index) => {
                    const percentage = metrics.totalSessions > 0 
                      ? (mode.value / metrics.totalSessions) * 100 
                      : 0;
                    
                    return (
                      <div key={mode.name} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">{mode.name}</Badge>
                            <span className="text-sm text-muted-foreground">
                              {mode.value} sessions
                            </span>
                          </div>
                          <span className="text-sm font-medium">
                            {percentage.toFixed(1)}%
                          </span>
                        </div>
                        <Progress value={percentage} className="h-2" />
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Top Notebooks */}
            <Card>
              <CardHeader>
                <CardTitle>Notebook Activity</CardTitle>
                <CardDescription>
                  Most active notebooks by time spent
                </CardDescription>
              </CardHeader>
              <CardContent>
                <BarChart
                  data={notebookUsageData.slice(0, 10)}
                  height={300}
                  color="#ffc658"
                  formatValue={(value) => formatTime(value)}
                  maxBars={10}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Activity Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Activity Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <div className="text-sm font-medium">Most Active Day</div>
              <div className="text-2xl font-bold text-blue-600">
                {(() => {
                  const dayActivity = sessions.reduce((acc, session) => {
                    const dayName = format(session.startTime, 'EEEE');
                    acc[dayName] = (acc[dayName] || 0) + session.activeTime;
                    return acc;
                  }, {} as Record<string, number>);
                  
                  const mostActive = Object.entries(dayActivity).sort(([,a], [,b]) => b - a)[0];
                  return mostActive ? mostActive[0] : 'N/A';
                })()}
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="text-sm font-medium">Peak Hour</div>
              <div className="text-2xl font-bold text-green-600">
                {(() => {
                  const hourActivity = sessions.reduce((acc, session) => {
                    const hour = session.startTime.getHours();
                    acc[hour] = (acc[hour] || 0) + session.activeTime;
                    return acc;
                  }, {} as Record<number, number>);
                  
                  const peakHour = Object.entries(hourActivity).sort(([,a], [,b]) => b - a)[0];
                  return peakHour ? `${peakHour[0]}:00` : 'N/A';
                })()}
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="text-sm font-medium">Longest Session</div>
              <div className="text-2xl font-bold text-purple-600">
                {sessions.length > 0 
                  ? formatTime(Math.max(...sessions.map(s => s.duration)))
                  : 'N/A'
                }
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UsageMetrics;