import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { TimeSeriesChart } from './charts/TimeSeriesChart';
import { BarChart } from './charts/BarChart';
import { PieChart } from './charts/PieChart';
import { ProductivityMetrics, InsightData, GoalMetrics, AnalyticsTimeRange } from '@/types/analytics';
import { 
  Brain, 
  TrendingUp, 
  Target, 
  Zap, 
  AlertCircle, 
  CheckCircle, 
  Clock, 
  Award,
  Lightbulb,
  ArrowUp,
  ArrowDown,
  Minus
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { format, subDays } from 'date-fns';

interface ProductivityInsightsProps {
  productivityMetrics: ProductivityMetrics;
  insights: InsightData[];
  goals: GoalMetrics[];
  timeRange: AnalyticsTimeRange;
  onGoalUpdate?: (goalId: string, currentValue: number) => void;
  onInsightAction?: (insight: InsightData, actionIndex: number) => void;
  className?: string;
}

const getScoreColor = (score: number): string => {
  if (score >= 80) return 'text-green-600';
  if (score >= 60) return 'text-yellow-600';
  return 'text-red-600';
};

const getScoreBadgeVariant = (score: number): "default" | "secondary" | "destructive" | "outline" => {
  if (score >= 80) return 'default';
  if (score >= 60) return 'secondary';
  return 'destructive';
};

const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
  switch (trend) {
    case 'up': return <ArrowUp className="h-4 w-4 text-green-600" />;
    case 'down': return <ArrowDown className="h-4 w-4 text-red-600" />;
    default: return <Minus className="h-4 w-4 text-gray-600" />;
  }
};

const getInsightIcon = (type: string) => {
  switch (type) {
    case 'suggestion': return <Lightbulb className="h-5 w-5 text-yellow-500" />;
    case 'warning': return <AlertCircle className="h-5 w-5 text-orange-500" />;
    case 'achievement': return <Award className="h-5 w-5 text-green-500" />;
    case 'trend': return <TrendingUp className="h-5 w-5 text-blue-500" />;
    default: return <Brain className="h-5 w-5 text-purple-500" />;
  }
};

const formatEfficiency = (efficiency: number): string => {
  return efficiency > 0 ? `${efficiency.toFixed(1)} tasks/hour` : 'No data';
};

const formatMinutes = (minutes: number): string => {
  if (minutes < 60) return `${Math.round(minutes)}m`;
  const hours = Math.floor(minutes / 60);
  const mins = Math.round(minutes % 60);
  return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
};

export const ProductivityInsights: React.FC<ProductivityInsightsProps> = ({
  productivityMetrics,
  insights,
  goals,
  timeRange,
  onGoalUpdate,
  onInsightAction,
  className
}) => {
  // Calculate trend data (mock for now - would need historical data)
  const trendData = React.useMemo(() => {
    // Generate mock trend data for demonstration
    const days = 7;
    const data = [];
    const baseScore = productivityMetrics.productivityScore;
    
    for (let i = days - 1; i >= 0; i--) {
      const date = subDays(new Date(), i);
      const variation = (Math.random() - 0.5) * 20;
      const score = Math.max(0, Math.min(100, baseScore + variation));
      
      data.push({
        timestamp: date,
        value: score,
        label: format(date, 'MMM dd')
      });
    }
    
    return data;
  }, [productivityMetrics.productivityScore]);

  // Focus breakdown data
  const focusBreakdown = React.useMemo(() => [
    { name: 'Deep Work', value: productivityMetrics.deepWorkMinutes },
    { name: 'Shallow Work', value: productivityMetrics.shallowWorkMinutes },
  ], [productivityMetrics]);

  // Active goals
  const activeGoals = goals.filter(goal => goal.status === 'active');
  const completedGoals = goals.filter(goal => goal.status === 'completed');

  // Categorize insights
  const categorizedInsights = React.useMemo(() => {
    const categories = {
      suggestions: insights.filter(i => i.type === 'suggestion'),
      warnings: insights.filter(i => i.type === 'warning'),
      achievements: insights.filter(i => i.type === 'achievement'),
      trends: insights.filter(i => i.type === 'trend')
    };
    return categories;
  }, [insights]);

  return (
    <div className={cn("space-y-6", className)}>
      {/* Productivity Score Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Focus Score</CardTitle>
            <Brain className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={cn("text-2xl font-bold", getScoreColor(productivityMetrics.focusScore))}>
              {Math.round(productivityMetrics.focusScore)}
            </div>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant={getScoreBadgeVariant(productivityMetrics.focusScore)}>
                {productivityMetrics.focusScore >= 80 ? 'Excellent' : 
                 productivityMetrics.focusScore >= 60 ? 'Good' : 'Needs Improvement'}
              </Badge>
              {getTrendIcon('up')}
            </div>
            <Progress 
              value={productivityMetrics.focusScore} 
              className="mt-2 h-2" 
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Productivity Score</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={cn("text-2xl font-bold", getScoreColor(productivityMetrics.productivityScore))}>
              {Math.round(productivityMetrics.productivityScore)}
            </div>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant={getScoreBadgeVariant(productivityMetrics.productivityScore)}>
                {productivityMetrics.productivityScore >= 80 ? 'High' : 
                 productivityMetrics.productivityScore >= 60 ? 'Medium' : 'Low'}
              </Badge>
              {getTrendIcon('stable')}
            </div>
            <Progress 
              value={productivityMetrics.productivityScore} 
              className="mt-2 h-2" 
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Deep Work</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {formatMinutes(productivityMetrics.deepWorkMinutes)}
            </div>
            <p className="text-xs text-muted-foreground">
              {productivityMetrics.contextSwitches} context switches
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Efficiency</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatEfficiency(productivityMetrics.efficiency)}
            </div>
            <p className="text-xs text-muted-foreground">
              {productivityMetrics.completedTasks} tasks completed
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="trends" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="goals">Goals</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
          <TabsTrigger value="breakdown">Breakdown</TabsTrigger>
        </TabsList>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Productivity Trend</CardTitle>
              <CardDescription>
                Your productivity score over the past week
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TimeSeriesChart
                data={trendData}
                height={300}
                color="#8884d8"
                showAverage
                formatValue={(value) => `${Math.round(value)}%`}
              />
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Work Distribution</CardTitle>
                <CardDescription>
                  Deep work vs shallow work time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <PieChart
                  data={focusBreakdown}
                  height={250}
                  formatValue={(value) => formatMinutes(value)}
                  showPercentages
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Focus Metrics</CardTitle>
                <CardDescription>
                  Detailed productivity breakdown
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Distractions</span>
                  <span className="font-medium">{productivityMetrics.distractionCount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Context Switches</span>
                  <span className="font-medium">{productivityMetrics.contextSwitches}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Avg Task Duration</span>
                  <span className="font-medium">{formatMinutes(productivityMetrics.averageTaskDuration)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Tasks Completed</span>
                  <span className="font-medium">{productivityMetrics.completedTasks}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="goals" className="space-y-4">
          <div className="grid gap-4">
            {/* Active Goals */}
            {activeGoals.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Active Goals ({activeGoals.length})
                  </CardTitle>
                  <CardDescription>
                    Track your progress towards productivity goals
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {activeGoals.map((goal) => (
                    <div key={goal.goalId} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">{goal.title}</div>
                          <div className="text-sm text-muted-foreground">
                            {goal.currentValue} of {goal.targetValue} {goal.unit}
                            {goal.deadline && (
                              <span className="ml-2">
                                • Due {format(goal.deadline, 'MMM dd, yyyy')}
                              </span>
                            )}
                          </div>
                        </div>
                        <Badge variant={
                          goal.progress >= 100 ? 'default' :
                          goal.progress >= 75 ? 'secondary' :
                          goal.progress >= 50 ? 'outline' :
                          'destructive'
                        }>
                          {Math.round(goal.progress)}%
                        </Badge>
                      </div>
                      <Progress value={goal.progress} className="h-2" />
                      {onGoalUpdate && (
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => onGoalUpdate(goal.goalId, goal.currentValue + 1)}
                          >
                            Update Progress
                          </Button>
                        </div>
                      )}
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* Completed Goals */}
            {completedGoals.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    Completed Goals ({completedGoals.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-2 sm:grid-cols-2">
                    {completedGoals.slice(0, 6).map((goal) => (
                      <div key={goal.goalId} className="flex items-center gap-2 p-2 rounded-lg bg-green-50 dark:bg-green-900/20">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="text-sm font-medium">{goal.title}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* No Goals State */}
            {activeGoals.length === 0 && completedGoals.length === 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Set Your Goals</CardTitle>
                  <CardDescription>
                    Define productivity goals to track your progress and stay motivated
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center py-6">
                  <Target className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground mb-4">
                    No goals set yet. Create your first productivity goal to get started.
                  </p>
                  <Button>Create Goal</Button>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          {/* Insights by Category */}
          {Object.entries(categorizedInsights).map(([category, categoryInsights]) => {
            if (categoryInsights.length === 0) return null;
            
            return (
              <Card key={category}>
                <CardHeader>
                  <CardTitle className="capitalize">{category}</CardTitle>
                  <CardDescription>
                    {category === 'suggestions' && 'AI-powered recommendations to improve your productivity'}
                    {category === 'warnings' && 'Areas that may need your attention'}
                    {category === 'achievements' && 'Celebrate your accomplishments'}
                    {category === 'trends' && 'Patterns in your productivity data'}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {categoryInsights.map((insight) => (
                    <div key={insight.id} className="flex gap-3 p-3 rounded-lg border">
                      <div className="flex-shrink-0">
                        {getInsightIcon(insight.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-2">
                          <div>
                            <div className="font-medium">{insight.title}</div>
                            <div className="text-sm text-muted-foreground mt-1">
                              {insight.description}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge 
                              variant={
                                insight.impact === 'high' ? 'destructive' :
                                insight.impact === 'medium' ? 'secondary' :
                                'outline'
                              }
                            >
                              {insight.impact}
                            </Badge>
                            <div className="text-xs text-muted-foreground">
                              {insight.confidence}%
                            </div>
                          </div>
                        </div>
                        
                        {insight.actionable && insight.actions && onInsightAction && (
                          <div className="flex gap-2 mt-3">
                            {insight.actions.map((action, index) => (
                              <Button
                                key={index}
                                size="sm"
                                variant="outline"
                                onClick={() => onInsightAction(insight, index)}
                              >
                                {action.label}
                              </Button>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            );
          })}

          {/* No Insights State */}
          {insights.length === 0 && (
            <Card>
              <CardContent className="text-center py-8">
                <Brain className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground">
                  No insights available yet. Use the app more to get personalized productivity recommendations.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="breakdown" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Productivity Factors</CardTitle>
                <CardDescription>
                  What contributes to your productivity score
                </CardDescription>
              </CardHeader>
              <CardContent>
                <BarChart
                  data={[
                    { name: 'Task Completion', value: productivityMetrics.completedTasks },
                    { name: 'Focus Time', value: productivityMetrics.deepWorkMinutes / 10 },
                    { name: 'Low Distractions', value: Math.max(0, 50 - productivityMetrics.distractionCount) },
                    { name: 'Consistency', value: Math.max(0, 30 - productivityMetrics.contextSwitches) },
                  ]}
                  height={250}
                  color="#82ca9d"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Time Allocation</CardTitle>
                <CardDescription>
                  How you spend your active time
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Deep Work</span>
                      <span>{formatMinutes(productivityMetrics.deepWorkMinutes)}</span>
                    </div>
                    <Progress 
                      value={(productivityMetrics.deepWorkMinutes / (productivityMetrics.deepWorkMinutes + productivityMetrics.shallowWorkMinutes)) * 100} 
                      className="h-2"
                    />
                  </div>
                  
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Shallow Work</span>
                      <span>{formatMinutes(productivityMetrics.shallowWorkMinutes)}</span>
                    </div>
                    <Progress 
                      value={(productivityMetrics.shallowWorkMinutes / (productivityMetrics.deepWorkMinutes + productivityMetrics.shallowWorkMinutes)) * 100} 
                      className="h-2"
                    />
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <div className="text-sm font-medium mb-2">Key Metrics</div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Tasks per Hour</span>
                      <span>{productivityMetrics.efficiency.toFixed(1)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Avg Task Time</span>
                      <span>{formatMinutes(productivityMetrics.averageTaskDuration)}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ProductivityInsights;