import React from 'react';
import {
  <PERSON><PERSON><PERSON> as Recharts<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Legend,
} from 'recharts';
import { useTheme } from '@/contexts/ThemeContext';
import { cn } from '@/lib/utils';

interface PieChartData {
  name: string;
  value: number;
  color?: string;
  percentage?: number;
}

interface PieChartProps {
  data: PieChartData[];
  title?: string;
  height?: number;
  showLegend?: boolean;
  showTooltip?: boolean;
  showPercentages?: boolean;
  innerRadius?: number;
  outerRadius?: number;
  colors?: string[];
  className?: string;
  formatValue?: (value: number) => string;
}

const DEFAULT_COLORS = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff7c7c', '#8dd1e1',
  '#d084d0', '#87d068', '#ffb347', '#ff9999', '#87ceeb'
];

const CustomTooltip = ({ active, payload, formatValue }: any) => {
  const { theme } = useTheme();
  
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    
    return (
      <div className={cn(
        "rounded-lg border shadow-lg p-3",
        theme === 'dark' 
          ? "bg-gray-800 border-gray-700 text-white" 
          : "bg-white border-gray-200 text-gray-900"
      )}>
        <p className="font-medium">{data.name}</p>
        <p className="text-sm">
          <span 
            className="inline-block w-3 h-3 rounded-full mr-2"
            style={{ backgroundColor: data.color || payload[0].color }}
          />
          {formatValue ? formatValue(data.value) : data.value}
          {data.percentage && (
            <span className="ml-1 text-muted-foreground">
              ({data.percentage.toFixed(1)}%)
            </span>
          )}
        </p>
      </div>
    );
  }

  return null;
};

const renderCustomizedLabel = ({ 
  cx, cy, midAngle, innerRadius, outerRadius, percent, name 
}: any) => {
  const RADIAN = Math.PI / 180;
  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  if (percent < 0.05) return null; // Don't show labels for slices < 5%

  return (
    <text 
      x={x} 
      y={y} 
      fill="white" 
      textAnchor={x > cx ? 'start' : 'end'} 
      dominantBaseline="central"
      fontSize={12}
      fontWeight="medium"
    >
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  );
};

export const PieChart: React.FC<PieChartProps> = ({
  data,
  title,
  height = 300,
  showLegend = true,
  showTooltip = true,
  showPercentages = false,
  innerRadius = 0,
  outerRadius,
  colors = DEFAULT_COLORS,
  className,
  formatValue
}) => {
  const { theme } = useTheme();

  // Calculate percentages and assign colors
  const chartData = React.useMemo(() => {
    const total = data.reduce((sum, item) => sum + item.value, 0);
    
    return data.map((item, index) => ({
      ...item,
      percentage: total > 0 ? (item.value / total) * 100 : 0,
      color: item.color || colors[index % colors.length]
    }));
  }, [data, colors]);

  // Calculate responsive outer radius
  const responsiveOuterRadius = outerRadius || Math.min(height * 0.35, 120);

  return (
    <div className={cn("w-full", className)}>
      {title && (
        <h3 className="text-lg font-semibold mb-4 text-foreground">{title}</h3>
      )}
      
      <div className="flex flex-col lg:flex-row items-center gap-6">
        <div className="flex-1 min-w-0">
          <ResponsiveContainer width="100%" height={height}>
            <RechartsPieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={showPercentages ? renderCustomizedLabel : false}
                outerRadius={responsiveOuterRadius}
                innerRadius={innerRadius}
                fill="#8884d8"
                dataKey="value"
                animationBegin={0}
                animationDuration={800}
              >
                {chartData.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={entry.color}
                    stroke={theme === 'dark' ? '#374151' : '#e5e7eb'}
                    strokeWidth={1}
                  />
                ))}
              </Pie>
              
              {showTooltip && (
                <Tooltip 
                  content={<CustomTooltip formatValue={formatValue} />}
                />
              )}
              
              {showLegend && (
                <Legend
                  verticalAlign="bottom"
                  height={36}
                  iconType="circle"
                  wrapperStyle={{
                    fontSize: '12px',
                    color: theme === 'dark' ? '#e5e7eb' : '#374151'
                  }}
                />
              )}
            </RechartsPieChart>
          </ResponsiveContainer>
        </div>

        {/* Custom Legend for better mobile experience */}
        {showLegend && (
          <div className="lg:hidden w-full">
            <div className="grid grid-cols-2 gap-2">
              {chartData.map((entry, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div 
                    className="w-3 h-3 rounded-full flex-shrink-0"
                    style={{ backgroundColor: entry.color }}
                  />
                  <span className="text-sm text-foreground truncate">
                    {entry.name}
                  </span>
                  <span className="text-xs text-muted-foreground ml-auto">
                    {entry.percentage?.toFixed(1)}%
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Desktop legend with values */}
        {showLegend && (
          <div className="hidden lg:block lg:w-64">
            <div className="space-y-2">
              {chartData.map((entry, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    <div 
                      className="w-3 h-3 rounded-full flex-shrink-0"
                      style={{ backgroundColor: entry.color }}
                    />
                    <span className="text-sm text-foreground truncate">
                      {entry.name}
                    </span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-foreground">
                      {formatValue ? formatValue(entry.value) : entry.value}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {entry.percentage?.toFixed(1)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Summary stats */}
      <div className="mt-4 pt-4 border-t border-border">
        <div className="flex justify-between text-sm">
          <span className="text-muted-foreground">Total</span>
          <span className="font-medium text-foreground">
            {formatValue 
              ? formatValue(chartData.reduce((sum, item) => sum + item.value, 0))
              : chartData.reduce((sum, item) => sum + item.value, 0)
            }
          </span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-muted-foreground">Categories</span>
          <span className="font-medium text-foreground">
            {chartData.length}
          </span>
        </div>
      </div>
    </div>
  );
};

export default PieChart;