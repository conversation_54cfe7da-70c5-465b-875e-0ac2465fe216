import React from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { HeatmapDataPoint } from '@/types/analytics';
import { cn } from '@/lib/utils';

interface HeatmapChartProps {
  data: HeatmapDataPoint[];
  title?: string;
  height?: number;
  showLabels?: boolean;
  showTooltip?: boolean;
  colorScale?: [string, string]; // [min color, max color]
  formatValue?: (value: number) => string;
  className?: string;
}

const DAYS_OF_WEEK = [
  'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'
];

const HOURS = Array.from({ length: 24 }, (_, i) => i);

const getIntensityColor = (
  intensity: number, 
  maxIntensity: number, 
  colorScale: [string, string],
  theme: 'light' | 'dark'
): string => {
  if (intensity === 0) {
    return theme === 'dark' ? '#1f2937' : '#f3f4f6';
  }
  
  const ratio = intensity / maxIntensity;
  
  // Default color scales based on theme
  const defaultLightScale: [string, string] = ['#dbeafe', '#1d4ed8'];
  const defaultDarkScale: [string, string] = ['#1e3a8a', '#60a5fa'];
  
  const [minColor, maxColor] = colorScale || (theme === 'dark' ? defaultDarkScale : defaultLightScale);
  
  // Simple linear interpolation between min and max color
  const minRgb = hexToRgb(minColor);
  const maxRgb = hexToRgb(maxColor);
  
  if (!minRgb || !maxRgb) return minColor;
  
  const r = Math.round(minRgb.r + (maxRgb.r - minRgb.r) * ratio);
  const g = Math.round(minRgb.g + (maxRgb.g - minRgb.g) * ratio);
  const b = Math.round(minRgb.b + (maxRgb.b - minRgb.b) * ratio);
  
  return `rgb(${r}, ${g}, ${b})`;
};

const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
};

export const HeatmapChart: React.FC<HeatmapChartProps> = ({
  data,
  title,
  height = 300,
  showLabels = false,
  showTooltip = true,
  colorScale,
  formatValue,
  className
}) => {
  const { theme } = useTheme();
  const [hoveredCell, setHoveredCell] = React.useState<{
    day: number;
    hour: number;
    value: number;
  } | null>(null);

  // Create a map for quick data lookup
  const dataMap = React.useMemo(() => {
    const map = new Map();
    data.forEach(point => {
      map.set(`${point.day}-${point.hour}`, point);
    });
    return map;
  }, [data]);

  // Calculate max intensity for color scaling
  const maxIntensity = React.useMemo(() => {
    return Math.max(...data.map(d => d.value), 1);
  }, [data]);

  // Generate grid cells
  const cells = React.useMemo(() => {
    const result = [];
    for (let day = 0; day < 7; day++) {
      for (let hour = 0; hour < 24; hour++) {
        const point = dataMap.get(`${day}-${hour}`);
        const value = point?.value || 0;
        
        result.push({
          day,
          hour,
          value,
          intensity: point?.intensity || 'low',
          color: getIntensityColor(value, maxIntensity, colorScale || ['', ''], theme)
        });
      }
    }
    return result;
  }, [dataMap, maxIntensity, colorScale, theme]);

  const cellSize = Math.max(12, Math.min(20, (height - 120) / 7));

  return (
    <div className={cn("w-full", className)}>
      {title && (
        <h3 className="text-lg font-semibold mb-4 text-foreground">{title}</h3>
      )}

      <div className="relative">
        {/* Day labels */}
        <div className="flex">
          <div className="w-16" /> {/* Space for hour labels */}
          <div className="flex-1 grid grid-cols-24 gap-0.5">
            {HOURS.map(hour => (
              <div
                key={hour}
                className="text-xs text-muted-foreground text-center"
                style={{ minWidth: cellSize }}
              >
                {hour % 4 === 0 ? hour : ''}
              </div>
            ))}
          </div>
        </div>

        {/* Heatmap grid */}
        <div className="flex mt-2">
          {/* Hour labels */}
          <div className="w-16 flex flex-col gap-0.5">
            {DAYS_OF_WEEK.map((day, dayIndex) => (
              <div
                key={dayIndex}
                className="text-xs text-muted-foreground text-right pr-2 flex items-center justify-end"
                style={{ height: cellSize }}
              >
                {day.slice(0, 3)}
              </div>
            ))}
          </div>

          {/* Heat cells */}
          <div className="flex-1 grid grid-cols-24 gap-0.5">
            {cells.map(cell => (
              <div
                key={`${cell.day}-${cell.hour}`}
                className={cn(
                  "rounded-sm border cursor-pointer transition-all duration-200",
                  "hover:scale-110 hover:z-10 relative",
                  theme === 'dark' ? 'border-gray-600' : 'border-gray-200'
                )}
                style={{
                  backgroundColor: cell.color,
                  width: cellSize,
                  height: cellSize,
                  gridColumn: cell.hour + 1,
                  gridRow: cell.day + 1
                }}
                onMouseEnter={() => showTooltip && setHoveredCell(cell)}
                onMouseLeave={() => setHoveredCell(null)}
              >
                {showLabels && cell.value > 0 && cellSize > 16 && (
                  <div className="text-xs text-white font-medium text-center leading-none pt-0.5">
                    {cell.value}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Tooltip */}
        {showTooltip && hoveredCell && (
          <div className={cn(
            "absolute z-20 rounded-lg border shadow-lg p-3 pointer-events-none",
            "transform -translate-x-1/2 -translate-y-full -mt-2",
            theme === 'dark' 
              ? "bg-gray-800 border-gray-700 text-white" 
              : "bg-white border-gray-200 text-gray-900"
          )}
          style={{
            left: `${((hoveredCell.hour + 1) / 24) * 100}%`,
            top: `${((hoveredCell.day + 1) / 7) * 100}%`
          }}>
            <div className="font-medium">
              {DAYS_OF_WEEK[hoveredCell.day]} at {hoveredCell.hour}:00
            </div>
            <div className="text-sm">
              Activity: {formatValue ? formatValue(hoveredCell.value) : hoveredCell.value}
            </div>
          </div>
        )}
      </div>

      {/* Color scale legend */}
      <div className="mt-4 flex items-center gap-4">
        <span className="text-sm text-muted-foreground">Less</span>
        <div className="flex gap-0.5">
          {Array.from({ length: 5 }, (_, i) => (
            <div
              key={i}
              className="w-3 h-3 rounded-sm"
              style={{
                backgroundColor: getIntensityColor(
                  (i / 4) * maxIntensity, 
                  maxIntensity, 
                  colorScale || ['', ''], 
                  theme
                )
              }}
            />
          ))}
        </div>
        <span className="text-sm text-muted-foreground">More</span>
      </div>

      {/* Statistics */}
      <div className="mt-4 grid grid-cols-2 sm:grid-cols-4 gap-4 text-sm">
        <div>
          <div className="font-medium text-foreground">
            {Math.round(data.reduce((sum, d) => sum + d.value, 0))}
          </div>
          <div className="text-muted-foreground">Total Activity</div>
        </div>
        
        <div>
          <div className="font-medium text-foreground">
            {data.filter(d => d.value > 0).length}
          </div>
          <div className="text-muted-foreground">Active Hours</div>
        </div>
        
        <div>
          <div className="font-medium text-foreground">
            {Math.round(maxIntensity)}
          </div>
          <div className="text-muted-foreground">Peak Activity</div>
        </div>
        
        <div>
          <div className="font-medium text-foreground">
            {data.length > 0 ? Math.round(data.reduce((sum, d) => sum + d.value, 0) / data.length) : 0}
          </div>
          <div className="text-muted-foreground">Avg per Hour</div>
        </div>
      </div>

      {/* Peak hours insight */}
      {data.length > 0 && (
        <div className="mt-4 p-3 rounded-lg bg-muted/50">
          <div className="text-sm font-medium text-foreground mb-2">
            Most Active Times
          </div>
          <div className="text-sm text-muted-foreground">
            {(() => {
              const sorted = [...data].sort((a, b) => b.value - a.value);
              const top3 = sorted.slice(0, 3).filter(d => d.value > 0);
              
              if (top3.length === 0) return "No activity recorded";
              
              return top3.map(d => 
                `${DAYS_OF_WEEK[d.day]} ${d.hour}:00`
              ).join(', ');
            })()}
          </div>
        </div>
      )}
    </div>
  );
};

export default HeatmapChart;