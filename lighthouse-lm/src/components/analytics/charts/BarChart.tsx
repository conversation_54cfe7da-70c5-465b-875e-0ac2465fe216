import React from 'react';
import {
  <PERSON><PERSON>hart as RechartsBar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine,
} from 'recharts';
import { useTheme } from '@/contexts/ThemeContext';
import { cn } from '@/lib/utils';

interface BarChartData {
  name: string;
  value: number;
  color?: string;
  category?: string;
  metadata?: Record<string, any>;
}

interface BarChartProps {
  data: BarChartData[];
  title?: string;
  height?: number;
  color?: string;
  showGrid?: boolean;
  showTooltip?: boolean;
  showAverage?: boolean;
  horizontal?: boolean;
  maxBars?: number;
  sortBy?: 'value' | 'name' | 'none';
  sortOrder?: 'asc' | 'desc';
  formatValue?: (value: number) => string;
  formatLabel?: (label: string) => string;
  className?: string;
}

const CustomTooltip = ({ active, payload, label, formatValue }: any) => {
  const { theme } = useTheme();
  
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    
    return (
      <div className={cn(
        "rounded-lg border shadow-lg p-3",
        theme === 'dark' 
          ? "bg-gray-800 border-gray-700 text-white" 
          : "bg-white border-gray-200 text-gray-900"
      )}>
        <p className="font-medium">{data.name}</p>
        <p className="text-sm">
          <span 
            className="inline-block w-3 h-3 rounded-full mr-2"
            style={{ backgroundColor: payload[0].color }}
          />
          {formatValue ? formatValue(payload[0].value) : payload[0].value}
        </p>
        {data.category && (
          <p className="text-xs text-muted-foreground mt-1">
            Category: {data.category}
          </p>
        )}
        {data.metadata && (
          <div className="mt-2 text-xs text-muted-foreground">
            {Object.entries(data.metadata).map(([key, value]) => (
              <div key={key}>
                {key}: {String(value)}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  }

  return null;
};

export const BarChart: React.FC<BarChartProps> = ({
  data,
  title,
  height = 300,
  color = '#8884d8',
  showGrid = true,
  showTooltip = true,
  showAverage = false,
  horizontal = false,
  maxBars = 20,
  sortBy = 'none',
  sortOrder = 'desc',
  formatValue,
  formatLabel,
  className
}) => {
  const { theme } = useTheme();

  // Process and sort data
  const processedData = React.useMemo(() => {
    let processed = [...data];

    // Sort data if requested
    if (sortBy !== 'none') {
      processed.sort((a, b) => {
        let aVal = sortBy === 'value' ? a.value : a.name;
        let bVal = sortBy === 'value' ? b.value : b.name;
        
        if (typeof aVal === 'string' && typeof bVal === 'string') {
          aVal = aVal.toLowerCase();
          bVal = bVal.toLowerCase();
        }
        
        const comparison = aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
        return sortOrder === 'asc' ? comparison : -comparison;
      });
    }

    // Limit number of bars
    if (maxBars && processed.length > maxBars) {
      processed = processed.slice(0, maxBars);
    }

    return processed.map(item => ({
      ...item,
      displayName: formatLabel ? formatLabel(item.name) : item.name,
      color: item.color || color
    }));
  }, [data, sortBy, sortOrder, maxBars, formatLabel, color]);

  // Calculate average if needed
  const average = React.useMemo(() => {
    if (!showAverage || processedData.length === 0) return null;
    return processedData.reduce((sum, item) => sum + item.value, 0) / processedData.length;
  }, [processedData, showAverage]);

  const gridColor = theme === 'dark' ? '#374151' : '#e5e7eb';
  const axisColor = theme === 'dark' ? '#9ca3af' : '#6b7280';

  // Custom bar colors based on data
  const CustomBar = (props: any) => {
    const { payload } = props;
    return <Bar {...props} fill={payload?.color || color} />;
  };

  return (
    <div className={cn("w-full", className)}>
      {title && (
        <h3 className="text-lg font-semibold mb-4 text-foreground">{title}</h3>
      )}

      <ResponsiveContainer width="100%" height={height}>
        <RechartsBarChart
          data={processedData}
          layout={horizontal ? 'horizontal' : 'vertical'}
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        >
          {showGrid && (
            <CartesianGrid 
              strokeDasharray="3 3" 
              stroke={gridColor}
              opacity={0.3}
            />
          )}
          
          {horizontal ? (
            <>
              <XAxis 
                type="number"
                stroke={axisColor}
                fontSize={12}
                tickFormatter={formatValue}
              />
              <YAxis 
                type="category"
                dataKey="displayName"
                stroke={axisColor}
                fontSize={12}
                width={100}
              />
            </>
          ) : (
            <>
              <XAxis 
                dataKey="displayName"
                stroke={axisColor}
                fontSize={12}
                angle={processedData.length > 10 ? -45 : 0}
                textAnchor={processedData.length > 10 ? 'end' : 'middle'}
                height={processedData.length > 10 ? 80 : 60}
              />
              <YAxis 
                stroke={axisColor}
                fontSize={12}
                tickFormatter={formatValue}
              />
            </>
          )}

          {showTooltip && (
            <Tooltip 
              content={<CustomTooltip formatValue={formatValue} />}
              cursor={{ fill: 'rgba(136, 132, 216, 0.1)' }}
            />
          )}

          {showAverage && average !== null && (
            <ReferenceLine 
              {...(horizontal ? { x: average } : { y: average })}
              stroke={color} 
              strokeDasharray="5 5" 
              opacity={0.6}
            />
          )}

          <Bar
            dataKey="value"
            fill={color}
            radius={horizontal ? [0, 4, 4, 0] : [4, 4, 0, 0]}
            animationBegin={0}
            animationDuration={800}
          />
        </RechartsBarChart>
      </ResponsiveContainer>

      {/* Summary info */}
      <div className="mt-4 flex flex-wrap justify-between text-sm text-muted-foreground">
        <div>
          Showing {processedData.length} of {data.length} items
          {maxBars && data.length > maxBars && (
            <span className="ml-1">(top {maxBars})</span>
          )}
        </div>
        
        {showAverage && average !== null && (
          <div>
            Average: {formatValue ? formatValue(average) : average.toFixed(1)}
          </div>
        )}
      </div>

      {/* Top performers callout */}
      {processedData.length > 3 && (
        <div className="mt-4 p-3 rounded-lg bg-muted/50">
          <div className="text-sm font-medium text-foreground mb-2">
            Top Performers
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
            {processedData.slice(0, 3).map((item, index) => (
              <div key={item.name} className="flex items-center gap-2">
                <div className="flex items-center justify-center w-6 h-6 rounded-full bg-primary/10 text-primary text-xs font-bold">
                  {index + 1}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-foreground truncate">
                    {item.displayName}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {formatValue ? formatValue(item.value) : item.value}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default BarChart;