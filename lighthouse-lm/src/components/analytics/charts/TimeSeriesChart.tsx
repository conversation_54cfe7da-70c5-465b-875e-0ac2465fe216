import React from 'react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine,
} from 'recharts';
import { format, isToday, isYesterday, subDays } from 'date-fns';
import { useTheme } from '@/contexts/ThemeContext';
import { ChartDataPoint } from '@/types/analytics';
import { cn } from '@/lib/utils';

interface TimeSeriesChartProps {
  data: ChartDataPoint[];
  title?: string;
  color?: string;
  type?: 'line' | 'area';
  height?: number;
  showGrid?: boolean;
  showTooltip?: boolean;
  showAverage?: boolean;
  formatValue?: (value: number) => string;
  formatDate?: (date: Date) => string;
  className?: string;
}

const CustomTooltip = ({ 
  active, 
  payload, 
  label, 
  formatValue, 
  formatDate 
}: any) => {
  const { theme } = useTheme();
  
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    const date = new Date(label);
    
    let dateLabel = formatDate ? formatDate(date) : label;
    if (!formatDate) {
      if (isToday(date)) {
        dateLabel = 'Today';
      } else if (isYesterday(date)) {
        dateLabel = 'Yesterday';
      } else {
        dateLabel = format(date, 'MMM dd, yyyy');
      }
    }

    return (
      <div className={cn(
        "rounded-lg border shadow-lg p-3",
        theme === 'dark' 
          ? "bg-gray-800 border-gray-700 text-white" 
          : "bg-white border-gray-200 text-gray-900"
      )}>
        <p className="font-medium">{dateLabel}</p>
        <p className="text-sm">
          <span className="inline-block w-3 h-3 rounded-full mr-2"
                style={{ backgroundColor: payload[0].color }}></span>
          {formatValue ? formatValue(payload[0].value) : payload[0].value}
        </p>
        {data.metadata && (
          <div className="mt-2 text-xs text-muted-foreground">
            {Object.entries(data.metadata).map(([key, value]) => (
              <div key={key}>
                {key}: {String(value)}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  }

  return null;
};

export const TimeSeriesChart: React.FC<TimeSeriesChartProps> = ({
  data,
  title,
  color = '#8884d8',
  type = 'line',
  height = 300,
  showGrid = true,
  showTooltip = true,
  showAverage = false,
  formatValue,
  formatDate,
  className
}) => {
  const { theme } = useTheme();
  
  // Calculate average if needed
  const average = React.useMemo(() => {
    if (!showAverage || data.length === 0) return null;
    return data.reduce((sum, point) => sum + point.value, 0) / data.length;
  }, [data, showAverage]);

  // Format data for recharts
  const chartData = React.useMemo(() => {
    return data.map(point => ({
      timestamp: point.timestamp.getTime(),
      value: point.value,
      label: point.label,
      category: point.category,
      metadata: point.metadata,
      formattedDate: format(point.timestamp, 'MMM dd')
    }));
  }, [data]);

  const gridColor = theme === 'dark' ? '#374151' : '#e5e7eb';
  const axisColor = theme === 'dark' ? '#9ca3af' : '#6b7280';

  const Chart = type === 'area' ? AreaChart : LineChart;
  const ChartElement = type === 'area' ? Area : Line;

  return (
    <div className={cn("w-full", className)}>
      {title && (
        <h3 className="text-lg font-semibold mb-4 text-foreground">{title}</h3>
      )}
      <ResponsiveContainer width="100%" height={height}>
        <Chart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          {showGrid && (
            <CartesianGrid 
              strokeDasharray="3 3" 
              stroke={gridColor}
              opacity={0.3}
            />
          )}
          <XAxis 
            dataKey="timestamp"
            type="number"
            scale="time"
            domain={['dataMin', 'dataMax']}
            tickFormatter={(timestamp) => format(new Date(timestamp), 'MMM dd')}
            stroke={axisColor}
            fontSize={12}
          />
          <YAxis 
            stroke={axisColor}
            fontSize={12}
            tickFormatter={formatValue}
          />
          {showTooltip && (
            <Tooltip 
              content={<CustomTooltip formatValue={formatValue} formatDate={formatDate} />}
              cursor={{ strokeDasharray: '3 3' }}
            />
          )}
          
          {showAverage && average !== null && (
            <ReferenceLine 
              y={average} 
              stroke={color} 
              strokeDasharray="5 5" 
              opacity={0.6}
            />
          )}
          
          <ChartElement
            type="monotone"
            dataKey="value"
            stroke={color}
            strokeWidth={type === 'line' ? 2 : 1}
            fill={type === 'area' ? color : undefined}
            fillOpacity={type === 'area' ? 0.3 : undefined}
            dot={type === 'line' ? { r: 4, strokeWidth: 2 } : false}
            activeDot={type === 'line' ? { r: 6, strokeWidth: 2 } : undefined}
          />
        </Chart>
      </ResponsiveContainer>
      
      {showAverage && average !== null && (
        <div className="mt-2 text-sm text-muted-foreground">
          Average: {formatValue ? formatValue(average) : average.toFixed(1)}
        </div>
      )}
    </div>
  );
};

export default TimeSeriesChart;