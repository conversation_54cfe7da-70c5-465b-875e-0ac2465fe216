import React, { useState, useEffect, useRef } from 'react';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Bot,
  Send,
  Sparkles,
  FileText,
  Lightbulb,
  TrendingUp,
  Search,
  Tag,
  Link,
  Copy,
  ThumbsUp,
  ThumbsDown,
  RefreshCw,
  Settings,
  Zap,
  Brain,
  Target,
  BookOpen,
  MessageSquare,
  Clock,
  Star,
  AlertCircle,
  CheckCircle,
  Loader2,
  Eye,
} from 'lucide-react';
import { Source } from './types';

interface AIAssistantPanelProps {
  sources: Source[];
  selectedSources?: Source[];
  onSourceSelect?: (source: Source) => void;
  onApplyRecommendation?: (recommendation: AIRecommendation) => void;
}

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  sources?: Source[];
  recommendations?: AIRecommendation[];
}

interface AIRecommendation {
  id: string;
  type: 'tag' | 'relation' | 'organization' | 'insight';
  title: string;
  description: string;
  confidence: number;
  sources: Source[];
  action?: {
    type: 'add_tag' | 'create_relation' | 'move_to_folder' | 'merge_sources';
    params: any;
  };
}

interface AIInsight {
  id: string;
  type: 'summary' | 'pattern' | 'suggestion' | 'warning';
  title: string;
  content: string;
  relevance: number;
  sources: Source[];
}

const AIAssistantPanel: React.FC<AIAssistantPanelProps> = ({
  sources,
  selectedSources = [],
  onSourceSelect,
  onApplyRecommendation,
}) => {
  const [activeTab, setActiveTab] = useState('chat');
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [aiMode, setAiMode] = useState<'general' | 'analysis' | 'organization'>('general');
  const [recommendations, setRecommendations] = useState<AIRecommendation[]>([]);
  const [insights, setInsights] = useState<AIInsight[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Mock AI responses and recommendations
  const mockRecommendations: AIRecommendation[] = [
    {
      id: '1',
      type: 'tag',
      title: 'Add Research Tags',
      description: 'I noticed several sources about machine learning. Consider adding tags like "ML", "AI", "Research" for better organization.',
      confidence: 0.85,
      sources: sources.filter(s => s.title.toLowerCase().includes('machine') || s.title.toLowerCase().includes('ai')),
      action: {
        type: 'add_tag',
        params: { tags: ['ML', 'AI', 'Research'] }
      }
    },
    {
      id: '2',
      type: 'relation',
      title: 'Create Topic Relations',
      description: 'These sources seem related to the same project. Would you like me to create relationships between them?',
      confidence: 0.78,
      sources: sources.slice(0, 3),
      action: {
        type: 'create_relation',
        params: { relationType: 'related_to' }
      }
    },
    {
      id: '3',
      type: 'organization',
      title: 'Organize by Date',
      description: 'You have many sources from the same time period. Consider organizing them by project timeline.',
      confidence: 0.72,
      sources: sources.filter(s => new Date(s.created_at).getMonth() === new Date().getMonth()),
      action: {
        type: 'move_to_folder',
        params: { folder: 'Current Month' }
      }
    }
  ];

  const mockInsights: AIInsight[] = [
    {
      id: '1',
      type: 'summary',
      title: 'Collection Overview',
      content: `You have ${sources.length} sources with a focus on technical documentation and research papers. Most sources are recent (last 30 days) and well-tagged.`,
      relevance: 0.9,
      sources: sources
    },
    {
      id: '2',
      type: 'pattern',
      title: 'Usage Pattern Detected',
      content: 'You tend to collect sources in batches, typically 5-10 at a time, suggesting project-based research workflows.',
      relevance: 0.75,
      sources: sources.slice(0, 10)
    },
    {
      id: '3',
      type: 'suggestion',
      title: 'Optimization Opportunity',
      content: 'Consider creating templates for your most common source types to speed up future additions.',
      relevance: 0.68,
      sources: []
    },
    {
      id: '4',
      type: 'warning',
      title: 'Duplicate Content Alert',
      content: 'Found 2 sources that might be duplicates based on content similarity. Review recommended.',
      relevance: 0.82,
      sources: sources.slice(0, 2)
    }
  ];

  useEffect(() => {
    setRecommendations(mockRecommendations);
    setInsights(mockInsights);
    
    // Add welcome message
    if (chatMessages.length === 0) {
      setChatMessages([{
        id: '1',
        type: 'assistant',
        content: `Hello! I'm your AI assistant for source management. I can help you analyze your ${sources.length} sources, suggest improvements, and answer questions about your collection. What would you like to explore?`,
        timestamp: new Date(),
        recommendations: mockRecommendations.slice(0, 2)
      }]);
    }
  }, [sources]);

  useEffect(() => {
    scrollToBottom();
  }, [chatMessages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    setChatMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    // Simulate AI response
    setTimeout(() => {
      const responses = {
        general: [
          'I can help you analyze your sources and provide insights. What specific aspect would you like to explore?',
          'Based on your collection, I notice some interesting patterns. Would you like me to elaborate?',
          'I can suggest ways to better organize your sources. What are your main goals?'
        ],
        analysis: [
          `I've analyzed your ${sources.length} sources. Here are the key findings: Most sources are technical documents with good metadata coverage.`,
          'Your collection shows a strong focus on recent research. I can help identify knowledge gaps or suggest related sources.',
          'The content analysis reveals several topic clusters. Would you like me to create automatic groupings?'
        ],
        organization: [
          'I can suggest several organizational improvements based on your usage patterns.',
          'Your tagging strategy is good, but I notice some opportunities for better categorization.',
          'Consider creating project-based folders for better source management.'
        ]
      };

      const responseContent = responses[aiMode][Math.floor(Math.random() * responses[aiMode].length)];
      
      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: responseContent,
        timestamp: new Date(),
        sources: selectedSources.length > 0 ? selectedSources : sources.slice(0, 3),
        recommendations: Math.random() > 0.5 ? mockRecommendations.slice(0, 2) : undefined
      };

      setChatMessages(prev => [...prev, assistantMessage]);
      setIsLoading(false);
    }, 1000 + Math.random() * 2000);
  };

  const handleApplyRecommendation = (recommendation: AIRecommendation) => {
    onApplyRecommendation?.(recommendation);
    // Remove applied recommendation
    setRecommendations(prev => prev.filter(r => r.id !== recommendation.id));
  };

  const getInsightIcon = (type: AIInsight['type']) => {
    switch (type) {
      case 'summary': return <BookOpen className="h-4 w-4" />;
      case 'pattern': return <TrendingUp className="h-4 w-4" />;
      case 'suggestion': return <Lightbulb className="h-4 w-4" />;
      case 'warning': return <AlertCircle className="h-4 w-4" />;
      default: return <Brain className="h-4 w-4" />;
    }
  };

  const getInsightColor = (type: AIInsight['type']) => {
    switch (type) {
      case 'summary': return 'text-blue-600';
      case 'pattern': return 'text-green-600';
      case 'suggestion': return 'text-yellow-600';
      case 'warning': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getRecommendationIcon = (type: AIRecommendation['type']) => {
    switch (type) {
      case 'tag': return <Tag className="h-4 w-4" />;
      case 'relation': return <Link className="h-4 w-4" />;
      case 'organization': return <Target className="h-4 w-4" />;
      case 'insight': return <Lightbulb className="h-4 w-4" />;
      default: return <Sparkles className="h-4 w-4" />;
    }
  };

  const renderChatMessage = (message: ChatMessage) => {
    return (
      <div key={message.id} className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
        {message.type === 'assistant' && (
          <div className="flex-shrink-0 w-8 h-8 bg-primary rounded-full flex items-center justify-center">
            <Bot className="h-4 w-4 text-primary-foreground" />
          </div>
        )}
        
        <div className={`max-w-[80%] ${message.type === 'user' ? 'order-first' : ''}`}>
          <div className={`p-3 rounded-lg ${
            message.type === 'user' 
              ? 'bg-primary text-primary-foreground ml-auto' 
              : 'bg-muted'
          }`}>
            <p className="text-sm">{message.content}</p>
            
            {message.sources && message.sources.length > 0 && (
              <div className="mt-2 pt-2 border-t border-border/50">
                <p className="text-xs opacity-75 mb-1">Related sources:</p>
                <div className="flex flex-wrap gap-1">
                  {message.sources.slice(0, 3).map(source => (
                    <Badge 
                      key={source.id} 
                      variant="secondary" 
                      className="text-xs cursor-pointer hover:bg-secondary/80"
                      onClick={() => onSourceSelect?.(source)}
                    >
                      {source.title.length > 20 ? source.title.substring(0, 20) + '...' : source.title}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            
            {message.recommendations && message.recommendations.length > 0 && (
              <div className="mt-2 pt-2 border-t border-border/50">
                <p className="text-xs opacity-75 mb-2">Recommendations:</p>
                <div className="space-y-2">
                  {message.recommendations.map(rec => (
                    <div key={rec.id} className="bg-background/50 p-2 rounded text-xs">
                      <div className="flex items-center gap-1 mb-1">
                        {getRecommendationIcon(rec.type)}
                        <span className="font-medium">{rec.title}</span>
                        <Badge variant="outline" className="text-xs">
                          {Math.round(rec.confidence * 100)}%
                        </Badge>
                      </div>
                      <p className="text-xs opacity-75 mb-2">{rec.description}</p>
                      <Button 
                        size="sm" 
                        variant="outline" 
                        className="h-6 text-xs"
                        onClick={() => handleApplyRecommendation(rec)}
                      >
                        Apply
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
          
          <div className="flex items-center gap-2 mt-1">
            <span className="text-xs text-muted-foreground">
              {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </span>
            {message.type === 'assistant' && (
              <div className="flex gap-1">
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <ThumbsUp className="h-3 w-3" />
                </Button>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <ThumbsDown className="h-3 w-3" />
                </Button>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            )}
          </div>
        </div>
        
        {message.type === 'user' && (
          <div className="flex-shrink-0 w-8 h-8 bg-muted rounded-full flex items-center justify-center">
            <span className="text-sm font-medium">U</span>
          </div>
        )}
      </div>
    );
  };

  const renderRecommendations = () => {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="font-medium">AI Recommendations</h4>
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
        
        {recommendations.length === 0 ? (
          <Card>
            <CardContent className="p-6 text-center">
              <Sparkles className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">
                No recommendations available. Try adding more sources or ask me for suggestions.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-3">
            {recommendations.map(recommendation => (
              <Card key={recommendation.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                      {getRecommendationIcon(recommendation.type)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h5 className="font-medium text-sm">{recommendation.title}</h5>
                        <Badge variant="outline" className="text-xs">
                          {Math.round(recommendation.confidence * 100)}% confidence
                        </Badge>
                      </div>
                      
                      <p className="text-sm text-muted-foreground mb-2">
                        {recommendation.description}
                      </p>
                      
                      <div className="flex items-center gap-2 mb-3">
                        <span className="text-xs text-muted-foreground">
                          Affects {recommendation.sources.length} source{recommendation.sources.length !== 1 ? 's' : ''}
                        </span>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button 
                          size="sm" 
                          onClick={() => handleApplyRecommendation(recommendation)}
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Apply
                        </Button>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          Preview
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    );
  };

  const renderInsights = () => {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="font-medium">AI Insights</h4>
          <Button variant="outline" size="sm">
            <Brain className="h-4 w-4 mr-2" />
            Analyze
          </Button>
        </div>
        
        <div className="grid gap-4">
          {insights.map(insight => (
            <Card key={insight.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${getInsightColor(insight.type)} bg-current/10`}>
                    <div className={getInsightColor(insight.type)}>
                      {getInsightIcon(insight.type)}
                    </div>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h5 className="font-medium text-sm">{insight.title}</h5>
                      <div className="flex items-center gap-1">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <Star 
                            key={i} 
                            className={`h-3 w-3 ${
                              i < Math.round(insight.relevance * 5) 
                                ? 'text-yellow-400 fill-current' 
                                : 'text-gray-300'
                            }`} 
                          />
                        ))}
                      </div>
                    </div>
                    
                    <p className="text-sm text-muted-foreground mb-2">
                      {insight.content}
                    </p>
                    
                    {insight.sources.length > 0 && (
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-muted-foreground">
                          Based on {insight.sources.length} source{insight.sources.length !== 1 ? 's' : ''}
                        </span>
                        <Button variant="ghost" size="sm" className="h-6 text-xs">
                          View Sources
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <Bot className="h-5 w-5" />
          <h3 className="font-semibold">AI Assistant</h3>
          <Badge variant="secondary" className="text-xs">
            {sources.length} sources
          </Badge>
        </div>
        
        <div className="flex items-center gap-2">
          <Select value={aiMode} onValueChange={(value: any) => setAiMode(value)}>
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="general">General Help</SelectItem>
              <SelectItem value="analysis">Analysis Mode</SelectItem>
              <SelectItem value="organization">Organization</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <TabsList className="mx-4 mt-4">
            <TabsTrigger value="chat" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Chat
            </TabsTrigger>
            <TabsTrigger value="recommendations" className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Recommendations
              {recommendations.length > 0 && (
                <Badge variant="secondary" className="text-xs ml-1">
                  {recommendations.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="insights" className="flex items-center gap-2">
              <Brain className="h-4 w-4" />
              Insights
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="chat" className="flex-1 flex flex-col mt-4 mx-4">
            {/* Chat Messages */}
            <ScrollArea className="flex-1 pr-4">
              <div className="space-y-4 pb-4">
                {chatMessages.map(renderChatMessage)}
                {isLoading && (
                  <div className="flex gap-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                      <Bot className="h-4 w-4 text-primary-foreground" />
                    </div>
                    <div className="bg-muted p-3 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="text-sm text-muted-foreground">AI is thinking...</span>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>
            
            {/* Chat Input */}
            <div className="border-t pt-4">
              <div className="flex gap-2">
                <Input
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  placeholder="Ask me anything about your sources..."
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  disabled={isLoading}
                />
                <Button 
                  onClick={handleSendMessage} 
                  disabled={!inputMessage.trim() || isLoading}
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="recommendations" className="flex-1 mt-4 mx-4">
            <ScrollArea className="h-full">
              {renderRecommendations()}
            </ScrollArea>
          </TabsContent>
          
          <TabsContent value="insights" className="flex-1 mt-4 mx-4">
            <ScrollArea className="h-full">
              {renderInsights()}
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AIAssistantPanel;