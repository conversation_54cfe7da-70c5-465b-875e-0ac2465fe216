import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Slider } from '@/components/ui/slider';
import {
  Settings,
  Save,
  RotateCcw,
  Bell,
  Shield,
  Database,
  Palette,
  Globe,
  Users,
  FileText,
  Search,
  Download,
  Upload,
  Trash2,
  AlertTriangle,
  CheckCircle,
  Info,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  Clock,
  Calendar,
  Tag,
  Filter,
  SortAsc,
  Grid,
  List,
  Monitor,
  Smartphone,
  Tablet,
  Moon,
  Sun,
  Volume2,
  VolumeX,
  Wifi,
  WifiOff,
  HardDrive,
  Cloud,
  Key,
  UserCheck,
  Mail,
  MessageSquare,
  Zap,
  Activity,
  BarChart3,
  PieChart,
  TrendingUp,
  Target,
  Layers,
  Archive,
  RefreshCw,
  ExternalLink,
} from 'lucide-react';
import { Source } from './types';

interface SourceSettingsPanelProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  source: Source;
  onSaveSettings?: (settings: SourceSettings) => Promise<void>;
  onResetSettings?: () => Promise<void>;
  onExportSettings?: () => Promise<void>;
  onImportSettings?: (settings: SourceSettings) => Promise<void>;
}

interface SourceSettings {
  general: GeneralSettings;
  display: DisplaySettings;
  privacy: PrivacySettings;
  notifications: NotificationSettings;
  storage: StorageSettings;
  advanced: AdvancedSettings;
  integrations: IntegrationSettings;
  accessibility: AccessibilitySettings;
}

interface GeneralSettings {
  title: string;
  tags: string[];
  category: string;
  language: string;
  timezone: string;
  autoSave: boolean;
  autoBackup: boolean;
  versionControl: boolean;
  collaborationEnabled: boolean;
}

interface DisplaySettings {
  theme: 'light' | 'dark' | 'auto';
  layout: 'grid' | 'list' | 'compact';
  density: 'comfortable' | 'compact' | 'spacious';
  showThumbnails: boolean;
  showMetadata: boolean;
  showTimestamps: boolean;
  showFileSize: boolean;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  itemsPerPage: number;
  enableAnimations: boolean;
  fontSize: number;
  lineHeight: number;
}

interface PrivacySettings {
  visibility: 'public' | 'private' | 'restricted';
  allowIndexing: boolean;
  allowDownload: boolean;
  allowSharing: boolean;
  allowComments: boolean;
  allowRating: boolean;
  trackAnalytics: boolean;
  showInSearch: boolean;
  requireAuthentication: boolean;
  allowedDomains: string[];
  blockedUsers: string[];
  accessLevel: 'read' | 'write' | 'admin';
}

interface NotificationSettings {
  emailNotifications: boolean;
  pushNotifications: boolean;
  inAppNotifications: boolean;
  notifyOnComment: boolean;
  notifyOnShare: boolean;
  notifyOnEdit: boolean;
  notifyOnDownload: boolean;
  notifyOnMention: boolean;
  digestFrequency: 'immediate' | 'hourly' | 'daily' | 'weekly' | 'never';
  quietHours: {
    enabled: boolean;
    start: string;
    end: string;
  };
}

interface StorageSettings {
  storageLocation: 'local' | 'cloud' | 'hybrid';
  compressionEnabled: boolean;
  compressionLevel: number;
  encryptionEnabled: boolean;
  backupFrequency: 'realtime' | 'hourly' | 'daily' | 'weekly' | 'monthly';
  retentionPeriod: number;
  maxFileSize: number;
  allowedFileTypes: string[];
  autoCleanup: boolean;
  cleanupThreshold: number;
}

interface AdvancedSettings {
  enableDebugMode: boolean;
  enableExperimentalFeatures: boolean;
  apiRateLimit: number;
  cacheSize: number;
  cacheDuration: number;
  enableLogging: boolean;
  logLevel: 'error' | 'warn' | 'info' | 'debug';
  enableProfiling: boolean;
  customCSS: string;
  customJS: string;
  webhookUrl: string;
  enableWebhooks: boolean;
}

interface IntegrationSettings {
  enabledIntegrations: string[];
  apiKeys: Record<string, string>;
  syncFrequency: number;
  autoSync: boolean;
  conflictResolution: 'manual' | 'auto-local' | 'auto-remote';
  enabledServices: string[];
}

interface AccessibilitySettings {
  highContrast: boolean;
  reducedMotion: boolean;
  screenReaderOptimized: boolean;
  keyboardNavigation: boolean;
  focusIndicators: boolean;
  altTextRequired: boolean;
  fontSize: number;
  lineSpacing: number;
  colorBlindFriendly: boolean;
}

const SourceSettingsPanel: React.FC<SourceSettingsPanelProps> = ({
  open,
  onOpenChange,
  source,
  onSaveSettings,
  onResetSettings,
  onExportSettings,
  onImportSettings,
}) => {
  const [activeTab, setActiveTab] = useState('general');
  const [settings, setSettings] = useState<SourceSettings>({
    general: {
      title: source.title,
      tags: source.tags || [],
      category: source.type,
      language: 'en',
      timezone: 'UTC',
      autoSave: true,
      autoBackup: true,
      versionControl: true,
      collaborationEnabled: false,
    },
    display: {
      theme: 'auto',
      layout: 'grid',
      density: 'comfortable',
      showThumbnails: true,
      showMetadata: true,
      showTimestamps: true,
      showFileSize: true,
      sortBy: 'created_at',
      sortOrder: 'desc',
      itemsPerPage: 20,
      enableAnimations: true,
      fontSize: 14,
      lineHeight: 1.5,
    },
    privacy: {
      visibility: 'private',
      allowIndexing: false,
      allowDownload: true,
      allowSharing: false,
      allowComments: false,
      allowRating: false,
      trackAnalytics: true,
      showInSearch: false,
      requireAuthentication: true,
      allowedDomains: [],
      blockedUsers: [],
      accessLevel: 'read',
    },
    notifications: {
      emailNotifications: true,
      pushNotifications: false,
      inAppNotifications: true,
      notifyOnComment: true,
      notifyOnShare: true,
      notifyOnEdit: true,
      notifyOnDownload: false,
      notifyOnMention: true,
      digestFrequency: 'daily',
      quietHours: {
        enabled: false,
        start: '22:00',
        end: '08:00',
      },
    },
    storage: {
      storageLocation: 'cloud',
      compressionEnabled: true,
      compressionLevel: 5,
      encryptionEnabled: true,
      backupFrequency: 'daily',
      retentionPeriod: 90,
      maxFileSize: 100,
      allowedFileTypes: ['pdf', 'doc', 'docx', 'txt', 'md'],
      autoCleanup: false,
      cleanupThreshold: 80,
    },
    advanced: {
      enableDebugMode: false,
      enableExperimentalFeatures: false,
      apiRateLimit: 100,
      cacheSize: 50,
      cacheDuration: 3600,
      enableLogging: true,
      logLevel: 'info',
      enableProfiling: false,
      customCSS: '',
      customJS: '',
      webhookUrl: '',
      enableWebhooks: false,
    },
    integrations: {
      enabledIntegrations: [],
      apiKeys: {},
      syncFrequency: 60,
      autoSync: true,
      conflictResolution: 'manual',
      enabledServices: [],
    },
    accessibility: {
      highContrast: false,
      reducedMotion: false,
      screenReaderOptimized: false,
      keyboardNavigation: true,
      focusIndicators: true,
      altTextRequired: false,
      fontSize: 14,
      lineSpacing: 1.5,
      colorBlindFriendly: false,
    },
  });

  const [hasChanges, setHasChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isResetting, setIsResetting] = useState(false);

  useEffect(() => {
    // Check if settings have changed
    setHasChanges(true); // Simplified for demo
  }, [settings]);

  const handleSaveSettings = async () => {
    setIsSaving(true);
    try {
      await onSaveSettings?.(settings);
      setHasChanges(false);
    } catch (error) {
      console.error('Failed to save settings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleResetSettings = async () => {
    setIsResetting(true);
    try {
      await onResetSettings?.();
      // Reset to default values
      setHasChanges(false);
    } catch (error) {
      console.error('Failed to reset settings:', error);
    } finally {
      setIsResetting(false);
    }
  };

  const updateSettings = <T extends keyof SourceSettings>(
    category: T,
    updates: Partial<SourceSettings[T]>
  ) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        ...updates,
      },
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Source Settings - {source.title}
          </DialogTitle>
          <DialogDescription>
            Configure preferences and settings for this source
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="display">Display</TabsTrigger>
              <TabsTrigger value="privacy">Privacy</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>

            <div className="flex-1 overflow-hidden mt-4">
              <ScrollArea className="h-full">
                <TabsContent value="general" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        Basic Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label htmlFor="title">Title</Label>
                        <Input
                          id="title"
                          value={settings.general.title}
                          onChange={(e) => updateSettings('general', { title: e.target.value })}
                        />
                      </div>
                      <div>
                        <Label htmlFor="category">Category</Label>
                        <Select
                          value={settings.general.category}
                          onValueChange={(value) => updateSettings('general', { category: value })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="document">Document</SelectItem>
                            <SelectItem value="image">Image</SelectItem>
                            <SelectItem value="video">Video</SelectItem>
                            <SelectItem value="audio">Audio</SelectItem>
                            <SelectItem value="archive">Archive</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="language">Language</Label>
                          <Select
                            value={settings.general.language}
                            onValueChange={(value) => updateSettings('general', { language: value })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="en">English</SelectItem>
                              <SelectItem value="es">Spanish</SelectItem>
                              <SelectItem value="fr">French</SelectItem>
                              <SelectItem value="de">German</SelectItem>
                              <SelectItem value="zh">Chinese</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label htmlFor="timezone">Timezone</Label>
                          <Select
                            value={settings.general.timezone}
                            onValueChange={(value) => updateSettings('general', { timezone: value })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="UTC">UTC</SelectItem>
                              <SelectItem value="America/New_York">Eastern Time</SelectItem>
                              <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                              <SelectItem value="Europe/London">London</SelectItem>
                              <SelectItem value="Asia/Tokyo">Tokyo</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Zap className="h-4 w-4" />
                        Features
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label>Auto Save</Label>
                          <p className="text-sm text-muted-foreground">Automatically save changes</p>
                        </div>
                        <Switch
                          checked={settings.general.autoSave}
                          onCheckedChange={(checked) => updateSettings('general', { autoSave: checked })}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label>Auto Backup</Label>
                          <p className="text-sm text-muted-foreground">Create automatic backups</p>
                        </div>
                        <Switch
                          checked={settings.general.autoBackup}
                          onCheckedChange={(checked) => updateSettings('general', { autoBackup: checked })}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label>Version Control</Label>
                          <p className="text-sm text-muted-foreground">Track version history</p>
                        </div>
                        <Switch
                          checked={settings.general.versionControl}
                          onCheckedChange={(checked) => updateSettings('general', { versionControl: checked })}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label>Collaboration</Label>
                          <p className="text-sm text-muted-foreground">Enable team collaboration</p>
                        </div>
                        <Switch
                          checked={settings.general.collaborationEnabled}
                          onCheckedChange={(checked) => updateSettings('general', { collaborationEnabled: checked })}
                        />
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="display" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Palette className="h-4 w-4" />
                        Appearance
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label>Theme</Label>
                        <Select
                          value={settings.display.theme}
                          onValueChange={(value: 'light' | 'dark' | 'auto') => updateSettings('display', { theme: value })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="light">
                              <div className="flex items-center gap-2">
                                <Sun className="h-4 w-4" />
                                Light
                              </div>
                            </SelectItem>
                            <SelectItem value="dark">
                              <div className="flex items-center gap-2">
                                <Moon className="h-4 w-4" />
                                Dark
                              </div>
                            </SelectItem>
                            <SelectItem value="auto">
                              <div className="flex items-center gap-2">
                                <Monitor className="h-4 w-4" />
                                Auto
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label>Layout</Label>
                        <Select
                          value={settings.display.layout}
                          onValueChange={(value: 'grid' | 'list' | 'compact') => updateSettings('display', { layout: value })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="grid">
                              <div className="flex items-center gap-2">
                                <Grid className="h-4 w-4" />
                                Grid
                              </div>
                            </SelectItem>
                            <SelectItem value="list">
                              <div className="flex items-center gap-2">
                                <List className="h-4 w-4" />
                                List
                              </div>
                            </SelectItem>
                            <SelectItem value="compact">
                              <div className="flex items-center gap-2">
                                <Layers className="h-4 w-4" />
                                Compact
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label>Font Size: {settings.display.fontSize}px</Label>
                        <Slider
                          value={[settings.display.fontSize]}
                          onValueChange={([value]) => updateSettings('display', { fontSize: value })}
                          min={10}
                          max={24}
                          step={1}
                          className="mt-2"
                        />
                      </div>
                      <div>
                        <Label>Items per Page: {settings.display.itemsPerPage}</Label>
                        <Slider
                          value={[settings.display.itemsPerPage]}
                          onValueChange={([value]) => updateSettings('display', { itemsPerPage: value })}
                          min={10}
                          max={100}
                          step={10}
                          className="mt-2"
                        />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Eye className="h-4 w-4" />
                        Visibility Options
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center justify-between">
                        <Label>Show Thumbnails</Label>
                        <Switch
                          checked={settings.display.showThumbnails}
                          onCheckedChange={(checked) => updateSettings('display', { showThumbnails: checked })}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label>Show Metadata</Label>
                        <Switch
                          checked={settings.display.showMetadata}
                          onCheckedChange={(checked) => updateSettings('display', { showMetadata: checked })}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label>Show Timestamps</Label>
                        <Switch
                          checked={settings.display.showTimestamps}
                          onCheckedChange={(checked) => updateSettings('display', { showTimestamps: checked })}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label>Show File Size</Label>
                        <Switch
                          checked={settings.display.showFileSize}
                          onCheckedChange={(checked) => updateSettings('display', { showFileSize: checked })}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label>Enable Animations</Label>
                        <Switch
                          checked={settings.display.enableAnimations}
                          onCheckedChange={(checked) => updateSettings('display', { enableAnimations: checked })}
                        />
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="privacy" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Shield className="h-4 w-4" />
                        Access Control
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label>Visibility</Label>
                        <Select
                          value={settings.privacy.visibility}
                          onValueChange={(value: 'public' | 'private' | 'restricted') => updateSettings('privacy', { visibility: value })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="public">
                              <div className="flex items-center gap-2">
                                <Globe className="h-4 w-4" />
                                Public
                              </div>
                            </SelectItem>
                            <SelectItem value="private">
                              <div className="flex items-center gap-2">
                                <Lock className="h-4 w-4" />
                                Private
                              </div>
                            </SelectItem>
                            <SelectItem value="restricted">
                              <div className="flex items-center gap-2">
                                <UserCheck className="h-4 w-4" />
                                Restricted
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label>Allow Indexing</Label>
                          <p className="text-sm text-muted-foreground">Allow search engines to index</p>
                        </div>
                        <Switch
                          checked={settings.privacy.allowIndexing}
                          onCheckedChange={(checked) => updateSettings('privacy', { allowIndexing: checked })}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label>Allow Download</Label>
                          <p className="text-sm text-muted-foreground">Allow users to download</p>
                        </div>
                        <Switch
                          checked={settings.privacy.allowDownload}
                          onCheckedChange={(checked) => updateSettings('privacy', { allowDownload: checked })}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label>Allow Sharing</Label>
                          <p className="text-sm text-muted-foreground">Allow users to share</p>
                        </div>
                        <Switch
                          checked={settings.privacy.allowSharing}
                          onCheckedChange={(checked) => updateSettings('privacy', { allowSharing: checked })}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label>Require Authentication</Label>
                          <p className="text-sm text-muted-foreground">Require login to access</p>
                        </div>
                        <Switch
                          checked={settings.privacy.requireAuthentication}
                          onCheckedChange={(checked) => updateSettings('privacy', { requireAuthentication: checked })}
                        />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Bell className="h-4 w-4" />
                        Notifications
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center justify-between">
                        <Label>Email Notifications</Label>
                        <Switch
                          checked={settings.notifications.emailNotifications}
                          onCheckedChange={(checked) => updateSettings('notifications', { emailNotifications: checked })}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label>Push Notifications</Label>
                        <Switch
                          checked={settings.notifications.pushNotifications}
                          onCheckedChange={(checked) => updateSettings('notifications', { pushNotifications: checked })}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label>In-App Notifications</Label>
                        <Switch
                          checked={settings.notifications.inAppNotifications}
                          onCheckedChange={(checked) => updateSettings('notifications', { inAppNotifications: checked })}
                        />
                      </div>
                      <div>
                        <Label>Digest Frequency</Label>
                        <Select
                          value={settings.notifications.digestFrequency}
                          onValueChange={(value: 'immediate' | 'hourly' | 'daily' | 'weekly' | 'never') => 
                            updateSettings('notifications', { digestFrequency: value })
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="immediate">Immediate</SelectItem>
                            <SelectItem value="hourly">Hourly</SelectItem>
                            <SelectItem value="daily">Daily</SelectItem>
                            <SelectItem value="weekly">Weekly</SelectItem>
                            <SelectItem value="never">Never</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="advanced" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Database className="h-4 w-4" />
                        Storage & Backup
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label>Storage Location</Label>
                        <Select
                          value={settings.storage.storageLocation}
                          onValueChange={(value: 'local' | 'cloud' | 'hybrid') => updateSettings('storage', { storageLocation: value })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="local">
                              <div className="flex items-center gap-2">
                                <HardDrive className="h-4 w-4" />
                                Local
                              </div>
                            </SelectItem>
                            <SelectItem value="cloud">
                              <div className="flex items-center gap-2">
                                <Cloud className="h-4 w-4" />
                                Cloud
                              </div>
                            </SelectItem>
                            <SelectItem value="hybrid">
                              <div className="flex items-center gap-2">
                                <Layers className="h-4 w-4" />
                                Hybrid
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label>Enable Compression</Label>
                          <p className="text-sm text-muted-foreground">Compress files to save space</p>
                        </div>
                        <Switch
                          checked={settings.storage.compressionEnabled}
                          onCheckedChange={(checked) => updateSettings('storage', { compressionEnabled: checked })}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label>Enable Encryption</Label>
                          <p className="text-sm text-muted-foreground">Encrypt stored files</p>
                        </div>
                        <Switch
                          checked={settings.storage.encryptionEnabled}
                          onCheckedChange={(checked) => updateSettings('storage', { encryptionEnabled: checked })}
                        />
                      </div>
                      <div>
                        <Label>Max File Size: {settings.storage.maxFileSize}MB</Label>
                        <Slider
                          value={[settings.storage.maxFileSize]}
                          onValueChange={([value]) => updateSettings('storage', { maxFileSize: value })}
                          min={1}
                          max={1000}
                          step={1}
                          className="mt-2"
                        />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Activity className="h-4 w-4" />
                        Performance
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label>Cache Size: {settings.advanced.cacheSize}MB</Label>
                        <Slider
                          value={[settings.advanced.cacheSize]}
                          onValueChange={([value]) => updateSettings('advanced', { cacheSize: value })}
                          min={10}
                          max={500}
                          step={10}
                          className="mt-2"
                        />
                      </div>
                      <div>
                        <Label>API Rate Limit: {settings.advanced.apiRateLimit} requests/min</Label>
                        <Slider
                          value={[settings.advanced.apiRateLimit]}
                          onValueChange={([value]) => updateSettings('advanced', { apiRateLimit: value })}
                          min={10}
                          max={1000}
                          step={10}
                          className="mt-2"
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label>Enable Debug Mode</Label>
                          <p className="text-sm text-muted-foreground">Show debug information</p>
                        </div>
                        <Switch
                          checked={settings.advanced.enableDebugMode}
                          onCheckedChange={(checked) => updateSettings('advanced', { enableDebugMode: checked })}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <Label>Enable Logging</Label>
                          <p className="text-sm text-muted-foreground">Log system events</p>
                        </div>
                        <Switch
                          checked={settings.advanced.enableLogging}
                          onCheckedChange={(checked) => updateSettings('advanced', { enableLogging: checked })}
                        />
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </ScrollArea>
            </div>
          </Tabs>
        </div>

        <DialogFooter className="flex justify-between">
          <div className="flex gap-2">
            <Button variant="outline" onClick={onExportSettings}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" onClick={handleResetSettings} disabled={isResetting}>
              {isResetting ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <RotateCcw className="h-4 w-4 mr-2" />
              )}
              Reset
            </Button>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveSettings} disabled={isSaving || !hasChanges}>
              {isSaving ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Save Changes
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SourceSettingsPanel;