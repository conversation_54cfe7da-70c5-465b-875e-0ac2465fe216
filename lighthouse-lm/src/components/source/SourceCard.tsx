import React from 'react';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Clock,
  FileText,
  Globe,
  HardDrive,
  Image,
  MoreVertical,
  Video,
} from 'lucide-react';
import { SourceCardProps } from './types';

const SourceCard: React.FC<SourceCardProps> = ({
  source,
  isSelected,
  onSelect,
  onVersionHistory,
}) => {
  const getTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'pdf':
      case 'doc':
      case 'docx':
        return <FileText className="h-4 w-4" />;
      case 'web':
      case 'url':
        return <Globe className="h-4 w-4" />;
      case 'image':
      case 'jpg':
      case 'png':
      case 'gif':
        return <Image className="h-4 w-4" />;
      case 'video':
      case 'mp4':
      case 'avi':
        return <Video className="h-4 w-4" />;
      default:
        return <HardDrive className="h-4 w-4" />;
    }
  };

  const getTypeBadgeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'pdf':
        return 'bg-red-100 text-red-800';
      case 'web':
      case 'url':
        return 'bg-blue-100 text-blue-800';
      case 'doc':
      case 'docx':
        return 'bg-blue-100 text-blue-800';
      case 'image':
        return 'bg-green-100 text-green-800';
      case 'video':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown size';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <Card className={`cursor-pointer transition-all hover:shadow-md ${
      isSelected ? 'ring-2 ring-primary' : ''
    }`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            <Checkbox
              checked={isSelected}
              onCheckedChange={() => onSelect(source.id)}
              onClick={(e) => e.stopPropagation()}
            />
            <Badge className={getTypeBadgeColor(source.type)}>
              {getTypeIcon(source.type)}
              <span className="ml-1">{source.type.toUpperCase()}</span>
            </Badge>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onVersionHistory(source)}>
                Version History
              </DropdownMenuItem>
              <DropdownMenuItem>Edit Metadata</DropdownMenuItem>
              <DropdownMenuItem>Download</DropdownMenuItem>
              <DropdownMenuItem className="text-destructive">
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <CardTitle className="text-base line-clamp-2">{source.title}</CardTitle>
        {source.content && (
          <CardDescription className="line-clamp-3">
            {source.content.substring(0, 150)}...
          </CardDescription>
        )}
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2">
          {source.tags && source.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {source.tags.slice(0, 3).map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {source.tags.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{source.tags.length - 3}
                </Badge>
              )}
            </div>
          )}
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {formatDate(source.created_at)}
            </div>
            {source.file_size && (
              <span>{formatFileSize(source.file_size)}</span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SourceCard;