import React, { useState, useMemo } from 'react';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import {
  Download,
  FileText,
  FileSpreadsheet,
  FileJson,
  FileCode,
  Globe,
  Archive,
  Settings,
  Eye,
  Calendar,
  Tag,
  User,
  Link,
  CheckCircle,
  AlertCircle,
  Loader2,
  Copy,
  Share,
  Cloud,
  HardDrive,
  Mail,
  Clock,
  Filter,
  Zap,
  Shield,
} from 'lucide-react';
import { Source } from './types';

// Define export format as union type for this component
type ExportFormatType = 'json' | 'csv' | 'pdf' | 'html' | 'zip';

interface SourceExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  sources: Source[];
  selectedSources?: Source[];
  onExport?: (config: ExportConfig) => Promise<void>;
}

interface ExportConfig {
  format: ExportFormatType;
  sources: Source[];
  fields: string[];
  options: {
    includeMetadata: boolean;
    includeContent: boolean;
    includeTags: boolean;
    includeRelations: boolean;
    dateFormat: string;
    compression: boolean;
    password: string;
    batchSize: number;
    enableScheduling: boolean;
    scheduleTime?: string;
    enableFiltering: boolean;
    filterCriteria?: {
      dateRange?: { start: string; end: string };
      types?: string[];
      tags?: string[];
      minSize?: number;
      maxSize?: number;
    };
    enableEncryption: boolean;
    encryptionKey?: string;
    enableWatermark: boolean;
    watermarkText?: string;
  };
  destination: 'download' | 'cloud' | 'email';
  filename: string;
  template?: string;
  cloudProvider?: 'dropbox' | 'gdrive' | 's3';
  emailRecipients?: string[];
  emailSubject?: string;
  emailMessage?: string;
}

interface ExportTemplate {
  id: string;
  name: string;
  description: string;
  format: ExportFormatType;
  fields: string[];
  options: {
    includeMetadata: boolean;
    includeContent: boolean;
    includeTags: boolean;
    includeRelations: boolean;
    dateFormat: string;
    compression: boolean;
  };
}

const SourceExportDialog: React.FC<SourceExportDialogProps> = ({
  open,
  onOpenChange,
  sources,
  selectedSources = [],
  onExport,
}) => {
  const [activeTab, setActiveTab] = useState('format');
  const [exportFormat, setExportFormat] = useState<ExportFormatType>('json');
  const [selectedFields, setSelectedFields] = useState<string[]>([
    'title', 'type', 'created_at', 'tags'
  ]);
  const [exportOptions, setExportOptions] = useState({
    includeMetadata: true,
    includeContent: false,
    includeTags: true,
    includeRelations: false,
    dateFormat: 'iso',
    compression: false,
    password: '',
    batchSize: 100,
    enableScheduling: false,
    scheduleTime: '',
    enableFiltering: false,
    filterCriteria: {
      dateRange: { start: '', end: '' },
      types: [],
      tags: [],
      minSize: 0,
      maxSize: 0,
    },
    enableEncryption: false,
    encryptionKey: '',
    enableWatermark: false,
    watermarkText: 'Exported from Source Manager',
  });
  const [destination, setDestination] = useState<'download' | 'cloud' | 'email'>('download');
  const [filename, setFilename] = useState('sources_export');
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [isExporting, setIsExporting] = useState(false);
  const [previewData, setPreviewData] = useState<string>('');

  const exportSources = selectedSources.length > 0 ? selectedSources : sources;

  const availableFields = [
    { id: 'id', label: 'ID', description: 'Unique identifier' },
    { id: 'title', label: 'Title', description: 'Source title' },
    { id: 'type', label: 'Type', description: 'Source type (pdf, web, etc.)' },
    { id: 'description', label: 'Description', description: 'Source description' },
    { id: 'content', label: 'Content', description: 'Full source content' },
    { id: 'url', label: 'URL', description: 'Source URL if applicable' },
    { id: 'file_path', label: 'File Path', description: 'Local file path' },
    { id: 'file_size', label: 'File Size', description: 'File size in bytes' },
    { id: 'created_at', label: 'Created Date', description: 'Creation timestamp' },
    { id: 'tags', label: 'Tags', description: 'Associated tags' },
    { id: 'metadata', label: 'Metadata', description: 'Additional metadata' },
  ];

  const exportFormats: { value: ExportFormatType; label: string; icon: React.ReactNode; description: string }[] = [
    {
      value: 'json',
      label: 'JSON',
      icon: <FileCode className="h-4 w-4" />,
      description: 'Structured data format, ideal for developers'
    },
    {
      value: 'csv',
      label: 'CSV',
      icon: <FileSpreadsheet className="h-4 w-4" />,
      description: 'Spreadsheet format, compatible with Excel'
    },
    {
      value: 'pdf',
      label: 'PDF',
      icon: <FileText className="h-4 w-4" />,
      description: 'Formatted document, ideal for sharing'
    },
    {
      value: 'html',
      label: 'HTML',
      icon: <Globe className="h-4 w-4" />,
      description: 'Web format with rich formatting'
    },
    {
      value: 'zip',
      label: 'ZIP Archive',
      icon: <Archive className="h-4 w-4" />,
      description: 'Compressed archive with all files'
    },
  ];

  const exportTemplates: ExportTemplate[] = [
    {
      id: 'basic',
      name: 'Basic Export',
      description: 'Essential fields only',
      format: 'csv',
      fields: ['title', 'type', 'created_at'],
      options: {
        includeMetadata: false,
        includeContent: false,
        includeTags: false,
        includeRelations: false,
        dateFormat: 'iso',
        compression: false,
      }
    },
    {
      id: 'detailed',
      name: 'Detailed Export',
      description: 'All available information',
      format: 'json',
      fields: ['id', 'title', 'type', 'description', 'url', 'file_size', 'created_at', 'tags', 'metadata'],
      options: {
        includeMetadata: true,
        includeContent: true,
        includeTags: true,
        includeRelations: true,
        dateFormat: 'iso',
        compression: false,
      }
    },
    {
      id: 'research',
      name: 'Research Report',
      description: 'Formatted for academic use',
      format: 'pdf',
      fields: ['title', 'type', 'description', 'url', 'created_at', 'tags'],
      options: {
        includeMetadata: true,
        includeContent: false,
        includeTags: true,
        includeRelations: false,
        dateFormat: 'readable',
        compression: false,
      }
    },
    {
      id: 'backup',
      name: 'Full Backup',
      description: 'Complete data backup',
      format: 'zip',
      fields: ['id', 'title', 'type', 'description', 'content', 'url', 'file_path', 'file_size', 'created_at', 'tags', 'metadata'],
      options: {
        includeMetadata: true,
        includeContent: true,
        includeTags: true,
        includeRelations: true,
        dateFormat: 'iso',
        compression: true,
      }
    },
  ];

  const generatePreview = useMemo(() => {
    if (exportSources.length === 0) return '';

    const sampleSource = exportSources[0];
    const filteredData: any = {};
    
    selectedFields.forEach(field => {
      if (field in sampleSource) {
        filteredData[field] = (sampleSource as any)[field];
      }
    });

    switch (exportFormat) {
      case 'json':
        return JSON.stringify([filteredData], null, 2);
      case 'csv':
        const headers = selectedFields.join(',');
        const values = selectedFields.map(field => 
          JSON.stringify(filteredData[field] || '')
        ).join(',');
        return `${headers}\n${values}`;
      case 'html':
        return `<!DOCTYPE html>
<html>
<head><title>Sources Export</title></head>
<body>
<h1>Sources Export</h1>
<table border="1">
<tr>${selectedFields.map(f => `<th>${f}</th>`).join('')}</tr>
<tr>${selectedFields.map(f => `<td>${filteredData[f] || ''}</td>`).join('')}</tr>
</table>
</body>
</html>`;
      default:
        return 'Preview not available for this format';
    }
  }, [exportFormat, selectedFields, exportSources]);

  const handleFieldToggle = (fieldId: string) => {
    setSelectedFields(prev => 
      prev.includes(fieldId)
        ? prev.filter(f => f !== fieldId)
        : [...prev, fieldId]
    );
  };

  const handleTemplateSelect = (templateId: string) => {
    const template = exportTemplates.find(t => t.id === templateId);
    if (template) {
      setSelectedTemplate(templateId);
      setExportFormat(template.format);
      setSelectedFields(template.fields);
      setExportOptions({
        ...exportOptions,
        ...template.options,
        password: '',
        batchSize: exportOptions.batchSize,
        enableScheduling: exportOptions.enableScheduling,
        scheduleTime: exportOptions.scheduleTime,
        enableFiltering: exportOptions.enableFiltering,
        filterCriteria: exportOptions.filterCriteria,
        enableEncryption: exportOptions.enableEncryption,
        encryptionKey: exportOptions.encryptionKey,
        enableWatermark: exportOptions.enableWatermark,
        watermarkText: exportOptions.watermarkText,
      });
    }
  };

  const handleExport = async () => {
    setIsExporting(true);
    
    const config: ExportConfig = {
      format: exportFormat,
      sources: exportSources,
      fields: selectedFields,
      options: exportOptions,
      destination,
      filename,
      template: selectedTemplate,
    };

    try {
      await onExport?.(config);
      onOpenChange(false);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const getFormatIcon = (format: ExportFormatType) => {
    const formatData = exportFormats.find(f => f.value === format);
    return formatData?.icon || <FileText className="h-4 w-4" />;
  };

  const estimatedSize = useMemo(() => {
    const baseSize = exportSources.length * selectedFields.length * 50; // rough estimate
    const contentMultiplier = exportOptions.includeContent ? 10 : 1;
    const metadataMultiplier = exportOptions.includeMetadata ? 2 : 1;
    const compressionMultiplier = exportOptions.compression ? 0.3 : 1;
    
    const size = baseSize * contentMultiplier * metadataMultiplier * compressionMultiplier;
    
    if (size < 1024) return `${Math.round(size)} B`;
    if (size < 1024 * 1024) return `${Math.round(size / 1024)} KB`;
    return `${Math.round(size / (1024 * 1024))} MB`;
  }, [exportSources.length, selectedFields.length, exportOptions]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Sources
          </DialogTitle>
          <DialogDescription>
            Export {exportSources.length} source{exportSources.length !== 1 ? 's' : ''} in your preferred format
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="format">Format</TabsTrigger>
              <TabsTrigger value="fields">Fields</TabsTrigger>
              <TabsTrigger value="options">Options</TabsTrigger>
              <TabsTrigger value="preview">Preview</TabsTrigger>
            </TabsList>

            <div className="flex-1 overflow-hidden mt-4">
              <TabsContent value="format" className="h-full">
                <ScrollArea className="h-full">
                  <div className="space-y-6">
                    {/* Templates */}
                    <div>
                      <Label className="text-base font-medium">Quick Templates</Label>
                      <p className="text-sm text-muted-foreground mb-3">
                        Choose a pre-configured export template
                      </p>
                      <div className="grid grid-cols-2 gap-3">
                        {exportTemplates.map(template => (
                          <Card 
                            key={template.id} 
                            className={`cursor-pointer transition-colors ${
                              selectedTemplate === template.id 
                                ? 'ring-2 ring-primary' 
                                : 'hover:bg-muted/50'
                            }`}
                            onClick={() => handleTemplateSelect(template.id)}
                          >
                            <CardContent className="p-4">
                              <div className="flex items-start gap-3">
                                <div className="flex-shrink-0">
                                  {getFormatIcon(template.format)}
                                </div>
                                <div className="flex-1 min-w-0">
                                  <h4 className="font-medium text-sm">{template.name}</h4>
                                  <p className="text-xs text-muted-foreground mb-2">
                                    {template.description}
                                  </p>
                                  <div className="flex items-center gap-2">
                                    <Badge variant="outline" className="text-xs">
                                      {template.format.toUpperCase()}
                                    </Badge>
                                    <span className="text-xs text-muted-foreground">
                                      {template.fields.length} fields
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>

                    <Separator />

                    {/* Format Selection */}
                    <div>
                      <Label className="text-base font-medium">Export Format</Label>
                      <p className="text-sm text-muted-foreground mb-3">
                        Choose the output format for your export
                      </p>
                      <RadioGroup 
                        value={exportFormat} 
                        onValueChange={(value: ExportFormatType) => setExportFormat(value as ExportFormatType)}
                      >
                        <div className="grid grid-cols-1 gap-3">
                          {exportFormats.map(format => (
                            <div key={format.value} className="flex items-center space-x-2">
                              <RadioGroupItem value={format.value} id={format.value} />
                              <Label 
                                htmlFor={format.value} 
                                className="flex items-center gap-3 flex-1 cursor-pointer p-3 rounded-lg border hover:bg-muted/50"
                              >
                                <div className="flex-shrink-0">
                                  {format.icon}
                                </div>
                                <div className="flex-1">
                                  <div className="font-medium">{format.label}</div>
                                  <div className="text-sm text-muted-foreground">
                                    {format.description}
                                  </div>
                                </div>
                              </Label>
                            </div>
                          ))}
                        </div>
                      </RadioGroup>
                    </div>
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value="fields" className="h-full">
                <ScrollArea className="h-full">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-base font-medium">Select Fields</Label>
                        <p className="text-sm text-muted-foreground">
                          Choose which fields to include in the export
                        </p>
                      </div>
                      <div className="flex gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => setSelectedFields(availableFields.map(f => f.id))}
                        >
                          Select All
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => setSelectedFields([])}
                        >
                          Clear All
                        </Button>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 gap-2">
                      {availableFields.map(field => (
                        <div key={field.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={field.id}
                            checked={selectedFields.includes(field.id)}
                            onCheckedChange={() => handleFieldToggle(field.id)}
                          />
                          <Label 
                            htmlFor={field.id} 
                            className="flex-1 cursor-pointer p-2 rounded hover:bg-muted/50"
                          >
                            <div className="font-medium">{field.label}</div>
                            <div className="text-sm text-muted-foreground">
                              {field.description}
                            </div>
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value="options" className="h-full">
                <ScrollArea className="h-full">
                  <div className="space-y-6">
                    {/* Content Options */}
                    <div>
                      <Label className="text-base font-medium">Content Options</Label>
                      <div className="space-y-3 mt-3">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="includeMetadata"
                            checked={exportOptions.includeMetadata}
                            onCheckedChange={(checked) => 
                              setExportOptions(prev => ({ ...prev, includeMetadata: !!checked }))
                            }
                          />
                          <Label htmlFor="includeMetadata" className="cursor-pointer">
                            Include metadata
                          </Label>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="includeContent"
                            checked={exportOptions.includeContent}
                            onCheckedChange={(checked) => 
                              setExportOptions(prev => ({ ...prev, includeContent: !!checked }))
                            }
                          />
                          <Label htmlFor="includeContent" className="cursor-pointer">
                            Include full content
                          </Label>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="includeTags"
                            checked={exportOptions.includeTags}
                            onCheckedChange={(checked) => 
                              setExportOptions(prev => ({ ...prev, includeTags: !!checked }))
                            }
                          />
                          <Label htmlFor="includeTags" className="cursor-pointer">
                            Include tags
                          </Label>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="includeRelations"
                            checked={exportOptions.includeRelations}
                            onCheckedChange={(checked) => 
                              setExportOptions(prev => ({ ...prev, includeRelations: !!checked }))
                            }
                          />
                          <Label htmlFor="includeRelations" className="cursor-pointer">
                            Include relationships
                          </Label>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    {/* Format Options */}
                    <div>
                      <Label className="text-base font-medium">Format Options</Label>
                      <div className="space-y-3 mt-3">
                        <div>
                          <Label htmlFor="dateFormat" className="text-sm font-medium">
                            Date Format
                          </Label>
                          <Select 
                            value={exportOptions.dateFormat} 
                            onValueChange={(value) => 
                              setExportOptions(prev => ({ ...prev, dateFormat: value }))
                            }
                          >
                            <SelectTrigger className="mt-1">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="iso">ISO 8601 (2024-01-15T10:30:00Z)</SelectItem>
                              <SelectItem value="readable">Readable (Jan 15, 2024)</SelectItem>
                              <SelectItem value="short">Short (01/15/24)</SelectItem>
                              <SelectItem value="timestamp">Unix Timestamp</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="compression"
                            checked={exportOptions.compression}
                            onCheckedChange={(checked) => 
                              setExportOptions(prev => ({ ...prev, compression: !!checked }))
                            }
                          />
                          <Label htmlFor="compression" className="cursor-pointer">
                            Enable compression
                          </Label>
                        </div>
                        
                        {exportOptions.compression && (
                          <div>
                            <Label htmlFor="password" className="text-sm font-medium">
                              Password (optional)
                            </Label>
                            <Input
                              id="password"
                              type="password"
                              value={exportOptions.password}
                              onChange={(e) => 
                                setExportOptions(prev => ({ ...prev, password: e.target.value }))
                              }
                              placeholder="Enter password for encrypted archive"
                              className="mt-1"
                            />
                          </div>
                        )}
                      </div>
                    </div>

                    <Separator />

                    {/* Destination */}
                    <div>
                      <Label className="text-base font-medium">Export Destination</Label>
                      <RadioGroup 
                        value={destination} 
                        onValueChange={(value: any) => setDestination(value)}
                        className="mt-3"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="download" id="download" />
                          <Label htmlFor="download" className="flex items-center gap-2 cursor-pointer">
                            <HardDrive className="h-4 w-4" />
                            Download to device
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="cloud" id="cloud" />
                          <Label htmlFor="cloud" className="flex items-center gap-2 cursor-pointer">
                            <Cloud className="h-4 w-4" />
                            Save to cloud storage
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="email" id="email" />
                          <Label htmlFor="email" className="flex items-center gap-2 cursor-pointer">
                            <Mail className="h-4 w-4" />
                            Send via email
                          </Label>
                        </div>
                      </RadioGroup>
                    </div>

                    {/* Filename */}
                    <div>
                      <Label htmlFor="filename" className="text-sm font-medium">
                        Filename
                      </Label>
                      <Input
                        id="filename"
                        value={filename}
                        onChange={(e) => setFilename(e.target.value)}
                        placeholder="Enter filename"
                        className="mt-1"
                      />
                    </div>
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value="preview" className="h-full">
                <div className="space-y-4 h-full flex flex-col">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-base font-medium">Export Preview</Label>
                      <p className="text-sm text-muted-foreground">
                        Preview of the first source in your export
                      </p>
                    </div>
                    <Button variant="outline" size="sm" onClick={() => navigator.clipboard.writeText(generatePreview)}>
                      <Copy className="h-4 w-4 mr-2" />
                      Copy
                    </Button>
                  </div>
                  
                  <Card className="flex-1">
                    <CardContent className="p-4 h-full">
                      <ScrollArea className="h-full">
                        <pre className="text-xs font-mono whitespace-pre-wrap">
                          {generatePreview}
                        </pre>
                      </ScrollArea>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </div>

        <DialogFooter className="flex items-center justify-between">
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <span>{exportSources.length} sources</span>
            <span>{selectedFields.length} fields</span>
            <span>~{estimatedSize}</span>
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleExport} 
              disabled={isExporting || selectedFields.length === 0}
            >
              {isExporting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </>
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SourceExportDialog;