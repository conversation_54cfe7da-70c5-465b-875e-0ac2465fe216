import React, { useState, useMemo, useCallback } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  GitBranch,
  Plus,
  Search,
  Filter,
  MoreVertical,
  ArrowRight,
  Trash2,
  Edit,
  Eye,
  Network,
  Zap,
  Shield,
  TrendingUp,
  RefreshCw,
  BarChart3,
  Layers,
  Target,
  Compass,
  Workflow,
  Share2,
  Download,
  Settings,
  Maximize2,
  Minimize2,
} from 'lucide-react';
import { Source, SourceRelation } from './types';

interface SourceRelationshipViewProps {
  sources: Source[];
  relations: SourceRelation[];
  onCreateRelation: (relation: Omit<SourceRelation, 'id'>) => void;
  onUpdateRelation: (id: string, relation: Partial<SourceRelation>) => void;
  onDeleteRelation: (id: string) => void;
}

const SourceRelationshipView: React.FC<SourceRelationshipViewProps> = ({
  sources,
  relations,
  onCreateRelation,
  onUpdateRelation,
  onDeleteRelation,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [selectedSource, setSelectedSource] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'graph' | 'list' | 'matrix' | 'timeline'>('graph');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingRelation, setEditingRelation] = useState<SourceRelation | null>(null);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [clusterMode, setClusterMode] = useState<'type' | 'strength' | 'date' | 'none'>('none');
  const [layoutAlgorithm, setLayoutAlgorithm] = useState<'force' | 'hierarchical' | 'circular'>('force');
  const [showWeakConnections, setShowWeakConnections] = useState(true);
  const [minStrength, setMinStrength] = useState(1);
  const [maxDepth, setMaxDepth] = useState(3);
  const [highlightPath, setHighlightPath] = useState<string[]>([]);

  // New relation form state
  const [newRelation, setNewRelation] = useState({
    sourceId: '',
    targetId: '',
    type: 'references' as SourceRelation['type'],
    strength: 5,
    notes: '',
  });

  const relationTypes = [
    { value: 'references', label: 'References', icon: ArrowRight, color: 'bg-blue-100 text-blue-800' },
    { value: 'contradicts', label: 'Contradicts', icon: Shield, color: 'bg-red-100 text-red-800' },
    { value: 'supports', label: 'Supports', icon: TrendingUp, color: 'bg-green-100 text-green-800' },
    { value: 'extends', label: 'Extends', icon: Zap, color: 'bg-purple-100 text-purple-800' },
    { value: 'replaces', label: 'Replaces', icon: RefreshCw, color: 'bg-orange-100 text-orange-800' },
  ];

  const getRelationTypeInfo = (type: SourceRelation['type']) => {
    return relationTypes.find(rt => rt.value === type) || relationTypes[0];
  };

  const filteredRelations = useMemo(() => {
    let filtered = relations;

    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(relation => {
        const sourceTitle = sources.find(s => s.id === relation.sourceId)?.title.toLowerCase() || '';
        const targetTitle = sources.find(s => s.id === relation.targetId)?.title.toLowerCase() || '';
        return sourceTitle.includes(query) || targetTitle.includes(query) || relation.notes?.toLowerCase().includes(query);
      });
    }

    if (filterType !== 'all') {
      filtered = filtered.filter(relation => relation.type === filterType);
    }

    if (selectedSource) {
      filtered = filtered.filter(relation => 
        relation.sourceId === selectedSource || relation.targetId === selectedSource
      );
    }

    return filtered;
  }, [relations, searchQuery, filterType, selectedSource, sources]);

  const handleCreateRelation = useCallback(async () => {
    if (!newRelation.sourceId || !newRelation.targetId || !newRelation.type) {
      return;
    }

    // Prevent self-referencing relationships
    if (newRelation.sourceId === newRelation.targetId) {
      alert('A source cannot have a relationship with itself');
      return;
    }

    // Check for duplicate relationships
    const existingRelation = relations.find(r => 
      (r.sourceId === newRelation.sourceId && r.targetId === newRelation.targetId) ||
      (r.sourceId === newRelation.targetId && r.targetId === newRelation.sourceId)
    );

    if (existingRelation) {
      alert('A relationship between these sources already exists');
      return;
    }

    const relation: SourceRelation = {
      id: Date.now().toString(),
      ...newRelation,
      strength: newRelation.strength || 5,
    };

    await onCreateRelation(relation);
    setNewRelation({ sourceId: '', targetId: '', type: 'references', strength: 5, notes: '' });
    setShowCreateDialog(false);
  }, [newRelation, onCreateRelation, relations]);

  const handleUpdateRelation = useCallback(async (relation: SourceRelation) => {
    if (!editingRelation) return;

    // Validate the updated relationship
    if (relation.sourceId === relation.targetId) {
      alert('A source cannot have a relationship with itself');
      return;
    }

    await onUpdateRelation(editingRelation.id, relation);
    setEditingRelation(null);
  }, [editingRelation, onUpdateRelation]);

  const getSourceTitle = (sourceId: string) => {
    return sources.find(s => s.id === sourceId)?.title || 'Unknown Source';
  };

  // Advanced analytics functions
  const calculateNetworkMetrics = useMemo(() => {
    const metrics = {
      totalNodes: sources.length,
      totalEdges: relations.length,
      density: 0,
      avgDegree: 0,
      centralNodes: [] as Array<{ id: string; title: string; degree: number; betweenness: number }>,
      clusters: [] as Array<{ id: string; nodes: string[]; strength: number }>,
      strongestConnections: [] as Array<{ source: string; target: string; strength: number; type: string }>,
    };

    if (sources.length > 1) {
      const maxPossibleEdges = (sources.length * (sources.length - 1)) / 2;
      metrics.density = relations.length / maxPossibleEdges;
    }

    // Calculate node degrees
    const nodeDegrees = new Map<string, number>();
    sources.forEach(source => {
      const degree = relations.filter(r => r.sourceId === source.id || r.targetId === source.id).length;
      nodeDegrees.set(source.id, degree);
    });

    metrics.avgDegree = Array.from(nodeDegrees.values()).reduce((sum, degree) => sum + degree, 0) / sources.length;

    // Find central nodes (highest degree)
    metrics.centralNodes = sources
      .map(source => ({
        id: source.id,
        title: source.title,
        degree: nodeDegrees.get(source.id) || 0,
        betweenness: calculateBetweennessCentrality(source.id),
      }))
      .sort((a, b) => b.degree - a.degree)
      .slice(0, 5);

    // Find strongest connections
    metrics.strongestConnections = relations
      .sort((a, b) => (b.strength || 0) - (a.strength || 0))
      .slice(0, 10)
      .map(r => ({
        source: getSourceTitle(r.sourceId),
        target: getSourceTitle(r.targetId),
        strength: r.strength || 0,
        type: r.type,
      }));

    return metrics;
  }, [sources, relations]);

  const calculateBetweennessCentrality = (nodeId: string): number => {
    // Simplified betweenness centrality calculation
    // In a real implementation, you'd use a proper graph algorithm
    const connectedNodes = relations
      .filter(r => r.sourceId === nodeId || r.targetId === nodeId)
      .map(r => r.sourceId === nodeId ? r.targetId : r.sourceId);
    
    return connectedNodes.length;
  };

  const findShortestPath = useCallback((startId: string, endId: string): string[] => {
    // Simple BFS to find shortest path
    const visited = new Set<string>();
    const queue = [{ nodeId: startId, path: [startId] }];
    
    while (queue.length > 0) {
      const { nodeId, path } = queue.shift()!;
      
      if (nodeId === endId) {
        return path;
      }
      
      if (visited.has(nodeId)) continue;
      visited.add(nodeId);
      
      const neighbors = relations
        .filter(r => (r.sourceId === nodeId || r.targetId === nodeId) && (r.strength || 0) >= minStrength)
        .map(r => r.sourceId === nodeId ? r.targetId : r.sourceId);
      
      neighbors.forEach(neighborId => {
        if (!visited.has(neighborId) && path.length < maxDepth) {
          queue.push({ nodeId: neighborId, path: [...path, neighborId] });
        }
      });
    }
    
    return [];
  }, [relations, minStrength, maxDepth]);

  const detectCommunities = useMemo(() => {
    // Simple community detection based on connection strength
    const communities: Array<{ id: string; nodes: string[]; avgStrength: number }> = [];
    const processed = new Set<string>();
    
    sources.forEach(source => {
      if (processed.has(source.id)) return;
      
      const community = { id: `community-${communities.length}`, nodes: [source.id], avgStrength: 0 };
      const strongConnections = relations
        .filter(r => 
          (r.sourceId === source.id || r.targetId === source.id) && 
          (r.strength || 0) >= 7
        );
      
      strongConnections.forEach(relation => {
        const connectedId = relation.sourceId === source.id ? relation.targetId : relation.sourceId;
        if (!processed.has(connectedId)) {
          community.nodes.push(connectedId);
          processed.add(connectedId);
        }
      });
      
      if (community.nodes.length > 1) {
        const totalStrength = strongConnections.reduce((sum, r) => sum + (r.strength || 0), 0);
        community.avgStrength = totalStrength / strongConnections.length;
        communities.push(community);
      }
      
      processed.add(source.id);
    });
    
    return communities;
  }, [sources, relations]);

  const renderGraphView = () => {
    // Simplified graph representation - in a real implementation, you'd use a graph library like D3.js or vis.js
    const nodes = sources.map(source => ({
      id: source.id,
      title: source.title,
      type: source.type,
    }));

    const edges = filteredRelations.map(relation => ({
      from: relation.sourceId,
      to: relation.targetId,
      type: relation.type,
      strength: relation.strength,
    }));

    return (
      <div className="space-y-4">
        <div className="text-center py-8 border-2 border-dashed border-muted-foreground/25 rounded-lg">
          <Network className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">Relationship Graph</h3>
          <p className="text-muted-foreground mb-4">
            Interactive graph visualization would be rendered here using a graph library like D3.js or vis.js
          </p>
          <div className="text-sm text-muted-foreground">
            <p>Nodes: {nodes.length} sources</p>
            <p>Edges: {edges.length} relationships</p>
          </div>
        </div>
      </div>
    );
  };

  const renderListView = () => {
    return (
      <div className="space-y-4">
        {filteredRelations.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No relationships found matching your criteria
          </div>
        ) : (
          filteredRelations.map((relation) => {
            const typeInfo = getRelationTypeInfo(relation.type);
            const IconComponent = typeInfo.icon;
            
            return (
              <Card key={relation.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4 flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-sm">
                          {getSourceTitle(relation.sourceId)}
                        </span>
                        <div className="flex items-center gap-1">
                          <IconComponent className="h-4 w-4 text-muted-foreground" />
                          <Badge className={typeInfo.color}>
                            {typeInfo.label}
                          </Badge>
                        </div>
                        <span className="font-medium text-sm">
                          {getSourceTitle(relation.targetId)}
                        </span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-muted-foreground">
                          Strength: {relation.strength}/10
                        </span>
                        <div className="w-16 h-2 bg-muted rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-primary transition-all"
                            style={{ width: `${(relation.strength / 10) * 100}%` }}
                          />
                        </div>
                      </div>
                    </div>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => setEditingRelation(relation)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => onDeleteRelation(relation.id)}>
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  
                  {relation.notes && (
                    <div className="mt-2 text-sm text-muted-foreground">
                      {relation.notes}
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Source Relationships</h3>
          <p className="text-sm text-muted-foreground">
            Visualize and manage connections between your sources
          </p>
        </div>
        
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              Add Relationship
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Relationship</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Source</Label>
                  <Select value={newRelation.sourceId} onValueChange={(value) => 
                    setNewRelation(prev => ({ ...prev, sourceId: value }))
                  }>
                    <SelectTrigger>
                      <SelectValue placeholder="Select source" />
                    </SelectTrigger>
                    <SelectContent>
                      {sources.map(source => (
                        <SelectItem key={source.id} value={source.id}>
                          {source.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>Target</Label>
                  <Select value={newRelation.targetId} onValueChange={(value) => 
                    setNewRelation(prev => ({ ...prev, targetId: value }))
                  }>
                    <SelectTrigger>
                      <SelectValue placeholder="Select target" />
                    </SelectTrigger>
                    <SelectContent>
                      {sources.filter(s => s.id !== newRelation.sourceId).map(source => (
                        <SelectItem key={source.id} value={source.id}>
                          {source.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label>Relationship Type</Label>
                <Select value={newRelation.type} onValueChange={(value: SourceRelation['type']) => 
                  setNewRelation(prev => ({ ...prev, type: value }))
                }>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {relationTypes.map(type => {
                      const IconComponent = type.icon;
                      return (
                        <SelectItem key={type.value} value={type.value}>
                          <div className="flex items-center gap-2">
                            <IconComponent className="h-4 w-4" />
                            {type.label}
                          </div>
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label>Strength (1-10)</Label>
                <Input
                  type="number"
                  min="1"
                  max="10"
                  value={newRelation.strength}
                  onChange={(e) => setNewRelation(prev => ({ 
                    ...prev, 
                    strength: parseInt(e.target.value) || 1 
                  }))}
                />
              </div>
              
              <div className="space-y-2">
                <Label>Notes (Optional)</Label>
                <Textarea
                  value={newRelation.notes}
                  onChange={(e) => setNewRelation(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="Add notes about this relationship..."
                />
              </div>
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateRelation}>
                  Create Relationship
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters and Controls */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search relationships..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={filterType} onValueChange={setFilterType}>
          <SelectTrigger className="w-[180px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            {relationTypes.map(type => (
              <SelectItem key={type.value} value={type.value}>
                {type.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <Select value={selectedSource || 'all'} onValueChange={(value) => 
          setSelectedSource(value === 'all' ? null : value)
        }>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Filter by source" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Sources</SelectItem>
            {sources.map(source => (
              <SelectItem key={source.id} value={source.id}>
                {source.title}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <div className="flex items-center gap-1 border rounded-md">
          <Button
            variant={viewMode === 'graph' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('graph')}
          >
            <Network className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <Eye className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{filteredRelations.length}</div>
            <div className="text-sm text-muted-foreground">Total Relationships</div>
          </CardContent>
        </Card>
        
        {relationTypes.slice(0, 3).map(type => {
          const count = filteredRelations.filter(r => r.type === type.value).length;
          return (
            <Card key={type.value}>
              <CardContent className="p-4">
                <div className="text-2xl font-bold">{count}</div>
                <div className="text-sm text-muted-foreground">{type.label}</div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Main Content */}
      {viewMode === 'graph' ? renderGraphView() : renderListView()}
    </div>
  );
};

export default SourceRelationshipView;