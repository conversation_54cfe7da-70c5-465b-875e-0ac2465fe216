import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  FileText, 
  Upload, 
  HardDrive, 
  Users, 
  TrendingUp, 
  Clock, 
  Tag,
  MoreHorizontal 
} from 'lucide-react';
import { OverviewStatsProps } from './types';

const OverviewStats: React.FC<OverviewStatsProps> = ({
  totalSources,
  recentUploads,
  storageUsed,
  collaborators,
}) => {
  // Mock data for recent activity - in real implementation this would come from props
  const recentActivity = [
    {
      id: '1',
      action: 'uploaded',
      item: 'Research Paper.pdf',
      user: '<PERSON>',
      time: '2 hours ago',
      type: 'pdf'
    },
    {
      id: '2',
      action: 'tagged',
      item: 'Meeting Notes.docx',
      user: '<PERSON>',
      time: '4 hours ago',
      type: 'doc'
    },
    {
      id: '3',
      action: 'shared',
      item: 'Project Specs.md',
      user: '<PERSON>',
      time: '6 hours ago',
      type: 'markdown'
    },
    {
      id: '4',
      action: 'archived',
      item: 'Old Dataset.csv',
      user: '<PERSON>',
      time: '1 day ago',
      type: 'data'
    }
  ];

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'pdf':
        return 'bg-red-100 text-red-800';
      case 'doc':
        return 'bg-blue-100 text-blue-800';
      case 'markdown':
        return 'bg-green-100 text-green-800';
      case 'data':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Total Sources */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sources</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalSources.toLocaleString()}</div>
            <div className="flex items-center text-xs text-muted-foreground mt-1">
              <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
              <span className="text-green-500">+12%</span>
              <span className="ml-1">from last month</span>
            </div>
          </CardContent>
        </Card>

        {/* Recent Uploads */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recent Uploads</CardTitle>
            <Upload className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{recentUploads}</div>
            <div className="flex items-center text-xs text-muted-foreground mt-1">
              <Clock className="h-3 w-3 mr-1" />
              <span>Last 7 days</span>
            </div>
          </CardContent>
        </Card>

        {/* Storage Used */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Storage Used</CardTitle>
            <HardDrive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{storageUsed}</div>
            <div className="flex items-center text-xs text-muted-foreground mt-1">
              <div className="w-full bg-muted rounded-full h-1.5 mr-2">
                <div className="bg-primary h-1.5 rounded-full" style={{ width: '68%' }} />
              </div>
              <span>68% of 10GB</span>
            </div>
          </CardContent>
        </Card>

        {/* Collaborators */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Collaborators</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{collaborators}</div>
            <div className="flex items-center text-xs text-muted-foreground mt-1">
              <span>Active this month</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Recent Activity</CardTitle>
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-center justify-between py-2">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className={getTypeColor(activity.type)}>
                      {activity.type.toUpperCase()}
                    </Badge>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm">
                      <span className="font-medium">{activity.user}</span>
                      <span className="text-muted-foreground"> {activity.action} </span>
                      <span className="font-medium">{activity.item}</span>
                    </p>
                  </div>
                </div>
                <div className="text-xs text-muted-foreground">
                  {activity.time}
                </div>
              </div>
            ))}
          </div>
          
          <div className="pt-4 border-t">
            <Button variant="ghost" className="w-full text-sm">
              View All Activity
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="cursor-pointer hover:bg-muted/50 transition-colors">
          <CardContent className="p-4 text-center">
            <div className="space-y-2">
              <div className="p-3 bg-blue-100 rounded-full w-fit mx-auto">
                <Upload className="h-6 w-6 text-blue-600" />
              </div>
              <h4 className="font-medium">Upload Sources</h4>
              <p className="text-xs text-muted-foreground">
                Add new documents, files, or web content
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:bg-muted/50 transition-colors">
          <CardContent className="p-4 text-center">
            <div className="space-y-2">
              <div className="p-3 bg-green-100 rounded-full w-fit mx-auto">
                <Tag className="h-6 w-6 text-green-600" />
              </div>
              <h4 className="font-medium">Organize Tags</h4>
              <p className="text-xs text-muted-foreground">
                Create and manage tags for better organization
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:bg-muted/50 transition-colors">
          <CardContent className="p-4 text-center">
            <div className="space-y-2">
              <div className="p-3 bg-purple-100 rounded-full w-fit mx-auto">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
              <h4 className="font-medium">Invite Team</h4>
              <p className="text-xs text-muted-foreground">
                Collaborate with team members on sources
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default OverviewStats;