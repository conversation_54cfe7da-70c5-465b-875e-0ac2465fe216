import React, { useState, useMemo, useCallback } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  ArrowLeftRight,
  FileText,
  Calendar,
  User,
  Tag,
  BarChart3,
  Eye,
  Download,
  Share,
  Copy,
  Check,
  X,
  Plus,
  Minus,
  Equal,
  Maximize2,
  Minimize2,
} from 'lucide-react';
import { Source } from './types';

interface SourceComparisonViewProps {
  sources: Source[];
  initialSourceIds?: [string, string];
  onSourceSelect?: (sourceId: string, position: 'left' | 'right') => void;
}

interface ComparisonMetrics {
  similarity: number;
  wordCount: [number, number];
  readingTime: [number, number];
  commonTags: string[];
  uniqueTags: [string[], string[]];
}

interface DiffLine {
  type: 'added' | 'removed' | 'unchanged' | 'modified';
  content: string;
  lineNumber: [number | null, number | null];
}

const SourceComparisonView: React.FC<SourceComparisonViewProps> = ({
  sources,
  initialSourceIds,
  onSourceSelect,
}) => {
  const [leftSourceId, setLeftSourceId] = useState<string>(initialSourceIds?.[0] || '');
  const [rightSourceId, setRightSourceId] = useState<string>(initialSourceIds?.[1] || '');
  const [viewMode, setViewMode] = useState<'side-by-side' | 'unified'>('side-by-side');
  const [showLineNumbers, setShowLineNumbers] = useState(true);
  const [highlightDifferences, setHighlightDifferences] = useState(true);
  const [activeTab, setActiveTab] = useState('content');
  const [isFullscreen, setIsFullscreen] = useState(false);

  const leftSource = sources.find(s => s.id === leftSourceId);
  const rightSource = sources.find(s => s.id === rightSourceId);

  // Mock content for demonstration - in real implementation, this would come from the source
  const getSourceContent = (source: Source | undefined): string => {
    if (!source) return '';
    return `# ${source.title}\n\nThis is the content of ${source.title}.\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\n\nUt enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.`;
  };

  const comparisonMetrics = useMemo((): ComparisonMetrics => {
    if (!leftSource || !rightSource) {
      return {
        similarity: 0,
        wordCount: [0, 0],
        readingTime: [0, 0],
        commonTags: [],
        uniqueTags: [[], []],
      };
    }

    const leftContent = getSourceContent(leftSource);
    const rightContent = getSourceContent(rightSource);
    
    const leftWords = leftContent.split(/\s+/).length;
    const rightWords = rightContent.split(/\s+/).length;
    
    const leftTags = leftSource.tags || [];
    const rightTags = rightSource.tags || [];
    const commonTags = leftTags.filter(tag => rightTags.includes(tag));
    const leftUnique = leftTags.filter(tag => !rightTags.includes(tag));
    const rightUnique = rightTags.filter(tag => !leftTags.includes(tag));
    
    // Simple similarity calculation based on common words
    const leftWordsSet = new Set(leftContent.toLowerCase().split(/\s+/));
    const rightWordsSet = new Set(rightContent.toLowerCase().split(/\s+/));
    const intersection = new Set([...leftWordsSet].filter(x => rightWordsSet.has(x)));
    const union = new Set([...leftWordsSet, ...rightWordsSet]);
    const similarity = union.size > 0 ? (intersection.size / union.size) * 100 : 0;

    return {
      similarity: Math.round(similarity),
      wordCount: [leftWords, rightWords],
      readingTime: [Math.ceil(leftWords / 200), Math.ceil(rightWords / 200)],
      commonTags,
      uniqueTags: [leftUnique, rightUnique],
    };
  }, [leftSource, rightSource]);

  const generateDiff = useMemo((): DiffLine[] => {
    if (!leftSource || !rightSource) return [];
    
    const leftLines = getSourceContent(leftSource).split('\n');
    const rightLines = getSourceContent(rightSource).split('\n');
    
    // Simple diff algorithm - in real implementation, use a proper diff library
    const diff: DiffLine[] = [];
    const maxLines = Math.max(leftLines.length, rightLines.length);
    
    for (let i = 0; i < maxLines; i++) {
      const leftLine = leftLines[i];
      const rightLine = rightLines[i];
      
      if (leftLine === rightLine) {
        diff.push({
          type: 'unchanged',
          content: leftLine || '',
          lineNumber: [i + 1, i + 1],
        });
      } else if (leftLine && rightLine) {
        diff.push({
          type: 'modified',
          content: `- ${leftLine}`,
          lineNumber: [i + 1, null],
        });
        diff.push({
          type: 'modified',
          content: `+ ${rightLine}`,
          lineNumber: [null, i + 1],
        });
      } else if (leftLine) {
        diff.push({
          type: 'removed',
          content: leftLine,
          lineNumber: [i + 1, null],
        });
      } else if (rightLine) {
        diff.push({
          type: 'added',
          content: rightLine,
          lineNumber: [null, i + 1],
        });
      }
    }
    
    return diff;
  }, [leftSource, rightSource]);

  const handleSourceSelect = useCallback((sourceId: string, position: 'left' | 'right') => {
    if (position === 'left') {
      setLeftSourceId(sourceId);
    } else {
      setRightSourceId(sourceId);
    }
    onSourceSelect?.(sourceId, position);
  }, [onSourceSelect]);

  const handleSwapSources = useCallback(() => {
    const temp = leftSourceId;
    setLeftSourceId(rightSourceId);
    setRightSourceId(temp);
  }, [leftSourceId, rightSourceId]);

  const renderSourceSelector = (position: 'left' | 'right', value: string) => {
    const availableSources = sources.filter(s => s.id !== (position === 'left' ? rightSourceId : leftSourceId));
    
    return (
      <Select value={value} onValueChange={(sourceId) => handleSourceSelect(sourceId, position)}>
        <SelectTrigger className="w-full">
          <SelectValue placeholder={`Select ${position} source`} />
        </SelectTrigger>
        <SelectContent>
          {availableSources.map(source => (
            <SelectItem key={source.id} value={source.id}>
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                <span>{source.title}</span>
                <Badge variant="outline" className="ml-auto">
                  {source.type}
                </Badge>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    );
  };

  const renderMetadata = (source: Source | undefined, position: 'left' | 'right') => {
    if (!source) return null;
    
    const metrics = position === 'left' ? 
      { words: comparisonMetrics.wordCount[0], readingTime: comparisonMetrics.readingTime[0] } :
      { words: comparisonMetrics.wordCount[1], readingTime: comparisonMetrics.readingTime[1] };
    
    return (
      <div className="space-y-3">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label className="text-xs text-muted-foreground">Type</Label>
            <Badge variant="outline">{source.type}</Badge>
          </div>
          <div>
            <Label className="text-xs text-muted-foreground">Size</Label>
            <div className="text-sm">{source.size}</div>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label className="text-xs text-muted-foreground">Words</Label>
            <div className="text-sm font-medium">{metrics.words.toLocaleString()}</div>
          </div>
          <div>
            <Label className="text-xs text-muted-foreground">Reading Time</Label>
            <div className="text-sm font-medium">{metrics.readingTime} min</div>
          </div>
        </div>
        
        <div>
          <Label className="text-xs text-muted-foreground">Created</Label>
          <div className="text-sm">{new Date(source.created_at).toLocaleDateString()}</div>
        </div>
        
        <div>
          <Label className="text-xs text-muted-foreground">Modified</Label>
          <div className="text-sm">{new Date(source.created_at).toLocaleDateString()}</div>
        </div>
        
        {source.tags && source.tags.length > 0 && (
          <div>
            <Label className="text-xs text-muted-foreground">Tags</Label>
            <div className="flex flex-wrap gap-1 mt-1">
              {source.tags.map(tag => (
                <Badge key={tag} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderSideBySideContent = () => {
    return (
      <div className="grid grid-cols-2 gap-4 h-full">
        <Card className="flex flex-col">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">
              {leftSource?.title || 'No source selected'}
            </CardTitle>
          </CardHeader>
          <CardContent className="flex-1">
            <ScrollArea className="h-full">
              <pre className="text-sm whitespace-pre-wrap">
                {getSourceContent(leftSource)}
              </pre>
            </ScrollArea>
          </CardContent>
        </Card>
        
        <Card className="flex flex-col">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">
              {rightSource?.title || 'No source selected'}
            </CardTitle>
          </CardHeader>
          <CardContent className="flex-1">
            <ScrollArea className="h-full">
              <pre className="text-sm whitespace-pre-wrap">
                {getSourceContent(rightSource)}
              </pre>
            </ScrollArea>
          </CardContent>
        </Card>
      </div>
    );
  };

  const renderUnifiedDiff = () => {
    return (
      <Card className="flex flex-col">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Unified Diff</CardTitle>
        </CardHeader>
        <CardContent className="flex-1">
          <ScrollArea className="h-full">
            <div className="space-y-1">
              {generateDiff.map((line, index) => {
                let className = 'text-sm font-mono px-2 py-1 ';
                let icon = null;
                
                switch (line.type) {
                  case 'added':
                    className += 'bg-green-50 text-green-800 border-l-2 border-green-500';
                    icon = <Plus className="h-3 w-3 text-green-600" />;
                    break;
                  case 'removed':
                    className += 'bg-red-50 text-red-800 border-l-2 border-red-500';
                    icon = <Minus className="h-3 w-3 text-red-600" />;
                    break;
                  case 'modified':
                    className += line.content.startsWith('+') ? 
                      'bg-green-50 text-green-800 border-l-2 border-green-500' :
                      'bg-red-50 text-red-800 border-l-2 border-red-500';
                    icon = line.content.startsWith('+') ? 
                      <Plus className="h-3 w-3 text-green-600" /> :
                      <Minus className="h-3 w-3 text-red-600" />;
                    break;
                  default:
                    className += 'text-muted-foreground';
                    icon = <Equal className="h-3 w-3 text-muted-foreground" />;
                }
                
                return (
                  <div key={index} className={className}>
                    <div className="flex items-start gap-2">
                      {showLineNumbers && (
                        <div className="flex gap-2 text-xs text-muted-foreground min-w-[60px]">
                          <span className="w-6 text-right">{line.lineNumber[0] || ''}</span>
                          <span className="w-6 text-right">{line.lineNumber[1] || ''}</span>
                        </div>
                      )}
                      {icon}
                      <span className="flex-1">{line.content}</span>
                    </div>
                  </div>
                );
              })}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    );
  };

  const renderComparisonStats = () => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
              <Label className="text-sm">Similarity</Label>
            </div>
            <div className="mt-2">
              <div className="text-2xl font-bold">{comparisonMetrics.similarity}%</div>
              <div className="w-full bg-muted rounded-full h-2 mt-1">
                <div 
                  className="bg-primary h-2 rounded-full transition-all"
                  style={{ width: `${comparisonMetrics.similarity}%` }}
                />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-muted-foreground" />
              <Label className="text-sm">Word Count</Label>
            </div>
            <div className="mt-2 space-y-1">
              <div className="flex justify-between text-sm">
                <span>Left:</span>
                <span className="font-medium">{comparisonMetrics.wordCount[0].toLocaleString()}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Right:</span>
                <span className="font-medium">{comparisonMetrics.wordCount[1].toLocaleString()}</span>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Tag className="h-4 w-4 text-muted-foreground" />
              <Label className="text-sm">Common Tags</Label>
            </div>
            <div className="mt-2">
              <div className="text-2xl font-bold">{comparisonMetrics.commonTags.length}</div>
              {comparisonMetrics.commonTags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {comparisonMetrics.commonTags.slice(0, 3).map(tag => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {comparisonMetrics.commonTags.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{comparisonMetrics.commonTags.length - 3}
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  if (!leftSource && !rightSource) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <ArrowLeftRight className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">Select Sources to Compare</h3>
          <p className="text-muted-foreground">Choose two sources to see a detailed comparison</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${isFullscreen ? 'fixed inset-0 z-50 bg-background p-6' : ''}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Source Comparison</h3>
          <p className="text-sm text-muted-foreground">
            Compare sources side-by-side to identify differences and similarities
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsFullscreen(!isFullscreen)}
          >
            {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Share className="h-4 w-4 mr-2" />
            Share
          </Button>
        </div>
      </div>

      {/* Source Selectors */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label>Left Source</Label>
          {renderSourceSelector('left', leftSourceId)}
        </div>
        
        <div className="space-y-2">
          <Label>Right Source</Label>
          <div className="flex gap-2">
            {renderSourceSelector('right', rightSourceId)}
            <Button
              variant="outline"
              size="sm"
              onClick={handleSwapSources}
              disabled={!leftSourceId || !rightSourceId}
            >
              <ArrowLeftRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Comparison Stats */}
      {leftSource && rightSource && renderComparisonStats()}

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <div className="flex items-center justify-between">
          <TabsList>
            <TabsTrigger value="content">Content</TabsTrigger>
            <TabsTrigger value="metadata">Metadata</TabsTrigger>
            <TabsTrigger value="diff">Diff</TabsTrigger>
          </TabsList>
          
          {activeTab === 'content' && (
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'side-by-side' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('side-by-side')}
              >
                Side by Side
              </Button>
              <Button
                variant={viewMode === 'unified' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('unified')}
              >
                Unified
              </Button>
            </div>
          )}
        </div>
        
        <TabsContent value="content" className="mt-4">
          <div className={isFullscreen ? 'h-[calc(100vh-200px)]' : 'h-96'}>
            {viewMode === 'side-by-side' ? renderSideBySideContent() : renderUnifiedDiff()}
          </div>
        </TabsContent>
        
        <TabsContent value="metadata" className="mt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Left Source Metadata</CardTitle>
              </CardHeader>
              <CardContent>
                {renderMetadata(leftSource, 'left')}
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Right Source Metadata</CardTitle>
              </CardHeader>
              <CardContent>
                {renderMetadata(rightSource, 'right')}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="diff" className="mt-4">
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="line-numbers"
                  checked={showLineNumbers}
                  onChange={(e) => setShowLineNumbers(e.target.checked)}
                  className="rounded"
                />
                <Label htmlFor="line-numbers" className="text-sm">
                  Show line numbers
                </Label>
              </div>
              
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="highlight-diff"
                  checked={highlightDifferences}
                  onChange={(e) => setHighlightDifferences(e.target.checked)}
                  className="rounded"
                />
                <Label htmlFor="highlight-diff" className="text-sm">
                  Highlight differences
                </Label>
              </div>
            </div>
            
            <div className={isFullscreen ? 'h-[calc(100vh-300px)]' : 'h-96'}>
              {renderUnifiedDiff()}
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SourceComparisonView;