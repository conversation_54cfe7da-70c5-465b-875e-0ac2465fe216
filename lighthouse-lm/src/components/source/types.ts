import React from 'react';

export interface SourceManagementDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  notebookId?: string;
  onSourceSelect?: (source: any) => void;
  onBulkOperation?: (operation: string, sourceIds: string[]) => void;
}

export interface SourceTemplate {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  fields: Array<{
    name: string;
    type: string;
    required: boolean;
    placeholder?: string;
  }>;
  category: string;
}

export interface SourceRelation {
  id: string;
  sourceId: string;
  targetId: string;
  type: 'references' | 'contradicts' | 'supports' | 'extends' | 'replaces';
  strength: number;
  notes?: string;
}

export interface SourceVersion {
  id: string;
  sourceId: string;
  version: number;
  timestamp: Date;
  changes: string;
  author: string;
  size: number;
}

export interface ExportFormat {
  id: string;
  name: string;
  extension: string;
  icon: React.ReactNode;
  description: string;
  available: boolean;
}

export interface IntegrationService {
  id: string;
  name: string;
  icon: React.ReactNode;
  connected: boolean;
  description: string;
  features: string[];
}

export type ViewMode = 'grid' | 'list' | 'kanban' | 'timeline' | 'graph';

export type FilterType = 'all' | 'pdf' | 'web' | 'doc' | 'code' | 'media';

export type SortBy = 'date' | 'name' | 'size' | 'type';

export interface DateRange {
  from: Date | undefined;
  to: Date | undefined;
}

export interface Source {
  id: string;
  title: string;
  content: string;
  type: string;
  created_at: string;
  size?: number;
  tags?: string[];
  status?: string;
  file_path?: string;
  file_size?: number;
}

export interface SourceCardProps {
  source: Source;
  isSelected: boolean;
  onSelect: (sourceId: string) => void;
  onVersionHistory: (source: Source) => void;
}

export interface SourceListItemProps {
  source: Source;
  isSelected: boolean;
  onSelect: (sourceId: string) => void;
}

export interface SourceToolbarProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  filterType: FilterType;
  onFilterChange: (type: FilterType) => void;
  sortBy: SortBy;
  onSortChange: (sort: SortBy) => void;
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
  selectedCount: number;
  totalCount: number;
  onSelectAll: () => void;
  onBulkOperation: (operation: string) => void;
  showAdvancedFilters: boolean;
  onToggleAdvancedFilters: () => void;
}

export interface AdvancedFiltersProps {
  dateRange: DateRange;
  onDateRangeChange: (range: DateRange) => void;
  tags: string[];
  onTagsChange: (tags: string[]) => void;
}

export interface FileUploadZoneProps {
  onDrop: (files: File[]) => void;
  isUploading: boolean;
  uploadProgress: number;
  isDragActive: boolean;
  getRootProps: () => any;
  getInputProps: () => any;
}

export interface SourceTemplateGridProps {
  templates: SourceTemplate[];
  onTemplateSelect: (template: SourceTemplate) => void;
}

export interface OverviewStatsProps {
  totalSources: number;
  recentUploads: number;
  storageUsed: string;
  collaborators: number;
}

export interface SidebarNavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  totalSources: number;
  selectedCount: number;
  storageUsed: string;
}

export interface NavigationItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  count?: number;
}