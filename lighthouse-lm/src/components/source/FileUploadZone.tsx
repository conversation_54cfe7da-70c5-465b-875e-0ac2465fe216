import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Upload, FileText, AlertCircle } from 'lucide-react';
import { FileUploadZoneProps } from './types';

const FileUploadZone: React.FC<FileUploadZoneProps> = ({
  onDrop,
  isUploading,
  uploadProgress,
  isDragActive,
  getRootProps,
  getInputProps,
}) => {
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      onDrop(Array.from(files));
    }
  };

  return (
    <div className="space-y-4">
      {/* Main Upload Zone */}
      <Card
        {...getRootProps()}
        className={`border-2 border-dashed transition-colors cursor-pointer ${
          isDragActive
            ? 'border-primary bg-primary/5'
            : 'border-muted-foreground/25 hover:border-primary/50'
        } ${
          isUploading ? 'pointer-events-none opacity-50' : ''
        }`}
      >
        <CardContent className="flex flex-col items-center justify-center py-12 px-6">
          <input {...getInputProps()} />
          
          <div className="flex flex-col items-center text-center space-y-4">
            <div className={`p-4 rounded-full ${
              isDragActive ? 'bg-primary/10' : 'bg-muted'
            }`}>
              <Upload className={`h-8 w-8 ${
                isDragActive ? 'text-primary' : 'text-muted-foreground'
              }`} />
            </div>
            
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">
                {isDragActive ? 'Drop files here' : 'Upload your sources'}
              </h3>
              <p className="text-muted-foreground max-w-sm">
                {isDragActive
                  ? 'Release to upload your files'
                  : 'Drag and drop files here, or click to browse and select files from your computer'
                }
              </p>
            </div>
            
            {!isDragActive && (
              <Button variant="outline" disabled={isUploading}>
                <FileText className="h-4 w-4 mr-2" />
                Choose Files
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Upload Progress */}
      {isUploading && (
        <Card>
          <CardContent className="p-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="font-medium">Uploading files...</span>
                <span className="text-muted-foreground">{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} className="h-2" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Supported File Types */}
      <Card className="bg-muted/30">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-muted-foreground mt-0.5" />
            <div className="space-y-1">
              <h4 className="text-sm font-medium">Supported file types</h4>
              <p className="text-xs text-muted-foreground">
                Documents: PDF, DOC, DOCX, TXT, MD, RTF
              </p>
              <p className="text-xs text-muted-foreground">
                Images: JPG, PNG, GIF, SVG, WEBP
              </p>
              <p className="text-xs text-muted-foreground">
                Code: JS, TS, PY, HTML, CSS, JSON, XML
              </p>
              <p className="text-xs text-muted-foreground">
                Archives: ZIP, RAR, 7Z
              </p>
              <p className="text-xs text-muted-foreground">
                Maximum file size: 100MB per file
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Alternative Upload Methods */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="cursor-pointer hover:bg-muted/50 transition-colors">
          <CardContent className="p-4 text-center">
            <div className="space-y-2">
              <div className="p-2 bg-blue-100 rounded-full w-fit mx-auto">
                <FileText className="h-5 w-5 text-blue-600" />
              </div>
              <h4 className="font-medium text-sm">Import from URL</h4>
              <p className="text-xs text-muted-foreground">
                Import content directly from web URLs
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:bg-muted/50 transition-colors">
          <CardContent className="p-4 text-center">
            <div className="space-y-2">
              <div className="p-2 bg-green-100 rounded-full w-fit mx-auto">
                <Upload className="h-5 w-5 text-green-600" />
              </div>
              <h4 className="font-medium text-sm">Cloud Storage</h4>
              <p className="text-xs text-muted-foreground">
                Connect Google Drive, Dropbox, OneDrive
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:bg-muted/50 transition-colors">
          <CardContent className="p-4 text-center">
            <div className="space-y-2">
              <div className="p-2 bg-purple-100 rounded-full w-fit mx-auto">
                <FileText className="h-5 w-5 text-purple-600" />
              </div>
              <h4 className="font-medium text-sm">GitHub Import</h4>
              <p className="text-xs text-muted-foreground">
                Import repositories and documentation
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default FileUploadZone;