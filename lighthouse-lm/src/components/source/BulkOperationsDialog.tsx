import React, { useState, useMemo, useEffect } from 'react';
import { tauriService } from '@/services/tauriService';
import { errorHandler } from '@/services/errorHandler';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  AlertTriangle,
  Archive,
  Copy,
  Download,
  Edit3,
  FileText,
  FolderPlus,
  Move,
  Plus,
  Settings,
  Tag,
  Trash2,
  Upload,
  Users,
  X,
  CheckCircle,
  Clock,
  AlertCircle,
} from 'lucide-react';
import { Source } from './types';

interface BulkOperationsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedSources: Source[];
  onOperationComplete?: (operation: string, results: any) => void;
}

type BulkOperationType = 
  | 'delete'
  | 'archive'
  | 'duplicate'
  | 'move'
  | 'export'
  | 'tag'
  | 'categorize'
  | 'share'
  | 'merge'
  | 'transform'
  | 'analyze'
  | 'backup';

interface BulkOperation {
  id: BulkOperationType;
  name: string;
  description: string;
  icon: React.ReactNode;
  category: 'management' | 'organization' | 'sharing' | 'analysis';
  requiresInput: boolean;
  destructive?: boolean;
  batchSize?: number;
}

interface OperationConfig {
  targetFolder?: string;
  newCategory?: string;
  tags?: string[];
  exportFormat?: string;
  shareSettings?: {
    permissions: 'read' | 'write' | 'admin';
    expiry?: string;
    password?: string;
  };
  mergeStrategy?: 'combine' | 'replace' | 'append';
  transformOptions?: {
    format: string;
    quality?: number;
    compression?: boolean;
  };
  analysisType?: 'content' | 'metadata' | 'relationships' | 'duplicates';
  backupLocation?: string;
}

interface OperationResult {
  sourceId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  message?: string;
  progress?: number;
}

const BULK_OPERATIONS: BulkOperation[] = [
  {
    id: 'delete',
    name: 'Delete Sources',
    description: 'Permanently remove selected sources',
    icon: <Trash2 className="h-4 w-4" />,
    category: 'management',
    requiresInput: false,
    destructive: true,
  },
  {
    id: 'archive',
    name: 'Archive Sources',
    description: 'Move sources to archive folder',
    icon: <Archive className="h-4 w-4" />,
    category: 'management',
    requiresInput: false,
  },
  {
    id: 'duplicate',
    name: 'Duplicate Sources',
    description: 'Create copies of selected sources',
    icon: <Copy className="h-4 w-4" />,
    category: 'management',
    requiresInput: false,
  },
  {
    id: 'move',
    name: 'Move to Folder',
    description: 'Move sources to a specific folder',
    icon: <Move className="h-4 w-4" />,
    category: 'organization',
    requiresInput: true,
  },
  {
    id: 'tag',
    name: 'Add Tags',
    description: 'Apply tags to multiple sources',
    icon: <Tag className="h-4 w-4" />,
    category: 'organization',
    requiresInput: true,
  },
  {
    id: 'categorize',
    name: 'Change Category',
    description: 'Update category for selected sources',
    icon: <FolderPlus className="h-4 w-4" />,
    category: 'organization',
    requiresInput: true,
  },
  {
    id: 'export',
    name: 'Bulk Export',
    description: 'Export sources in various formats',
    icon: <Download className="h-4 w-4" />,
    category: 'sharing',
    requiresInput: true,
  },
  {
    id: 'share',
    name: 'Bulk Share',
    description: 'Share sources with team members',
    icon: <Users className="h-4 w-4" />,
    category: 'sharing',
    requiresInput: true,
  },
  {
    id: 'merge',
    name: 'Merge Sources',
    description: 'Combine multiple sources into one',
    icon: <FileText className="h-4 w-4" />,
    category: 'management',
    requiresInput: true,
  },
  {
    id: 'analyze',
    name: 'Bulk Analysis',
    description: 'Run analysis on selected sources',
    icon: <Settings className="h-4 w-4" />,
    category: 'analysis',
    requiresInput: true,
  },
  {id: 'backup',
    name: 'Create Backup',
    description: 'Create backup of selected sources',
    icon: <Upload className="h-4 w-4" />,
    category: 'management',
    requiresInput: true,
  },
  {
    id: 'transform',
    name: 'Transform Sources',
    description: 'Transform sources to different formats',
    icon: <Edit3 className="h-4 w-4" />,
    category: 'management',
    requiresInput: true,
  },
];

// These will be populated from the backend
interface Folder {
  id: string;
  name: string;
  path: string;
}

const DEFAULT_CATEGORIES = [
  'document',
  'research',
  'reference',
  'template',
  'media',
  'code',
  'presentation',
];

const EXPORT_FORMATS = [
  { id: 'pdf', name: 'PDF', extension: '.pdf' },
  { id: 'docx', name: 'Word Document', extension: '.docx' },
  { id: 'txt', name: 'Plain Text', extension: '.txt' },
  { id: 'json', name: 'JSON', extension: '.json' },
  { id: 'csv', name: 'CSV', extension: '.csv' },
  { id: 'zip', name: 'ZIP Archive', extension: '.zip' },
];

export function BulkOperationsDialog({
  open,
  onOpenChange,
  selectedSources,
  onOperationComplete,
}: BulkOperationsDialogProps) {
  const [selectedOperation, setSelectedOperation] = useState<BulkOperationType | null>(null);
  const [config, setConfig] = useState<OperationConfig>({});
  const [isProcessing, setIsProcessing] = useState(false);
  const [results, setResults] = useState<OperationResult[]>([]);
  const [currentStep, setCurrentStep] = useState<'select' | 'configure' | 'confirm' | 'process' | 'complete'>('select');
  const [newTag, setNewTag] = useState('');
  const [folders, setFolders] = useState<Folder[]>([]);
  const [categories, setCategories] = useState<string[]>(DEFAULT_CATEGORIES);
  const [isLoadingData, setIsLoadingData] = useState(false);

  // Fetch folders and categories from backend
  useEffect(() => {
    const fetchData = async () => {
      if (!open) return;
      
      setIsLoadingData(true);
      try {
        // For now, we'll use default categories since there's no specific backend endpoint
        // In a real implementation, you would fetch these from the backend
        const defaultFolders: Folder[] = [
          { id: '1', name: 'Documents', path: '/documents' },
          { id: '2', name: 'Research', path: '/research' },
          { id: '3', name: 'Archive', path: '/archive' },
          { id: '4', name: 'Shared', path: '/shared' },
          { id: '5', name: 'Templates', path: '/templates' },
        ];
        setFolders(defaultFolders);
      } catch (error) {
        await errorHandler.handleError(error as Error, {
          component: 'BulkOperationsDialog.fetchData',
          operation: 'fetch_folders_categories'
        });
      } finally {
        setIsLoadingData(false);
      }
    };

    fetchData();
  }, [open]);

  const selectedOperationData = useMemo(() => {
    return BULK_OPERATIONS.find(op => op.id === selectedOperation);
  }, [selectedOperation]);

  const operationsByCategory = useMemo(() => {
    return BULK_OPERATIONS.reduce((acc, operation) => {
      if (!acc[operation.category]) {
        acc[operation.category] = [];
      }
      acc[operation.category].push(operation);
      return acc;
    }, {} as Record<string, BulkOperation[]>);
  }, []);

  const handleOperationSelect = (operationId: BulkOperationType) => {
    setSelectedOperation(operationId);
    setCurrentStep(BULK_OPERATIONS.find(op => op.id === operationId)?.requiresInput ? 'configure' : 'confirm');
  };

  const handleConfigUpdate = (updates: Partial<OperationConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };

  const handleAddTag = () => {
    if (newTag.trim()) {
      const currentTags = config.tags || [];
      if (!currentTags.includes(newTag.trim())) {
        handleConfigUpdate({ tags: [...currentTags, newTag.trim()] });
      }
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    const currentTags = config.tags || [];
    handleConfigUpdate({ tags: currentTags.filter(tag => tag !== tagToRemove) });
  };

  const handleExecuteOperation = async () => {
    setIsProcessing(true);
    setCurrentStep('process');
    
    // Initialize results
    const initialResults: OperationResult[] = selectedSources.map(source => ({
      sourceId: source.id,
      status: 'pending',
      progress: 0,
    }));
    setResults(initialResults);

    try {
      // Process sources in batches for better performance
      const batchSize = 5;
      for (let i = 0; i < selectedSources.length; i += batchSize) {
        const batch = selectedSources.slice(i, i + batchSize);
        
        // Process batch concurrently
        await Promise.all(batch.map(async (source) => {
          // Update to processing
          setResults(prev => prev.map(result => 
            result.sourceId === source.id 
              ? { ...result, status: 'processing', progress: 0 }
              : result
          ));

          try {
            // Simulate operation-specific processing
            await processSourceOperation(source, selectedOperation!, config);
            
            // Simulate progress updates
            for (let progress = 25; progress <= 100; progress += 25) {
              await new Promise(resolve => setTimeout(resolve, 200));
              setResults(prev => prev.map(result => 
                result.sourceId === source.id 
                  ? { ...result, progress }
                  : result
              ));
            }

            // Mark as completed
            setResults(prev => prev.map(result => 
              result.sourceId === source.id 
                ? { ...result, status: 'completed', progress: 100, message: getSuccessMessage(selectedOperation!) }
                : result
            ));
          } catch (error) {
            // Mark as failed
            setResults(prev => prev.map(result => 
              result.sourceId === source.id 
                ? { ...result, status: 'failed', message: error instanceof Error ? error.message : 'Operation failed' }
                : result
            ));
          }
        }));
      }
    } catch (error) {
      await errorHandler.handleError(error as Error, {
         component: 'BulkOperationsDialog.executeBulkOperation',
         operation: selectedOperation || 'unknown_bulk_operation'
       });
    } finally {
      setIsProcessing(false);
      setCurrentStep('complete');
      
      if (onOperationComplete) {
        onOperationComplete(selectedOperation!, results);
      }
    }
  };

  const processSourceOperation = async (source: any, operation: BulkOperationType, config: OperationConfig) => {
    // Simulate API calls based on operation type
    switch (operation) {
      case 'delete':
        // Simulate delete API call
        await new Promise(resolve => setTimeout(resolve, 500));
        break;
      case 'archive':
        // Simulate archive API call
        await new Promise(resolve => setTimeout(resolve, 300));
        break;
      case 'tag':
        if (!config.tags?.length) throw new Error('No tags specified');
        await new Promise(resolve => setTimeout(resolve, 200));
        break;
      case 'move':
        if (!config.targetFolder) throw new Error('No target folder specified');
        await new Promise(resolve => setTimeout(resolve, 400));
        break;
      case 'export':
        if (!config.exportFormat) throw new Error('No export format specified');
        await new Promise(resolve => setTimeout(resolve, 800));
        break;
      default:
        await new Promise(resolve => setTimeout(resolve, 300));
    }
  };

  const getSuccessMessage = (operation: BulkOperationType): string => {
    const messages = {
      delete: 'Successfully deleted',
      archive: 'Successfully archived',
      tag: 'Tags added successfully',
      move: 'Moved to folder successfully',
      export: 'Exported successfully',
      duplicate: 'Duplicated successfully',
      categorize: 'Category updated successfully',
      share: 'Shared successfully',
      merge: 'Merged successfully',
      analyze: 'Analysis completed',
      backup: 'Backup created successfully',
      transform: 'Transformed successfully',
    };
    return messages[operation] || 'Operation completed successfully';
  };



  const handleReset = () => {
    setSelectedOperation(null);
    setConfig({});
    setResults([]);
    setCurrentStep('select');
    setIsProcessing(false);
    setNewTag('');
  };

  const handleClose = () => {
    handleReset();
    onOpenChange(false);
  };

  const renderOperationSelection = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">Bulk Operations</h3>
        <p className="text-sm text-muted-foreground">
          Select an operation to perform on {selectedSources.length} source{selectedSources.length !== 1 ? 's' : ''}
        </p>
      </div>

      <Tabs defaultValue="management" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="management">Management</TabsTrigger>
          <TabsTrigger value="organization">Organization</TabsTrigger>
          <TabsTrigger value="sharing">Sharing</TabsTrigger>
          <TabsTrigger value="analysis">Analysis</TabsTrigger>
        </TabsList>

        {Object.entries(operationsByCategory).map(([category, operations]) => (
          <TabsContent key={category} value={category} className="space-y-3">
            {operations.map((operation) => (
              <Card 
                key={operation.id}
                className={`cursor-pointer transition-colors hover:bg-accent ${
                  operation.destructive ? 'border-destructive/20' : ''
                }`}
                onClick={() => handleOperationSelect(operation.id)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${
                      operation.destructive 
                        ? 'bg-destructive/10 text-destructive' 
                        : 'bg-primary/10 text-primary'
                    }`}>
                      {operation.icon}
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-sm">{operation.name}</CardTitle>
                      <CardDescription className="text-xs">
                        {operation.description}
                      </CardDescription>
                    </div>
                    {operation.destructive && (
                      <AlertTriangle className="h-4 w-4 text-destructive" />
                    )}
                  </div>
                </CardHeader>
              </Card>
            ))}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );

  const renderConfiguration = () => {
    if (!selectedOperationData) return null;

    return (
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-2">{selectedOperationData.name}</h3>
          <p className="text-sm text-muted-foreground">
            Configure settings for {selectedSources.length} source{selectedSources.length !== 1 ? 's' : ''}
          </p>
        </div>

        <div className="space-y-4">
          {selectedOperation === 'move' && (
            <div>
              <Label htmlFor="target-folder">Target Folder</Label>
              <Select onValueChange={(value) => handleConfigUpdate({ targetFolder: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select destination folder" />
                </SelectTrigger>
                <SelectContent>
                  {folders.map((folder) => (
                    <SelectItem key={folder.id} value={folder.id}>
                      {folder.name} ({folder.path})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {selectedOperation === 'categorize' && (
            <div>
              <Label htmlFor="new-category">New Category</Label>
              <Select onValueChange={(value) => handleConfigUpdate({ newCategory: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select new category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {selectedOperation === 'tag' && (
            <div className="space-y-3">
              <Label>Tags to Add</Label>
              <div className="flex gap-2">
                <Input
                  placeholder="Enter tag name"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                />
                <Button onClick={handleAddTag} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              {config.tags && config.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {config.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <X 
                        className="h-3 w-3 cursor-pointer" 
                        onClick={() => handleRemoveTag(tag)}
                      />
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          )}

          {selectedOperation === 'export' && (
            <div>
              <Label htmlFor="export-format">Export Format</Label>
              <Select onValueChange={(value) => handleConfigUpdate({ exportFormat: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select export format" />
                </SelectTrigger>
                <SelectContent>
                  {EXPORT_FORMATS.map((format) => (
                    <SelectItem key={format.id} value={format.id}>
                      {format.name} ({format.extension})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {selectedOperation === 'share' && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="permissions">Permissions</Label>
                <Select onValueChange={(value: 'read' | 'write' | 'admin') => 
                  handleConfigUpdate({ 
                    shareSettings: { 
                      permissions: value,
                      ...config.shareSettings
                    } 
                  })
                }>
                  <SelectTrigger>
                    <SelectValue placeholder="Select permissions" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="read">Read Only</SelectItem>
                    <SelectItem value="write">Read & Write</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="expiry">Link Expiry (Optional)</Label>
                <Input
                  type="date"
                  onChange={(e) => 
                    handleConfigUpdate({ 
                      shareSettings: { 
                        permissions: config.shareSettings?.permissions || 'read',
                        ...config.shareSettings, 
                        expiry: e.target.value 
                      } 
                    })
                  }
                />
              </div>
            </div>
          )}

          {selectedOperation === 'merge' && (
            <div>
              <Label htmlFor="merge-strategy">Merge Strategy</Label>
              <Select onValueChange={(value: 'combine' | 'replace' | 'append') => 
                handleConfigUpdate({ mergeStrategy: value })
              }>
                <SelectTrigger>
                  <SelectValue placeholder="Select merge strategy" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="combine">Combine Content</SelectItem>
                  <SelectItem value="replace">Replace Duplicates</SelectItem>
                  <SelectItem value="append">Append Content</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {selectedOperation === 'analyze' && (
            <div>
              <Label htmlFor="analysis-type">Analysis Type</Label>
              <Select onValueChange={(value: 'content' | 'metadata' | 'relationships' | 'duplicates') => 
                handleConfigUpdate({ analysisType: value })
              }>
                <SelectTrigger>
                  <SelectValue placeholder="Select analysis type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="content">Content Analysis</SelectItem>
                  <SelectItem value="metadata">Metadata Analysis</SelectItem>
                  <SelectItem value="relationships">Relationship Analysis</SelectItem>
                  <SelectItem value="duplicates">Duplicate Detection</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {selectedOperation === 'backup' && (
            <div>
              <Label htmlFor="backup-location">Backup Location</Label>
              <Input
                placeholder="Enter backup path or leave empty for default"
                onChange={(e) => handleConfigUpdate({ backupLocation: e.target.value })}
              />
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderConfirmation = () => {
    if (!selectedOperationData) return null;

    return (
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-2">Confirm Operation</h3>
          <p className="text-sm text-muted-foreground">
            Review the details before proceeding
          </p>
        </div>

        <Card>
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-lg ${
                selectedOperationData.destructive 
                  ? 'bg-destructive/10 text-destructive' 
                  : 'bg-primary/10 text-primary'
              }`}>
                {selectedOperationData.icon}
              </div>
              <div>
                <CardTitle className="text-base">{selectedOperationData.name}</CardTitle>
                <CardDescription>{selectedOperationData.description}</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-sm font-medium">Selected Sources</Label>
              <p className="text-sm text-muted-foreground">
                {selectedSources.length} source{selectedSources.length !== 1 ? 's' : ''} selected
              </p>
            </div>

            {Object.entries(config).map(([key, value]) => {
              if (!value) return null;
              
              return (
                <div key={key}>
                  <Label className="text-sm font-medium capitalize">
                    {key.replace(/([A-Z])/g, ' $1').toLowerCase()}
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    {Array.isArray(value) ? value.join(', ') : String(value)}
                  </p>
                </div>
              );
            })}

            {selectedOperationData.destructive && (
              <div className="flex items-center gap-2 p-3 bg-destructive/10 rounded-lg">
                <AlertTriangle className="h-4 w-4 text-destructive" />
                <p className="text-sm text-destructive">
                  This operation cannot be undone. Please proceed with caution.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  };

  const renderProcessing = () => {
    const completedCount = results.filter(r => r.status === 'completed').length;
    const failedCount = results.filter(r => r.status === 'failed').length;
    const processingCount = results.filter(r => r.status === 'processing').length;
    const overallProgress = (completedCount + failedCount) / results.length * 100;

    return (
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-2">Processing Operation</h3>
          <p className="text-sm text-muted-foreground">
            {isProcessing ? 'Please wait while we process your request...' : 'Operation completed'}
          </p>
        </div>

        <div className="space-y-4">
          <div>
            <div className="flex justify-between text-sm mb-2">
              <span>Overall Progress</span>
              <span>{Math.round(overallProgress)}%</span>
            </div>
            <Progress value={overallProgress} className="h-2" />
          </div>

          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="p-3 bg-green-50 rounded-lg">
              <CheckCircle className="h-5 w-5 text-green-600 mx-auto mb-1" />
              <p className="text-sm font-medium text-green-600">{completedCount}</p>
              <p className="text-xs text-green-600">Completed</p>
            </div>
            <div className="p-3 bg-blue-50 rounded-lg">
              <Clock className="h-5 w-5 text-blue-600 mx-auto mb-1" />
              <p className="text-sm font-medium text-blue-600">{processingCount}</p>
              <p className="text-xs text-blue-600">Processing</p>
            </div>
            <div className="p-3 bg-red-50 rounded-lg">
              <AlertCircle className="h-5 w-5 text-red-600 mx-auto mb-1" />
              <p className="text-sm font-medium text-red-600">{failedCount}</p>
              <p className="text-xs text-red-600">Failed</p>
            </div>
          </div>

          <ScrollArea className="h-48">
            <div className="space-y-2">
              {results.map((result) => {
                const source = selectedSources.find(s => s.id === result.sourceId);
                return (
                  <div key={result.sourceId} className="flex items-center gap-3 p-2 border rounded">
                    <div className="flex-shrink-0">
                      {result.status === 'completed' && <CheckCircle className="h-4 w-4 text-green-600" />}
                      {result.status === 'processing' && <Clock className="h-4 w-4 text-blue-600 animate-spin" />}
                      {result.status === 'failed' && <AlertCircle className="h-4 w-4 text-red-600" />}
                      {result.status === 'pending' && <Clock className="h-4 w-4 text-gray-400" />}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{source?.title}</p>
                      {result.message && (
                        <p className="text-xs text-muted-foreground">{result.message}</p>
                      )}
                    </div>
                    <Badge 
                      variant={result.status === 'completed' ? 'default' : 
                              result.status === 'failed' ? 'destructive' : 'secondary'}
                      className="text-xs"
                    >
                      {result.status}
                    </Badge>
                  </div>
                );
              })}
            </div>
          </ScrollArea>
        </div>
      </div>
    );
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'select':
        return renderOperationSelection();
      case 'configure':
        return renderConfiguration();
      case 'confirm':
        return renderConfirmation();
      case 'process':
      case 'complete':
        return renderProcessing();
      default:
        return null;
    }
  };

  const getStepButtons = () => {
    switch (currentStep) {
      case 'select':
        return (
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
        );
      case 'configure':
        return (
          <>
            <Button variant="outline" onClick={() => setCurrentStep('select')}>
              Back
            </Button>
            <Button onClick={() => setCurrentStep('confirm')}>
              Continue
            </Button>
          </>
        );
      case 'confirm':
        return (
          <>
            <Button 
              variant="outline" 
              onClick={() => setCurrentStep(selectedOperationData?.requiresInput ? 'configure' : 'select')}
            >
              Back
            </Button>
            <Button 
              onClick={handleExecuteOperation}
              variant={selectedOperationData?.destructive ? 'destructive' : 'default'}
            >
              {selectedOperationData?.destructive ? 'Confirm & Execute' : 'Execute Operation'}
            </Button>
          </>
        );
      case 'process':
        return (
          <Button variant="outline" disabled={isProcessing}>
            Processing...
          </Button>
        );
      case 'complete':
        return (
          <>
            <Button variant="outline" onClick={handleReset}>
              Start New Operation
            </Button>
            <Button onClick={handleClose}>
              Close
            </Button>
          </>
        );
      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Bulk Operations</DialogTitle>
          <DialogDescription>
            Perform operations on multiple sources at once
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="flex-1 px-1">
          {renderStepContent()}
        </ScrollArea>

        <Separator />
        
        <DialogFooter className="flex justify-between">
          {getStepButtons()}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}