import React from 'react';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Clock,
  FileText,
  Globe,
  HardDrive,
  Image,
  MoreVertical,
  Video,
} from 'lucide-react';
import { SourceListItemProps } from './types';

const SourceListItem: React.FC<SourceListItemProps> = ({
  source,
  isSelected,
  onSelect,
}) => {
  const getTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'pdf':
      case 'doc':
      case 'docx':
        return <FileText className="h-4 w-4" />;
      case 'web':
      case 'url':
        return <Globe className="h-4 w-4" />;
      case 'image':
      case 'jpg':
      case 'png':
      case 'gif':
        return <Image className="h-4 w-4" />;
      case 'video':
      case 'mp4':
      case 'avi':
        return <Video className="h-4 w-4" />;
      default:
        return <HardDrive className="h-4 w-4" />;
    }
  };

  const getTypeBadgeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'pdf':
        return 'bg-red-100 text-red-800';
      case 'web':
      case 'url':
        return 'bg-blue-100 text-blue-800';
      case 'doc':
      case 'docx':
        return 'bg-blue-100 text-blue-800';
      case 'image':
        return 'bg-green-100 text-green-800';
      case 'video':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown size';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div className={`flex items-center gap-4 p-4 border rounded-lg hover:bg-muted/50 transition-colors ${
      isSelected ? 'bg-muted border-primary' : ''
    }`}>
      <Checkbox
        checked={isSelected}
        onCheckedChange={() => onSelect(source.id)}
      />
      
      <div className="flex items-center gap-2">
        <Badge className={getTypeBadgeColor(source.type)}>
          {getTypeIcon(source.type)}
          <span className="ml-1">{source.type.toUpperCase()}</span>
        </Badge>
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <h3 className="font-medium truncate">{source.title}</h3>
          {source.tags && source.tags.length > 0 && (
            <div className="flex gap-1">
              {source.tags.slice(0, 2).map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {source.tags.length > 2 && (
                <Badge variant="outline" className="text-xs">
                  +{source.tags.length - 2}
                </Badge>
              )}
            </div>
          )}
        </div>
        {source.content && (
          <p className="text-sm text-muted-foreground truncate">
            {source.content.substring(0, 100)}...
          </p>
        )}
      </div>
      
      <div className="flex items-center gap-4 text-sm text-muted-foreground">
        <div className="flex items-center gap-1">
          <Clock className="h-3 w-3" />
          {formatDate(source.created_at)}
        </div>
        {source.file_size && (
          <span>{formatFileSize(source.file_size)}</span>
        )}
      </div>
      
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <MoreVertical className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem>View Details</DropdownMenuItem>
          <DropdownMenuItem>Edit Metadata</DropdownMenuItem>
          <DropdownMenuItem>Download</DropdownMenuItem>
          <DropdownMenuItem>Version History</DropdownMenuItem>
          <DropdownMenuItem className="text-destructive">
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default SourceListItem;