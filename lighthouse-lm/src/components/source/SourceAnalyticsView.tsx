import React, { useState, useMemo } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Calendar,
  FileText,
  Users,
  Clock,
  Tag,
  Download,
  Share,
  RefreshCw,
  Eye,
  Edit,
  Trash2,
  Plus,
  Minus,
  Activity,
  PieChart,
  LineChart,
} from 'lucide-react';
import { Source } from './types';

interface SourceAnalyticsViewProps {
  sources: Source[];
}

interface AnalyticsData {
  totalSources: number;
  totalSize: number;
  averageSize: number;
  typeDistribution: { type: string; count: number; percentage: number }[];
  tagDistribution: { tag: string; count: number }[];
  creationTrend: { date: string; count: number }[];
  sizeDistribution: { range: string; count: number }[];
  recentActivity: {
    date?: string;
    action: 'created' | 'updated' | 'deleted';
    source: string;
    type?: string;
  }[];
}

const SourceAnalyticsView: React.FC<SourceAnalyticsViewProps> = ({
  sources,
}) => {
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [activeTab, setActiveTab] = useState('overview');

  const analyticsData = useMemo((): AnalyticsData => {
    // Filter sources based on time range
    const now = new Date();
    const cutoffDate = new Date();
    
    switch (timeRange) {
      case '7d':
        cutoffDate.setDate(now.getDate() - 7);
        break;
      case '30d':
        cutoffDate.setDate(now.getDate() - 30);
        break;
      case '90d':
        cutoffDate.setDate(now.getDate() - 90);
        break;
      case '1y':
        cutoffDate.setFullYear(now.getFullYear() - 1);
        break;
    }

    const filteredSources = sources.filter(source =>
      source.created_at && new Date(source.created_at) >= cutoffDate
    );

    // Calculate type distribution
    const typeMap = new Map<string, number>();
    filteredSources.forEach(source => {
      typeMap.set(source.type, (typeMap.get(source.type) || 0) + 1);
    });
    
    const typeDistribution = Array.from(typeMap.entries()).map(([type, count]) => ({
      type,
      count,
      percentage: Math.round((count / filteredSources.length) * 100),
    })).sort((a, b) => b.count - a.count);

    // Calculate tag distribution
    const tagMap = new Map<string, number>();
    filteredSources.forEach(source => {
      source.tags?.forEach(tag => {
        tagMap.set(tag, (tagMap.get(tag) || 0) + 1);
      });
    });
    
    const tagDistribution = Array.from(tagMap.entries())
      .map(([tag, count]) => ({ tag, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Calculate creation trend
    let days: number;
    switch (timeRange) {
      case '7d':
        days = 7;
        break;
      case '30d':
        days = 30;
        break;
      case '90d':
        days = 90;
        break;
      case '1y':
        days = 365;
        break;
      default:
        days = 30;
    }
    const dateMap = new Map<string, number>();
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      dateMap.set(dateStr, 0);
    }
    
    filteredSources.forEach(source => {
      const dateStr = source.created_at ? new Date(source.created_at).toISOString().split('T')[0] : '';
      if (dateMap.has(dateStr)) {
        dateMap.set(dateStr, dateMap.get(dateStr)! + 1);
      }
    });
    
    const creationTrend = Array.from(dateMap.entries()).map(([date, count]) => ({
      date,
      count,
    }));

    // Calculate size distribution
    const sizeRanges = [
      { range: '< 1KB', min: 0, max: 1024 },
      { range: '1KB - 10KB', min: 1024, max: 10240 },
      { range: '10KB - 100KB', min: 10240, max: 102400 },
      { range: '100KB - 1MB', min: 102400, max: 1048576 },
      { range: '> 1MB', min: 1048576, max: Infinity },
    ];
    
    const sizeDistribution = sizeRanges.map(({ range, min, max }) => {
      const count = filteredSources.filter(source => {
        const size = source.file_size || 0;
        return size >= min && size < max;
      }).length;
      return { range, count };
    });

    // Calculate totals
    const totalSources = filteredSources.length;
    const totalSize = filteredSources.reduce((sum, source) => sum + (source.file_size || 0), 0);
    const averageSize = totalSources > 0 ? totalSize / totalSources : 0;

    // Generate recent activity (mock data)
    const recentActivity = filteredSources
      .slice(0, 10)
      .map(source => ({
        date: source.created_at,
        action: 'created' as const,
        source: source.title,
        type: source.type,
      }))
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    return {
      totalSources,
      totalSize,
      averageSize,
      typeDistribution,
      tagDistribution,
      creationTrend,
      sizeDistribution,
      recentActivity,
    };
  }, [sources, timeRange]);

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateStr: string): string => {
    return new Date(dateStr).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  };

  const getTypeColor = (type: string): string => {
    const colors: Record<string, string> = {
      pdf: 'bg-red-100 text-red-800',
      web: 'bg-blue-100 text-blue-800',
      doc: 'bg-green-100 text-green-800',
      code: 'bg-purple-100 text-purple-800',
      media: 'bg-orange-100 text-orange-800',
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const renderOverviewStats = () => {
    const previousPeriodSources = sources.filter(source => {
      const sourceDate = new Date(source.created_at);
      const now = new Date();
      const currentCutoff = new Date();
      const previousCutoff = new Date();
      
      switch (timeRange) {
        case '7d':
          currentCutoff.setDate(now.getDate() - 7);
          previousCutoff.setDate(now.getDate() - 14);
          break;
        case '30d':
          currentCutoff.setDate(now.getDate() - 30);
          previousCutoff.setDate(now.getDate() - 60);
          break;
        case '90d':
          currentCutoff.setDate(now.getDate() - 90);
          previousCutoff.setDate(now.getDate() - 180);
          break;
        case '1y':
          currentCutoff.setFullYear(now.getFullYear() - 1);
          previousCutoff.setFullYear(now.getFullYear() - 2);
          break;
      }
      
      return sourceDate >= previousCutoff && sourceDate < currentCutoff;
    }).length;
    
    const growth = previousPeriodSources > 0 
      ? ((analyticsData.totalSources - previousPeriodSources) / previousPeriodSources) * 100
      : 0;

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Sources</p>
                <p className="text-2xl font-bold">{analyticsData.totalSources.toLocaleString()}</p>
              </div>
              <FileText className="h-8 w-8 text-muted-foreground" />
            </div>
            <div className="flex items-center mt-2">
              {growth >= 0 ? (
                <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-600 mr-1" />
              )}
              <span className={`text-sm font-medium ${
                growth >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {Math.abs(growth).toFixed(1)}%
              </span>
              <span className="text-sm text-muted-foreground ml-1">
                vs previous period
              </span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Size</p>
                <p className="text-2xl font-bold">{formatBytes(analyticsData.totalSize)}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-muted-foreground" />
            </div>
            <div className="mt-2">
              <span className="text-sm text-muted-foreground">
                Avg: {formatBytes(analyticsData.averageSize)}
              </span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Most Common Type</p>
                <p className="text-2xl font-bold">
                  {analyticsData.typeDistribution[0]?.type || 'N/A'}
                </p>
              </div>
              <PieChart className="h-8 w-8 text-muted-foreground" />
            </div>
            <div className="mt-2">
              <span className="text-sm text-muted-foreground">
                {analyticsData.typeDistribution[0]?.percentage || 0}% of sources
              </span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Tags</p>
                <p className="text-2xl font-bold">{analyticsData.tagDistribution.length}</p>
              </div>
              <Tag className="h-8 w-8 text-muted-foreground" />
            </div>
            <div className="mt-2">
              <span className="text-sm text-muted-foreground">
                {analyticsData.tagDistribution[0]?.tag || 'None'} most used
              </span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  const renderTypeDistribution = () => {
    const maxCount = Math.max(...analyticsData.typeDistribution.map(t => t.count));
    
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PieChart className="h-5 w-5" />
            Source Type Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analyticsData.typeDistribution.map((item, index) => (
              <div key={item.type} className="flex items-center gap-4">
                <div className="w-16 text-sm font-medium">{item.type}</div>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <div className="flex-1 bg-muted rounded-full h-2 overflow-hidden">
                      <div 
                        className="h-full bg-primary transition-all"
                        style={{ width: `${(item.count / maxCount) * 100}%` }}
                      />
                    </div>
                    <div className="text-sm text-muted-foreground w-12 text-right">
                      {item.count}
                    </div>
                    <Badge className={getTypeColor(item.type)}>
                      {item.percentage}%
                    </Badge>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderCreationTrend = () => {
    const maxCount = Math.max(...analyticsData.creationTrend.map(t => t.count));
    
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <LineChart className="h-5 w-5" />
            Creation Trend
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-end gap-1 h-32">
              {analyticsData.creationTrend.map((item, index) => {
                const height = maxCount > 0 ? (item.count / maxCount) * 100 : 0;
                return (
                  <div key={item.date} className="flex-1 flex flex-col items-center">
                    <div 
                      className="w-full bg-primary rounded-t transition-all hover:bg-primary/80"
                      style={{ height: `${height}%`, minHeight: item.count > 0 ? '4px' : '0' }}
                      title={`${formatDate(item.date)}: ${item.count} sources`}
                    />
                    {index % Math.ceil(analyticsData.creationTrend.length / 7) === 0 && (
                      <div className="text-xs text-muted-foreground mt-1 rotate-45 origin-left">
                        {formatDate(item.date)}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderTagCloud = () => {
    const maxCount = Math.max(...analyticsData.tagDistribution.map(t => t.count));
    
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Tag className="h-5 w-5" />
            Popular Tags
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {analyticsData.tagDistribution.map((item) => {
              const size = Math.max(0.8, (item.count / maxCount) * 1.5);
              return (
                <Badge 
                  key={item.tag} 
                  variant="secondary"
                  className="transition-all hover:scale-105"
                  style={{ fontSize: `${size}rem` }}
                >
                  {item.tag} ({item.count})
                </Badge>
              );
            })}
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderSizeDistribution = () => {
    const maxCount = Math.max(...analyticsData.sizeDistribution.map(s => s.count));
    
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Size Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analyticsData.sizeDistribution.map((item) => (
              <div key={item.range} className="flex items-center gap-4">
                <div className="w-24 text-sm font-medium">{item.range}</div>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <div className="flex-1 bg-muted rounded-full h-2 overflow-hidden">
                      <div 
                        className="h-full bg-primary transition-all"
                        style={{ width: `${maxCount > 0 ? (item.count / maxCount) * 100 : 0}%` }}
                      />
                    </div>
                    <div className="text-sm text-muted-foreground w-8 text-right">
                      {item.count}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderRecentActivity = () => {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analyticsData.recentActivity.length === 0 ? (
              <div className="text-center py-4 text-muted-foreground">
                No recent activity
              </div>
            ) : (
              analyticsData.recentActivity.map((activity, index) => (
                <div key={index} className="flex items-center gap-3 p-2 rounded-lg hover:bg-muted/50">
                  <div className="flex-shrink-0">
                    {activity.action === 'created' && <Plus className="h-4 w-4 text-green-600" />}
                    {activity.action === 'updated' && <Edit className="h-4 w-4 text-blue-600" />}
                    {activity.action === 'deleted' && <Trash2 className="h-4 w-4 text-red-600" />}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium truncate">{activity.source}</div>
                    <div className="text-xs text-muted-foreground">
                      {activity.action} • {activity.type} • {formatDate(activity.date)}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Source Analytics</h3>
          <p className="text-sm text-muted-foreground">
            Insights and trends about your source collection
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={(value) => setTimeRange(value as '7d' | '30d' | '90d' | '1y')}>
            <SelectTrigger className="w-[120px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      {renderOverviewStats()}

      {/* Main Analytics */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="distribution">Distribution</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {renderTypeDistribution()}
            {renderTagCloud()}
          </div>
        </TabsContent>
        
        <TabsContent value="trends" className="mt-6">
          <div className="space-y-6">
            {renderCreationTrend()}
          </div>
        </TabsContent>
        
        <TabsContent value="distribution" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {renderSizeDistribution()}
            {renderTypeDistribution()}
          </div>
        </TabsContent>
        
        <TabsContent value="activity" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {renderRecentActivity()}
            {renderTagCloud()}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SourceAnalyticsView;