import React, { useState, useMemo } from 'react';
import { useAuth } from '@/services/auth';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Users,
  UserPlus,
  Settings,
  Share2,
  Lock,
  Unlock,
  Eye,
  Edit,
  Trash2,
  Crown,
  Shield,
  User,
  Mail,
  Copy,
  ExternalLink,
  Clock,
  CheckCircle,
  AlertCircle,
  MoreHorizontal,
  Search,
  Filter,
  Send,
  Link2,
  Globe,
  Building,
  Calendar,
  Activity,
  Bell,
  MessageSquare,
} from 'lucide-react';
import { Source } from './types';

interface CollaborationPanelProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedSources?: Source[];
  onInviteUser?: (invitation: UserInvitation) => Promise<void>;
  onUpdatePermissions?: (userId: string, permissions: UserPermissions) => Promise<void>;
  onRemoveUser?: (userId: string) => Promise<void>;
  onCreateShareLink?: (config: ShareLinkConfig) => Promise<string>;
}

interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'owner' | 'admin' | 'editor' | 'viewer';
  status: 'active' | 'pending' | 'inactive';
  lastActive: string;
  joinedAt: string;
}

interface UserPermissions {
  canView: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canShare: boolean;
  canManageUsers: boolean;
  canExport: boolean;
  canComment: boolean;
}

interface UserInvitation {
  email: string;
  role: 'admin' | 'editor' | 'viewer';
  permissions: UserPermissions;
  message?: string;
  expiresAt?: Date;
}

interface ShareLinkConfig {
  permissions: UserPermissions;
  expiresAt?: Date;
  password?: string;
  allowAnonymous: boolean;
  maxUses?: number;
}

interface ShareLink {
  id: string;
  url: string;
  permissions: UserPermissions;
  createdBy: string;
  createdAt: string;
  expiresAt?: string;
  uses: number;
  maxUses?: number;
  isActive: boolean;
}

const CollaborationPanel: React.FC<CollaborationPanelProps> = ({
  open,
  onOpenChange,
  selectedSources = [],
  onInviteUser,
  onUpdatePermissions,
  onRemoveUser,
  onCreateShareLink,
}) => {
  const [activeTab, setActiveTab] = useState('users');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRole, setSelectedRole] = useState('all');
  const [showInviteDialog, setShowInviteDialog] = useState(false);
  const [showShareDialog, setShowShareDialog] = useState(false);
  const [inviteData, setInviteData] = useState<Partial<UserInvitation>>({});
  const [shareConfig, setShareConfig] = useState<Partial<ShareLinkConfig>>({});
  const [isInviting, setIsInviting] = useState(false);
  const [isCreatingLink, setIsCreatingLink] = useState(false);

  const { user: currentUser } = useAuth();
  
  // Get users from collaboration data (would come from backend in real implementation)
  const users: User[] = useMemo(() => {
    if (!currentUser) return [];
    
    // Include current user as owner
    const currentUserData: User = {
      id: currentUser.id,
      name: currentUser.full_name || currentUser.username,
      email: currentUser.email,
      avatar: currentUser.avatar_url,
      role: 'owner',
      status: 'active',
      lastActive: 'Now',
      joinedAt: new Date(currentUser.created_at).toLocaleDateString(),
    };
    
    // In a real implementation, you would fetch collaboration users from the backend
    // For now, we'll show just the current user
    return [currentUserData];
  }, [currentUser]);

  // Mock data for share links
  const shareLinks: ShareLink[] = [
    {
      id: '1',
      url: 'https://app.example.com/share/abc123',
      permissions: {
        canView: true,
        canEdit: false,
        canDelete: false,
        canShare: false,
        canManageUsers: false,
        canExport: true,
        canComment: true,
      },
      createdBy: 'John Doe',
      createdAt: '2024-02-15',
      expiresAt: '2024-03-15',
      uses: 12,
      maxUses: 50,
      isActive: true,
    },
    {
      id: '2',
      url: 'https://app.example.com/share/def456',
      permissions: {
        canView: true,
        canEdit: true,
        canDelete: false,
        canShare: false,
        canManageUsers: false,
        canExport: false,
        canComment: true,
      },
      createdBy: 'Jane Smith',
      createdAt: '2024-02-10',
      uses: 5,
      isActive: true,
    },
  ];

  const rolePermissions: Record<string, UserPermissions> = {
    admin: {
      canView: true,
      canEdit: true,
      canDelete: true,
      canShare: true,
      canManageUsers: true,
      canExport: true,
      canComment: true,
    },
    editor: {
      canView: true,
      canEdit: true,
      canDelete: false,
      canShare: true,
      canManageUsers: false,
      canExport: true,
      canComment: true,
    },
    viewer: {
      canView: true,
      canEdit: false,
      canDelete: false,
      canShare: false,
      canManageUsers: false,
      canExport: false,
      canComment: true,
    },
  };

  const filteredUsers = useMemo(() => {
    return users.filter(user => {
      const matchesSearch = user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           user.email.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesRole = selectedRole === 'all' || user.role === selectedRole;
      return matchesSearch && matchesRole;
    });
  }, [searchQuery, selectedRole]);

  const handleInviteUser = async () => {
    if (!inviteData.email || !inviteData.role) return;
    
    setIsInviting(true);
    try {
      const invitation: UserInvitation = {
        email: inviteData.email,
        role: inviteData.role,
        permissions: rolePermissions[inviteData.role],
        message: inviteData.message,
        expiresAt: inviteData.expiresAt,
      };
      
      await onInviteUser?.(invitation);
      setShowInviteDialog(false);
      setInviteData({});
    } catch (error) {
      console.error('Failed to invite user:', error);
    } finally {
      setIsInviting(false);
    }
  };

  const handleCreateShareLink = async () => {
    setIsCreatingLink(true);
    try {
      const config: ShareLinkConfig = {
        permissions: shareConfig.permissions || rolePermissions.viewer,
        expiresAt: shareConfig.expiresAt,
        password: shareConfig.password,
        allowAnonymous: shareConfig.allowAnonymous || false,
        maxUses: shareConfig.maxUses,
      };
      
      const url = await onCreateShareLink?.(config);
      console.log('Share link created:', url);
      setShowShareDialog(false);
      setShareConfig({});
    } catch (error) {
      console.error('Failed to create share link:', error);
    } finally {
      setIsCreatingLink(false);
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner':
        return <Crown className="h-4 w-4 text-yellow-500" />;
      case 'admin':
        return <Shield className="h-4 w-4 text-blue-500" />;
      case 'editor':
        return <Edit className="h-4 w-4 text-green-500" />;
      case 'viewer':
        return <Eye className="h-4 w-4 text-gray-500" />;
      default:
        return <User className="h-4 w-4" />;
    }
  };

  const getRoleBadge = (role: string) => {
    const variants: Record<string, any> = {
      owner: 'default',
      admin: 'secondary',
      editor: 'outline',
      viewer: 'outline',
    };
    
    return (
      <Badge variant={variants[role]} className="text-xs flex items-center gap-1">
        {getRoleIcon(role)}
        {role.charAt(0).toUpperCase() + role.slice(1)}
      </Badge>
    );
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="text-xs">Active</Badge>;
      case 'pending':
        return <Badge variant="secondary" className="text-xs">Pending</Badge>;
      case 'inactive':
        return <Badge variant="outline" className="text-xs">Inactive</Badge>;
      default:
        return null;
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Collaboration
            </DialogTitle>
            <DialogDescription>
              Manage team access and sharing for {selectedSources.length} source{selectedSources.length !== 1 ? 's' : ''}
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-hidden">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="users">Team Members ({users.length})</TabsTrigger>
                <TabsTrigger value="links">Share Links ({shareLinks.length})</TabsTrigger>
                <TabsTrigger value="activity">Activity</TabsTrigger>
              </TabsList>

              <div className="flex-1 overflow-hidden mt-4">
                <TabsContent value="users" className="h-full">
                  <div className="space-y-4 h-full flex flex-col">
                    {/* Search and Actions */}
                    <div className="flex gap-4">
                      <div className="flex-1 relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Search team members..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                      <Select value={selectedRole} onValueChange={setSelectedRole}>
                        <SelectTrigger className="w-40">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Roles</SelectItem>
                          <SelectItem value="owner">Owner</SelectItem>
                          <SelectItem value="admin">Admin</SelectItem>
                          <SelectItem value="editor">Editor</SelectItem>
                          <SelectItem value="viewer">Viewer</SelectItem>
                        </SelectContent>
                      </Select>
                      <Button onClick={() => setShowInviteDialog(true)}>
                        <UserPlus className="h-4 w-4 mr-2" />
                        Invite User
                      </Button>
                    </div>

                    {/* Users List */}
                    <ScrollArea className="flex-1">
                      <div className="space-y-3">
                        {filteredUsers.map(user => (
                          <Card key={user.id}>
                            <CardContent className="p-4">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                  <Avatar className="h-10 w-10">
                                    <AvatarImage src={user.avatar} alt={user.name} />
                                    <AvatarFallback>
                                      {user.name.split(' ').map(n => n[0]).join('')}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div className="flex-1">
                                    <div className="flex items-center gap-2">
                                      <h4 className="font-medium">{user.name}</h4>
                                      {getRoleBadge(user.role)}
                                      {getStatusBadge(user.status)}
                                    </div>
                                    <p className="text-sm text-muted-foreground">{user.email}</p>
                                    <p className="text-xs text-muted-foreground">
                                      Last active: {user.lastActive} • Joined {user.joinedAt}
                                    </p>
                                  </div>
                                </div>
                                
                                <div className="flex items-center gap-2">
                                  {user.role !== 'owner' && (
                                    <DropdownMenu>
                                      <DropdownMenuTrigger asChild>
                                        <Button variant="ghost" size="sm">
                                          <MoreHorizontal className="h-4 w-4" />
                                        </Button>
                                      </DropdownMenuTrigger>
                                      <DropdownMenuContent align="end">
                                        <DropdownMenuItem>
                                          <Settings className="h-4 w-4 mr-2" />
                                          Edit Permissions
                                        </DropdownMenuItem>
                                        <DropdownMenuItem>
                                          <Mail className="h-4 w-4 mr-2" />
                                          Send Message
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem className="text-red-600">
                                          <Trash2 className="h-4 w-4 mr-2" />
                                          Remove User
                                        </DropdownMenuItem>
                                      </DropdownMenuContent>
                                    </DropdownMenu>
                                  )}
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                </TabsContent>

                <TabsContent value="links" className="h-full">
                  <div className="space-y-4 h-full flex flex-col">
                    {/* Actions */}
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="text-lg font-medium">Share Links</h3>
                        <p className="text-sm text-muted-foreground">
                          Create secure links to share sources with external users
                        </p>
                      </div>
                      <Button onClick={() => setShowShareDialog(true)}>
                        <Link2 className="h-4 w-4 mr-2" />
                        Create Link
                      </Button>
                    </div>

                    {/* Share Links List */}
                    <ScrollArea className="flex-1">
                      <div className="space-y-3">
                        {shareLinks.length === 0 ? (
                          <div className="text-center py-12">
                            <Link2 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                            <h3 className="text-lg font-medium mb-2">No share links created</h3>
                            <p className="text-muted-foreground mb-4">
                              Create secure links to share sources with external users
                            </p>
                            <Button onClick={() => setShowShareDialog(true)}>
                              Create First Link
                            </Button>
                          </div>
                        ) : (
                          shareLinks.map(link => (
                            <Card key={link.id}>
                              <CardContent className="p-4">
                                <div className="space-y-3">
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                      <Globe className="h-4 w-4 text-muted-foreground" />
                                      <span className="font-medium">Public Link</span>
                                      {link.isActive ? (
                                        <Badge variant="default" className="text-xs">Active</Badge>
                                      ) : (
                                        <Badge variant="secondary" className="text-xs">Inactive</Badge>
                                      )}
                                    </div>
                                    <DropdownMenu>
                                      <DropdownMenuTrigger asChild>
                                        <Button variant="ghost" size="sm">
                                          <MoreHorizontal className="h-4 w-4" />
                                        </Button>
                                      </DropdownMenuTrigger>
                                      <DropdownMenuContent align="end">
                                        <DropdownMenuItem onClick={() => copyToClipboard(link.url)}>
                                          <Copy className="h-4 w-4 mr-2" />
                                          Copy Link
                                        </DropdownMenuItem>
                                        <DropdownMenuItem>
                                          <Settings className="h-4 w-4 mr-2" />
                                          Edit Settings
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem className="text-red-600">
                                          <Trash2 className="h-4 w-4 mr-2" />
                                          Delete Link
                                        </DropdownMenuItem>
                                      </DropdownMenuContent>
                                    </DropdownMenu>
                                  </div>
                                  
                                  <div className="bg-muted p-3 rounded-md">
                                    <div className="flex items-center gap-2">
                                      <code className="text-sm flex-1 truncate">{link.url}</code>
                                      <Button 
                                        variant="ghost" 
                                        size="sm"
                                        onClick={() => copyToClipboard(link.url)}
                                      >
                                        <Copy className="h-4 w-4" />
                                      </Button>
                                    </div>
                                  </div>
                                  
                                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                                    <div className="flex items-center gap-4">
                                      <span>Created by {link.createdBy}</span>
                                      <span>•</span>
                                      <span>{link.uses} uses</span>
                                      {link.maxUses && <span>/ {link.maxUses} max</span>}
                                      {link.expiresAt && (
                                        <>
                                          <span>•</span>
                                          <span>Expires {link.expiresAt}</span>
                                        </>
                                      )}
                                    </div>
                                    <div className="flex items-center gap-2">
                                      {link.permissions.canView && <Eye className="h-3 w-3" />}
                                      {link.permissions.canEdit && <Edit className="h-3 w-3" />}
                                      {link.permissions.canComment && <MessageSquare className="h-3 w-3" />}
                                    </div>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          ))
                        )}
                      </div>
                    </ScrollArea>
                  </div>
                </TabsContent>

                <TabsContent value="activity" className="h-full">
                  <ScrollArea className="h-full">
                    <div className="space-y-4">
                      <div className="text-center py-12">
                        <Activity className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium mb-2">Activity Feed</h3>
                        <p className="text-muted-foreground">
                          Recent collaboration activity will appear here
                        </p>
                      </div>
                    </div>
                  </ScrollArea>
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </DialogContent>
      </Dialog>

      {/* Invite User Dialog */}
      <Dialog open={showInviteDialog} onOpenChange={setShowInviteDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <UserPlus className="h-5 w-5" />
              Invite Team Member
            </DialogTitle>
            <DialogDescription>
              Send an invitation to collaborate on these sources
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={inviteData.email || ''}
                onChange={(e) => setInviteData(prev => ({ ...prev, email: e.target.value }))}
              />
            </div>

            <div>
              <Label htmlFor="role">Role</Label>
              <Select value={inviteData.role} onValueChange={(role: any) => setInviteData(prev => ({ ...prev, role }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="admin">Admin - Full access</SelectItem>
                  <SelectItem value="editor">Editor - Can edit and share</SelectItem>
                  <SelectItem value="viewer">Viewer - Read-only access</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="message">Personal Message (Optional)</Label>
              <Textarea
                id="message"
                placeholder="Add a personal message to the invitation..."
                value={inviteData.message || ''}
                onChange={(e) => setInviteData(prev => ({ ...prev, message: e.target.value }))}
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowInviteDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleInviteUser} disabled={isInviting || !inviteData.email || !inviteData.role}>
              {isInviting ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Send Invitation
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Create Share Link Dialog */}
      <Dialog open={showShareDialog} onOpenChange={setShowShareDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Link2 className="h-5 w-5" />
              Create Share Link
            </DialogTitle>
            <DialogDescription>
              Generate a secure link to share these sources
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label>Permissions</Label>
              <div className="space-y-2 mt-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="can-view" className="text-sm">Can view</Label>
                  <Switch
                    id="can-view"
                    checked={shareConfig.permissions?.canView ?? true}
                    onCheckedChange={(checked) => setShareConfig(prev => ({
                      ...prev,
                      permissions: { 
                        canView: true,
                        canEdit: false,
                        canDelete: false,
                        canShare: false,
                        canManageUsers: false,
                        canExport: false,
                        ...prev.permissions, 
                        canView: checked 
                      } as UserPermissions
                    }))}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="can-edit" className="text-sm">Can edit</Label>
                  <Switch
                    id="can-edit"
                    checked={shareConfig.permissions?.canEdit ?? false}
                    onCheckedChange={(checked) => setShareConfig(prev => ({
                      ...prev,
                      permissions: { 
                        canView: true,
                        canEdit: false,
                        canDelete: false,
                        canShare: false,
                        canManageUsers: false,
                        canExport: false,
                        ...prev.permissions, 
                        canEdit: checked 
                      } as UserPermissions
                    }))}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="can-comment" className="text-sm">Can comment</Label>
                  <Switch
                    id="can-comment"
                    checked={shareConfig.permissions?.canComment ?? true}
                    onCheckedChange={(checked) => setShareConfig(prev => ({
                      ...prev,
                      permissions: { 
                        canView: true,
                        canEdit: false,
                        canDelete: false,
                        canShare: false,
                        canManageUsers: false,
                        canExport: false,
                        ...prev.permissions, 
                        canComment: checked 
                      } as UserPermissions
                    }))}
                  />
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <Label htmlFor="max-uses">Maximum Uses (Optional)</Label>
              <Input
                id="max-uses"
                type="number"
                placeholder="Unlimited"
                value={shareConfig.maxUses || ''}
                onChange={(e) => setShareConfig(prev => ({ 
                  ...prev, 
                  maxUses: e.target.value ? Number(e.target.value) : undefined 
                }))}
              />
            </div>

            <div>
              <Label htmlFor="password">Password Protection (Optional)</Label>
              <Input
                id="password"
                type="password"
                placeholder="Enter password"
                value={shareConfig.password || ''}
                onChange={(e) => setShareConfig(prev => ({ ...prev, password: e.target.value }))}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowShareDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateShareLink} disabled={isCreatingLink}>
              {isCreatingLink ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Link2 className="h-4 w-4 mr-2" />
                  Create Link
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default CollaborationPanel;