import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Archive,
  ChevronDown,
  Filter,
  Grid,
  List,
  MoreHorizontal,
  Search,
  Tag,
  Trash2,
} from 'lucide-react';
import { SourceToolbarProps, FilterType, SortBy, ViewMode } from './types';

const SourceToolbar: React.FC<SourceToolbarProps> = ({
  searchQuery,
  onSearchChange,
  filterType,
  onFilterChange,
  sortBy,
  onSortChange,
  viewMode,
  onViewModeChange,
  selectedCount,
  totalCount,
  onSelectAll,
  onBulkOperation,
  showAdvancedFilters,
  onToggleAdvancedFilters,
}) => {
  const filterOptions: { value: FilterType; label: string }[] = [
    { value: 'all', label: 'All Types' },
    { value: 'pdf', label: 'PDF' },
    { value: 'web', label: 'Web' },
    { value: 'doc', label: 'Documents' },
    { value: 'code', label: 'Code' },
    { value: 'media', label: 'Media' },
  ];

  const sortOptions: { value: SortBy; label: string }[] = [
    { value: 'date', label: 'Date' },
    { value: 'name', label: 'Name' },
    { value: 'size', label: 'Size' },
    { value: 'type', label: 'Type' },
  ];

  const viewModeOptions: { value: ViewMode; label: string; icon: React.ReactNode }[] = [
    { value: 'grid', label: 'Grid', icon: <Grid className="h-4 w-4" /> },
    { value: 'list', label: 'List', icon: <List className="h-4 w-4" /> },
  ];

  return (
    <div className="space-y-4">
      {/* Main Toolbar */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center gap-2 flex-1">
          {/* Search */}
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search sources..."
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Filter by Type */}
          <Select value={filterType} onValueChange={onFilterChange}>
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {filterOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Sort By */}
          <Select value={sortBy} onValueChange={onSortChange}>
            <SelectTrigger className="w-[120px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {sortOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Advanced Filters Toggle */}
          <Button
            variant="outline"
            size="sm"
            onClick={onToggleAdvancedFilters}
            className={showAdvancedFilters ? 'bg-muted' : ''}
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
            <ChevronDown className="h-4 w-4 ml-2" />
          </Button>
        </div>

        <div className="flex items-center gap-2">
          {/* Selection Info */}
          {selectedCount > 0 && (
            <Badge variant="secondary">
              {selectedCount} of {totalCount} selected
            </Badge>
          )}

          {/* Bulk Operations */}
          {selectedCount > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <MoreHorizontal className="h-4 w-4 mr-2" />
                  Actions
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => onBulkOperation('archive')}>
                  <Archive className="h-4 w-4 mr-2" />
                  Archive Selected
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onBulkOperation('tag')}>
                  <Tag className="h-4 w-4 mr-2" />
                  Add Tags
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={() => onBulkOperation('delete')}
                  className="text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Selected
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* Select All */}
          <Button variant="outline" size="sm" onClick={onSelectAll}>
            {selectedCount === totalCount ? 'Deselect All' : 'Select All'}
          </Button>

          {/* View Mode Toggle */}
          <div className="flex border rounded-md">
            {viewModeOptions.map((option) => (
              <Button
                key={option.value}
                variant={viewMode === option.value ? 'default' : 'ghost'}
                size="sm"
                onClick={() => onViewModeChange(option.value)}
                className="rounded-none first:rounded-l-md last:rounded-r-md"
              >
                {option.icon}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* Results Summary */}
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <span>
          {totalCount} source{totalCount !== 1 ? 's' : ''} found
          {filterType !== 'all' && ` (filtered by ${filterType})`}
        </span>
        {selectedCount > 0 && (
          <span>{selectedCount} selected</span>
        )}
      </div>
    </div>
  );
};

export default SourceToolbar;