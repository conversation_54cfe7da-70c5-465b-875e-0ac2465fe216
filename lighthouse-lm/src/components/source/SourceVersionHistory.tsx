import React, { useState, useMemo } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  History,
  Clock,
  User,
  GitBranch,
  GitCommit,
  Download,
  RotateCcw,
  Eye,
  FileText,
  Calendar,
  Search,
  Filter,
  MoreHorizontal,
  ArrowRight,
  ArrowLeft,
  Diff,
  Plus,
  Minus,
  AlertTriangle,
  CheckCircle,
  Info,
  Tag,
  Bookmark,
  Archive,
  Trash2,
  Copy,
  ExternalLink,
  RefreshCw,
  Activity,
  Users,
  MessageSquare,
} from 'lucide-react';
import { Source } from './types';

interface SourceVersionHistoryProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  source: Source;
  onRestoreVersion?: (versionId: string) => Promise<void>;
  onCompareVersions?: (versionA: string, versionB: string) => void;
  onCreateTag?: (versionId: string, tag: VersionTag) => Promise<void>;
  onDeleteVersion?: (versionId: string) => Promise<void>;
}

interface SourceVersion {
  id: string;
  version: string;
  created_at: string;
  author: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  changeType: 'created' | 'updated' | 'metadata' | 'content' | 'restored' | 'tagged';
  changes: VersionChange[];
  size: number;
  checksum: string;
  message?: string;
  tags: VersionTag[];
  isActive: boolean;
  parentVersion?: string;
  branchName?: string;
}

interface VersionChange {
  type: 'added' | 'modified' | 'deleted' | 'renamed';
  field: string;
  oldValue?: any;
  newValue?: any;
  description: string;
}

interface VersionTag {
  id: string;
  name: string;
  color: string;
  description?: string;
  created_at: string;
  created_by: string;
}

interface VersionComparison {
  versionA: SourceVersion;
  versionB: SourceVersion;
  differences: VersionDifference[];
}

interface VersionDifference {
  field: string;
  type: 'added' | 'removed' | 'modified';
  oldValue?: any;
  newValue?: any;
  context?: string;
}

const SourceVersionHistory: React.FC<SourceVersionHistoryProps> = ({
  open,
  onOpenChange,
  source,
  onRestoreVersion,
  onCompareVersions,
  onCreateTag,
  onDeleteVersion,
}) => {
  const [activeTab, setActiveTab] = useState('timeline');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedAuthor, setSelectedAuthor] = useState('all');
  const [selectedChangeType, setSelectedChangeType] = useState('all');
  const [selectedVersions, setSelectedVersions] = useState<string[]>([]);
  const [showCompareDialog, setShowCompareDialog] = useState(false);
  const [showTagDialog, setShowTagDialog] = useState(false);
  const [selectedVersionForTag, setSelectedVersionForTag] = useState<string | null>(null);
  const [tagData, setTagData] = useState<Partial<VersionTag>>({});
  const [isRestoring, setIsRestoring] = useState(false);
  const [isCreatingTag, setIsCreatingTag] = useState(false);

  // Mock data for version history
  const versions: SourceVersion[] = [
    {
      id: '1',
      version: '1.4.2',
      created_at: '2024-02-15T10:30:00Z',
      author: {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        avatar: 'https://github.com/shadcn.png',
      },
      changeType: 'content',
      changes: [
        {
          type: 'modified',
          field: 'content',
          oldValue: 'Previous content...',
          newValue: 'Updated content with new information...',
          description: 'Updated main content section',
        },
        {
          type: 'added',
          field: 'tags',
          newValue: ['research', 'analysis'],
          description: 'Added research and analysis tags',
        },
      ],
      size: 2048576,
      checksum: 'sha256:abc123...',
      message: 'Updated content with latest research findings',
      tags: [
        {
          id: '1',
          name: 'stable',
          color: 'green',
          description: 'Stable release',
          created_at: '2024-02-15T10:35:00Z',
          created_by: 'John Doe',
        },
      ],
      isActive: true,
      branchName: 'main',
    },
    {
      id: '2',
      version: '1.4.1',
      created_at: '2024-02-14T15:20:00Z',
      author: {
        id: '2',
        name: 'Jane Smith',
        email: '<EMAIL>',
      },
      changeType: 'metadata',
      changes: [
        {
          type: 'modified',
          field: 'title',
          oldValue: 'Old Title',
          newValue: 'Updated Title',
          description: 'Updated document title',
        },
        {
          type: 'modified',
          field: 'description',
          oldValue: 'Old description',
          newValue: 'New comprehensive description',
          description: 'Enhanced description with more details',
        },
      ],
      size: 2045632,
      checksum: 'sha256:def456...',
      message: 'Updated metadata and title',
      tags: [],
      isActive: false,
      parentVersion: '3',
      branchName: 'main',
    },
    {
      id: '3',
      version: '1.4.0',
      created_at: '2024-02-13T09:15:00Z',
      author: {
        id: '3',
        name: 'Bob Johnson',
        email: '<EMAIL>',
      },
      changeType: 'created',
      changes: [
        {
          type: 'added',
          field: 'document',
          newValue: 'Initial document creation',
          description: 'Created new document',
        },
      ],
      size: 1024000,
      checksum: 'sha256:ghi789...',
      message: 'Initial document creation',
      tags: [
        {
          id: '2',
          name: 'initial',
          color: 'blue',
          description: 'Initial version',
          created_at: '2024-02-13T09:20:00Z',
          created_by: 'Bob Johnson',
        },
      ],
      isActive: false,
      branchName: 'main',
    },
  ];

  const authors = useMemo(() => {
    const uniqueAuthors = Array.from(
      new Set(versions.map(v => v.author.id))
    ).map(id => versions.find(v => v.author.id === id)?.author).filter(Boolean);
    return uniqueAuthors;
  }, [versions]);

  const filteredVersions = useMemo(() => {
    return versions.filter(version => {
      const matchesSearch = 
        version.version.toLowerCase().includes(searchQuery.toLowerCase()) ||
        version.message?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        version.author.name.toLowerCase().includes(searchQuery.toLowerCase());
      
      const matchesAuthor = selectedAuthor === 'all' || version.author.id === selectedAuthor;
      const matchesChangeType = selectedChangeType === 'all' || version.changeType === selectedChangeType;
      
      return matchesSearch && matchesAuthor && matchesChangeType;
    });
  }, [versions, searchQuery, selectedAuthor, selectedChangeType]);

  const handleVersionSelect = (versionId: string) => {
    setSelectedVersions(prev => {
      if (prev.includes(versionId)) {
        return prev.filter(id => id !== versionId);
      } else if (prev.length < 2) {
        return [...prev, versionId];
      } else {
        return [prev[1], versionId];
      }
    });
  };

  const handleRestoreVersion = async (versionId: string) => {
    setIsRestoring(true);
    try {
      await onRestoreVersion?.(versionId);
    } catch (error) {
      console.error('Failed to restore version:', error);
    } finally {
      setIsRestoring(false);
    }
  };

  const handleCompareVersions = () => {
    if (selectedVersions.length === 2) {
      onCompareVersions?.(selectedVersions[0], selectedVersions[1]);
      setShowCompareDialog(true);
    }
  };

  const handleCreateTag = async () => {
    if (!selectedVersionForTag || !tagData.name) return;
    
    setIsCreatingTag(true);
    try {
      const tag: VersionTag = {
        id: Date.now().toString(),
        name: tagData.name,
        color: tagData.color || 'blue',
        description: tagData.description,
        created_at: new Date().toISOString(),
        created_by: 'Current User',
      };
      
      await onCreateTag?.(selectedVersionForTag, tag);
      setShowTagDialog(false);
      setTagData({});
      setSelectedVersionForTag(null);
    } catch (error) {
      console.error('Failed to create tag:', error);
    } finally {
      setIsCreatingTag(false);
    }
  };

  const getChangeTypeIcon = (changeType: string) => {
    switch (changeType) {
      case 'created':
        return <Plus className="h-4 w-4 text-green-500" />;
      case 'updated':
        return <RefreshCw className="h-4 w-4 text-blue-500" />;
      case 'content':
        return <FileText className="h-4 w-4 text-purple-500" />;
      case 'metadata':
        return <Info className="h-4 w-4 text-orange-500" />;
      case 'restored':
        return <RotateCcw className="h-4 w-4 text-yellow-500" />;
      case 'tagged':
        return <Tag className="h-4 w-4 text-pink-500" />;
      default:
        return <GitCommit className="h-4 w-4 text-gray-500" />;
    }
  };

  const getChangeTypeBadge = (changeType: string) => {
    const variants: Record<string, any> = {
      created: 'default',
      updated: 'secondary',
      content: 'outline',
      metadata: 'outline',
      restored: 'outline',
      tagged: 'outline',
    };
    
    return (
      <Badge variant={variants[changeType]} className="text-xs flex items-center gap-1">
        {getChangeTypeIcon(changeType)}
        {changeType.charAt(0).toUpperCase() + changeType.slice(1)}
      </Badge>
    );
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)} hours ago`;
    } else if (diffInHours < 48) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <History className="h-5 w-5" />
              Version History - {source.title}
            </DialogTitle>
            <DialogDescription>
              Track changes and manage versions of this source
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-hidden">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="timeline">Timeline ({versions.length})</TabsTrigger>
                <TabsTrigger value="branches">Branches</TabsTrigger>
                <TabsTrigger value="tags">Tags</TabsTrigger>
              </TabsList>

              <div className="flex-1 overflow-hidden mt-4">
                <TabsContent value="timeline" className="h-full">
                  <div className="space-y-4 h-full flex flex-col">
                    {/* Search and Filters */}
                    <div className="flex gap-4">
                      <div className="flex-1 relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Search versions..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                      <Select value={selectedAuthor} onValueChange={setSelectedAuthor}>
                        <SelectTrigger className="w-40">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Authors</SelectItem>
                          {authors.map(author => (
                            <SelectItem key={author?.id} value={author?.id || ''}>
                              {author?.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <Select value={selectedChangeType} onValueChange={setSelectedChangeType}>
                        <SelectTrigger className="w-40">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Changes</SelectItem>
                          <SelectItem value="created">Created</SelectItem>
                          <SelectItem value="updated">Updated</SelectItem>
                          <SelectItem value="content">Content</SelectItem>
                          <SelectItem value="metadata">Metadata</SelectItem>
                          <SelectItem value="restored">Restored</SelectItem>
                          <SelectItem value="tagged">Tagged</SelectItem>
                        </SelectContent>
                      </Select>
                      {selectedVersions.length === 2 && (
                        <Button onClick={handleCompareVersions}>
                          <Diff className="h-4 w-4 mr-2" />
                          Compare
                        </Button>
                      )}
                    </div>

                    {/* Version Timeline */}
                    <ScrollArea className="flex-1">
                      <div className="space-y-4">
                        {filteredVersions.map((version, index) => (
                          <Card key={version.id} className={`relative ${
                            selectedVersions.includes(version.id) ? 'ring-2 ring-primary' : ''
                          }`}>
                            <CardContent className="p-4">
                              <div className="flex items-start justify-between">
                                <div className="flex items-start gap-3 flex-1">
                                  <input
                                    type="checkbox"
                                    checked={selectedVersions.includes(version.id)}
                                    onChange={() => handleVersionSelect(version.id)}
                                    className="mt-1"
                                  />
                                  
                                  <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-2">
                                      <h4 className="font-medium flex items-center gap-2">
                                        <GitCommit className="h-4 w-4" />
                                        Version {version.version}
                                        {version.isActive && (
                                          <Badge variant="default" className="text-xs">Current</Badge>
                                        )}
                                      </h4>
                                      {getChangeTypeBadge(version.changeType)}
                                      {version.tags.map(tag => (
                                        <Badge key={tag.id} variant="outline" className="text-xs">
                                          <Tag className="h-3 w-3 mr-1" />
                                          {tag.name}
                                        </Badge>
                                      ))}
                                    </div>
                                    
                                    <div className="flex items-center gap-4 text-sm text-muted-foreground mb-2">
                                      <div className="flex items-center gap-2">
                                        <Avatar className="h-6 w-6">
                                          <AvatarImage src={version.author.avatar} alt={version.author.name} />
                                          <AvatarFallback className="text-xs">
                                            {version.author.name.split(' ').map(n => n[0]).join('')}
                                          </AvatarFallback>
                                        </Avatar>
                                        <span>{version.author.name}</span>
                                      </div>
                                      <span>•</span>
                                      <span>{formatDate(version.created_at)}</span>
                                      <span>•</span>
                                      <span>{formatFileSize(version.size)}</span>
                                    </div>
                                    
                                    {version.message && (
                                      <p className="text-sm mb-3">{version.message}</p>
                                    )}
                                    
                                    <div className="space-y-1">
                                      {version.changes.map((change, changeIndex) => (
                                        <div key={changeIndex} className="flex items-center gap-2 text-xs text-muted-foreground">
                                          {change.type === 'added' && <Plus className="h-3 w-3 text-green-500" />}
                                          {change.type === 'modified' && <RefreshCw className="h-3 w-3 text-blue-500" />}
                                          {change.type === 'deleted' && <Minus className="h-3 w-3 text-red-500" />}
                                          {change.type === 'renamed' && <ArrowRight className="h-3 w-3 text-orange-500" />}
                                          <span>{change.description}</span>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                </div>
                                
                                <div className="flex items-center gap-2">
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button variant="ghost" size="sm">
                                        <MoreHorizontal className="h-4 w-4" />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                      <DropdownMenuItem>
                                        <Eye className="h-4 w-4 mr-2" />
                                        View Version
                                      </DropdownMenuItem>
                                      <DropdownMenuItem>
                                        <Download className="h-4 w-4 mr-2" />
                                        Download
                                      </DropdownMenuItem>
                                      <DropdownMenuItem>
                                        <Copy className="h-4 w-4 mr-2" />
                                        Copy Checksum
                                      </DropdownMenuItem>
                                      <DropdownMenuItem 
                                        onClick={() => {
                                          setSelectedVersionForTag(version.id);
                                          setShowTagDialog(true);
                                        }}
                                      >
                                        <Tag className="h-4 w-4 mr-2" />
                                        Add Tag
                                      </DropdownMenuItem>
                                      <DropdownMenuSeparator />
                                      {!version.isActive && (
                                        <DropdownMenuItem 
                                          onClick={() => handleRestoreVersion(version.id)}
                                          disabled={isRestoring}
                                        >
                                          <RotateCcw className="h-4 w-4 mr-2" />
                                          Restore Version
                                        </DropdownMenuItem>
                                      )}
                                      <DropdownMenuItem className="text-red-600">
                                        <Trash2 className="h-4 w-4 mr-2" />
                                        Delete Version
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </div>
                              </div>
                            </CardContent>
                            
                            {/* Timeline connector */}
                            {index < filteredVersions.length - 1 && (
                              <div className="absolute left-8 bottom-0 w-px h-4 bg-border transform translate-y-full" />
                            )}
                          </Card>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                </TabsContent>

                <TabsContent value="branches" className="h-full">
                  <div className="text-center py-12">
                    <GitBranch className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-2">Branch Management</h3>
                    <p className="text-muted-foreground">
                      Branch management features will be available in a future update
                    </p>
                  </div>
                </TabsContent>

                <TabsContent value="tags" className="h-full">
                  <div className="text-center py-12">
                    <Tag className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-2">Version Tags</h3>
                    <p className="text-muted-foreground">
                      Manage version tags and releases
                    </p>
                  </div>
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </DialogContent>
      </Dialog>

      {/* Create Tag Dialog */}
      <Dialog open={showTagDialog} onOpenChange={setShowTagDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Tag className="h-5 w-5" />
              Create Version Tag
            </DialogTitle>
            <DialogDescription>
              Add a tag to mark this version
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="tag-name">Tag Name</Label>
              <Input
                id="tag-name"
                placeholder="e.g., stable, release, beta"
                value={tagData.name || ''}
                onChange={(e) => setTagData(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>

            <div>
              <Label htmlFor="tag-color">Color</Label>
              <Select value={tagData.color} onValueChange={(color) => setTagData(prev => ({ ...prev, color }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a color" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="blue">Blue</SelectItem>
                  <SelectItem value="green">Green</SelectItem>
                  <SelectItem value="yellow">Yellow</SelectItem>
                  <SelectItem value="red">Red</SelectItem>
                  <SelectItem value="purple">Purple</SelectItem>
                  <SelectItem value="pink">Pink</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="tag-description">Description (Optional)</Label>
              <Textarea
                id="tag-description"
                placeholder="Describe this version..."
                value={tagData.description || ''}
                onChange={(e) => setTagData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowTagDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateTag} disabled={isCreatingTag || !tagData.name}>
              {isCreatingTag ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Tag className="h-4 w-4 mr-2" />
                  Create Tag
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default SourceVersionHistory;