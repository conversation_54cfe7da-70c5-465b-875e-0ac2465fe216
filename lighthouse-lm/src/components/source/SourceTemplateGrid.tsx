import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, FileText, Database, Code, Image, Archive, Globe } from 'lucide-react';
import { SourceTemplateGridProps } from './types';

const SourceTemplateGrid: React.FC<SourceTemplateGridProps> = ({
  templates,
  onTemplateSelect,
}) => {
  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'document':
        return FileText;
      case 'data':
        return Database;
      case 'code':
        return Code;
      case 'media':
        return Image;
      case 'archive':
        return Archive;
      case 'web':
        return Globe;
      default:
        return FileText;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'document':
        return 'bg-blue-100 text-blue-800';
      case 'data':
        return 'bg-green-100 text-green-800';
      case 'code':
        return 'bg-purple-100 text-purple-800';
      case 'media':
        return 'bg-pink-100 text-pink-800';
      case 'archive':
        return 'bg-orange-100 text-orange-800';
      case 'web':
        return 'bg-cyan-100 text-cyan-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with Create Template Button */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Source Templates</h3>
          <p className="text-sm text-muted-foreground">
            Choose a template to structure your source data
          </p>
        </div>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          Create Template
        </Button>
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {templates.map((template) => {
          const IconComponent = getCategoryIcon(template.category);
          const isSelected = false; // Selection logic can be added later
          
          return (
            <Card
              key={template.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                isSelected
                  ? 'ring-2 ring-primary border-primary'
                  : 'hover:border-primary/50'
              }`}
              onClick={() => onTemplateSelect(template)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${
                      getCategoryColor(template.category).split(' ')[0]
                    }`}>
                      <IconComponent className={`h-5 w-5 ${
                        getCategoryColor(template.category).split(' ')[1]
                      }`} />
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-base">{template.name}</CardTitle>
                      <Badge 
                        variant="secondary" 
                        className={`mt-1 text-xs ${getCategoryColor(template.category)}`}
                      >
                        {template.category}
                      </Badge>
                    </div>
                  </div>
                  {isSelected && (
                    <div className="h-2 w-2 bg-primary rounded-full" />
                  )}
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                <div className="space-y-3">
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {template.description}
                  </p>
                  
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>{template.fields.length} fields</span>
                    <span>Template</span>
                  </div>
                  
                  {/* Template Preview Fields */}
                  {template.fields.length > 0 && (
                    <div className="pt-2 border-t">
                      <div className="text-xs text-muted-foreground mb-2">Sample fields:</div>
                      <div className="flex flex-wrap gap-1">
                        {/* Mock field names based on category */}
                        {template.category.toLowerCase() === 'document' && (
                          <>
                            <Badge variant="outline" className="text-xs">Title</Badge>
                            <Badge variant="outline" className="text-xs">Author</Badge>
                            <Badge variant="outline" className="text-xs">Content</Badge>
                          </>
                        )}
                        {template.category.toLowerCase() === 'data' && (
                          <>
                            <Badge variant="outline" className="text-xs">ID</Badge>
                            <Badge variant="outline" className="text-xs">Value</Badge>
                            <Badge variant="outline" className="text-xs">Timestamp</Badge>
                          </>
                        )}
                        {template.category.toLowerCase() === 'code' && (
                          <>
                            <Badge variant="outline" className="text-xs">Function</Badge>
                            <Badge variant="outline" className="text-xs">Language</Badge>
                            <Badge variant="outline" className="text-xs">Lines</Badge>
                          </>
                        )}
                        {template.category.toLowerCase() === 'media' && (
                          <>
                            <Badge variant="outline" className="text-xs">Format</Badge>
                            <Badge variant="outline" className="text-xs">Size</Badge>
                            <Badge variant="outline" className="text-xs">Resolution</Badge>
                          </>
                        )}
                        {!['document', 'data', 'code', 'media'].includes(template.category.toLowerCase()) && (
                          <>
                            <Badge variant="outline" className="text-xs">Name</Badge>
                            <Badge variant="outline" className="text-xs">Type</Badge>
                            <Badge variant="outline" className="text-xs">Size</Badge>
                          </>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Empty State */}
      {templates.length === 0 && (
        <Card className="border-dashed">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="p-4 bg-muted rounded-full mb-4">
              <FileText className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-semibold mb-2">No templates found</h3>
            <p className="text-muted-foreground text-center mb-4 max-w-sm">
              Create your first source template to standardize how you structure and organize your sources.
            </p>
            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              Create Your First Template
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default SourceTemplateGrid;