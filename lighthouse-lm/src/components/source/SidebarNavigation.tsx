import React from 'react';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  BarChart3,
  Bot,
  Database,
  Download,
  FileText,
  FolderOpen,
  GitBranch,
  Home,
  Link,
  Settings,
  Upload,
  Users,
} from 'lucide-react';
import { SidebarNavigationProps, NavigationItem } from './types';

const SidebarNavigation: React.FC<SidebarNavigationProps> = ({
  activeTab,
  onTabChange,
  totalSources,
  selectedCount,
  storageUsed,
}) => {
  const navigationItems: NavigationItem[] = [
    { id: 'overview', label: 'Overview', icon: <Home className="h-4 w-4" /> },
    { id: 'sources', label: 'All Sources', icon: <Database className="h-4 w-4" />, count: totalSources },
    { id: 'upload', label: 'Upload & Import', icon: <Upload className="h-4 w-4" /> },
    { id: 'templates', label: 'Templates', icon: <FileText className="h-4 w-4" /> },
    { id: 'relationships', label: 'Relationships', icon: <GitBranch className="h-4 w-4" /> },
    { id: 'comparison', label: 'Comparison', icon: <BarChart3 className="h-4 w-4" /> },
    { id: 'analytics', label: 'Analytics', icon: <BarChart3 className="h-4 w-4" /> },
    { id: 'ai-assistant', label: 'AI Assistant', icon: <Bot className="h-4 w-4" /> },
    { id: 'export', label: 'Export', icon: <Download className="h-4 w-4" /> },
    { id: 'integrations', label: 'Integrations', icon: <Link className="h-4 w-4" /> },
    { id: 'collaboration', label: 'Collaboration', icon: <Users className="h-4 w-4" /> },
    { id: 'settings', label: 'Settings', icon: <Settings className="h-4 w-4" /> },
  ];

  return (
    <div className="w-64 border-r bg-muted/10">
      <div className="p-4">
        <div className="flex items-center gap-2 mb-4">
          <FolderOpen className="h-5 w-5" />
          <h2 className="font-semibold">Source Management</h2>
        </div>
        
        {/* Quick Stats */}
        <div className="space-y-2 mb-4">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Total Sources</span>
            <Badge variant="secondary">{totalSources}</Badge>
          </div>
          {selectedCount > 0 && (
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Selected</span>
              <Badge variant="default">{selectedCount}</Badge>
            </div>
          )}
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Storage Used</span>
            <span className="text-xs text-muted-foreground">{storageUsed}</span>
          </div>
        </div>
        
        <Separator className="mb-4" />
      </div>
      
      <ScrollArea className="flex-1">
        <div className="px-2 pb-4">
          {navigationItems.map((item) => (
            <Button
              key={item.id}
              variant={activeTab === item.id ? 'secondary' : 'ghost'}
              className="w-full justify-start mb-1"
              onClick={() => onTabChange(item.id)}
            >
              {item.icon}
              <span className="ml-2">{item.label}</span>
              {item.count !== undefined && (
                <Badge variant="outline" className="ml-auto">
                  {item.count}
                </Badge>
              )}
            </Button>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
};

export default SidebarNavigation;