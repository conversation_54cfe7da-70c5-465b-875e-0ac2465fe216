import React, { useState, useMemo } from 'react';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Plus,
  Settings,
  Trash2,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Clock,
  Zap,
  Globe,
  Database,
  Cloud,
  FileText,
  Mail,
  MessageSquare,
  Calendar,
  Users,
  BarChart3,
  Shield,
  Key,
  Link,
  Download,
  Upload,
  Eye,
  EyeOff,
  Copy,
  ExternalLink,
  AlertTriangle,
  Info,
} from 'lucide-react';
import { IntegrationService } from './types';

interface IntegrationsPanelProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onIntegrationConnect?: (integration: IntegrationConfig) => Promise<void>;
  onIntegrationDisconnect?: (integrationId: string) => Promise<void>;
  onIntegrationTest?: (integrationId: string) => Promise<boolean>;
}

interface IntegrationConfig {
  id: string;
  name: string;
  type: string;
  credentials: Record<string, string>;
  settings: Record<string, any>;
  enabled: boolean;
}

interface IntegrationTemplate {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  category: string;
  features: string[];
  authType: 'oauth' | 'api_key' | 'basic' | 'custom';
  fields: Array<{
    key: string;
    label: string;
    type: 'text' | 'password' | 'url' | 'select';
    required: boolean;
    placeholder?: string;
    options?: string[];
    description?: string;
  }>;
  settings: Array<{
    key: string;
    label: string;
    type: 'boolean' | 'text' | 'number' | 'select';
    defaultValue: any;
    description?: string;
    options?: string[];
  }>;
  status: 'available' | 'beta' | 'coming_soon';
}

const IntegrationsPanel: React.FC<IntegrationsPanelProps> = ({
  open,
  onOpenChange,
  onIntegrationConnect,
  onIntegrationDisconnect,
  onIntegrationTest,
}) => {
  const [activeTab, setActiveTab] = useState('available');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showConfigDialog, setShowConfigDialog] = useState(false);
  const [selectedIntegration, setSelectedIntegration] = useState<IntegrationTemplate | null>(null);
  const [configData, setConfigData] = useState<Record<string, any>>({});
  const [showCredentials, setShowCredentials] = useState<Record<string, boolean>>({});
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectedIntegrations, setConnectedIntegrations] = useState<IntegrationConfig[]>([]);
  const [testResults, setTestResults] = useState<Record<string, boolean | null>>({});

  // Enhanced mock data for available integrations
  const availableIntegrations: IntegrationTemplate[] = [
    {
      id: 'google-drive',
      name: 'Google Drive',
      description: 'Import documents and files from Google Drive',
      icon: <Cloud className="h-5 w-5" />,
      category: 'storage',
      features: ['File Import', 'Auto-sync', 'Folder Monitoring'],
      authType: 'oauth',
      fields: [
        {
          key: 'client_id',
          label: 'Client ID',
          type: 'text',
          required: true,
          placeholder: 'Enter Google OAuth Client ID',
        },
        {
          key: 'client_secret',
          label: 'Client Secret',
          type: 'password',
          required: true,
          placeholder: 'Enter Google OAuth Client Secret',
        },
      ],
      settings: [
        {
          key: 'auto_sync',
          label: 'Auto-sync files',
          type: 'boolean',
          defaultValue: true,
          description: 'Automatically sync new files from Google Drive',
        },
        {
          key: 'sync_interval',
          label: 'Sync interval (minutes)',
          type: 'number',
          defaultValue: 30,
          description: 'How often to check for new files',
        },
      ],
      status: 'available',
    },
    {
      id: 'dropbox',
      name: 'Dropbox',
      description: 'Import files and folders from Dropbox',
      icon: <Cloud className="h-5 w-5" />,
      category: 'storage',
      features: ['File Import', 'Folder Sync', 'Version History'],
      authType: 'oauth',
      fields: [
        {
          key: 'app_key',
          label: 'App Key',
          type: 'text',
          required: true,
          placeholder: 'Enter Dropbox App Key',
        },
        {
          key: 'app_secret',
          label: 'App Secret',
          type: 'password',
          required: true,
          placeholder: 'Enter Dropbox App Secret',
        },
      ],
      settings: [
        {
          key: 'auto_sync',
          label: 'Auto-sync files',
          type: 'boolean',
          defaultValue: true,
        },
      ],
      status: 'available',
    },
    {
      id: 'jira',
      name: 'Jira',
      description: 'Import issues and projects from Atlassian Jira',
      icon: <BarChart3 className="h-5 w-5" />,
      category: 'productivity',
      features: ['Issue Import', 'Project Sync', 'Custom Fields'],
      authType: 'basic',
      fields: [
        {
          key: 'base_url',
          label: 'Base URL',
          type: 'url',
          required: true,
          placeholder: 'https://your-domain.atlassian.net',
        },
        {
          key: 'username',
          label: 'Username',
          type: 'text',
          required: true,
          placeholder: '<EMAIL>',
        },
        {
          key: 'api_token',
          label: 'API Token',
          type: 'password',
          required: true,
          placeholder: 'Enter Jira API Token',
        },
      ],
      settings: [
        {
          key: 'projects',
          label: 'Project keys',
          type: 'text',
          defaultValue: '',
          description: 'Comma-separated list of project keys to import',
        },
      ],
      status: 'available',
    },
    {
      id: 'trello',
      name: 'Trello',
      description: 'Import boards and cards from Trello',
      icon: <Calendar className="h-5 w-5" />,
      category: 'productivity',
      features: ['Board Import', 'Card Sync', 'Checklist Support'],
      authType: 'api_key',
      fields: [
        {
          key: 'api_key',
          label: 'API Key',
          type: 'text',
          required: true,
          placeholder: 'Enter Trello API Key',
        },
        {
          key: 'token',
          label: 'Token',
          type: 'password',
          required: true,
          placeholder: 'Enter Trello Token',
        },
      ],
      settings: [
        {
          key: 'include_archived',
          label: 'Include archived cards',
          type: 'boolean',
          defaultValue: false,
        },
      ],
      status: 'available',
    },
    {
      id: 'airtable',
      name: 'Airtable',
      description: 'Import bases and records from Airtable',
      icon: <Database className="h-5 w-5" />,
      category: 'productivity',
      features: ['Base Import', 'Record Sync', 'Field Mapping'],
      authType: 'api_key',
      fields: [
        {
          key: 'api_key',
          label: 'API Key',
          type: 'password',
          required: true,
          placeholder: 'Enter Airtable API Key',
        },
      ],
      settings: [
        {
          key: 'base_ids',
          label: 'Base IDs',
          type: 'text',
          defaultValue: '',
          description: 'Comma-separated list of base IDs to import',
        },
      ],
      status: 'available',
    },
    {
      id: 'discord',
      name: 'Discord',
      description: 'Import messages and files from Discord servers',
      icon: <MessageSquare className="h-5 w-5" />,
      category: 'communication',
      features: ['Message Import', 'Server Sync', 'Attachment Handling'],
      authType: 'api_key',
      fields: [
        {
          key: 'bot_token',
          label: 'Bot Token',
          type: 'password',
          required: true,
          placeholder: 'Enter Discord Bot Token',
        },
      ],
      settings: [
        {
          key: 'guild_ids',
          label: 'Server IDs',
          type: 'text',
          defaultValue: '',
          description: 'Comma-separated list of server IDs to monitor',
        },
      ],
      status: 'beta',
    },
    {
      id: 'linear',
      name: 'Linear',
      description: 'Import issues and projects from Linear',
      icon: <Zap className="h-5 w-5" />,
      category: 'development',
      features: ['Issue Import', 'Project Sync', 'Team Management'],
      authType: 'api_key',
      fields: [
        {
          key: 'api_key',
          label: 'API Key',
          type: 'password',
          required: true,
          placeholder: 'Enter Linear API Key',
        },
      ],
      settings: [
        {
          key: 'team_ids',
          label: 'Team IDs',
          type: 'text',
          defaultValue: '',
          description: 'Comma-separated list of team IDs to import',
        },
      ],
      status: 'available',
    },
    {
      id: 'figma',
      name: 'Figma',
      description: 'Import designs and comments from Figma',
      icon: <FileText className="h-5 w-5" />,
      category: 'design',
      features: ['File Import', 'Comment Sync', 'Version History'],
      authType: 'api_key',
      fields: [
        {
          key: 'access_token',
          label: 'Access Token',
          type: 'password',
          required: true,
          placeholder: 'Enter Figma Access Token',
        },
      ],
      settings: [
        {
          key: 'file_keys',
          label: 'File Keys',
          type: 'text',
          defaultValue: '',
          description: 'Comma-separated list of file keys to import',
        },
      ],
      status: 'beta',
    },
    {
      id: 'zendesk',
      name: 'Zendesk',
      description: 'Import tickets and knowledge base from Zendesk',
      icon: <Users className="h-5 w-5" />,
      category: 'support',
      features: ['Ticket Import', 'KB Sync', 'User Management'],
      authType: 'basic',
      fields: [
        {
          key: 'subdomain',
          label: 'Subdomain',
          type: 'text',
          required: true,
          placeholder: 'your-subdomain',
          description: 'Your Zendesk subdomain (without .zendesk.com)',
        },
        {
          key: 'username',
          label: 'Username',
          type: 'text',
          required: true,
          placeholder: '<EMAIL>',
        },
        {
          key: 'api_token',
          label: 'API Token',
          type: 'password',
          required: true,
          placeholder: 'Enter Zendesk API Token',
        },
      ],
      settings: [
        {
          key: 'include_solved',
          label: 'Include solved tickets',
          type: 'boolean',
          defaultValue: false,
        },
      ],
      status: 'available',
    },
    {
      id: 'notion',
      name: 'Notion',
      description: 'Import pages and databases from Notion',
      icon: <FileText className="h-5 w-5" />,
      category: 'productivity',
      features: ['Page Import', 'Database Sync', 'Real-time Updates'],
      authType: 'api_key',
      fields: [
        {
          key: 'api_key',
          label: 'API Key',
          type: 'password',
          required: true,
          placeholder: 'Enter Notion API Key',
          description: 'Get your API key from Notion integrations page',
        },
      ],
      settings: [
        {
          key: 'import_blocks',
          label: 'Import block content',
          type: 'boolean',
          defaultValue: true,
          description: 'Include detailed block content in imports',
        },
      ],
      status: 'available',
    },
    {
      id: 'slack',
      name: 'Slack',
      description: 'Import messages and files from Slack channels',
      icon: <MessageSquare className="h-5 w-5" />,
      category: 'communication',
      features: ['Message Import', 'File Sync', 'Channel Monitoring'],
      authType: 'oauth',
      fields: [
        {
          key: 'bot_token',
          label: 'Bot Token',
          type: 'password',
          required: true,
          placeholder: 'xoxb-...',
          description: 'Slack Bot User OAuth Token',
        },
      ],
      settings: [
        {
          key: 'channels',
          label: 'Monitor channels',
          type: 'text',
          defaultValue: '',
          description: 'Comma-separated list of channel names to monitor',
        },
      ],
      status: 'available',
    },
    {
      id: 'github',
      name: 'GitHub',
      description: 'Import repositories, issues, and documentation',
      icon: <Database className="h-5 w-5" />,
      category: 'development',
      features: ['Repository Import', 'Issue Tracking', 'Documentation Sync'],
      authType: 'api_key',
      fields: [
        {
          key: 'token',
          label: 'Personal Access Token',
          type: 'password',
          required: true,
          placeholder: 'ghp_...',
          description: 'GitHub Personal Access Token with repo permissions',
        },
      ],
      settings: [
        {
          key: 'include_issues',
          label: 'Import issues',
          type: 'boolean',
          defaultValue: true,
        },
        {
          key: 'include_prs',
          label: 'Import pull requests',
          type: 'boolean',
          defaultValue: false,
        },
      ],
      status: 'available',
    },
    {
      id: 'confluence',
      name: 'Confluence',
      description: 'Import pages and spaces from Atlassian Confluence',
      icon: <Globe className="h-5 w-5" />,
      category: 'productivity',
      features: ['Page Import', 'Space Sync', 'Attachment Handling'],
      authType: 'basic',
      fields: [
        {
          key: 'base_url',
          label: 'Base URL',
          type: 'url',
          required: true,
          placeholder: 'https://your-domain.atlassian.net',
        },
        {
          key: 'username',
          label: 'Username',
          type: 'text',
          required: true,
          placeholder: '<EMAIL>',
        },
        {
          key: 'api_token',
          label: 'API Token',
          type: 'password',
          required: true,
          placeholder: 'Enter Confluence API Token',
        },
      ],
      settings: [
        {
          key: 'spaces',
          label: 'Space keys',
          type: 'text',
          defaultValue: '',
          description: 'Comma-separated list of space keys to import',
        },
      ],
      status: 'available',
    },
    {
      id: 'zapier',
      name: 'Zapier',
      description: 'Connect with 5000+ apps via Zapier webhooks',
      icon: <Zap className="h-5 w-5" />,
      category: 'automation',
      features: ['Webhook Integration', 'Multi-app Workflows', 'Real-time Triggers'],
      authType: 'custom',
      fields: [
        {
          key: 'webhook_url',
          label: 'Webhook URL',
          type: 'url',
          required: true,
          placeholder: 'https://hooks.zapier.com/hooks/catch/...',
        },
      ],
      settings: [
        {
          key: 'trigger_events',
          label: 'Trigger events',
          type: 'select',
          defaultValue: 'all',
          options: ['all', 'create', 'update', 'delete'],
          description: 'Which events should trigger the webhook',
        },
      ],
      status: 'beta',
    },
  ];

  // Mock connected integrations
  const mockConnectedIntegrations: IntegrationConfig[] = [
    {
      id: 'google-drive-1',
      name: 'Google Drive (Personal)',
      type: 'google-drive',
      credentials: { client_id: '***', client_secret: '***' },
      settings: { auto_sync: true, sync_interval: 30 },
      enabled: true,
    },
    {
      id: 'notion-1',
      name: 'Notion Workspace',
      type: 'notion',
      credentials: { api_key: '***' },
      settings: { import_blocks: true },
      enabled: false,
    },
  ];

  const categories = [
    { id: 'all', label: 'All Categories', count: availableIntegrations.length },
    { id: 'storage', label: 'Storage', count: availableIntegrations.filter(i => i.category === 'storage').length },
    { id: 'productivity', label: 'Productivity', count: availableIntegrations.filter(i => i.category === 'productivity').length },
    { id: 'communication', label: 'Communication', count: availableIntegrations.filter(i => i.category === 'communication').length },
    { id: 'development', label: 'Development', count: availableIntegrations.filter(i => i.category === 'development').length },
    { id: 'automation', label: 'Automation', count: availableIntegrations.filter(i => i.category === 'automation').length },
  ];

  const filteredIntegrations = useMemo(() => {
    return availableIntegrations.filter(integration => {
      const matchesCategory = selectedCategory === 'all' || integration.category === selectedCategory;
      const matchesSearch = integration.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           integration.description.toLowerCase().includes(searchQuery.toLowerCase());
      return matchesCategory && matchesSearch;
    });
  }, [selectedCategory, searchQuery]);

  const handleConfigureIntegration = (integration: IntegrationTemplate) => {
    setSelectedIntegration(integration);
    setConfigData({});
    setShowConfigDialog(true);
  };

  const handleConnect = async () => {
    if (!selectedIntegration) return;
    
    setIsConnecting(true);
    try {
      const config: IntegrationConfig = {
        id: `${selectedIntegration.id}-${Date.now()}`,
        name: `${selectedIntegration.name} Integration`,
        type: selectedIntegration.id,
        credentials: configData.credentials || {},
        settings: configData.settings || {},
        enabled: true,
      };
      
      await onIntegrationConnect?.(config);
      setConnectedIntegrations(prev => [...prev, config]);
      setShowConfigDialog(false);
      setSelectedIntegration(null);
      setConfigData({});
    } catch (error) {
      console.error('Failed to connect integration:', error);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleDisconnect = async (integrationId: string) => {
    try {
      await onIntegrationDisconnect?.(integrationId);
      setConnectedIntegrations(prev => prev.filter(i => i.id !== integrationId));
    } catch (error) {
      console.error('Failed to disconnect integration:', error);
    }
  };

  const handleTestConnection = async (integrationId: string) => {
    try {
      const result = await onIntegrationTest?.(integrationId);
      setTestResults(prev => ({ ...prev, [integrationId]: result || false }));
    } catch (error) {
      console.error('Failed to test integration:', error);
      setTestResults(prev => ({ ...prev, [integrationId]: false }));
    }
  };

  const toggleCredentialVisibility = (fieldKey: string) => {
    setShowCredentials(prev => ({
      ...prev,
      [fieldKey]: !prev[fieldKey]
    }));
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'available':
        return <Badge variant="default" className="text-xs">Available</Badge>;
      case 'beta':
        return <Badge variant="secondary" className="text-xs">Beta</Badge>;
      case 'coming_soon':
        return <Badge variant="outline" className="text-xs">Coming Soon</Badge>;
      default:
        return null;
    }
  };

  const getConnectionStatus = (integration: IntegrationConfig) => {
    if (!integration.enabled) {
      return <Badge variant="secondary" className="text-xs">Disabled</Badge>;
    }
    
    const testResult = testResults[integration.id];
    if (testResult === true) {
      return <Badge variant="default" className="text-xs flex items-center gap-1">
        <CheckCircle className="h-3 w-3" />
        Connected
      </Badge>;
    } else if (testResult === false) {
      return <Badge variant="destructive" className="text-xs flex items-center gap-1">
        <AlertCircle className="h-3 w-3" />
        Error
      </Badge>;
    }
    
    return <Badge variant="outline" className="text-xs flex items-center gap-1">
      <Clock className="h-3 w-3" />
      Unknown
    </Badge>;
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Link className="h-5 w-5" />
              Integrations
            </DialogTitle>
            <DialogDescription>
              Connect external services to import and sync your sources
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-hidden">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="available">Available Integrations</TabsTrigger>
                <TabsTrigger value="connected">Connected ({mockConnectedIntegrations.length})</TabsTrigger>
              </TabsList>

              <div className="flex-1 overflow-hidden mt-4">
                <TabsContent value="available" className="h-full">
                  <div className="space-y-4 h-full flex flex-col">
                    {/* Search and Filter */}
                    <div className="flex gap-4">
                      <div className="flex-1">
                        <Input
                          placeholder="Search integrations..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                        />
                      </div>
                      <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                        <SelectTrigger className="w-48">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {categories.map(category => (
                            <SelectItem key={category.id} value={category.id}>
                              {category.label} ({category.count})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Integrations Grid */}
                    <ScrollArea className="flex-1">
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {filteredIntegrations.map(integration => (
                          <Card key={integration.id} className="hover:shadow-md transition-shadow">
                            <CardHeader className="pb-3">
                              <div className="flex items-start justify-between">
                                <div className="flex items-center gap-3">
                                  <div className="flex-shrink-0">
                                    {integration.icon}
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <CardTitle className="text-base">{integration.name}</CardTitle>
                                    <p className="text-sm text-muted-foreground mt-1">
                                      {integration.description}
                                    </p>
                                  </div>
                                </div>
                                {getStatusBadge(integration.status)}
                              </div>
                            </CardHeader>
                            <CardContent className="pt-0">
                              <div className="space-y-3">
                                <div className="flex flex-wrap gap-1">
                                  {integration.features.slice(0, 3).map(feature => (
                                    <Badge key={feature} variant="outline" className="text-xs">
                                      {feature}
                                    </Badge>
                                  ))}
                                  {integration.features.length > 3 && (
                                    <Badge variant="outline" className="text-xs">
                                      +{integration.features.length - 3} more
                                    </Badge>
                                  )}
                                </div>
                                
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                    <Shield className="h-3 w-3" />
                                    {integration.authType.replace('_', ' ').toUpperCase()}
                                  </div>
                                  <Button 
                                    size="sm" 
                                    onClick={() => handleConfigureIntegration(integration)}
                                    disabled={integration.status === 'coming_soon'}
                                  >
                                    <Plus className="h-4 w-4 mr-1" />
                                    Connect
                                  </Button>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                </TabsContent>

                <TabsContent value="connected" className="h-full">
                  <ScrollArea className="h-full">
                    <div className="space-y-4">
                      {mockConnectedIntegrations.length === 0 ? (
                        <div className="text-center py-12">
                          <Link className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                          <h3 className="text-lg font-medium mb-2">No integrations connected</h3>
                          <p className="text-muted-foreground mb-4">
                            Connect your first integration to start importing sources
                          </p>
                          <Button onClick={() => setActiveTab('available')}>
                            Browse Integrations
                          </Button>
                        </div>
                      ) : (
                        mockConnectedIntegrations.map(integration => (
                          <Card key={integration.id}>
                            <CardHeader>
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                  <div className="flex-shrink-0">
                                    {availableIntegrations.find(i => i.id === integration.type)?.icon}
                                  </div>
                                  <div>
                                    <CardTitle className="text-base">{integration.name}</CardTitle>
                                    <p className="text-sm text-muted-foreground">
                                      {availableIntegrations.find(i => i.id === integration.type)?.description}
                                    </p>
                                  </div>
                                </div>
                                <div className="flex items-center gap-2">
                                  {getConnectionStatus(integration)}
                                  <Switch
                                    checked={integration.enabled}
                                    onCheckedChange={(checked) => {
                                      // Handle enable/disable
                                    }}
                                  />
                                </div>
                              </div>
                            </CardHeader>
                            <CardContent>
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                  <Clock className="h-4 w-4" />
                                  Last sync: 2 hours ago
                                </div>
                                <div className="flex gap-2">
                                  <Button 
                                    variant="outline" 
                                    size="sm"
                                    onClick={() => handleTestConnection(integration.id)}
                                  >
                                    <RefreshCw className="h-4 w-4 mr-1" />
                                    Test
                                  </Button>
                                  <Button variant="outline" size="sm">
                                    <Settings className="h-4 w-4 mr-1" />
                                    Configure
                                  </Button>
                                  <Button 
                                    variant="outline" 
                                    size="sm"
                                    onClick={() => handleDisconnect(integration.id)}
                                  >
                                    <Trash2 className="h-4 w-4 mr-1" />
                                    Disconnect
                                  </Button>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))
                      )}
                    </div>
                  </ScrollArea>
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </DialogContent>
      </Dialog>

      {/* Configuration Dialog */}
      <Dialog open={showConfigDialog} onOpenChange={setShowConfigDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {selectedIntegration?.icon}
              Configure {selectedIntegration?.name}
            </DialogTitle>
            <DialogDescription>
              Enter your credentials and configure settings for this integration
            </DialogDescription>
          </DialogHeader>

          {selectedIntegration && (
            <div className="space-y-6">
              {/* Authentication */}
              <div>
                <Label className="text-base font-medium">Authentication</Label>
                <p className="text-sm text-muted-foreground mb-4">
                  {selectedIntegration.authType === 'oauth' && 'OAuth authentication required'}
                  {selectedIntegration.authType === 'api_key' && 'API key authentication'}
                  {selectedIntegration.authType === 'basic' && 'Basic authentication with username and password'}
                  {selectedIntegration.authType === 'custom' && 'Custom authentication method'}
                </p>
                
                <div className="space-y-4">
                  {selectedIntegration.fields.map(field => (
                    <div key={field.key}>
                      <Label htmlFor={field.key} className="flex items-center gap-2">
                        {field.label}
                        {field.required && <span className="text-red-500">*</span>}
                      </Label>
                      {field.description && (
                        <p className="text-xs text-muted-foreground mt-1 mb-2">
                          {field.description}
                        </p>
                      )}
                      <div className="relative">
                        <Input
                          id={field.key}
                          type={field.type === 'password' && !showCredentials[field.key] ? 'password' : 'text'}
                          placeholder={field.placeholder}
                          value={configData.credentials?.[field.key] || ''}
                          onChange={(e) => setConfigData(prev => ({
                            ...prev,
                            credentials: {
                              ...prev.credentials,
                              [field.key]: e.target.value
                            }
                          }))}
                        />
                        {field.type === 'password' && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3"
                            onClick={() => toggleCredentialVisibility(field.key)}
                          >
                            {showCredentials[field.key] ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <Separator />

              {/* Settings */}
              <div>
                <Label className="text-base font-medium">Settings</Label>
                <p className="text-sm text-muted-foreground mb-4">
                  Configure how this integration behaves
                </p>
                
                <div className="space-y-4">
                  {selectedIntegration.settings.map(setting => (
                    <div key={setting.key} className="flex items-center justify-between">
                      <div className="flex-1">
                        <Label htmlFor={setting.key}>{setting.label}</Label>
                        {setting.description && (
                          <p className="text-xs text-muted-foreground mt-1">
                            {setting.description}
                          </p>
                        )}
                      </div>
                      <div className="ml-4">
                        {setting.type === 'boolean' ? (
                          <Switch
                            id={setting.key}
                            checked={configData.settings?.[setting.key] ?? setting.defaultValue}
                            onCheckedChange={(checked) => setConfigData(prev => ({
                              ...prev,
                              settings: {
                                ...prev.settings,
                                [setting.key]: checked
                              }
                            }))}
                          />
                        ) : setting.type === 'select' ? (
                          <Select
                            value={configData.settings?.[setting.key] || setting.defaultValue}
                            onValueChange={(value) => setConfigData(prev => ({
                              ...prev,
                              settings: {
                                ...prev.settings,
                                [setting.key]: value
                              }
                            }))}
                          >
                            <SelectTrigger className="w-32">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {setting.options?.map(option => (
                                <SelectItem key={option} value={option}>
                                  {option}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <Input
                            id={setting.key}
                            type={setting.type === 'number' ? 'number' : 'text'}
                            value={configData.settings?.[setting.key] || setting.defaultValue}
                            onChange={(e) => setConfigData(prev => ({
                              ...prev,
                              settings: {
                                ...prev.settings,
                                [setting.key]: setting.type === 'number' ? Number(e.target.value) : e.target.value
                              }
                            }))}
                            className="w-32"
                          />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowConfigDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleConnect} disabled={isConnecting}>
              {isConnecting ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Connecting...
                </>
              ) : (
                <>
                  <Link className="h-4 w-4 mr-2" />
                  Connect
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default IntegrationsPanel;