import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar, X } from 'lucide-react';
import { AdvancedFiltersProps, DateRange } from './types';

const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  dateRange,
  onDateRangeChange,
  tags,
  onTagsChange,
}) => {
  const [newTag, setNewTag] = React.useState('');
  const [fileSizeMin, setFileSizeMin] = React.useState('');
  const [fileSizeMax, setFileSizeMax] = React.useState('');
  const [fileSizeUnit, setFileSizeUnit] = React.useState('MB');
  const [contentSearch, setContentSearch] = React.useState('');
  const [authorFilter, setAuthorFilter] = React.useState('');
  const [statusFilter, setStatusFilter] = React.useState('all');
  const [sourceTypeFilter, setSourceTypeFilter] = React.useState('all');

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      onTagsChange([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    onTagsChange(tags.filter(tag => tag !== tagToRemove));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAddTag();
    }
  };

  const formatDateForInput = (date: Date | undefined) => {
    if (!date) return '';
    return date.toISOString().split('T')[0];
  };

  const handleDateChange = (type: 'from' | 'to', value: string) => {
    const date = value ? new Date(value) : undefined;
    if (type === 'from') {
      onDateRangeChange({ ...dateRange, from: date });
    } else {
      onDateRangeChange({ ...dateRange, to: date });
    }
  };

  const clearAllFilters = () => {
    onDateRangeChange({ from: undefined, to: undefined });
    onTagsChange([]);
    setFileSizeMin('');
    setFileSizeMax('');
    setContentSearch('');
    setAuthorFilter('');
    setStatusFilter('all');
    setSourceTypeFilter('all');
  };

  const convertFileSize = (size: number, unit: string) => {
    const units = { B: 1, KB: 1024, MB: 1024 * 1024, GB: 1024 * 1024 * 1024 };
    return size * (units[unit as keyof typeof units] || 1);
  };

  const isFileSizeInRange = (fileSize: number) => {
    const minSize = fileSizeMin ? convertFileSize(parseFloat(fileSizeMin), fileSizeUnit) : 0;
    const maxSize = fileSizeMax ? convertFileSize(parseFloat(fileSizeMax), fileSizeUnit) : Infinity;
    return fileSize >= minSize && fileSize <= maxSize;
  };

  return (
    <Card className="mb-4">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base">Advanced Filters</CardTitle>
          <Button variant="ghost" size="sm" onClick={clearAllFilters}>
            Clear All
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Date Range Filter */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Date Range</Label>
          <div className="flex items-center gap-2">
            <div className="flex-1">
              <Label htmlFor="date-from" className="text-xs text-muted-foreground">
                From
              </Label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  id="date-from"
                  type="date"
                  value={formatDateForInput(dateRange.from)}
                  onChange={(e) => handleDateChange('from', e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex-1">
              <Label htmlFor="date-to" className="text-xs text-muted-foreground">
                To
              </Label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  id="date-to"
                  type="date"
                  value={formatDateForInput(dateRange.to)}
                  onChange={(e) => handleDateChange('to', e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Tags Filter */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Tags</Label>
          <div className="flex gap-2">
            <Input
              placeholder="Add tag..."
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              onKeyPress={handleKeyPress}
              className="flex-1"
            />
            <Button onClick={handleAddTag} size="sm">
              Add
            </Button>
          </div>
          {tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {tags.map((tag, index) => (
                <Badge key={index} variant="secondary" className="flex items-center gap-1">
                  {tag}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-auto p-0 hover:bg-transparent"
                    onClick={() => handleRemoveTag(tag)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              ))}
            </div>
          )}
        </div>

        {/* File Size Filter */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">File Size</Label>
          <div className="flex items-center gap-2">
            <div className="flex-1">
              <Label htmlFor="size-min" className="text-xs text-muted-foreground">
                Min
              </Label>
              <Input
                id="size-min"
                type="number"
                placeholder="0"
                value={fileSizeMin}
                onChange={(e) => setFileSizeMin(e.target.value)}
              />
            </div>
            <div className="flex-1">
              <Label htmlFor="size-max" className="text-xs text-muted-foreground">
                Max
              </Label>
              <Input
                id="size-max"
                type="number"
                placeholder="∞"
                value={fileSizeMax}
                onChange={(e) => setFileSizeMax(e.target.value)}
              />
            </div>
            <div className="w-20">
              <Label className="text-xs text-muted-foreground">Unit</Label>
              <Select value={fileSizeUnit} onValueChange={setFileSizeUnit}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="KB">KB</SelectItem>
                  <SelectItem value="MB">MB</SelectItem>
                  <SelectItem value="GB">GB</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AdvancedFilters;