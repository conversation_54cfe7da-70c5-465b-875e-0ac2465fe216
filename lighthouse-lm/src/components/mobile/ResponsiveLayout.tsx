import React, { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import { useScreenSize } from '@/hooks/useScreenSize';
import { motion, AnimatePresence } from 'framer-motion';

interface ResponsiveLayoutProps {
  children: React.ReactNode;
  className?: string;
  mobileLayout?: 'stack' | 'tabs' | 'drawer';
  desktopLayout?: 'sidebar' | 'split' | 'grid';
  breakpoint?: number;
}

interface ResponsiveGridProps {
  children: React.ReactNode;
  className?: string;
  mobileColumns?: 1 | 2;
  tabletColumns?: 2 | 3 | 4;
  desktopColumns?: 3 | 4 | 5 | 6;
  gap?: 'sm' | 'md' | 'lg';
}

interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg';
}

export const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({
  children,
  className,
  mobileLayout = 'stack',
  desktopLayout = 'sidebar',
  breakpoint = 1024,
}) => {
  const { width } = useScreenSize();
  const isMobile = width < breakpoint;

  const getMobileLayoutClass = () => {
    switch (mobileLayout) {
      case 'stack':
        return 'flex flex-col space-y-4';
      case 'tabs':
        return 'flex flex-col';
      case 'drawer':
        return 'relative';
      default:
        return '';
    }
  };

  const getDesktopLayoutClass = () => {
    switch (desktopLayout) {
      case 'sidebar':
        return 'flex gap-6';
      case 'split':
        return 'grid grid-cols-2 gap-6';
      case 'grid':
        return 'grid grid-cols-3 gap-6';
      default:
        return '';
    }
  };

  return (
    <div
      className={cn(
        'w-full transition-all duration-300',
        isMobile ? getMobileLayoutClass() : getDesktopLayoutClass(),
        className
      )}
    >
      {children}
    </div>
  );
};

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  className,
  mobileColumns = 1,
  tabletColumns = 2,
  desktopColumns = 3,
  gap = 'md',
}) => {
  const gapClasses = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
  };

  return (
    <div
      className={cn(
        'grid',
        gapClasses[gap],
        `grid-cols-${mobileColumns}`,
        `sm:grid-cols-${mobileColumns}`,
        `md:grid-cols-${tabletColumns}`,
        `lg:grid-cols-${desktopColumns}`,
        className
      )}
    >
      {children}
    </div>
  );
};

export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  className,
  maxWidth = 'xl',
  padding = 'md',
}) => {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-full',
  };

  const paddingClasses = {
    none: '',
    sm: 'px-2 py-2 sm:px-4 sm:py-4',
    md: 'px-4 py-4 sm:px-6 sm:py-6',
    lg: 'px-6 py-6 sm:px-8 sm:py-8',
  };

  return (
    <div
      className={cn(
        'mx-auto w-full',
        maxWidthClasses[maxWidth],
        paddingClasses[padding],
        className
      )}
    >
      {children}
    </div>
  );
};

// Responsive Tab Layout
interface TabItem {
  id: string;
  label: string;
  content: React.ReactNode;
  icon?: React.ReactNode;
}

interface ResponsiveTabsProps {
  tabs: TabItem[];
  defaultTab?: string;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
}

export const ResponsiveTabs: React.FC<ResponsiveTabsProps> = ({
  tabs,
  defaultTab,
  className,
  orientation = 'horizontal',
}) => {
  const [activeTab, setActiveTab] = useState(defaultTab || tabs[0]?.id);
  const { width } = useScreenSize();
  const isMobile = width < 768;

  const activeTabContent = tabs.find(tab => tab.id === activeTab)?.content;

  return (
    <div className={cn('w-full', className)}>
      {/* Tab Headers */}
      <div
        className={cn(
          'border-b',
          orientation === 'vertical' && !isMobile ? 'border-b-0 border-r' : '',
          isMobile ? 'overflow-x-auto' : ''
        )}
      >
        <div
          className={cn(
            'flex',
            orientation === 'vertical' && !isMobile ? 'flex-col' : 'flex-row',
            isMobile ? 'min-w-max' : ''
          )}
        >
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={cn(
                'px-4 py-3 font-medium transition-colors relative whitespace-nowrap',
                'hover:bg-muted/50',
                activeTab === tab.id
                  ? 'text-primary'
                  : 'text-muted-foreground',
                isMobile ? 'text-sm' : 'text-base'
              )}
            >
              <div className="flex items-center gap-2">
                {tab.icon}
                <span>{tab.label}</span>
              </div>
              {activeTab === tab.id && (
                <motion.div
                  layoutId="activeTab"
                  className={cn(
                    'absolute bg-primary',
                    orientation === 'vertical' && !isMobile
                      ? 'left-0 top-0 bottom-0 w-0.5'
                      : 'bottom-0 left-0 right-0 h-0.5'
                  )}
                />
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <AnimatePresence mode="wait">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.2 }}
          className="py-4"
        >
          {activeTabContent}
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

// Responsive Drawer
interface ResponsiveDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  position?: 'left' | 'right' | 'bottom';
  className?: string;
}

export const ResponsiveDrawer: React.FC<ResponsiveDrawerProps> = ({
  isOpen,
  onClose,
  children,
  position = 'left',
  className,
}) => {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const getDrawerAnimation = () => {
    switch (position) {
      case 'left':
        return { initial: { x: '-100%' }, animate: { x: 0 }, exit: { x: '-100%' } };
      case 'right':
        return { initial: { x: '100%' }, animate: { x: 0 }, exit: { x: '100%' } };
      case 'bottom':
        return { initial: { y: '100%' }, animate: { y: 0 }, exit: { y: '100%' } };
      default:
        return { initial: { x: '-100%' }, animate: { x: 0 }, exit: { x: '-100%' } };
    }
  };

  const getDrawerPosition = () => {
    switch (position) {
      case 'left':
        return 'left-0 top-0 bottom-0 w-80 max-w-[85vw]';
      case 'right':
        return 'right-0 top-0 bottom-0 w-80 max-w-[85vw]';
      case 'bottom':
        return 'left-0 right-0 bottom-0 max-h-[85vh]';
      default:
        return 'left-0 top-0 bottom-0 w-80';
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-40"
            onClick={onClose}
          />

          {/* Drawer */}
          <motion.div
            {...getDrawerAnimation()}
            transition={{ type: 'spring', damping: 25, stiffness: 300 }}
            className={cn(
              'fixed bg-background border z-50',
              getDrawerPosition(),
              position === 'bottom' ? 'rounded-t-xl' : '',
              className
            )}
          >
            {children}
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

// Responsive breakpoint hook
export const useResponsive = () => {
  const { width } = useScreenSize();
  
  return {
    isMobile: width < 640,
    isTablet: width >= 640 && width < 1024,
    isDesktop: width >= 1024,
    isLargeDesktop: width >= 1280,
    width,
  };
};