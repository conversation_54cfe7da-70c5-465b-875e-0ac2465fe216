import React, { useRef, useEffect, useCallback } from 'react';
import { motion, useMotionValue, useTransform, useAnimation, PanInfo } from 'framer-motion';

interface SwipeGesturesProps {
  children: React.ReactNode;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  onLongPress?: () => void;
  onDoubleTap?: () => void;
  threshold?: number;
  longPressDelay?: number;
  className?: string;
  disabled?: boolean;
}

interface TouchGesture {
  type: 'swipe' | 'pinch' | 'tap' | 'long-press' | 'double-tap';
  direction?: 'left' | 'right' | 'up' | 'down';
  action: () => void;
  threshold?: number;
}

export const SwipeGestures: React.FC<SwipeGesturesProps> = ({
  children,
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  onLongPress,
  onDoubleTap,
  threshold = 50,
  longPressDelay = 500,
  className,
  disabled = false,
}) => {
  const x = useMotionValue(0);
  const y = useMotionValue(0);
  const controls = useAnimation();
  const longPressTimer = useRef<NodeJS.Timeout>();
  const lastTapTime = useRef<number>(0);
  const tapCount = useRef<number>(0);
  const isLongPress = useRef<boolean>(false);

  // Transform values for visual feedback
  const opacity = useTransform(
    x,
    [-100, 0, 100],
    [0.5, 1, 0.5]
  );

  const scale = useTransform(
    x,
    [-100, 0, 100],
    [0.95, 1, 0.95]
  );

  const handleDragEnd = useCallback(
    async (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
      if (disabled || isLongPress.current) {
        isLongPress.current = false;
        return;
      }

      const { offset, velocity } = info;
      const swipeThreshold = threshold;
      const swipeVelocity = 0.5;

      // Determine swipe direction
      if (Math.abs(offset.x) > Math.abs(offset.y)) {
        // Horizontal swipe
        if (offset.x > swipeThreshold && velocity.x > swipeVelocity && onSwipeRight) {
          await controls.start({ x: 200, opacity: 0, transition: { duration: 0.2 } });
          onSwipeRight();
        } else if (offset.x < -swipeThreshold && velocity.x < -swipeVelocity && onSwipeLeft) {
          await controls.start({ x: -200, opacity: 0, transition: { duration: 0.2 } });
          onSwipeLeft();
        }
      } else {
        // Vertical swipe
        if (offset.y > swipeThreshold && velocity.y > swipeVelocity && onSwipeDown) {
          await controls.start({ y: 200, opacity: 0, transition: { duration: 0.2 } });
          onSwipeDown();
        } else if (offset.y < -swipeThreshold && velocity.y < -swipeVelocity && onSwipeUp) {
          await controls.start({ y: -200, opacity: 0, transition: { duration: 0.2 } });
          onSwipeUp();
        }
      }

      // Reset position
      await controls.start({
        x: 0,
        y: 0,
        opacity: 1,
        transition: { type: 'spring', stiffness: 300, damping: 25 }
      });
    },
    [controls, disabled, onSwipeDown, onSwipeLeft, onSwipeRight, onSwipeUp, threshold]
  );

  const handleDragStart = useCallback(() => {
    if (disabled) return;

    // Start long press timer
    if (onLongPress) {
      longPressTimer.current = setTimeout(() => {
        isLongPress.current = true;
        onLongPress();
        // Provide haptic feedback if available
        if ('vibrate' in navigator) {
          navigator.vibrate(50);
        }
      }, longPressDelay);
    }
  }, [disabled, longPressDelay, onLongPress]);

  const handleDrag = useCallback(() => {
    // Cancel long press if user starts dragging
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
    }
  }, []);

  const handleTap = useCallback(() => {
    if (disabled) return;

    // Clear long press timer
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
    }

    // Handle double tap
    const now = Date.now();
    const timeSinceLastTap = now - lastTapTime.current;
    
    if (timeSinceLastTap < 300 && onDoubleTap) {
      onDoubleTap();
      tapCount.current = 0;
    } else {
      tapCount.current = 1;
    }
    
    lastTapTime.current = now;
  }, [disabled, onDoubleTap]);

  // Clean up timer on unmount
  useEffect(() => {
    return () => {
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current);
      }
    };
  }, []);

  if (disabled) {
    return <div className={className}>{children}</div>;
  }

  return (
    <motion.div
      className={className}
      drag
      dragConstraints={{ left: 0, right: 0, top: 0, bottom: 0 }}
      dragElastic={0.2}
      onDragStart={handleDragStart}
      onDrag={handleDrag}
      onDragEnd={handleDragEnd}
      onTap={handleTap}
      animate={controls}
      style={{ x, y, opacity, scale }}
      whileTap={{ scale: 0.98 }}
    >
      {children}
    </motion.div>
  );
};

// Custom hook for gesture detection
export const useGestures = (gestures: TouchGesture[]) => {
  const gestureHandlers = useRef<Map<string, () => void>>(new Map());

  useEffect(() => {
    gestures.forEach((gesture) => {
      const key = `${gesture.type}-${gesture.direction || ''}`;
      gestureHandlers.current.set(key, gesture.action);
    });
  }, [gestures]);

  const handleGesture = useCallback((type: string, direction?: string) => {
    const key = `${type}-${direction || ''}`;
    const handler = gestureHandlers.current.get(key);
    if (handler) {
      handler();
    }
  }, []);

  return { handleGesture };
};

// Pinch to zoom component
export const PinchToZoom: React.FC<{ children: React.ReactNode; className?: string }> = ({
  children,
  className,
}) => {
  const scale = useMotionValue(1);
  const controls = useAnimation();
  const containerRef = useRef<HTMLDivElement>(null);
  const initialDistance = useRef<number>(0);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleTouchStart = (e: TouchEvent) => {
      if (e.touches.length === 2) {
        const touch1 = e.touches[0];
        const touch2 = e.touches[1];
        initialDistance.current = Math.hypot(
          touch2.clientX - touch1.clientX,
          touch2.clientY - touch1.clientY
        );
      }
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (e.touches.length === 2 && initialDistance.current > 0) {
        e.preventDefault();
        const touch1 = e.touches[0];
        const touch2 = e.touches[1];
        const currentDistance = Math.hypot(
          touch2.clientX - touch1.clientX,
          touch2.clientY - touch1.clientY
        );
        const scaleFactor = currentDistance / initialDistance.current;
        scale.set(Math.min(Math.max(scaleFactor, 0.5), 3));
      }
    };

    const handleTouchEnd = () => {
      initialDistance.current = 0;
      controls.start({
        scale: 1,
        transition: { type: 'spring', stiffness: 300, damping: 25 }
      });
    };

    container.addEventListener('touchstart', handleTouchStart, { passive: false });
    container.addEventListener('touchmove', handleTouchMove, { passive: false });
    container.addEventListener('touchend', handleTouchEnd);

    return () => {
      container.removeEventListener('touchstart', handleTouchStart);
      container.removeEventListener('touchmove', handleTouchMove);
      container.removeEventListener('touchend', handleTouchEnd);
    };
  }, [controls, scale]);

  return (
    <motion.div
      ref={containerRef}
      className={className}
      animate={controls}
      style={{ scale }}
    >
      {children}
    </motion.div>
  );
};