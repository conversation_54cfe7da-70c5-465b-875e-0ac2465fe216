/**
 * Type definitions for mobile components
 */

export interface TouchGesture {
  type: 'swipe' | 'pinch' | 'tap' | 'long-press' | 'double-tap';
  direction?: 'left' | 'right' | 'up' | 'down';
  action: () => void;
  threshold?: number;
}

export interface QuickAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  action: () => void;
  badge?: number | string;
}

export interface RecentItem {
  id: string;
  title: string;
  type: 'notebook' | 'source' | 'chat' | 'slide';
  timestamp: Date;
  starred?: boolean;
  thumbnail?: string;
}

export interface MobileNavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: number;
  disabled?: boolean;
}

export interface MobileSearchFilters {
  contentTypes?: string[];
  dateRange?: { from?: Date; to?: Date };
  tags?: string[];
  authors?: string[];
  sortBy?: 'relevance' | 'date' | 'title';
}

export interface ResponsiveBreakpoints {
  mobile: number;
  tablet: number;
  desktop: number;
  largeDesktop: number;
}

export interface SwipeConfig {
  threshold: number;
  velocity: number;
  preventScrollOnSwipe: boolean;
  trackMouse: boolean;
  trackTouch: boolean;
}

export interface MobileViewState {
  currentView: string;
  previousView?: string;
  viewHistory: string[];
  transitionDirection?: 'forward' | 'backward';
}