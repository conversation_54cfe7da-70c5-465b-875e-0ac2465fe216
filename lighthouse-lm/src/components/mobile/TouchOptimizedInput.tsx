import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { X, Search, Mic, Camera, Paperclip, Send } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface TouchOptimizedInputProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit?: () => void;
  placeholder?: string;
  type?: 'text' | 'search' | 'chat' | 'multiline';
  className?: string;
  autoFocus?: boolean;
  showClearButton?: boolean;
  showVoiceInput?: boolean;
  showAttachments?: boolean;
  onVoiceInput?: () => void;
  onAttachment?: (type: 'camera' | 'file') => void;
  minHeight?: number;
  maxHeight?: number;
}

export const TouchOptimizedInput: React.FC<TouchOptimizedInputProps> = ({
  value,
  onChange,
  onSubmit,
  placeholder = 'Type here...',
  type = 'text',
  className,
  autoFocus = false,
  showClearButton = true,
  showVoiceInput = false,
  showAttachments = false,
  onVoiceInput,
  onAttachment,
  minHeight = 44,
  maxHeight = 200,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [textareaHeight, setTextareaHeight] = useState(minHeight);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    if (type === 'multiline' && textareaRef.current) {
      const scrollHeight = textareaRef.current.scrollHeight;
      const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);
      setTextareaHeight(newHeight);
    }
  }, [value, type, minHeight, maxHeight]);

  const handleClear = () => {
    onChange('');
    if (type === 'multiline') {
      textareaRef.current?.focus();
    } else {
      inputRef.current?.focus();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && onSubmit) {
      e.preventDefault();
      onSubmit();
    }
  };

  const handleVoiceInput = () => {
    if (onVoiceInput) {
      onVoiceInput();
    } else {
      // Fallback to browser's speech recognition API
      if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        const SpeechRecognition = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition;
        const recognition = new SpeechRecognition();
        
        recognition.continuous = false;
        recognition.interimResults = false;
        recognition.lang = 'en-US';
        
        recognition.onresult = (event: any) => {
          const transcript = event.results[0][0].transcript;
          onChange(value + ' ' + transcript);
        };
        
        recognition.start();
      }
    }
  };

  const renderIcon = () => {
    switch (type) {
      case 'search':
        return <Search className="h-5 w-5 text-muted-foreground" />;
      case 'chat':
        return null;
      default:
        return null;
    }
  };

  const baseInputClass = cn(
    'w-full bg-background border rounded-lg transition-all duration-200',
    'text-base', // Optimized font size for mobile
    'focus:ring-2 focus:ring-primary/20 focus:border-primary',
    isFocused ? 'border-primary' : 'border-input',
    className
  );

  if (type === 'multiline' || type === 'chat') {
    return (
      <div className={cn('relative', className)}>
        <div className={cn(baseInputClass, 'flex items-end p-2')}>
          {showAttachments && (
            <div className="flex items-center gap-1 pb-1">
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => onAttachment?.('file')}
              >
                <Paperclip className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => onAttachment?.('camera')}
              >
                <Camera className="h-4 w-4" />
              </Button>
            </div>
          )}

          <textarea
            ref={textareaRef}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            autoFocus={autoFocus}
            style={{ height: `${textareaHeight}px` }}
            className={cn(
              'flex-1 resize-none bg-transparent outline-none',
              'px-2 py-1.5',
              'placeholder:text-muted-foreground',
              'min-h-[44px]' // Touch-friendly minimum height
            )}
          />

          <div className="flex items-center gap-1 pb-1">
            {showVoiceInput && !value && (
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={handleVoiceInput}
              >
                <Mic className="h-4 w-4" />
              </Button>
            )}

            {value && showClearButton && (
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={handleClear}
              >
                <X className="h-4 w-4" />
              </Button>
            )}

            {type === 'chat' && value && (
              <Button
                type="button"
                size="icon"
                className="h-8 w-8"
                onClick={onSubmit}
              >
                <Send className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      <div className={baseInputClass}>
        <div className="flex items-center px-3">
          {renderIcon()}
          
          <input
            ref={inputRef}
            type="text"
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            autoFocus={autoFocus}
            className={cn(
              'flex-1 bg-transparent outline-none',
              'py-3', // Touch-friendly padding
              'placeholder:text-muted-foreground',
              renderIcon() ? 'ml-2' : ''
            )}
          />

          <div className="flex items-center gap-1">
            {showVoiceInput && !value && (
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={handleVoiceInput}
              >
                <Mic className="h-4 w-4" />
              </Button>
            )}

            <AnimatePresence>
              {value && showClearButton && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.15 }}
                >
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={handleClear}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </div>
  );
};

// Floating Action Button for mobile
interface FloatingActionButtonProps {
  onClick: () => void;
  icon: React.ReactNode;
  label?: string;
  position?: 'bottom-right' | 'bottom-left' | 'bottom-center';
  className?: string;
}

export const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  onClick,
  icon,
  label,
  position = 'bottom-right',
  className,
}) => {
  const positionClasses = {
    'bottom-right': 'bottom-20 right-4',
    'bottom-left': 'bottom-20 left-4',
    'bottom-center': 'bottom-20 left-1/2 -translate-x-1/2',
  };

  return (
    <motion.button
      onClick={onClick}
      className={cn(
        'fixed z-30 flex items-center gap-2',
        'bg-primary text-primary-foreground',
        'rounded-full shadow-lg',
        'hover:shadow-xl transition-shadow',
        positionClasses[position],
        label ? 'px-4 py-3' : 'p-4',
        className
      )}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      {icon}
      {label && <span className="font-medium">{label}</span>}
    </motion.button>
  );
};

// Touch-optimized select/dropdown
interface TouchSelectProps {
  value: string;
  onChange: (value: string) => void;
  options: { value: string; label: string }[];
  placeholder?: string;
  className?: string;
}

export const TouchSelect: React.FC<TouchSelectProps> = ({
  value,
  onChange,
  options,
  placeholder = 'Select...',
  className,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const selectedOption = options.find(opt => opt.value === value);

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className={cn(
          'w-full flex items-center justify-between',
          'px-3 py-3 bg-background border rounded-lg',
          'text-left',
          'hover:bg-muted/50 transition-colors',
          className
        )}
      >
        <span className={cn(!selectedOption && 'text-muted-foreground')}>
          {selectedOption?.label || placeholder}
        </span>
        <X className="h-4 w-4 text-muted-foreground rotate-45" />
      </button>

      <AnimatePresence>
        {isOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 z-40"
              onClick={() => setIsOpen(false)}
            />

            <motion.div
              initial={{ y: '100%' }}
              animate={{ y: 0 }}
              exit={{ y: '100%' }}
              transition={{ type: 'spring', damping: 25, stiffness: 300 }}
              className="fixed left-0 right-0 bottom-0 bg-background rounded-t-xl z-50 max-h-[70vh]"
            >
              <div className="p-4 border-b">
                <h3 className="font-semibold text-lg">{placeholder}</h3>
              </div>
              
              <div className="overflow-y-auto max-h-[calc(70vh-80px)]">
                {options.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => {
                      onChange(option.value);
                      setIsOpen(false);
                    }}
                    className={cn(
                      'w-full px-4 py-3 text-left',
                      'hover:bg-muted/50 transition-colors',
                      'border-b',
                      value === option.value && 'bg-primary/10 text-primary'
                    )}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};