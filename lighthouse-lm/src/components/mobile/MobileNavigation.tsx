import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Menu,
  X,
  Home,
  BookOpen,
  MessageSquare,
  FileText,
  Search,
  Settings,
  Plus,
  ChevronRight,
  Clock,
  Star,
  Folder
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';

interface MobileNavigationProps {
  currentView: string;
  onViewChange: (view: string) => void;
  quickActions?: QuickAction[];
  recentItems?: RecentItem[];
  user?: {
    name: string;
    email: string;
    avatar?: string;
  };
  notifications?: number;
}

interface QuickAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  action: () => void;
}

interface RecentItem {
  id: string;
  title: string;
  type: 'notebook' | 'source' | 'chat' | 'slide';
  timestamp: Date;
  starred?: boolean;
}

const navigationItems = [
  { id: 'dashboard', label: 'Dashboard', icon: Home },
  { id: 'notebooks', label: 'Notebooks', icon: BookOpen },
  { id: 'chat', label: 'Chat', icon: MessageSquare },
  { id: 'sources', label: 'Sources', icon: FileText },
  { id: 'search', label: 'Search', icon: Search },
  { id: 'settings', label: 'Settings', icon: Settings },
];

const typeIcons = {
  notebook: BookOpen,
  source: FileText,
  chat: MessageSquare,
  slide: Folder,
};

export const MobileNavigation: React.FC<MobileNavigationProps> = ({
  currentView,
  onViewChange,
  quickActions = [],
  recentItems = [],
  user,
  notifications = 0,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'menu' | 'recent' | 'quick'>('menu');

  // Close menu when view changes
  useEffect(() => {
    setIsOpen(false);
  }, [currentView]);

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handleViewChange = (view: string) => {
    onViewChange(view);
    setIsOpen(false);
  };

  const formatTimeAgo = (date: Date) => {
    const seconds = Math.floor((new Date().getTime() - date.getTime()) / 1000);
    if (seconds < 60) return 'just now';
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  };

  return (
    <>
      {/* Mobile Header Bar */}
      <div className="lg:hidden fixed top-0 left-0 right-0 z-40 bg-background border-b">
        <div className="flex items-center justify-between p-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsOpen(!isOpen)}
            className="relative"
          >
            {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            {notifications > 0 && !isOpen && (
              <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center">
                {notifications}
              </Badge>
            )}
          </Button>

          <h1 className="text-lg font-semibold capitalize">{currentView}</h1>

          <Button variant="ghost" size="icon">
            <Plus className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Mobile Navigation Drawer */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="lg:hidden fixed inset-0 bg-black/50 z-40"
              onClick={() => setIsOpen(false)}
            />

            {/* Navigation Panel */}
            <motion.div
              initial={{ x: '-100%' }}
              animate={{ x: 0 }}
              exit={{ x: '-100%' }}
              transition={{ type: 'spring', damping: 25, stiffness: 300 }}
              className="lg:hidden fixed top-0 left-0 bottom-0 w-80 max-w-[85vw] bg-background border-r z-50 flex flex-col"
            >
              {/* User Profile Section */}
              {user && (
                <div className="p-6 bg-muted/50">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={user.avatar} alt={user.name} />
                      <AvatarFallback>{user.name[0]}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <p className="font-semibold truncate">{user.name}</p>
                      <p className="text-sm text-muted-foreground truncate">{user.email}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Tab Navigation */}
              <div className="flex border-b">
                {(['menu', 'recent', 'quick'] as const).map((tab) => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab)}
                    className={cn(
                      'flex-1 py-3 text-sm font-medium capitalize transition-colors',
                      activeTab === tab
                        ? 'text-primary border-b-2 border-primary'
                        : 'text-muted-foreground'
                    )}
                  >
                    {tab === 'quick' ? 'Quick Actions' : tab}
                  </button>
                ))}
              </div>

              {/* Tab Content */}
              <ScrollArea className="flex-1">
                {activeTab === 'menu' && (
                  <div className="p-4 space-y-2">
                    {navigationItems.map((item) => {
                      const Icon = item.icon;
                      const isActive = currentView === item.id;
                      return (
                        <button
                          key={item.id}
                          onClick={() => handleViewChange(item.id)}
                          className={cn(
                            'w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-colors',
                            isActive
                              ? 'bg-primary text-primary-foreground'
                              : 'hover:bg-muted'
                          )}
                        >
                          <Icon className="h-5 w-5" />
                          <span className="font-medium">{item.label}</span>
                          {item.id === 'chat' && notifications > 0 && (
                            <Badge className="ml-auto">{notifications}</Badge>
                          )}
                          <ChevronRight className="h-4 w-4 ml-auto opacity-50" />
                        </button>
                      );
                    })}
                  </div>
                )}

                {activeTab === 'recent' && (
                  <div className="p-4">
                    {recentItems.length === 0 ? (
                      <p className="text-center text-muted-foreground py-8">
                        No recent items
                      </p>
                    ) : (
                      <div className="space-y-2">
                        {recentItems.map((item) => {
                          const Icon = typeIcons[item.type];
                          return (
                            <button
                              key={item.id}
                              className="w-full flex items-start gap-3 p-3 rounded-lg hover:bg-muted transition-colors"
                            >
                              <div className="mt-0.5">
                                <Icon className="h-4 w-4 text-muted-foreground" />
                              </div>
                              <div className="flex-1 text-left min-w-0">
                                <p className="font-medium truncate">{item.title}</p>
                                <p className="text-xs text-muted-foreground">
                                  {formatTimeAgo(item.timestamp)}
                                </p>
                              </div>
                              {item.starred && (
                                <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                              )}
                            </button>
                          );
                        })}
                      </div>
                    )}
                  </div>
                )}

                {activeTab === 'quick' && (
                  <div className="p-4">
                    {quickActions.length === 0 ? (
                      <p className="text-center text-muted-foreground py-8">
                        No quick actions available
                      </p>
                    ) : (
                      <div className="grid grid-cols-2 gap-3">
                        {quickActions.map((action) => (
                          <button
                            key={action.id}
                            onClick={action.action}
                            className="flex flex-col items-center gap-2 p-4 rounded-lg bg-muted hover:bg-muted/80 transition-colors"
                          >
                            <div className="h-10 w-10 rounded-full bg-background flex items-center justify-center">
                              {action.icon}
                            </div>
                            <span className="text-xs font-medium text-center">
                              {action.label}
                            </span>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </ScrollArea>

              {/* Footer Actions */}
              <div className="p-4 border-t bg-muted/30">
                <div className="flex gap-2">
                  <Button variant="outline" className="flex-1" size="sm">
                    <Clock className="h-4 w-4 mr-2" />
                    History
                  </Button>
                  <Button variant="outline" className="flex-1" size="sm">
                    <Star className="h-4 w-4 mr-2" />
                    Starred
                  </Button>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Bottom Navigation Bar (Alternative) */}
      <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-background border-t z-30">
        <div className="flex justify-around py-2">
          {navigationItems.slice(0, 5).map((item) => {
            const Icon = item.icon;
            const isActive = currentView === item.id;
            return (
              <button
                key={item.id}
                onClick={() => onViewChange(item.id)}
                className={cn(
                  'flex flex-col items-center gap-1 px-3 py-2 rounded-lg transition-colors',
                  isActive ? 'text-primary' : 'text-muted-foreground'
                )}
              >
                <Icon className="h-5 w-5" />
                <span className="text-xs">{item.label}</span>
              </button>
            );
          })}
        </div>
      </div>
    </>
  );
};