import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, X, Clock, TrendingUp, Filter, ArrowLeft } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { TouchOptimizedInput } from './TouchOptimizedInput';
import { useDebounce } from '@/hooks/useDebounce';

interface MobileSearchProps {
  onSearch: (query: string, filters?: SearchFilters) => void;
  onClose?: () => void;
  suggestions?: string[];
  recentSearches?: string[];
  trendingSearches?: string[];
  className?: string;
}

interface SearchFilters {
  contentTypes?: string[];
  dateRange?: { from?: Date; to?: Date };
  tags?: string[];
}

export const MobileSearch: React.FC<MobileSearchProps> = ({
  onSearch,
  onClose,
  suggestions = [],
  recentSearches = [],
  trendingSearches = [],
  className,
}) => {
  const [query, setQuery] = useState('');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({});
  const [showSuggestions, setShowSuggestions] = useState(false);
  const debouncedQuery = useDebounce(query, 300);
  const searchInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (debouncedQuery.length > 0) {
      setShowSuggestions(true);
    } else {
      setShowSuggestions(false);
    }
  }, [debouncedQuery]);

  const handleSearch = (searchQuery: string = query) => {
    if (searchQuery.trim()) {
      onSearch(searchQuery, filters);
      // Add to recent searches (would be stored in local storage/backend)
      setQuery('');
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setQuery(suggestion);
    handleSearch(suggestion);
  };

  const clearFilters = () => {
    setFilters({});
  };

  const hasActiveFilters = Object.keys(filters).length > 0;

  return (
    <div className={cn('fixed inset-0 bg-background z-50 flex flex-col', className)}>
      {/* Header */}
      <div className="flex items-center gap-2 p-4 border-b">
        <Button
          variant="ghost"
          size="icon"
          onClick={onClose}
          className="shrink-0"
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>

        <div className="flex-1">
          <TouchOptimizedInput
            value={query}
            onChange={setQuery}
            onSubmit={() => handleSearch()}
            type="search"
            placeholder="Search everything..."
            autoFocus
            showVoiceInput
          />
        </div>

        <Button
          variant={hasActiveFilters ? 'default' : 'ghost'}
          size="icon"
          onClick={() => setIsFilterOpen(!isFilterOpen)}
          className="shrink-0 relative"
        >
          <Filter className="h-5 w-5" />
          {hasActiveFilters && (
            <span className="absolute -top-1 -right-1 h-2 w-2 bg-primary rounded-full" />
          )}
        </Button>
      </div>

      {/* Active Filters */}
      {hasActiveFilters && (
        <div className="px-4 py-2 border-b bg-muted/30">
          <div className="flex items-center gap-2 overflow-x-auto">
            <span className="text-sm text-muted-foreground shrink-0">Filters:</span>
            {filters.contentTypes?.map((type) => (
              <Badge key={type} variant="secondary" className="shrink-0">
                {type}
              </Badge>
            ))}
            {filters.tags?.map((tag) => (
              <Badge key={tag} variant="secondary" className="shrink-0">
                #{tag}
              </Badge>
            ))}
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="shrink-0 h-6 px-2"
            >
              Clear all
            </Button>
          </div>
        </div>
      )}

      {/* Search Content */}
      <div className="flex-1 overflow-y-auto">
        <AnimatePresence mode="wait">
          {showSuggestions && suggestions.length > 0 ? (
            // Search Suggestions
            <motion.div
              key="suggestions"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="p-4"
            >
              <h3 className="text-sm font-medium text-muted-foreground mb-3">
                Suggestions
              </h3>
              <div className="space-y-2">
                {suggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-muted transition-colors text-left"
                  >
                    <Search className="h-4 w-4 text-muted-foreground shrink-0" />
                    <span className="flex-1">
                      {suggestion.split(query).map((part, i) => (
                        <React.Fragment key={i}>
                          {i > 0 && <mark className="bg-primary/20 text-primary">{query}</mark>}
                          {part}
                        </React.Fragment>
                      ))}
                    </span>
                  </button>
                ))}
              </div>
            </motion.div>
          ) : (
            // Default View (Recent & Trending)
            <motion.div
              key="default"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="p-4 space-y-6"
            >
              {/* Recent Searches */}
              {recentSearches.length > 0 && (
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Recent
                    </h3>
                    <Button variant="ghost" size="sm" className="h-7 text-xs">
                      Clear
                    </Button>
                  </div>
                  <div className="space-y-2">
                    {recentSearches.map((search, index) => (
                      <button
                        key={index}
                        onClick={() => handleSuggestionClick(search)}
                        className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-muted transition-colors text-left"
                      >
                        <Clock className="h-4 w-4 text-muted-foreground shrink-0" />
                        <span className="flex-1">{search}</span>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Trending Searches */}
              {trendingSearches.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2 mb-3">
                    <TrendingUp className="h-4 w-4" />
                    Trending
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {trendingSearches.map((search, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        onClick={() => handleSuggestionClick(search)}
                        className="h-8"
                      >
                        {search}
                      </Button>
                    ))}
                  </div>
                </div>
              )}

              {/* Quick Filters */}
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-3">
                  Quick Filters
                </h3>
                <div className="grid grid-cols-2 gap-3">
                  {['Notebooks', 'Sources', 'Chats', 'Recent'].map((filter) => (
                    <Button
                      key={filter}
                      variant="outline"
                      onClick={() => {
                        setFilters({ contentTypes: [filter.toLowerCase()] });
                        handleSearch('');
                      }}
                      className="h-12"
                    >
                      {filter}
                    </Button>
                  ))}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Filter Panel */}
      <AnimatePresence>
        {isFilterOpen && (
          <MobileFilterPanel
            filters={filters}
            onFiltersChange={setFilters}
            onClose={() => setIsFilterOpen(false)}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

// Mobile Filter Panel
interface MobileFilterPanelProps {
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  onClose: () => void;
}

const MobileFilterPanel: React.FC<MobileFilterPanelProps> = ({
  filters,
  onFiltersChange,
  onClose,
}) => {
  const [localFilters, setLocalFilters] = useState<SearchFilters>(filters);

  const contentTypes = ['Notebooks', 'Sources', 'Chats', 'Slides'];
  const dateRanges = [
    { label: 'Today', value: 'today' },
    { label: 'This Week', value: 'week' },
    { label: 'This Month', value: 'month' },
    { label: 'This Year', value: 'year' },
  ];

  const handleApply = () => {
    onFiltersChange(localFilters);
    onClose();
  };

  const toggleContentType = (type: string) => {
    const types = localFilters.contentTypes || [];
    const newTypes = types.includes(type)
      ? types.filter(t => t !== type)
      : [...types, type];
    setLocalFilters({ ...localFilters, contentTypes: newTypes });
  };

  return (
    <>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 z-40"
        onClick={onClose}
      />

      <motion.div
        initial={{ y: '100%' }}
        animate={{ y: 0 }}
        exit={{ y: '100%' }}
        transition={{ type: 'spring', damping: 25, stiffness: 300 }}
        className="fixed left-0 right-0 bottom-0 bg-background rounded-t-xl z-50 max-h-[80vh]"
      >
        <div className="p-4 border-b">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-lg">Filters</h3>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-5 w-5" />
            </Button>
          </div>
        </div>

        <div className="p-4 space-y-6 overflow-y-auto max-h-[calc(80vh-140px)]">
          {/* Content Types */}
          <div>
            <h4 className="font-medium mb-3">Content Type</h4>
            <div className="grid grid-cols-2 gap-2">
              {contentTypes.map((type) => (
                <Button
                  key={type}
                  variant={localFilters.contentTypes?.includes(type) ? 'default' : 'outline'}
                  onClick={() => toggleContentType(type)}
                  className="justify-start"
                >
                  {type}
                </Button>
              ))}
            </div>
          </div>

          {/* Date Range */}
          <div>
            <h4 className="font-medium mb-3">Date Range</h4>
            <div className="grid grid-cols-2 gap-2">
              {dateRanges.map((range) => (
                <Button
                  key={range.value}
                  variant="outline"
                  className="justify-start"
                >
                  {range.label}
                </Button>
              ))}
            </div>
          </div>
        </div>

        <div className="p-4 border-t flex gap-2">
          <Button variant="outline" onClick={() => setLocalFilters({})} className="flex-1">
            Reset
          </Button>
          <Button onClick={handleApply} className="flex-1">
            Apply Filters
          </Button>
        </div>
      </motion.div>
    </>
  );
};