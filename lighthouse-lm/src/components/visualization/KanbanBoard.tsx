/**
 * KanbanBoard - Interactive Kanban board visualization
 * Built with dnd-kit for drag-and-drop task management
 */

import React, { useState, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  DragOverlay,
  UniqueIdentifier
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  rectSortingStrategy
} from '@dnd-kit/sortable';
import {
  useSortable
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

import {
  Plus,
  MoreHorizontal,
  Calendar,
  User,
  MessageSquare,
  Paperclip,
  Flag,
  Edit,
  Trash2,
  Filter
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';

import {
  KanbanVisualization,
  KanbanCard,
  KanbanColumn,
  VisualizationTheme,
  BaseVisualization
} from '@/types/visualization';

interface KanbanBoardProps {
  visualization?: KanbanVisualization;
  theme?: VisualizationTheme;
  editable?: boolean;
  onUpdate?: (visualization: BaseVisualization) => void;
  onEvent?: (event: any) => void;
  onCardMove?: (cardId: string, sourceColumn: string, targetColumn: string) => void;
  onCardClick?: (card: KanbanCard) => void;
}

// Sample Kanban data
const sampleColumns: KanbanColumn[] = [
  {
    id: 'todo',
    title: 'To Do',
    color: '#6b7280',
    limit: 10,
    position: 0,
    cards: [
      {
        id: 'card1',
        title: 'Design user interface',
        description: 'Create mockups and wireframes for the main dashboard',
        labels: ['Design', 'UI'],
        assignee: 'Alice Johnson',
        priority: 'high',
        dueDate: new Date('2024-02-15'),
        createdAt: new Date('2024-01-10'),
        color: '#3b82f6',
        attachments: 3,
        comments: 2,
        checklist: { total: 5, completed: 1 }
      },
      {
        id: 'card2',
        title: 'Research competitors',
        description: 'Analyze competitor features and pricing',
        labels: ['Research'],
        assignee: 'Bob Smith',
        priority: 'medium',
        dueDate: new Date('2024-02-20'),
        createdAt: new Date('2024-01-12'),
        color: '#10b981',
        attachments: 0,
        comments: 1
      }
    ]
  },
  {
    id: 'in-progress',
    title: 'In Progress',
    color: '#f59e0b',
    limit: 5,
    position: 1,
    cards: [
      {
        id: 'card3',
        title: 'Implement authentication',
        description: 'Set up user login and registration system',
        labels: ['Development', 'Backend'],
        assignee: 'Charlie Davis',
        priority: 'high',
        dueDate: new Date('2024-02-10'),
        createdAt: new Date('2024-01-08'),
        color: '#8b5cf6',
        attachments: 1,
        comments: 5,
        checklist: { total: 8, completed: 6 }
      },
      {
        id: 'card4',
        title: 'Database optimization',
        description: 'Improve query performance and indexing',
        labels: ['Database', 'Performance'],
        assignee: 'Diana Wilson',
        priority: 'medium',
        createdAt: new Date('2024-01-15'),
        color: '#ec4899',
        attachments: 2,
        comments: 3,
        checklist: { total: 4, completed: 2 }
      }
    ]
  },
  {
    id: 'review',
    title: 'Code Review',
    color: '#8b5cf6',
    limit: 3,
    position: 2,
    cards: [
      {
        id: 'card5',
        title: 'API documentation',
        description: 'Document all REST API endpoints',
        labels: ['Documentation'],
        assignee: 'Eve Brown',
        priority: 'low',
        dueDate: new Date('2024-02-25'),
        createdAt: new Date('2024-01-18'),
        color: '#06b6d4',
        attachments: 4,
        comments: 0
      }
    ]
  },
  {
    id: 'done',
    title: 'Done',
    color: '#10b981',
    position: 3,
    cards: [
      {
        id: 'card6',
        title: 'Project setup',
        description: 'Initialize project structure and dependencies',
        labels: ['Setup'],
        assignee: 'Frank Miller',
        priority: 'high',
        createdAt: new Date('2024-01-05'),
        color: '#6366f1',
        attachments: 0,
        comments: 1
      },
      {
        id: 'card7',
        title: 'Requirements gathering',
        description: 'Collect and document project requirements',
        labels: ['Planning'],
        assignee: 'Grace Lee',
        priority: 'high',
        createdAt: new Date('2024-01-03'),
        color: '#f97316',
        attachments: 2,
        comments: 8
      }
    ]
  }
];

const priorityColors = {
  low: '#94a3b8',
  medium: '#f59e0b',
  high: '#f97316',
  critical: '#ef4444'
};

// Sortable Card Component
const SortableCard: React.FC<{
  card: KanbanCard;
  onCardClick?: (card: KanbanCard) => void;
}> = ({ card, onCardClick }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: card.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1
  };

  return (
    <motion.div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="mb-3 cursor-grab active:cursor-grabbing"
      onClick={() => onCardClick?.(card)}
    >
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="p-3">
          {/* Labels */}
          {card.labels && card.labels.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-2">
              {card.labels.map((label, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {label}
                </Badge>
              ))}
            </div>
          )}

          {/* Title */}
          <h3 className="font-medium text-sm mb-2 line-clamp-2">
            {card.title}
          </h3>

          {/* Description */}
          {card.description && (
            <p className="text-xs text-muted-foreground mb-3 line-clamp-2">
              {card.description}
            </p>
          )}

          {/* Progress */}
          {card.checklist && (
            <div className="mb-3">
              <div className="flex items-center justify-between text-xs mb-1">
                <span className="text-muted-foreground">
                  {card.checklist.completed}/{card.checklist.total} tasks
                </span>
                <span className="text-muted-foreground">
                  {Math.round((card.checklist.completed / card.checklist.total) * 100)}%
                </span>
              </div>
              <Progress 
                value={(card.checklist.completed / card.checklist.total) * 100} 
                className="h-2"
              />
            </div>
          )}

          {/* Footer */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {/* Priority */}
              {card.priority && (
                <div
                  className="w-2 h-2 rounded-full"
                  style={{ backgroundColor: priorityColors[card.priority] }}
                />
              )}

              {/* Due date */}
              {card.dueDate && (
                <div className="flex items-center text-xs text-muted-foreground">
                  <Calendar className="h-3 w-3 mr-1" />
                  {card.dueDate.toLocaleDateString()}
                </div>
              )}
            </div>

            <div className="flex items-center gap-1">
              {/* Attachments */}
              {card.attachments && card.attachments > 0 && (
                <div className="flex items-center text-xs text-muted-foreground">
                  <Paperclip className="h-3 w-3 mr-1" />
                  {card.attachments}
                </div>
              )}

              {/* Comments */}
              {card.comments && card.comments > 0 && (
                <div className="flex items-center text-xs text-muted-foreground">
                  <MessageSquare className="h-3 w-3 mr-1" />
                  {card.comments}
                </div>
              )}

              {/* Assignee */}
              {card.assignee && (
                <Avatar className="h-6 w-6">
                  <AvatarImage src="" />
                  <AvatarFallback className="text-xs">
                    {card.assignee.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

// Sortable Column Component
const SortableColumn: React.FC<{
  column: KanbanColumn;
  cards: KanbanCard[];
  onCardClick?: (card: KanbanCard) => void;
  onAddCard?: (columnId: string) => void;
}> = ({ column, cards, onCardClick, onAddCard }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: column.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.8 : 1
  };

  return (
    <motion.div
      ref={setNodeRef}
      style={style}
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      className="flex flex-col w-80 bg-gray-50 rounded-lg"
    >
      {/* Column Header */}
      <div
        {...attributes}
        {...listeners}
        className="flex items-center justify-between p-4 cursor-grab active:cursor-grabbing"
      >
        <div className="flex items-center gap-2">
          <div
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: column.color }}
          />
          <h2 className="font-semibold text-sm">{column.title}</h2>
          <Badge variant="outline" className="text-xs">
            {cards.length}
            {column.limit && `/${column.limit}`}
          </Badge>
        </div>
        
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onAddCard?.(column.id)}
            className="h-6 w-6 p-0"
          >
            <Plus className="h-3 w-3" />
          </Button>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem>
                <Edit className="h-4 w-4 mr-2" />
                Edit Column
              </DropdownMenuItem>
              <DropdownMenuItem className="text-destructive">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Column
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Cards Container */}
      <div className="flex-1 p-4 pt-0 min-h-[400px]">
        <SortableContext items={cards.map(c => c.id)} strategy={verticalListSortingStrategy}>
          <AnimatePresence>
            {cards.map(card => (
              <SortableCard
                key={card.id}
                card={card}
                onCardClick={onCardClick}
              />
            ))}
          </AnimatePresence>
        </SortableContext>
      </div>
    </motion.div>
  );
};

export const KanbanBoard: React.FC<KanbanBoardProps> = ({
  visualization,
  theme,
  editable = true,
  onUpdate,
  onEvent,
  onCardMove,
  onCardClick
}) => {
  const [columns, setColumns] = useState<KanbanColumn[]>(
    visualization?.columns || sampleColumns
  );
  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);
  const [selectedCard, setSelectedCard] = useState<KanbanCard | null>(null);
  const [isCardDialogOpen, setIsCardDialogOpen] = useState(false);
  const [filter, setFilter] = useState<string>('all');

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  );

  // Get all cards
  const allCards = useMemo(() => {
    return columns.flatMap(column => column.cards);
  }, [columns]);

  // Get active card being dragged
  const activeCard = useMemo(() => {
    return allCards.find(card => card.id === activeId);
  }, [allCards, activeId]);

  // Handle drag start
  const handleDragStart = useCallback((event: DragStartEvent) => {
    setActiveId(event.active.id);
  }, []);

  // Handle drag over
  const handleDragOver = useCallback((event: DragOverEvent) => {
    const { active, over } = event;
    
    if (!over) return;
    
    const activeId = active.id;
    const overId = over.id;
    
    if (activeId === overId) return;

    const isActiveACard = allCards.some(card => card.id === activeId);
    const isOverACard = allCards.some(card => card.id === overId);
    const isOverAColumn = columns.some(column => column.id === overId);

    if (!isActiveACard) return;

    // Dropping a card over another card
    if (isActiveACard && isOverACard) {
      setColumns(columns => {
        const activeCard = allCards.find(card => card.id === activeId)!;
        const overCard = allCards.find(card => card.id === overId)!;
        
        const activeColumn = columns.find(col => col.cards.some(card => card.id === activeId))!;
        const overColumn = columns.find(col => col.cards.some(card => card.id === overId))!;
        
        if (activeColumn.id === overColumn.id) {
          // Same column - reorder cards
          const activeIndex = activeColumn.cards.findIndex(card => card.id === activeId);
          const overIndex = overColumn.cards.findIndex(card => card.id === overId);
          
          return columns.map(col => {
            if (col.id === activeColumn.id) {
              return {
                ...col,
                cards: arrayMove(col.cards, activeIndex, overIndex)
              };
            }
            return col;
          });
        } else {
          // Different columns - move card
          return columns.map(col => {
            if (col.id === activeColumn.id) {
              return {
                ...col,
                cards: col.cards.filter(card => card.id !== activeId)
              };
            } else if (col.id === overColumn.id) {
              const overIndex = col.cards.findIndex(card => card.id === overId);
              const newCards = [...col.cards];
              newCards.splice(overIndex, 0, activeCard);
              return {
                ...col,
                cards: newCards
              };
            }
            return col;
          });
        }
      });
    }

    // Dropping a card over a column
    if (isActiveACard && isOverAColumn) {
      setColumns(columns => {
        const activeCard = allCards.find(card => card.id === activeId)!;
        const activeColumn = columns.find(col => col.cards.some(card => card.id === activeId))!;
        const overColumn = columns.find(col => col.id === overId)!;
        
        if (activeColumn.id === overColumn.id) return columns;
        
        return columns.map(col => {
          if (col.id === activeColumn.id) {
            return {
              ...col,
              cards: col.cards.filter(card => card.id !== activeId)
            };
          } else if (col.id === overColumn.id) {
            return {
              ...col,
              cards: [...col.cards, activeCard]
            };
          }
          return col;
        });
      });
    }
  }, [allCards, columns]);

  // Handle drag end
  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;
    
    if (over && active.id !== over.id) {
      const activeCard = allCards.find(card => card.id === active.id);
      if (activeCard) {
        const sourceColumn = columns.find(col => col.cards.some(card => card.id === active.id));
        const targetColumn = columns.find(col => col.id === over.id || col.cards.some(card => card.id === over.id));
        
        if (sourceColumn && targetColumn && sourceColumn.id !== targetColumn.id) {
          onCardMove?.(activeCard.id, sourceColumn.id, targetColumn.id);
          onEvent?.({
            type: 'card-moved',
            data: {
              cardId: activeCard.id,
              sourceColumn: sourceColumn.id,
              targetColumn: targetColumn.id
            }
          });
        }
      }
    }
    
    setActiveId(null);
  }, [allCards, columns, onCardMove, onEvent]);

  // Handle card click
  const handleCardClick = useCallback((card: KanbanCard) => {
    setSelectedCard(card);
    setIsCardDialogOpen(true);
    onCardClick?.(card);
  }, [onCardClick]);

  // Add new card
  const handleAddCard = useCallback((columnId: string) => {
    const newCard: KanbanCard = {
      id: `card-${Date.now()}`,
      title: 'New Task',
      description: '',
      createdAt: new Date(),
      priority: 'medium'
    };

    setColumns(cols => cols.map(col => 
      col.id === columnId 
        ? { ...col, cards: [...col.cards, newCard] }
        : col
    ));

    setSelectedCard(newCard);
    setIsCardDialogOpen(true);
  }, []);

  // Filter cards based on assignee or label
  const filteredColumns = useMemo(() => {
    if (filter === 'all') return columns;
    
    return columns.map(column => ({
      ...column,
      cards: column.cards.filter(card => 
        card.assignee?.includes(filter) || 
        card.labels?.some(label => label.toLowerCase().includes(filter.toLowerCase()))
      )
    }));
  }, [columns, filter]);

  return (
    <div className="w-full h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div>
          <h1 className="text-xl font-semibold">Kanban Board</h1>
          <p className="text-sm text-muted-foreground">
            {allCards.length} total tasks across {columns.length} columns
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            <Input
              placeholder="Filter by assignee or label..."
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="w-64 h-8"
            />
          </div>
          
          {editable && (
            <Button size="sm">
              <Plus className="h-4 w-4 mr-1" />
              Add Column
            </Button>
          )}
        </div>
      </div>

      {/* Board */}
      <div className="flex-1 p-4 overflow-x-auto">
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragStart={handleDragStart}
          onDragOver={handleDragOver}
          onDragEnd={handleDragEnd}
        >
          <div className="flex gap-4 pb-4">
            <SortableContext items={columns.map(c => c.id)} strategy={rectSortingStrategy}>
              {filteredColumns.map(column => (
                <SortableColumn
                  key={column.id}
                  column={column}
                  cards={column.cards}
                  onCardClick={handleCardClick}
                  onAddCard={editable ? handleAddCard : undefined}
                />
              ))}
            </SortableContext>
          </div>

          {/* Drag Overlay */}
          <DragOverlay>
            {activeCard ? (
              <div className="w-80 opacity-90">
                <SortableCard card={activeCard} />
              </div>
            ) : null}
          </DragOverlay>
        </DndContext>
      </div>

      {/* Card Details Dialog */}
      <Dialog open={isCardDialogOpen} onOpenChange={setIsCardDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {selectedCard?.title || 'Card Details'}
            </DialogTitle>
          </DialogHeader>
          
          {selectedCard && (
            <div className="space-y-4">
              <div>
                <Label>Title</Label>
                <Input value={selectedCard.title} readOnly />
              </div>
              
              <div>
                <Label>Description</Label>
                <Textarea 
                  value={selectedCard.description || ''} 
                  readOnly 
                  rows={3}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Priority</Label>
                  <Select value={selectedCard.priority || 'medium'} disabled>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="critical">Critical</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label>Assignee</Label>
                  <Input value={selectedCard.assignee || ''} readOnly />
                </div>
              </div>
              
              {selectedCard.labels && selectedCard.labels.length > 0 && (
                <div>
                  <Label>Labels</Label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {selectedCard.labels.map((label, index) => (
                      <Badge key={index} variant="secondary">
                        {label}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
              
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>Created: {selectedCard.createdAt.toLocaleDateString()}</span>
                {selectedCard.dueDate && (
                  <span>Due: {selectedCard.dueDate.toLocaleDateString()}</span>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};