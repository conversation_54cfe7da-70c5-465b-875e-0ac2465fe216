/**
 * ChartBuilder - Interactive chart creation and editing component
 * Supports multiple chart types with drag-and-drop configuration
 */

import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
  ScatterChart,
  Scatter,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { 
  Bar<PERSON>hart3, 
  <PERSON><PERSON>hart as LineChartIcon, 
  <PERSON><PERSON>hart as PieChartIcon,
  Activity,
  Target,
  TrendingUp,
  Plus,
  X,
  Edit3,
  Palette
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';

import { 
  ChartType,
  ChartVisualization,
  ChartConfig,
  ChartDataPoint,
  ChartSeries,
  VisualizationTheme,
  BaseVisualization
} from '@/types/visualization';

interface ChartBuilderProps {
  visualization?: ChartVisualization;
  theme?: VisualizationTheme;
  editable?: boolean;
  onUpdate?: (visualization: BaseVisualization) => void;
  onEvent?: (event: any) => void;
}

// Chart type configurations
const CHART_TYPES: Record<ChartType, { icon: React.ReactNode; label: string; description: string }> = {
  line: {
    icon: <LineChartIcon className="h-4 w-4" />,
    label: 'Line Chart',
    description: 'Show trends over time'
  },
  bar: {
    icon: <BarChart3 className="h-4 w-4" />,
    label: 'Bar Chart',
    description: 'Compare categories'
  },
  area: {
    icon: <Activity className="h-4 w-4" />,
    label: 'Area Chart',
    description: 'Show cumulative values'
  },
  pie: {
    icon: <PieChartIcon className="h-4 w-4" />,
    label: 'Pie Chart',
    description: 'Show proportions'
  },
  scatter: {
    icon: <Target className="h-4 w-4" />,
    label: 'Scatter Plot',
    description: 'Show correlations'
  },
  radar: {
    icon: <TrendingUp className="h-4 w-4" />,
    label: 'Radar Chart',
    description: 'Compare multiple metrics'
  },
  treemap: {
    icon: <BarChart3 className="h-4 w-4" />,
    label: 'Treemap',
    description: 'Hierarchical data'
  },
  funnel: {
    icon: <TrendingUp className="h-4 w-4" />,
    label: 'Funnel Chart',
    description: 'Conversion rates'
  }
};

// Sample data for different chart types
const SAMPLE_DATA: Record<ChartType, ChartDataPoint[]> = {
  line: [
    { name: 'Jan', value: 400, value2: 240 },
    { name: 'Feb', value: 300, value2: 139 },
    { name: 'Mar', value: 300, value2: 980 },
    { name: 'Apr', value: 200, value2: 390 },
    { name: 'May', value: 278, value2: 480 },
    { name: 'Jun', value: 189, value2: 380 }
  ],
  bar: [
    { name: 'Product A', sales: 4000, profit: 2400 },
    { name: 'Product B', sales: 3000, profit: 1398 },
    { name: 'Product C', sales: 2000, profit: 9800 },
    { name: 'Product D', sales: 2780, profit: 3908 }
  ],
  area: [
    { name: 'Page A', uv: 4000, pv: 2400, amt: 2400 },
    { name: 'Page B', uv: 3000, pv: 1398, amt: 2210 },
    { name: 'Page C', uv: 2000, pv: 9800, amt: 2290 },
    { name: 'Page D', uv: 2780, pv: 3908, amt: 2000 }
  ],
  pie: [
    { name: 'Group A', value: 400 },
    { name: 'Group B', value: 300 },
    { name: 'Group C', value: 300 },
    { name: 'Group D', value: 200 }
  ],
  scatter: [
    { x: 100, y: 200, z: 200 },
    { x: 120, y: 100, z: 260 },
    { x: 170, y: 300, z: 400 },
    { x: 140, y: 250, z: 280 }
  ],
  radar: [
    { subject: 'Math', A: 120, B: 110, fullMark: 150 },
    { subject: 'Chinese', A: 98, B: 130, fullMark: 150 },
    { subject: 'English', A: 86, B: 130, fullMark: 150 },
    { subject: 'Geography', A: 99, B: 100, fullMark: 150 },
    { subject: 'Physics', A: 85, B: 90, fullMark: 150 },
    { subject: 'History', A: 65, B: 85, fullMark: 150 }
  ],
  treemap: [],
  funnel: []
};

export const ChartBuilder: React.FC<ChartBuilderProps> = ({
  visualization,
  theme,
  editable = true,
  onUpdate,
  onEvent
}) => {
  const [selectedChartType, setSelectedChartType] = useState<ChartType>(
    visualization?.chartType || 'line'
  );
  const [chartData, setChartData] = useState<ChartDataPoint[]>(
    visualization?.data || SAMPLE_DATA[selectedChartType]
  );
  const [chartConfig, setChartConfig] = useState<ChartConfig>(
    visualization?.config || {
      title: 'Sample Chart',
      responsive: true,
      legend: { show: true, position: 'top' },
      grid: true,
      colors: theme?.colors.accent || ['#8884d8', '#82ca9d', '#ffc658']
    }
  );

  // Update chart when type changes
  useEffect(() => {
    if (selectedChartType !== visualization?.chartType) {
      setChartData(SAMPLE_DATA[selectedChartType] || []);
    }
  }, [selectedChartType, visualization?.chartType]);

  // Handle chart type change
  const handleChartTypeChange = useCallback((type: ChartType) => {
    setSelectedChartType(type);
    setChartData(SAMPLE_DATA[type] || []);
    
    const updatedVisualization: ChartVisualization = {
      ...(visualization || {} as ChartVisualization),
      type: 'chart',
      chartType: type,
      data: SAMPLE_DATA[type] || [],
      config: chartConfig,
      updatedAt: new Date()
    };
    
    onUpdate?.(updatedVisualization);
    onEvent?.({ type: 'chart-type-change', data: { chartType: type } });
  }, [visualization, chartConfig, onUpdate, onEvent]);

  // Handle config changes
  const handleConfigChange = useCallback((key: keyof ChartConfig, value: any) => {
    const newConfig = { ...chartConfig, [key]: value };
    setChartConfig(newConfig);
    
    const updatedVisualization: ChartVisualization = {
      ...(visualization || {} as ChartVisualization),
      type: 'chart',
      chartType: selectedChartType,
      data: chartData,
      config: newConfig,
      updatedAt: new Date()
    };
    
    onUpdate?.(updatedVisualization);
  }, [chartConfig, visualization, selectedChartType, chartData, onUpdate]);

  // Render the appropriate chart component
  const renderChart = useMemo(() => {
    const colors = chartConfig.colors || theme?.colors.accent || ['#8884d8', '#82ca9d', '#ffc658'];
    
    const commonProps = {
      data: chartData,
      margin: { top: 20, right: 30, left: 20, bottom: 5 }
    };

    switch (selectedChartType) {
      case 'line':
        return (
          <LineChart {...commonProps}>
            {chartConfig.grid && <CartesianGrid strokeDasharray="3 3" />}
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            {chartConfig.legend?.show && <Legend />}
            <Line 
              type="monotone" 
              dataKey="value" 
              stroke={colors[0]} 
              strokeWidth={2}
              dot={{ fill: colors[0] }}
            />
            <Line 
              type="monotone" 
              dataKey="value2" 
              stroke={colors[1]} 
              strokeWidth={2}
              dot={{ fill: colors[1] }}
            />
          </LineChart>
        );

      case 'bar':
        return (
          <BarChart {...commonProps}>
            {chartConfig.grid && <CartesianGrid strokeDasharray="3 3" />}
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            {chartConfig.legend?.show && <Legend />}
            <Bar dataKey="sales" fill={colors[0]} />
            <Bar dataKey="profit" fill={colors[1]} />
          </BarChart>
        );

      case 'area':
        return (
          <AreaChart {...commonProps}>
            {chartConfig.grid && <CartesianGrid strokeDasharray="3 3" />}
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            {chartConfig.legend?.show && <Legend />}
            <Area 
              type="monotone" 
              dataKey="uv" 
              stackId="1" 
              stroke={colors[0]} 
              fill={colors[0]} 
            />
            <Area 
              type="monotone" 
              dataKey="pv" 
              stackId="1" 
              stroke={colors[1]} 
              fill={colors[1]} 
            />
          </AreaChart>
        );

      case 'pie':
        return (
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              outerRadius={80}
              dataKey="value"
              label={(entry) => entry.name}
            >
              {chartData.map((_, index) => (
                <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
              ))}
            </Pie>
            <Tooltip />
            {chartConfig.legend?.show && <Legend />}
          </PieChart>
        );

      case 'scatter':
        return (
          <ScatterChart {...commonProps}>
            {chartConfig.grid && <CartesianGrid strokeDasharray="3 3" />}
            <XAxis type="number" dataKey="x" />
            <YAxis type="number" dataKey="y" />
            <Tooltip cursor={{ strokeDasharray: '3 3' }} />
            {chartConfig.legend?.show && <Legend />}
            <Scatter name="Data" data={chartData} fill={colors[0]} />
          </ScatterChart>
        );

      case 'radar':
        return (
          <RadarChart cx="50%" cy="50%" outerRadius="80%" data={chartData}>
            <PolarGrid />
            <PolarAngleAxis dataKey="subject" />
            <PolarRadiusAxis />
            <Radar name="A" dataKey="A" stroke={colors[0]} fill={colors[0]} fillOpacity={0.6} />
            <Radar name="B" dataKey="B" stroke={colors[1]} fill={colors[1]} fillOpacity={0.6} />
            <Tooltip />
            {chartConfig.legend?.show && <Legend />}
          </RadarChart>
        );

      default:
        return (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            Chart type not implemented yet
          </div>
        );
    }
  }, [selectedChartType, chartData, chartConfig, theme]);

  return (
    <div className="w-full h-full flex">
      {/* Chart Display */}
      <div className="flex-1 p-4">
        <Card className="w-full h-full">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-xl">
                  {chartConfig.title || 'Untitled Chart'}
                </CardTitle>
                {chartConfig.subtitle && (
                  <p className="text-sm text-muted-foreground mt-1">
                    {chartConfig.subtitle}
                  </p>
                )}
              </div>
              <Badge variant="secondary" className="capitalize">
                {selectedChartType}
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="p-4">
            <div className="w-full" style={{ height: 400 }}>
              <ResponsiveContainer>
                {renderChart}
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Configuration Panel */}
      {editable && (
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="w-80 border-l bg-card/50 backdrop-blur-sm"
        >
          <div className="p-4 h-full overflow-y-auto">
            <Tabs defaultValue="type" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="type">Type</TabsTrigger>
                <TabsTrigger value="config">Config</TabsTrigger>
                <TabsTrigger value="data">Data</TabsTrigger>
              </TabsList>

              {/* Chart Type Selection */}
              <TabsContent value="type" className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">Chart Type</Label>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    {Object.entries(CHART_TYPES).map(([type, config]) => (
                      <Button
                        key={type}
                        variant={selectedChartType === type ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => handleChartTypeChange(type as ChartType)}
                        className="h-auto p-3 flex flex-col items-center gap-2"
                      >
                        {config.icon}
                        <span className="text-xs">{config.label}</span>
                      </Button>
                    ))}
                  </div>
                </div>

                <Separator />

                <div>
                  <Label className="text-sm font-medium mb-2 block">Description</Label>
                  <p className="text-sm text-muted-foreground">
                    {CHART_TYPES[selectedChartType]?.description}
                  </p>
                </div>
              </TabsContent>

              {/* Chart Configuration */}
              <TabsContent value="config" className="space-y-4">
                <div>
                  <Label htmlFor="title">Chart Title</Label>
                  <Input
                    id="title"
                    value={chartConfig.title || ''}
                    onChange={(e) => handleConfigChange('title', e.target.value)}
                    placeholder="Enter chart title"
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="subtitle">Subtitle</Label>
                  <Input
                    id="subtitle"
                    value={chartConfig.subtitle || ''}
                    onChange={(e) => handleConfigChange('subtitle', e.target.value)}
                    placeholder="Enter subtitle (optional)"
                    className="mt-1"
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <Label htmlFor="grid">Show Grid</Label>
                  <Switch
                    id="grid"
                    checked={chartConfig.grid || false}
                    onCheckedChange={(checked) => handleConfigChange('grid', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="legend">Show Legend</Label>
                  <Switch
                    id="legend"
                    checked={chartConfig.legend?.show || false}
                    onCheckedChange={(checked) => 
                      handleConfigChange('legend', { ...chartConfig.legend, show: checked })
                    }
                  />
                </div>

                {chartConfig.legend?.show && (
                  <div>
                    <Label htmlFor="legend-position">Legend Position</Label>
                    <Select
                      value={chartConfig.legend?.position || 'top'}
                      onValueChange={(value) => 
                        handleConfigChange('legend', { ...chartConfig.legend, position: value })
                      }
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="top">Top</SelectItem>
                        <SelectItem value="bottom">Bottom</SelectItem>
                        <SelectItem value="left">Left</SelectItem>
                        <SelectItem value="right">Right</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <Separator />

                <div>
                  <Label className="text-sm font-medium mb-2 block">Colors</Label>
                  <div className="flex gap-2">
                    {(chartConfig.colors || theme?.colors.accent || []).slice(0, 3).map((color, index) => (
                      <div
                        key={index}
                        className="w-8 h-8 rounded border-2 border-border"
                        style={{ backgroundColor: color }}
                      />
                    ))}
                    <Button variant="outline" size="sm" className="w-8 h-8 p-0">
                      <Palette className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </TabsContent>

              {/* Data Management */}
              <TabsContent value="data" className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Data Points</Label>
                  <Badge variant="outline">{chartData.length} items</Badge>
                </div>

                <div className="max-h-60 overflow-y-auto">
                  {chartData.slice(0, 5).map((item, index) => (
                    <div key={index} className="p-2 border rounded text-xs">
                      <pre className="whitespace-pre-wrap">
                        {JSON.stringify(item, null, 2)}
                      </pre>
                    </div>
                  ))}
                </div>

                <div className="flex gap-2">
                  <Button size="sm" variant="outline" className="flex-1">
                    <Plus className="h-4 w-4 mr-1" />
                    Add Row
                  </Button>
                  <Button size="sm" variant="outline">
                    <Edit3 className="h-4 w-4" />
                  </Button>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </motion.div>
      )}
    </div>
  );
};