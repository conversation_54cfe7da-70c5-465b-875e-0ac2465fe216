/**
 * TimelineVisualizer - Timeline and Gantt chart visualization component
 * Supports project planning, task scheduling, and timeline management
 */

import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { format, addDays, startOfWeek, endOfWeek, eachDayOfInterval, isToday, isWeekend } from 'date-fns';
import {
  Calendar,
  Clock,
  Users,
  Flag,
  ChevronLeft,
  ChevronRight,
  Plus,
  Filter,
  Layout,
  BarChart3,
  List,
  Settings
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

import {
  TimelineVisualization,
  TimelineItem,
  VisualizationTheme,
  BaseVisualization
} from '@/types/visualization';

interface TimelineVisualizerProps {
  visualization?: TimelineVisualization;
  theme?: VisualizationTheme;
  editable?: boolean;
  onUpdate?: (visualization: BaseVisualization) => void;
  onEvent?: (event: any) => void;
  onItemClick?: (item: TimelineItem) => void;
}

// Sample timeline data
const sampleItems: TimelineItem[] = [
  {
    id: 'task1',
    title: 'Project Planning',
    description: 'Initial project planning and requirement gathering',
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-01-15'),
    category: 'Planning',
    status: 'completed',
    priority: 'high',
    assignee: 'John Doe',
    progress: 100,
    color: '#10b981'
  },
  {
    id: 'task2',
    title: 'Design Phase',
    description: 'UI/UX design and wireframing',
    startDate: new Date('2024-01-10'),
    endDate: new Date('2024-02-05'),
    category: 'Design',
    status: 'completed',
    priority: 'high',
    assignee: 'Jane Smith',
    progress: 100,
    color: '#3b82f6'
  },
  {
    id: 'task3',
    title: 'Frontend Development',
    description: 'React components and UI implementation',
    startDate: new Date('2024-01-20'),
    endDate: new Date('2024-03-15'),
    category: 'Development',
    status: 'in-progress',
    priority: 'high',
    assignee: 'Mike Johnson',
    progress: 75,
    color: '#f59e0b',
    dependencies: ['task2']
  },
  {
    id: 'task4',
    title: 'Backend API',
    description: 'Server-side API development',
    startDate: new Date('2024-02-01'),
    endDate: new Date('2024-03-20'),
    category: 'Development',
    status: 'in-progress',
    priority: 'high',
    assignee: 'Sarah Wilson',
    progress: 60,
    color: '#8b5cf6',
    dependencies: ['task1']
  },
  {
    id: 'task5',
    title: 'Testing',
    description: 'Quality assurance and testing',
    startDate: new Date('2024-03-10'),
    endDate: new Date('2024-03-30'),
    category: 'Testing',
    status: 'planned',
    priority: 'medium',
    assignee: 'Tom Davis',
    progress: 0,
    color: '#ec4899',
    dependencies: ['task3', 'task4']
  },
  {
    id: 'task6',
    title: 'Deployment',
    description: 'Production deployment and go-live',
    startDate: new Date('2024-03-25'),
    endDate: new Date('2024-04-05'),
    category: 'Deployment',
    status: 'planned',
    priority: 'critical',
    assignee: 'Alex Brown',
    progress: 0,
    color: '#ef4444',
    dependencies: ['task5']
  }
];

const statusColors = {
  planned: '#94a3b8',
  'in-progress': '#f59e0b',
  completed: '#10b981',
  cancelled: '#ef4444'
};

const priorityColors = {
  low: '#94a3b8',
  medium: '#f59e0b',
  high: '#f97316',
  critical: '#ef4444'
};

export const TimelineVisualizer: React.FC<TimelineVisualizerProps> = ({
  visualization,
  theme,
  editable = true,
  onUpdate,
  onEvent,
  onItemClick
}) => {
  const [viewType, setViewType] = useState<'timeline' | 'gantt'>(
    visualization?.viewType || 'gantt'
  );
  const [items] = useState<TimelineItem[]>(visualization?.items || sampleItems);
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [filterAssignee, setFilterAssignee] = useState<string>('all');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [zoomLevel, setZoomLevel] = useState<'day' | 'week' | 'month'>('week');

  const containerRef = useRef<HTMLDivElement>(null);

  // Calculate date range
  const dateRange = useMemo(() => {
    if (items.length === 0) return { start: new Date(), end: addDays(new Date(), 30) };

    const allDates = items.flatMap(item => [item.startDate, item.endDate].filter(Boolean));
    const minDate = new Date(Math.min(...allDates.map(d => d!.getTime())));
    const maxDate = new Date(Math.max(...allDates.map(d => d!.getTime())));
    
    return {
      start: startOfWeek(minDate),
      end: endOfWeek(addDays(maxDate, 7))
    };
  }, [items]);

  // Filter items
  const filteredItems = useMemo(() => {
    return items.filter(item => {
      if (filterStatus !== 'all' && item.status !== filterStatus) return false;
      if (filterCategory !== 'all' && item.category !== filterCategory) return false;
      if (filterAssignee !== 'all' && item.assignee !== filterAssignee) return false;
      return true;
    });
  }, [items, filterStatus, filterCategory, filterAssignee]);

  // Get unique values for filters
  const uniqueStatuses = useMemo(() => [...new Set(items.map(item => item.status))], [items]);
  const uniqueCategories = useMemo(() => [...new Set(items.map(item => item.category).filter(Boolean))], [items]);
  const uniqueAssignees = useMemo(() => [...new Set(items.map(item => item.assignee).filter(Boolean))], [items]);

  // Generate time grid
  const timeGrid = useMemo(() => {
    const days = eachDayOfInterval({ start: dateRange.start, end: dateRange.end });
    
    switch (zoomLevel) {
      case 'day':
        return days.map(day => ({
          date: day,
          label: format(day, 'MMM d'),
          isWeekend: isWeekend(day),
          isToday: isToday(day)
        }));
      case 'week':
        const weeks = [];
        for (let i = 0; i < days.length; i += 7) {
          const weekStart = days[i];
          weeks.push({
            date: weekStart,
            label: format(weekStart, 'MMM d'),
            isWeekend: false,
            isToday: false
          });
        }
        return weeks;
      case 'month':
        const months = [];
        let currentMonth = null;
        days.forEach(day => {
          const month = format(day, 'MMM yyyy');
          if (month !== currentMonth) {
            months.push({
              date: day,
              label: month,
              isWeekend: false,
              isToday: false
            });
            currentMonth = month;
          }
        });
        return months;
    }
  }, [dateRange, zoomLevel]);

  // Handle item click
  const handleItemClick = useCallback((item: TimelineItem) => {
    setSelectedItem(item.id);
    onItemClick?.(item);
    onEvent?.({ type: 'timeline-item-click', target: item.id, data: item });
  }, [onItemClick, onEvent]);

  // Calculate item position and width
  const getItemStyle = useCallback((item: TimelineItem) => {
    const totalDuration = dateRange.end.getTime() - dateRange.start.getTime();
    const itemStart = item.startDate.getTime() - dateRange.start.getTime();
    const itemDuration = (item.endDate?.getTime() || item.startDate.getTime()) - item.startDate.getTime();
    
    const left = (itemStart / totalDuration) * 100;
    const width = Math.max((itemDuration / totalDuration) * 100, 2);
    
    return { left: `${left}%`, width: `${width}%` };
  }, [dateRange]);

  // Timeline view component
  const TimelineView = () => (
    <div className="space-y-4">
      {filteredItems.map((item, index) => (
        <motion.div
          key={item.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
          className={`
            relative p-4 border rounded-lg cursor-pointer transition-all
            ${selectedItem === item.id ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:bg-gray-50'}
          `}
          onClick={() => handleItemClick(item)}
        >
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: item.color }}
                />
                <h3 className="font-medium">{item.title}</h3>
                <Badge
                  variant="outline"
                  style={{ 
                    backgroundColor: statusColors[item.status],
                    color: 'white',
                    borderColor: statusColors[item.status]
                  }}
                >
                  {item.status}
                </Badge>
                {item.priority && (
                  <Badge
                    variant="outline"
                    style={{ 
                      backgroundColor: priorityColors[item.priority],
                      color: 'white',
                      borderColor: priorityColors[item.priority]
                    }}
                  >
                    {item.priority}
                  </Badge>
                )}
              </div>
              
              {item.description && (
                <p className="text-sm text-muted-foreground mb-2">
                  {item.description}
                </p>
              )}
              
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                <span className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {format(item.startDate, 'MMM d, yyyy')}
                  {item.endDate && ` - ${format(item.endDate, 'MMM d, yyyy')}`}
                </span>
                
                {item.assignee && (
                  <span className="flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    {item.assignee}
                  </span>
                )}
                
                {item.category && (
                  <Badge variant="secondary" className="text-xs">
                    {item.category}
                  </Badge>
                )}
              </div>
            </div>
            
            {item.progress !== undefined && (
              <div className="w-24">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs text-muted-foreground">Progress</span>
                  <span className="text-xs font-medium">{item.progress}%</span>
                </div>
                <Progress value={item.progress} className="h-2" />
              </div>
            )}
          </div>
        </motion.div>
      ))}
    </div>
  );

  // Gantt chart view component
  const GanttView = () => (
    <div className="relative">
      {/* Time header */}
      <div className="sticky top-0 z-10 bg-white border-b">
        <div className="flex">
          <div className="w-64 p-2 border-r bg-gray-50">
            <div className="font-medium text-sm">Task</div>
          </div>
          <div className="flex-1 overflow-x-auto">
            <div className="flex h-10" style={{ minWidth: '800px' }}>
              {timeGrid.map((period, index) => (
                <div
                  key={index}
                  className={`
                    flex-1 px-2 py-1 border-r text-xs text-center
                    ${period.isWeekend ? 'bg-gray-100' : ''}
                    ${period.isToday ? 'bg-blue-100 text-blue-700 font-medium' : ''}
                  `}
                  style={{ minWidth: `${100 / timeGrid.length}%` }}
                >
                  {period.label}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Gantt rows */}
      <div className="space-y-1">
        {filteredItems.map((item, index) => (
          <motion.div
            key={item.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.05 }}
            className="flex hover:bg-gray-50"
          >
            {/* Task info */}
            <div className="w-64 p-2 border-r">
              <div className="flex items-center gap-2">
                <div
                  className="w-3 h-3 rounded-full flex-shrink-0"
                  style={{ backgroundColor: item.color }}
                />
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm truncate">
                    {item.title}
                  </div>
                  {item.assignee && (
                    <div className="text-xs text-muted-foreground">
                      {item.assignee}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Gantt bar */}
            <div className="flex-1 relative p-2" style={{ minWidth: '800px' }}>
              <div className="relative h-6">
                <div
                  className={`
                    absolute top-0 h-6 rounded cursor-pointer transition-all
                    ${selectedItem === item.id ? 'ring-2 ring-blue-500' : ''}
                  `}
                  style={{
                    ...getItemStyle(item),
                    backgroundColor: item.color,
                    opacity: item.status === 'cancelled' ? 0.5 : 0.8
                  }}
                  onClick={() => handleItemClick(item)}
                >
                  {/* Progress bar */}
                  {item.progress !== undefined && item.progress > 0 && (
                    <div
                      className="h-full bg-black bg-opacity-20 rounded-l"
                      style={{ width: `${item.progress}%` }}
                    />
                  )}
                  
                  {/* Task label */}
                  <div className="absolute inset-0 flex items-center px-2 text-xs text-white font-medium">
                    <span className="truncate">{item.title}</span>
                  </div>
                </div>

                {/* Dependencies */}
                {item.dependencies?.map(depId => {
                  const depItem = items.find(i => i.id === depId);
                  if (!depItem) return null;
                  
                  return (
                    <div
                      key={depId}
                      className="absolute top-3 h-0.5 bg-gray-400 opacity-50"
                      style={{
                        left: `${((depItem.endDate?.getTime() || depItem.startDate.getTime()) - dateRange.start.getTime()) / (dateRange.end.getTime() - dateRange.start.getTime()) * 100}%`,
                        width: `${((item.startDate.getTime() - (depItem.endDate?.getTime() || depItem.startDate.getTime())) / (dateRange.end.getTime() - dateRange.start.getTime())) * 100}%`
                      }}
                    />
                  );
                })}
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="w-full h-full flex flex-col">
      {/* Header */}
      <Card className="border-b rounded-none">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              {viewType === 'timeline' ? (
                <List className="h-5 w-5" />
              ) : (
                <BarChart3 className="h-5 w-5" />
              )}
              {viewType === 'timeline' ? 'Timeline View' : 'Gantt Chart'}
            </CardTitle>
            
            <div className="flex items-center gap-2">
              <Tabs value={viewType} onValueChange={(value: any) => setViewType(value)}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="timeline" className="text-xs">Timeline</TabsTrigger>
                  <TabsTrigger value="gantt" className="text-xs">Gantt</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Controls */}
      <div className="flex items-center gap-4 p-4 border-b bg-gray-50">
        <div className="flex items-center gap-2">
          <Label className="text-xs">Status:</Label>
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-32 h-8 text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              {uniqueStatuses.map(status => (
                <SelectItem key={status} value={status}>
                  {status}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <Label className="text-xs">Category:</Label>
          <Select value={filterCategory} onValueChange={setFilterCategory}>
            <SelectTrigger className="w-32 h-8 text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              {uniqueCategories.map(category => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <Label className="text-xs">Assignee:</Label>
          <Select value={filterAssignee} onValueChange={setFilterAssignee}>
            <SelectTrigger className="w-32 h-8 text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              {uniqueAssignees.map(assignee => (
                <SelectItem key={assignee} value={assignee}>
                  {assignee}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {viewType === 'gantt' && (
          <div className="flex items-center gap-2">
            <Label className="text-xs">Zoom:</Label>
            <Select value={zoomLevel} onValueChange={(value: any) => setZoomLevel(value)}>
              <SelectTrigger className="w-20 h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="day">Day</SelectItem>
                <SelectItem value="week">Week</SelectItem>
                <SelectItem value="month">Month</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        <div className="flex-1" />

        <div className="flex items-center gap-2">
          <Badge variant="outline">{filteredItems.length} items</Badge>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 overflow-auto" ref={containerRef}>
        {viewType === 'timeline' ? <TimelineView /> : <GanttView />}
      </div>
    </div>
  );
};