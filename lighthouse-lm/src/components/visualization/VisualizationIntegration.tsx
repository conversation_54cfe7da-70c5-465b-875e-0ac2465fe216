/**
 * VisualizationIntegration - Example integration with existing lighthouse-lm systems
 * Shows how to integrate visualization components with notebooks, analytics, and sources
 */

import React, { useState, useCallback, useMemo } from 'react';
import { motion } from 'framer-motion';
import { 
  FileText, 
  BarChart3, 
  Network, 
  Calendar,
  Plus,
  Search,
  Filter
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { VisualizationEngine } from './VisualizationEngine';
import { lightTheme } from './utils';
import {
  BaseVisualization,
  ChartVisualization,
  VisualizationType,
  ChartDataPoint
} from '@/types/visualization';

// Mock data that would come from existing lighthouse-lm systems
interface NotebookData {
  id: string;
  title: string;
  content: string;
  type: 'analysis' | 'research' | 'planning';
  createdAt: Date;
  tags: string[];
}

interface AnalyticsData {
  sessions: number;
  pageViews: number;
  users: number;
  bounceRate: number;
  avgDuration: number;
  timestamp: Date;
}

interface SourceData {
  id: string;
  title: string;
  type: 'document' | 'api' | 'database' | 'file';
  size: number;
  lastModified: Date;
}

// Sample data
const sampleNotebooks: NotebookData[] = [
  {
    id: 'nb1',
    title: 'Market Research Analysis',
    content: 'Comprehensive analysis of market trends...',
    type: 'research',
    createdAt: new Date('2024-01-15'),
    tags: ['market', 'research', 'trends']
  },
  {
    id: 'nb2',
    title: 'Product Planning',
    content: 'Strategic planning for Q1 product releases...',
    type: 'planning',
    createdAt: new Date('2024-01-20'),
    tags: ['product', 'planning', 'roadmap']
  },
  {
    id: 'nb3',
    title: 'Performance Analysis',
    content: 'System performance metrics and optimization...',
    type: 'analysis',
    createdAt: new Date('2024-01-25'),
    tags: ['performance', 'optimization', 'metrics']
  }
];

const sampleAnalytics: AnalyticsData[] = [
  { sessions: 1240, pageViews: 3420, users: 890, bounceRate: 45, avgDuration: 180, timestamp: new Date('2024-01-01') },
  { sessions: 1180, pageViews: 3200, users: 850, bounceRate: 48, avgDuration: 165, timestamp: new Date('2024-01-02') },
  { sessions: 1350, pageViews: 3680, users: 920, bounceRate: 42, avgDuration: 195, timestamp: new Date('2024-01-03') },
  { sessions: 1420, pageViews: 3890, users: 980, bounceRate: 40, avgDuration: 210, timestamp: new Date('2024-01-04') },
  { sessions: 1380, pageViews: 3750, users: 950, bounceRate: 43, avgDuration: 185, timestamp: new Date('2024-01-05') }
];

const sampleSources: SourceData[] = [
  { id: 's1', title: 'Research Database', type: 'database', size: 15680, lastModified: new Date('2024-01-20') },
  { id: 's2', title: 'Market Data API', type: 'api', size: 0, lastModified: new Date('2024-01-22') },
  { id: 's3', title: 'Product Specs', type: 'document', size: 2450, lastModified: new Date('2024-01-18') },
  { id: 's4', title: 'User Feedback', type: 'file', size: 8920, lastModified: new Date('2024-01-25') }
];

export const VisualizationIntegration: React.FC = () => {
  const [selectedSource, setSelectedSource] = useState<'notebooks' | 'analytics' | 'sources'>('analytics');
  const [selectedVisualization, setSelectedVisualization] = useState<BaseVisualization | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<string>('all');

  // Transform data for visualization based on selected source
  const generateVisualization = useCallback((source: typeof selectedSource, dataType: string) => {
    const baseId = `${source}-${dataType}-${Date.now()}`;
    
    switch (source) {
      case 'analytics':
        const analyticsChart: ChartVisualization = {
          id: baseId,
          title: 'Website Analytics Dashboard',
          description: 'Daily analytics metrics visualization',
          type: 'chart',
          chartType: dataType as any || 'line',
          data: sampleAnalytics.map(item => ({
            date: item.timestamp.toLocaleDateString(),
            sessions: item.sessions,
            pageViews: item.pageViews,
            users: item.users,
            bounceRate: item.bounceRate,
            avgDuration: item.avgDuration
          })),
          config: {
            title: 'Website Analytics',
            subtitle: 'Daily performance metrics',
            xAxis: { label: 'Date', type: 'category' },
            yAxis: { label: 'Count', type: 'number' },
            legend: { show: true, position: 'top' },
            grid: true,
            colors: ['#3b82f6', '#10b981', '#f59e0b', '#8b5cf6', '#ec4899']
          },
          createdAt: new Date(),
          updatedAt: new Date(),
          version: 1
        };
        return analyticsChart;

      case 'notebooks':
        const notebookChart: ChartVisualization = {
          id: baseId,
          title: 'Notebook Distribution',
          description: 'Distribution of notebook types',
          type: 'chart',
          chartType: 'pie',
          data: [
            { type: 'Research', count: sampleNotebooks.filter(nb => nb.type === 'research').length },
            { type: 'Analysis', count: sampleNotebooks.filter(nb => nb.type === 'analysis').length },
            { type: 'Planning', count: sampleNotebooks.filter(nb => nb.type === 'planning').length }
          ],
          config: {
            title: 'Notebook Types',
            legend: { show: true, position: 'right' },
            colors: ['#3b82f6', '#10b981', '#f59e0b']
          },
          createdAt: new Date(),
          updatedAt: new Date(),
          version: 1
        };
        return notebookChart;

      case 'sources':
        const sourcesChart: ChartVisualization = {
          id: baseId,
          title: 'Data Sources Overview',
          description: 'Data sources by type and size',
          type: 'chart',
          chartType: 'bar',
          data: sampleSources.map(source => ({
            name: source.title,
            size: source.size,
            type: source.type,
            lastModified: source.lastModified.toLocaleDateString()
          })),
          config: {
            title: 'Data Sources',
            subtitle: 'Size and modification dates',
            xAxis: { label: 'Source', type: 'category' },
            yAxis: { label: 'Size (KB)', type: 'number' },
            legend: { show: true, position: 'top' },
            grid: true,
            colors: ['#3b82f6', '#10b981', '#f59e0b', '#8b5cf6']
          },
          createdAt: new Date(),
          updatedAt: new Date(),
          version: 1
        };
        return sourcesChart;

      default:
        return null;
    }
  }, []);

  // Filter data based on search and filter criteria
  const filteredData = useMemo(() => {
    switch (selectedSource) {
      case 'notebooks':
        return sampleNotebooks.filter(notebook => {
          const matchesSearch = notebook.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                               notebook.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
          const matchesFilter = filterType === 'all' || notebook.type === filterType;
          return matchesSearch && matchesFilter;
        });
      
      case 'sources':
        return sampleSources.filter(source => {
          const matchesSearch = source.title.toLowerCase().includes(searchQuery.toLowerCase());
          const matchesFilter = filterType === 'all' || source.type === filterType;
          return matchesSearch && matchesFilter;
        });
      
      default:
        return sampleAnalytics;
    }
  }, [selectedSource, searchQuery, filterType]);

  // Handle visualization creation
  const handleCreateVisualization = useCallback((chartType: string) => {
    const visualization = generateVisualization(selectedSource, chartType);
    if (visualization) {
      setSelectedVisualization(visualization);
    }
  }, [selectedSource, generateVisualization]);

  // Handle visualization updates
  const handleVisualizationUpdate = useCallback((updatedVisualization: BaseVisualization) => {
    setSelectedVisualization(updatedVisualization);
    // Here you would typically save to your data store
    console.log('Visualization updated:', updatedVisualization);
  }, []);

  return (
    <div className="w-full h-screen flex">
      {/* Data Sources Panel */}
      <div className="w-80 border-r bg-card/50 overflow-y-auto">
        <div className="p-4">
          <h2 className="text-lg font-semibold mb-4">Data Integration</h2>
          
          {/* Source Selection */}
          <Tabs value={selectedSource} onValueChange={(value: any) => setSelectedSource(value)}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="analytics" className="text-xs">Analytics</TabsTrigger>
              <TabsTrigger value="notebooks" className="text-xs">Notebooks</TabsTrigger>
              <TabsTrigger value="sources" className="text-xs">Sources</TabsTrigger>
            </TabsList>

            {/* Search and Filter */}
            <div className="mt-4 space-y-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-9"
                />
              </div>
              
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger>
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {selectedSource === 'notebooks' && (
                    <>
                      <SelectItem value="research">Research</SelectItem>
                      <SelectItem value="analysis">Analysis</SelectItem>
                      <SelectItem value="planning">Planning</SelectItem>
                    </>
                  )}
                  {selectedSource === 'sources' && (
                    <>
                      <SelectItem value="document">Document</SelectItem>
                      <SelectItem value="api">API</SelectItem>
                      <SelectItem value="database">Database</SelectItem>
                      <SelectItem value="file">File</SelectItem>
                    </>
                  )}
                </SelectContent>
              </Select>
            </div>

            {/* Data Content */}
            <TabsContent value="analytics" className="space-y-2 mt-4">
              <div className="text-sm font-medium mb-2">Analytics Data ({sampleAnalytics.length} records)</div>
              {sampleAnalytics.slice(0, 5).map((item, index) => (
                <Card key={index} className="p-3">
                  <div className="flex justify-between items-center">
                    <div>
                      <div className="text-sm font-medium">{item.timestamp.toLocaleDateString()}</div>
                      <div className="text-xs text-muted-foreground">
                        {item.sessions} sessions • {item.users} users
                      </div>
                    </div>
                    <Badge variant="outline">{item.bounceRate}% bounce</Badge>
                  </div>
                </Card>
              ))}
            </TabsContent>

            <TabsContent value="notebooks" className="space-y-2 mt-4">
              <div className="text-sm font-medium mb-2">
                Notebooks ({filteredData.length} filtered)
              </div>
              {(filteredData as NotebookData[]).map((notebook) => (
                <Card key={notebook.id} className="p-3">
                  <div className="flex items-start gap-3">
                    <FileText className="h-4 w-4 text-muted-foreground mt-0.5" />
                    <div className="flex-1">
                      <div className="text-sm font-medium">{notebook.title}</div>
                      <div className="text-xs text-muted-foreground mb-2">
                        {notebook.createdAt.toLocaleDateString()}
                      </div>
                      <div className="flex gap-1">
                        <Badge variant="secondary" className="text-xs">
                          {notebook.type}
                        </Badge>
                        {notebook.tags.slice(0, 2).map(tag => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </TabsContent>

            <TabsContent value="sources" className="space-y-2 mt-4">
              <div className="text-sm font-medium mb-2">
                Sources ({filteredData.length} filtered)
              </div>
              {(filteredData as SourceData[]).map((source) => (
                <Card key={source.id} className="p-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="text-sm font-medium">{source.title}</div>
                      <div className="text-xs text-muted-foreground">
                        {source.size > 0 ? `${(source.size / 1024).toFixed(1)} KB` : 'API'}
                      </div>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {source.type}
                    </Badge>
                  </div>
                </Card>
              ))}
            </TabsContent>
          </Tabs>

          {/* Visualization Creation */}
          <div className="mt-6 space-y-2">
            <div className="text-sm font-medium">Create Visualization</div>
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleCreateVisualization('line')}
                className="flex items-center gap-2"
              >
                <BarChart3 className="h-4 w-4" />
                Line Chart
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleCreateVisualization('bar')}
                className="flex items-center gap-2"
              >
                <BarChart3 className="h-4 w-4" />
                Bar Chart
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleCreateVisualization('pie')}
                className="flex items-center gap-2"
              >
                <BarChart3 className="h-4 w-4" />
                Pie Chart
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleCreateVisualization('area')}
                className="flex items-center gap-2"
              >
                <BarChart3 className="h-4 w-4" />
                Area Chart
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Visualization Display */}
      <div className="flex-1">
        {selectedVisualization ? (
          <VisualizationEngine
            visualization={selectedVisualization}
            theme={lightTheme}
            editable={true}
            showToolbar={true}
            onUpdate={handleVisualizationUpdate}
            onExport={(options) => console.log('Export:', options)}
            onEvent={(event) => console.log('Event:', event)}
          />
        ) : (
          <div className="h-full flex items-center justify-center text-muted-foreground">
            <div className="text-center">
              <div className="text-6xl mb-4">📊</div>
              <h3 className="text-lg font-semibold mb-2">No Visualization Selected</h3>
              <p className="mb-4">Select a data source and create a visualization to get started</p>
              <div className="flex items-center justify-center gap-2">
                <BarChart3 className="h-4 w-4" />
                <Network className="h-4 w-4" />
                <Calendar className="h-4 w-4" />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};