/**
 * NetworkGraph - Network and relationship visualization component
 * Built with D3.js for force-directed graph layouts
 */

import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import * as d3 from 'd3';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Play,
  Pause,
  RotateCcw,
  ZoomIn,
  ZoomOut,
  Maximize2,
  Filter,
  Settings,
  Network,
  Users,
  GitBranch
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Separator } from '@/components/ui/separator';

import {
  NetworkVisualization,
  NetworkNode,
  NetworkEdge,
  VisualizationTheme,
  BaseVisualization
} from '@/types/visualization';

interface NetworkGraphProps {
  visualization?: NetworkVisualization;
  theme?: VisualizationTheme;
  interactive?: boolean;
  onUpdate?: (visualization: BaseVisualization) => void;
  onEvent?: (event: any) => void;
  onNodeClick?: (node: NetworkNode) => void;
  onEdgeClick?: (edge: NetworkEdge) => void;
}

interface D3Node extends NetworkNode {
  x?: number;
  y?: number;
  vx?: number;
  vy?: number;
  fx?: number | null;
  fy?: number | null;
  index?: number;
}

interface D3Link extends NetworkEdge {
  source: D3Node | string;
  target: D3Node | string;
  index?: number;
}

// Sample network data
const sampleNodes: NetworkNode[] = [
  { id: 'central', label: 'Main Hub', group: 'core', size: 20, color: '#3b82f6' },
  { id: 'node1', label: 'Node 1', group: 'primary', size: 15, color: '#10b981' },
  { id: 'node2', label: 'Node 2', group: 'primary', size: 15, color: '#10b981' },
  { id: 'node3', label: 'Node 3', group: 'secondary', size: 12, color: '#f59e0b' },
  { id: 'node4', label: 'Node 4', group: 'secondary', size: 12, color: '#f59e0b' },
  { id: 'node5', label: 'Node 5', group: 'tertiary', size: 10, color: '#8b5cf6' },
  { id: 'node6', label: 'Node 6', group: 'tertiary', size: 10, color: '#8b5cf6' },
  { id: 'node7', label: 'Node 7', group: 'tertiary', size: 8, color: '#ec4899' }
];

const sampleEdges: NetworkEdge[] = [
  { id: 'e1', source: 'central', target: 'node1', weight: 5, width: 3 },
  { id: 'e2', source: 'central', target: 'node2', weight: 4, width: 2.5 },
  { id: 'e3', source: 'node1', target: 'node3', weight: 3, width: 2 },
  { id: 'e4', source: 'node2', target: 'node4', weight: 3, width: 2 },
  { id: 'e5', source: 'node3', target: 'node5', weight: 2, width: 1.5 },
  { id: 'e6', source: 'node4', target: 'node6', weight: 2, width: 1.5 },
  { id: 'e7', source: 'node5', target: 'node7', weight: 1, width: 1 },
  { id: 'e8', source: 'node1', target: 'node2', weight: 2, width: 1.5 }
];

export const NetworkGraph: React.FC<NetworkGraphProps> = ({
  visualization,
  theme,
  interactive = true,
  onUpdate,
  onEvent,
  onNodeClick,
  onEdgeClick
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [nodes] = useState<NetworkNode[]>(visualization?.nodes || sampleNodes);
  const [edges] = useState<NetworkEdge[]>(visualization?.edges || sampleEdges);
  const [isPlaying, setIsPlaying] = useState(true);
  const [selectedLayout, setSelectedLayout] = useState<'force' | 'circular' | 'hierarchical' | 'grid'>('force');
  const [filterGroup, setFilterGroup] = useState<string>('all');
  const [linkDistance, setLinkDistance] = useState([100]);
  const [chargeStrength, setChargeStrength] = useState([-300]);
  const [showLabels, setShowLabels] = useState(true);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);

  // Memoized simulation setup
  const simulation = useMemo(() => {
    return d3.forceSimulation()
      .force('link', d3.forceLink().distance(linkDistance[0]))
      .force('charge', d3.forceManyBody().strength(chargeStrength[0]))
      .force('center', d3.forceCenter(0, 0))
      .force('collision', d3.forceCollide().radius(d => (d as D3Node).size || 10));
  }, [linkDistance, chargeStrength]);

  // Filter nodes and edges
  const filteredData = useMemo(() => {
    const filteredNodes = filterGroup === 'all' 
      ? nodes 
      : nodes.filter(node => node.group === filterGroup);
    
    const nodeIds = new Set(filteredNodes.map(node => node.id));
    const filteredEdges = edges.filter(edge => 
      nodeIds.has(edge.source) && nodeIds.has(edge.target)
    );

    return { nodes: filteredNodes, edges: filteredEdges };
  }, [nodes, edges, filterGroup]);

  // Get unique groups for filter
  const groups = useMemo(() => {
    const uniqueGroups = Array.from(new Set(nodes.map(node => node.group).filter(Boolean)));
    return uniqueGroups;
  }, [nodes]);

  // Handle node click
  const handleNodeClick = useCallback((node: NetworkNode) => {
    setSelectedNode(node.id);
    onNodeClick?.(node);
    onEvent?.({ type: 'node-click', target: node.id, data: node });
  }, [onNodeClick, onEvent]);

  // Handle edge click
  const handleEdgeClick = useCallback((edge: NetworkEdge) => {
    onEdgeClick?.(edge);
    onEvent?.({ type: 'edge-click', target: edge.id, data: edge });
  }, [onEdgeClick, onEvent]);

  // Apply layout
  const applyLayout = useCallback((layout: typeof selectedLayout) => {
    const svg = d3.select(svgRef.current);
    const width = 800;
    const height = 600;

    switch (layout) {
      case 'circular':
        filteredData.nodes.forEach((node, i) => {
          const angle = (2 * Math.PI * i) / filteredData.nodes.length;
          const radius = Math.min(width, height) / 3;
          (node as D3Node).fx = radius * Math.cos(angle);
          (node as D3Node).fy = radius * Math.sin(angle);
        });
        break;

      case 'hierarchical':
        // Simple hierarchical layout based on node connections
        const levels: { [key: string]: number } = {};
        const getLevelRecursive = (nodeId: string, level = 0, visited = new Set()) => {
          if (visited.has(nodeId)) return level;
          visited.add(nodeId);
          
          if (levels[nodeId] === undefined || levels[nodeId] < level) {
            levels[nodeId] = level;
          }
          
          const connections = filteredData.edges.filter(edge => edge.source === nodeId);
          connections.forEach(edge => {
            getLevelRecursive(edge.target, level + 1, visited);
          });
          
          return level;
        };

        // Start from nodes with no incoming edges
        const rootNodes = filteredData.nodes.filter(node => 
          !filteredData.edges.some(edge => edge.target === node.id)
        );
        
        rootNodes.forEach(node => getLevelRecursive(node.id));
        
        const maxLevel = Math.max(...Object.values(levels));
        const nodesPerLevel: { [key: number]: NetworkNode[] } = {};
        
        filteredData.nodes.forEach(node => {
          const level = levels[node.id] || 0;
          if (!nodesPerLevel[level]) nodesPerLevel[level] = [];
          nodesPerLevel[level].push(node);
        });

        Object.entries(nodesPerLevel).forEach(([level, levelNodes]) => {
          const levelNum = parseInt(level);
          const y = (height / (maxLevel + 1)) * levelNum - height / 2;
          levelNodes.forEach((node, i) => {
            const x = (width / (levelNodes.length + 1)) * (i + 1) - width / 2;
            (node as D3Node).fx = x;
            (node as D3Node).fy = y;
          });
        });
        break;

      case 'grid':
        const cols = Math.ceil(Math.sqrt(filteredData.nodes.length));
        filteredData.nodes.forEach((node, i) => {
          const row = Math.floor(i / cols);
          const col = i % cols;
          (node as D3Node).fx = (col * (width / cols)) - width / 2;
          (node as D3Node).fy = (row * (height / cols)) - height / 2;
        });
        break;

      default: // force
        filteredData.nodes.forEach(node => {
          (node as D3Node).fx = null;
          (node as D3Node).fy = null;
        });
        break;
    }
  }, [filteredData]);

  // Initialize and update D3 visualization
  useEffect(() => {
    if (!svgRef.current) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const width = 800;
    const height = 600;

    // Set up SVG
    svg
      .attr('width', width)
      .attr('height', height)
      .attr('viewBox', [-width / 2, -height / 2, width, height]);

    // Add zoom behavior
    const g = svg.append('g');
    const zoom = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.1, 4])
      .on('zoom', (event) => {
        g.attr('transform', event.transform);
      });

    if (interactive) {
      svg.call(zoom as any);
    }

    // Create arrow markers
    svg.append('defs').selectAll('marker')
      .data(['arrow'])
      .join('marker')
      .attr('id', d => d)
      .attr('viewBox', '0 -5 10 10')
      .attr('refX', 15)
      .attr('refY', 0)
      .attr('markerWidth', 6)
      .attr('markerHeight', 6)
      .attr('orient', 'auto')
      .append('path')
      .attr('d', 'M0,-5L10,0L0,5')
      .attr('fill', '#999');

    // Prepare data
    const d3Nodes: D3Node[] = filteredData.nodes.map(d => ({ ...d }));
    const d3Links: D3Link[] = filteredData.edges.map(d => ({ ...d }));

    // Apply layout
    applyLayout(selectedLayout);

    // Create links
    const link = g.append('g')
      .attr('stroke', '#999')
      .attr('stroke-opacity', 0.6)
      .selectAll('line')
      .data(d3Links)
      .join('line')
      .attr('stroke-width', d => d.width || 1)
      .attr('marker-end', 'url(#arrow)')
      .style('cursor', interactive ? 'pointer' : 'default')
      .on('click', (event, d) => {
        if (interactive) {
          event.stopPropagation();
          handleEdgeClick(d);
        }
      });

    // Create nodes
    const node = g.append('g')
      .selectAll('circle')
      .data(d3Nodes)
      .join('circle')
      .attr('r', d => d.size || 10)
      .attr('fill', d => d.color || theme?.colors.primary || '#3b82f6')
      .attr('stroke', '#fff')
      .attr('stroke-width', d => selectedNode === d.id ? 3 : 1.5)
      .style('cursor', interactive ? 'pointer' : 'default')
      .on('click', (event, d) => {
        if (interactive) {
          event.stopPropagation();
          handleNodeClick(d);
        }
      })
      .call(interactive ? d3.drag<any, D3Node>()
        .on('start', (event, d) => {
          if (!event.active) simulation.alphaTarget(0.3).restart();
          d.fx = d.x;
          d.fy = d.y;
        })
        .on('drag', (event, d) => {
          d.fx = event.x;
          d.fy = event.y;
        })
        .on('end', (event, d) => {
          if (!event.active) simulation.alphaTarget(0);
          if (selectedLayout === 'force') {
            d.fx = null;
            d.fy = null;
          }
        }) as any : () => {});

    // Add labels
    const labels = g.append('g')
      .selectAll('text')
      .data(d3Nodes)
      .join('text')
      .text(d => d.label)
      .attr('font-size', '12px')
      .attr('text-anchor', 'middle')
      .attr('dy', d => (d.size || 10) + 15)
      .attr('fill', theme?.colors.text || '#333')
      .style('pointer-events', 'none')
      .style('opacity', showLabels ? 1 : 0);

    // Set up simulation
    simulation
      .nodes(d3Nodes)
      .force('link', d3.forceLink(d3Links).id(d => (d as D3Node).id).distance(linkDistance[0]))
      .on('tick', () => {
        link
          .attr('x1', d => (d.source as D3Node).x || 0)
          .attr('y1', d => (d.source as D3Node).y || 0)
          .attr('x2', d => (d.target as D3Node).x || 0)
          .attr('y2', d => (d.target as D3Node).y || 0);

        node
          .attr('cx', d => d.x || 0)
          .attr('cy', d => d.y || 0);

        labels
          .attr('x', d => d.x || 0)
          .attr('y', d => d.y || 0);
      });

    // Control simulation
    if (isPlaying) {
      simulation.restart();
    } else {
      simulation.stop();
    }

    return () => {
      simulation.stop();
    };
  }, [
    filteredData,
    selectedLayout,
    isPlaying,
    linkDistance,
    chargeStrength,
    showLabels,
    selectedNode,
    interactive,
    theme,
    handleNodeClick,
    handleEdgeClick,
    applyLayout,
    simulation
  ]);

  const resetView = useCallback(() => {
    const svg = d3.select(svgRef.current);
    svg.transition().duration(750).call(
      d3.zoom<SVGSVGElement, unknown>().transform as any,
      d3.zoomIdentity
    );
  }, []);

  return (
    <div className="w-full h-full flex">
      {/* Main visualization area */}
      <div className="flex-1 relative">
        <Card className="w-full h-full border-0 rounded-none">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Network className="h-5 w-5" />
                Network Graph
              </CardTitle>
              <div className="flex items-center gap-2">
                <Badge variant="outline">
                  {filteredData.nodes.length} nodes
                </Badge>
                <Badge variant="outline">
                  {filteredData.edges.length} edges
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-4 h-[calc(100%-80px)]">
            <div className="w-full h-full bg-white rounded border">
              <svg ref={svgRef} className="w-full h-full" />
            </div>
          </CardContent>
        </Card>

        {/* Control overlay */}
        <div className="absolute top-4 right-4 flex flex-col gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsPlaying(!isPlaying)}
                  className="h-8 w-8 p-0"
                >
                  {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                {isPlaying ? 'Pause' : 'Play'} Simulation
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={resetView}
                  className="h-8 w-8 p-0"
                >
                  <Maximize2 className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Reset View</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* Settings panel */}
      {interactive && (
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="w-80 border-l bg-card/50 backdrop-blur-sm p-4 overflow-y-auto"
        >
          <div className="space-y-6">
            <div>
              <Label className="text-sm font-medium">Layout</Label>
              <Select value={selectedLayout} onValueChange={(value: any) => setSelectedLayout(value)}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="force">Force-Directed</SelectItem>
                  <SelectItem value="circular">Circular</SelectItem>
                  <SelectItem value="hierarchical">Hierarchical</SelectItem>
                  <SelectItem value="grid">Grid</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Separator />

            <div>
              <Label className="text-sm font-medium">Filter by Group</Label>
              <Select value={filterGroup} onValueChange={setFilterGroup}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Groups</SelectItem>
                  {groups.map(group => (
                    <SelectItem key={group} value={group}>
                      {group}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Separator />

            <div className="space-y-4">
              <div>
                <Label className="text-sm font-medium">
                  Link Distance: {linkDistance[0]}
                </Label>
                <Slider
                  value={linkDistance}
                  onValueChange={setLinkDistance}
                  min={50}
                  max={300}
                  step={10}
                  className="mt-2"
                />
              </div>

              <div>
                <Label className="text-sm font-medium">
                  Charge Strength: {chargeStrength[0]}
                </Label>
                <Slider
                  value={chargeStrength}
                  onValueChange={setChargeStrength}
                  min={-1000}
                  max={-50}
                  step={50}
                  className="mt-2"
                />
              </div>
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <Label htmlFor="show-labels" className="text-sm font-medium">
                Show Labels
              </Label>
              <Switch
                id="show-labels"
                checked={showLabels}
                onCheckedChange={setShowLabels}
              />
            </div>

            {selectedNode && (
              <div className="p-3 bg-muted rounded">
                <h4 className="font-medium text-sm mb-2">Selected Node</h4>
                <p className="text-sm">
                  {nodes.find(n => n.id === selectedNode)?.label}
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedNode(null)}
                  className="mt-2"
                >
                  Clear Selection
                </Button>
              </div>
            )}
          </div>
        </motion.div>
      )}
    </div>
  );
};