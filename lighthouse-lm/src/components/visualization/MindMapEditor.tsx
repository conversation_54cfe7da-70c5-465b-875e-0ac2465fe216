/**
 * MindMapEditor - Interactive mind map and knowledge graph visualization
 * Built with React Flow for drag-and-drop node editing
 */

import React, { useState, useCallback, useRef } from 'react';
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  useNodesState,
  useEdgesState,
  Connection,
  NodeTypes,
  EdgeTypes,
  Controls,
  MiniMap,
  Background,
  Panel,
  ReactFlowProvider,
  useReactFlow
} from 'reactflow';
import 'reactflow/dist/style.css';

import {
  Plus,
  Layers,
  Maximize2,
  MousePointer2
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
// Removed unused Label import
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

import {
  MindMapVisualization,
  VisualizationTheme,
  BaseVisualization
} from '@/types/visualization';

interface MindMapEditorProps {
  visualization?: MindMapVisualization;
  theme?: VisualizationTheme;
  editable?: boolean;
  onUpdate?: (visualization: BaseVisualization) => void;
  onEvent?: (event: any) => void;
}

// Custom node component
const MindMapNode: React.FC<{
  data: {
    label: string;
    level: number;
    color?: string;
    size?: 'small' | 'medium' | 'large';
    onEdit?: (id: string, label: string) => void;
  };
  id: string;
  selected: boolean;
}> = ({ data, id, selected }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [label, setLabel] = useState(data.label);

  const handleDoubleClick = () => {
    if (data.onEdit) {
      setIsEditing(true);
    }
  };

  const handleSubmit = () => {
    data.onEdit?.(id, label);
    setIsEditing(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSubmit();
    } else if (e.key === 'Escape') {
      setLabel(data.label);
      setIsEditing(false);
    }
  };

  const sizeClasses = {
    small: 'px-3 py-2 text-xs',
    medium: 'px-4 py-3 text-sm',
    large: 'px-6 py-4 text-base'
  };

  const levelColors = {
    0: 'bg-blue-500 text-white', // Central node
    1: 'bg-green-400 text-white',
    2: 'bg-yellow-400 text-black',
    3: 'bg-purple-400 text-white',
    4: 'bg-pink-400 text-white'
  };

  return (
    <div
      className={`
        rounded-lg border-2 font-medium transition-all duration-200 min-w-[100px] text-center
        ${selected ? 'border-blue-500 shadow-lg' : 'border-gray-300 hover:border-gray-400'}
        ${sizeClasses[data.size || 'medium']}
        ${data.color ? '' : levelColors[data.level as keyof typeof levelColors] || 'bg-gray-200 text-gray-800'}
        ${isEditing ? 'ring-2 ring-blue-500' : ''}
      `}
      style={data.color ? { backgroundColor: data.color } : undefined}
      onDoubleClick={handleDoubleClick}
    >
      {isEditing ? (
        <Input
          value={label}
          onChange={(e) => setLabel(e.target.value)}
          onBlur={handleSubmit}
          onKeyDown={handleKeyPress}
          className="border-none bg-transparent p-0 text-center focus:ring-0"
          autoFocus
        />
      ) : (
        <div className="whitespace-nowrap overflow-hidden text-ellipsis max-w-[200px]">
          {data.label}
        </div>
      )}
    </div>
  );
};

// Custom edge component
const MindMapEdge: React.FC<any> = ({ id, sourceX, sourceY, targetX, targetY, style = {} }) => {
  const edgePath = `M${sourceX},${sourceY} Q${(sourceX + targetX) / 2},${sourceY - 50} ${targetX},${targetY}`;
  
  return (
    <path
      id={id}
      style={{ ...style, stroke: '#94a3b8', strokeWidth: 2 }}
      className="react-flow__edge-path"
      d={edgePath}
      markerEnd="url(#arrowhead)"
    />
  );
};

const nodeTypes: NodeTypes = {
  mindMapNode: MindMapNode
};

const edgeTypes: EdgeTypes = {
  mindMapEdge: MindMapEdge
};

// Default mind map data
const defaultNodes: Node[] = [
  {
    id: 'central',
    type: 'mindMapNode',
    position: { x: 400, y: 200 },
    data: { label: 'Central Idea', level: 0, size: 'large' },
    draggable: true
  },
  {
    id: 'branch1',
    type: 'mindMapNode',
    position: { x: 200, y: 100 },
    data: { label: 'Branch 1', level: 1, size: 'medium' },
    draggable: true
  },
  {
    id: 'branch2',
    type: 'mindMapNode',
    position: { x: 600, y: 100 },
    data: { label: 'Branch 2', level: 1, size: 'medium' },
    draggable: true
  },
  {
    id: 'branch3',
    type: 'mindMapNode',
    position: { x: 300, y: 350 },
    data: { label: 'Branch 3', level: 1, size: 'medium' },
    draggable: true
  }
];

const defaultEdges: Edge[] = [
  { id: 'e1', source: 'central', target: 'branch1', type: 'mindMapEdge' },
  { id: 'e2', source: 'central', target: 'branch2', type: 'mindMapEdge' },
  { id: 'e3', source: 'central', target: 'branch3', type: 'mindMapEdge' }
];

const MindMapFlow: React.FC<MindMapEditorProps> = ({
  editable = true,
  onEvent
}) => {
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const { fitView } = useReactFlow();
  
  const [nodes, setNodes, onNodesChange] = useNodesState(defaultNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(defaultEdges);
  const [selectedTool, setSelectedTool] = useState<'select' | 'add-node' | 'add-edge'>('select');
  const [nodeCounter, setNodeCounter] = useState(4);

  // Handle new connections
  const onConnect = useCallback((params: Connection) => {
    const newEdge = {
      ...params,
      id: `e${edges.length + 1}`,
      type: 'mindMapEdge'
    };
    setEdges((eds) => addEdge(newEdge, eds));
  }, [edges.length, setEdges]);

  // Add new node
  const addNode = useCallback((position?: { x: number; y: number }) => {
    const newNode: Node = {
      id: `node-${nodeCounter}`,
      type: 'mindMapNode',
      position: position || { x: Math.random() * 500 + 100, y: Math.random() * 300 + 100 },
      data: { 
        label: `New Node ${nodeCounter}`, 
        level: 2,
        size: 'medium',
        onEdit: handleNodeEdit
      },
      draggable: true
    };
    
    setNodes((nds) => [...nds, newNode]);
    setNodeCounter(prev => prev + 1);
    onEvent?.({ type: 'node-added', data: newNode });
  }, [nodeCounter, setNodes, onEvent]);

  // Handle node editing
  const handleNodeEdit = useCallback((nodeId: string, newLabel: string) => {
    setNodes((nds) =>
      nds.map((node) =>
        node.id === nodeId
          ? { ...node, data: { ...node.data, label: newLabel } }
          : node
      )
    );
  }, [setNodes]);

  // Handle canvas click for adding nodes
  const onPaneClick = useCallback((event: React.MouseEvent) => {
    if (selectedTool === 'add-node' && reactFlowWrapper.current) {
      const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
      const position = {
        x: event.clientX - reactFlowBounds.left - 50,
        y: event.clientY - reactFlowBounds.top - 25
      };
      addNode(position);
    }
  }, [selectedTool, addNode]);

  // Handle node deletion
  const onNodesDelete = useCallback((nodesToDelete: Node[]) => {
    const nodeIds = nodesToDelete.map(node => node.id);
    setEdges((eds) => eds.filter(edge => 
      !nodeIds.includes(edge.source) && !nodeIds.includes(edge.target)
    ));
    onEvent?.({ type: 'nodes-deleted', data: nodesToDelete });
  }, [setEdges, onEvent]);

  // Auto-layout function
  const autoLayout = useCallback(() => {
    // Simple radial layout around central node
    const centralNode = nodes.find(node => node.data.level === 0);
    if (!centralNode) return;

    const otherNodes = nodes.filter(node => node.data.level > 0);
    const radius = 200;
    const angleStep = (2 * Math.PI) / otherNodes.length;

    const updatedNodes = nodes.map((node) => {
      if (node.data.level === 0) return node;
      
      const otherNodeIndex = otherNodes.findIndex(n => n.id === node.id);
      const angle = angleStep * otherNodeIndex;
      const x = centralNode.position.x + radius * Math.cos(angle);
      const y = centralNode.position.y + radius * Math.sin(angle);

      return {
        ...node,
        position: { x, y }
      };
    });

    setNodes(updatedNodes);
    setTimeout(() => fitView(), 100);
  }, [nodes, setNodes, fitView]);

  // Tool panel
  const ToolPanel = () => (
    <Panel position="top-left" className="m-4">
      <Card className="p-2">
        <div className="flex items-center gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={selectedTool === 'select' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setSelectedTool('select')}
                  className="h-8 w-8 p-0"
                >
                  <MousePointer2 className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Select Tool</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={selectedTool === 'add-node' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setSelectedTool('add-node')}
                  className="h-8 w-8 p-0"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Add Node (Click on canvas)</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Separator orientation="vertical" className="h-6" />

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={autoLayout}
                  className="h-8 w-8 p-0"
                >
                  <Layers className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Auto Layout</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => fitView()}
                  className="h-8 w-8 p-0"
                >
                  <Maximize2 className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Fit View</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </Card>
    </Panel>
  );

  return (
    <div className="w-full h-full relative" ref={reactFlowWrapper}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodesDelete={onNodesDelete}
        onPaneClick={onPaneClick}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        fitView
        minZoom={0.1}
        maxZoom={2}
        defaultViewport={{ x: 0, y: 0, zoom: 0.8 }}
        nodesDraggable={editable}
        nodesConnectable={editable}
        elementsSelectable={editable}
        className={selectedTool === 'add-node' ? 'cursor-crosshair' : ''}
      >
        <Background color="#aaa" gap={16} />
        <MiniMap 
          nodeColor={(node) => {
            const level = node.data?.level || 0;
            const colors = ['#3b82f6', '#10b981', '#f59e0b', '#8b5cf6', '#ec4899'];
            return colors[level] || '#6b7280';
          }}
          className="bg-white border border-gray-200 rounded"
        />
        <Controls className="bg-white border border-gray-200 rounded" />
        <ToolPanel />
        
        <Panel position="bottom-right" className="m-4">
          <div className="flex items-center gap-2 bg-card p-2 rounded border">
            <Badge variant="outline" className="text-xs">
              {nodes.length} nodes
            </Badge>
            <Badge variant="outline" className="text-xs">
              {edges.length} connections
            </Badge>
          </div>
        </Panel>

        {/* Arrow marker for edges */}
        <defs>
          <marker
            id="arrowhead"
            markerWidth="10"
            markerHeight="7"
            refX="9"
            refY="3.5"
            orient="auto"
          >
            <polygon
              points="0 0, 10 3.5, 0 7"
              fill="#94a3b8"
            />
          </marker>
        </defs>
      </ReactFlow>
    </div>
  );
};

export const MindMapEditor: React.FC<MindMapEditorProps> = (props) => {
  return (
    <ReactFlowProvider>
      <MindMapFlow {...props} />
    </ReactFlowProvider>
  );
};