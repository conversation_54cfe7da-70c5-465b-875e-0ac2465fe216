/**
 * Visualization Utilities
 * Helper functions for visualization components
 */

import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { ExportOptions, BaseVisualization, VisualizationTheme } from '@/types/visualization';

// Default themes
export const lightTheme: VisualizationTheme = {
  name: 'light',
  colors: {
    primary: '#3b82f6',
    secondary: '#64748b',
    background: '#ffffff',
    surface: '#f8fafc',
    text: '#0f172a',
    border: '#e2e8f0',
    accent: ['#3b82f6', '#10b981', '#f59e0b', '#8b5cf6', '#ec4899']
  },
  fonts: {
    primary: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    monospace: '"SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, monospace'
  },
  spacing: {
    small: 8,
    medium: 16,
    large: 24
  }
};

export const darkTheme: VisualizationTheme = {
  name: 'dark',
  colors: {
    primary: '#60a5fa',
    secondary: '#94a3b8',
    background: '#0f172a',
    surface: '#1e293b',
    text: '#f1f5f9',
    border: '#334155',
    accent: ['#60a5fa', '#34d399', '#fbbf24', '#a78bfa', '#f472b6']
  },
  fonts: {
    primary: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    monospace: '"SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, monospace'
  },
  spacing: {
    small: 8,
    medium: 16,
    large: 24
  }
};

// Export functions
export const exportVisualization = async (
  elementId: string,
  options: ExportOptions
): Promise<void> => {
  const element = document.getElementById(elementId);
  if (!element) {
    throw new Error(`Element with ID "${elementId}" not found`);
  }

  try {
    switch (options.format) {
      case 'png':
      case 'jpg':
        await exportAsImage(element, options);
        break;
      case 'svg':
        await exportAsSVG(element, options);
        break;
      case 'pdf':
        await exportAsPDF(element, options);
        break;
      case 'json':
        await exportAsJSON(element, options);
        break;
      default:
        throw new Error(`Unsupported export format: ${options.format}`);
    }
  } catch (error) {
    console.error('Export failed:', error);
    throw error;
  }
};

const exportAsImage = async (
  element: HTMLElement,
  options: ExportOptions
): Promise<void> => {
  const canvas = await html2canvas(element, {
    width: options.width,
    height: options.height,
    backgroundColor: options.background === 'transparent' ? null : 
                     options.background === 'white' ? '#ffffff' : '#000000',
    scale: options.quality === 'high' ? 2 : options.quality === 'medium' ? 1.5 : 1,
    useCORS: true,
    allowTaint: true
  });

  const link = document.createElement('a');
  link.download = `visualization.${options.format}`;
  link.href = canvas.toDataURL(`image/${options.format}`);
  link.click();
};

const exportAsSVG = async (
  element: HTMLElement,
  options: ExportOptions
): Promise<void> => {
  // Find SVG elements within the visualization
  const svgElements = element.querySelectorAll('svg');
  
  if (svgElements.length === 0) {
    throw new Error('No SVG elements found to export');
  }

  // For now, export the first SVG found
  const svg = svgElements[0];
  const svgData = new XMLSerializer().serializeToString(svg);
  const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
  const svgUrl = URL.createObjectURL(svgBlob);

  const link = document.createElement('a');
  link.download = 'visualization.svg';
  link.href = svgUrl;
  link.click();

  URL.revokeObjectURL(svgUrl);
};

const exportAsPDF = async (
  element: HTMLElement,
  options: ExportOptions
): Promise<void> => {
  const canvas = await html2canvas(element, {
    width: options.width,
    height: options.height,
    backgroundColor: options.background === 'transparent' ? '#ffffff' : 
                     options.background === 'white' ? '#ffffff' : '#000000',
    scale: 2,
    useCORS: true,
    allowTaint: true
  });

  const imgData = canvas.toDataURL('image/png');
  const pdf = new jsPDF({
    orientation: canvas.width > canvas.height ? 'landscape' : 'portrait',
    unit: 'px',
    format: [canvas.width, canvas.height]
  });

  pdf.addImage(imgData, 'PNG', 0, 0, canvas.width, canvas.height);
  pdf.save('visualization.pdf');
};

const exportAsJSON = async (
  element: HTMLElement,
  options: ExportOptions
): Promise<void> => {
  // This would need to be implemented based on the specific visualization type
  // For now, we'll export basic metadata
  const data = {
    type: 'visualization',
    timestamp: new Date().toISOString(),
    metadata: {
      width: element.offsetWidth,
      height: element.offsetHeight,
      exportOptions: options
    }
  };

  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.download = 'visualization.json';
  link.href = url;
  link.click();

  URL.revokeObjectURL(url);
};

// Color utilities
export const generateColorPalette = (baseColor: string, count: number = 5): string[] => {
  // Simple palette generation - in a real implementation, you might use a more sophisticated algorithm
  const colors = [baseColor];
  const hsl = hexToHsl(baseColor);
  
  for (let i = 1; i < count; i++) {
    const newHue = (hsl.h + (360 / count) * i) % 360;
    colors.push(hslToHex(newHue, hsl.s, hsl.l));
  }
  
  return colors;
};

const hexToHsl = (hex: string) => {
  const r = parseInt(hex.slice(1, 3), 16) / 255;
  const g = parseInt(hex.slice(3, 5), 16) / 255;
  const b = parseInt(hex.slice(5, 7), 16) / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  const l = (max + min) / 2;

  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }

  return { h: h * 360, s: s * 100, l: l * 100 };
};

const hslToHex = (h: number, s: number, l: number): string => {
  h /= 360;
  s /= 100;
  l /= 100;

  const hue2rgb = (p: number, q: number, t: number) => {
    if (t < 0) t += 1;
    if (t > 1) t -= 1;
    if (t < 1/6) return p + (q - p) * 6 * t;
    if (t < 1/2) return q;
    if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
    return p;
  };

  const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
  const p = 2 * l - q;

  const r = hue2rgb(p, q, h + 1/3);
  const g = hue2rgb(p, q, h);
  const b = hue2rgb(p, q, h - 1/3);

  const toHex = (c: number) => {
    const hex = Math.round(c * 255).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };

  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
};

// Data transformation utilities
export const transformDataForChart = (
  data: any[],
  xKey: string,
  yKey: string
): Array<{ [key: string]: any }> => {
  return data.map(item => ({
    [xKey]: item[xKey],
    [yKey]: item[yKey],
    ...item
  }));
};

export const aggregateData = (
  data: any[],
  groupBy: string,
  aggregateKey: string,
  aggregateFunction: 'sum' | 'avg' | 'count' | 'min' | 'max' = 'sum'
): Array<{ [key: string]: any }> => {
  const grouped = data.reduce((acc, item) => {
    const key = item[groupBy];
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(item);
    return acc;
  }, {} as { [key: string]: any[] });

  return Object.entries(grouped).map(([key, items]) => {
    let value: number;
    
    switch (aggregateFunction) {
      case 'sum':
        value = items.reduce((sum, item) => sum + (parseFloat(item[aggregateKey]) || 0), 0);
        break;
      case 'avg':
        value = items.reduce((sum, item) => sum + (parseFloat(item[aggregateKey]) || 0), 0) / items.length;
        break;
      case 'count':
        value = items.length;
        break;
      case 'min':
        value = Math.min(...items.map(item => parseFloat(item[aggregateKey]) || 0));
        break;
      case 'max':
        value = Math.max(...items.map(item => parseFloat(item[aggregateKey]) || 0));
        break;
      default:
        value = 0;
    }

    return {
      [groupBy]: key,
      [aggregateKey]: value
    };
  });
};

// Validation utilities
export const validateVisualization = (visualization: BaseVisualization): boolean => {
  if (!visualization.id || !visualization.title || !visualization.type) {
    return false;
  }

  // Type-specific validations
  switch (visualization.type) {
    case 'chart':
      return validateChartVisualization(visualization as any);
    case 'mindmap':
      return validateMindMapVisualization(visualization as any);
    case 'network':
      return validateNetworkVisualization(visualization as any);
    case 'timeline':
      return validateTimelineVisualization(visualization as any);
    case 'kanban':
      return validateKanbanVisualization(visualization as any);
    default:
      return false;
  }
};

const validateChartVisualization = (viz: any): boolean => {
  return !!(viz.chartType && viz.data && Array.isArray(viz.data));
};

const validateMindMapVisualization = (viz: any): boolean => {
  return !!(viz.nodes && viz.edges && Array.isArray(viz.nodes) && Array.isArray(viz.edges));
};

const validateNetworkVisualization = (viz: any): boolean => {
  return !!(viz.nodes && viz.edges && Array.isArray(viz.nodes) && Array.isArray(viz.edges));
};

const validateTimelineVisualization = (viz: any): boolean => {
  return !!(viz.items && Array.isArray(viz.items));
};

const validateKanbanVisualization = (viz: any): boolean => {
  return !!(viz.columns && Array.isArray(viz.columns));
};

// Performance utilities
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(null, args), wait);
  };
};

export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func.apply(null, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};