/**
 * VisualizationDemo - Comprehensive demo showcasing all visualization components
 * Demonstrates features, integration patterns, and real-time collaboration
 */

import React, { useState, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  BarChart3,
  Network,
  GitBranch,
  Calendar,
  Kanban,
  Palette,
  Play,
  Pause,
  Maximize2,
  Minimize2
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

import { VisualizationEngine } from './VisualizationEngine';
import { lightTheme, darkTheme } from './utils';
import {
  BaseVisualization,
  ChartVisualization,
  MindMapVisualization,
  NetworkVisualization,
  TimelineVisualization,
  KanbanVisualization,
  VisualizationTheme,
  ExportOptions
} from '@/types/visualization';

// Sample visualizations for demo
const sampleVisualizations: Record<string, BaseVisualization> = {
  chart: {
    id: 'demo-chart',
    title: 'Sales Analytics Dashboard',
    description: 'Monthly sales performance across different product categories',
    type: 'chart',
    chartType: 'bar',
    data: [
      { month: 'Jan', sales: 4000, profit: 2400, expenses: 1600 },
      { month: 'Feb', sales: 3000, profit: 1398, expenses: 1602 },
      { month: 'Mar', sales: 2000, profit: 9800, expenses: -7800 },
      { month: 'Apr', sales: 2780, profit: 3908, expenses: -1128 },
      { month: 'May', sales: 1890, profit: 4800, expenses: -2910 },
      { month: 'Jun', sales: 2390, profit: 3800, expenses: -1410 }
    ],
    config: {
      title: 'Sales Performance',
      subtitle: 'Monthly breakdown with profit/loss analysis',
      xAxis: { label: 'Month', type: 'category' },
      yAxis: { label: 'Amount ($)', type: 'number' },
      legend: { show: true, position: 'top' },
      grid: true,
      colors: ['#3b82f6', '#10b981', '#f59e0b']
    },
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-20'),
    version: 1
  } as ChartVisualization,

  mindmap: {
    id: 'demo-mindmap',
    title: 'Project Architecture Mind Map',
    description: 'Visual representation of system architecture and components',
    type: 'mindmap',
    nodes: [
      { id: 'central', label: 'Lighthouse LM', x: 400, y: 300, level: 0, color: '#3b82f6', shape: 'circle', size: 'large' },
      { id: 'frontend', label: 'Frontend', x: 200, y: 200, level: 1, parentId: 'central', color: '#10b981', shape: 'rectangle', size: 'medium' },
      { id: 'backend', label: 'Backend', x: 600, y: 200, level: 1, parentId: 'central', color: '#f59e0b', shape: 'rectangle', size: 'medium' },
      { id: 'database', label: 'Database', x: 400, y: 450, level: 1, parentId: 'central', color: '#8b5cf6', shape: 'rectangle', size: 'medium' },
      { id: 'react', label: 'React', x: 100, y: 150, level: 2, parentId: 'frontend', color: '#06b6d4', shape: 'ellipse', size: 'small' },
      { id: 'tailwind', label: 'Tailwind', x: 100, y: 250, level: 2, parentId: 'frontend', color: '#06b6d4', shape: 'ellipse', size: 'small' },
      { id: 'api', label: 'REST API', x: 700, y: 150, level: 2, parentId: 'backend', color: '#f97316', shape: 'ellipse', size: 'small' },
      { id: 'auth', label: 'Authentication', x: 700, y: 250, level: 2, parentId: 'backend', color: '#f97316', shape: 'ellipse', size: 'small' }
    ],
    edges: [
      { id: 'e1', source: 'central', target: 'frontend', type: 'bezier' },
      { id: 'e2', source: 'central', target: 'backend', type: 'bezier' },
      { id: 'e3', source: 'central', target: 'database', type: 'bezier' },
      { id: 'e4', source: 'frontend', target: 'react', type: 'straight' },
      { id: 'e5', source: 'frontend', target: 'tailwind', type: 'straight' },
      { id: 'e6', source: 'backend', target: 'api', type: 'straight' },
      { id: 'e7', source: 'backend', target: 'auth', type: 'straight' }
    ],
    centralNode: 'central',
    layout: 'radial',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-18'),
    version: 1
  } as MindMapVisualization,

  network: {
    id: 'demo-network',
    title: 'Team Collaboration Network',
    description: 'Visualization of team interactions and collaboration patterns',
    type: 'network',
    nodes: [
      { id: 'alice', label: 'Alice (PM)', group: 'management', size: 20, color: '#3b82f6', shape: 'circle' },
      { id: 'bob', label: 'Bob (Dev)', group: 'engineering', size: 18, color: '#10b981', shape: 'circle' },
      { id: 'carol', label: 'Carol (Design)', group: 'design', size: 16, color: '#f59e0b', shape: 'circle' },
      { id: 'dave', label: 'Dave (QA)', group: 'quality', size: 14, color: '#8b5cf6', shape: 'circle' },
      { id: 'eve', label: 'Eve (DevOps)', group: 'engineering', size: 16, color: '#10b981', shape: 'circle' },
      { id: 'frank', label: 'Frank (Dev)', group: 'engineering', size: 15, color: '#10b981', shape: 'circle' }
    ],
    edges: [
      { id: 'e1', source: 'alice', target: 'bob', weight: 8, width: 3, label: 'Daily sync' },
      { id: 'e2', source: 'alice', target: 'carol', weight: 6, width: 2.5, label: 'Design review' },
      { id: 'e3', source: 'bob', target: 'frank', weight: 9, width: 3.5, label: 'Pair programming' },
      { id: 'e4', source: 'carol', target: 'dave', weight: 4, width: 2, label: 'UI testing' },
      { id: 'e5', source: 'dave', target: 'bob', weight: 5, width: 2, label: 'Bug reports' },
      { id: 'e6', source: 'eve', target: 'bob', weight: 3, width: 1.5, label: 'Deployment' },
      { id: 'e7', source: 'alice', target: 'dave', weight: 3, width: 1.5, label: 'Status updates' }
    ],
    layout: 'force',
    clustering: false,
    createdAt: new Date('2024-01-12'),
    updatedAt: new Date('2024-01-22'),
    version: 1
  } as NetworkVisualization,

  timeline: {
    id: 'demo-timeline',
    title: 'Product Development Timeline',
    description: 'Complete product development lifecycle from concept to launch',
    type: 'timeline',
    viewType: 'gantt',
    items: [
      {
        id: 'research',
        title: 'Market Research',
        description: 'Competitor analysis and user research',
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-15'),
        category: 'Research',
        status: 'completed',
        priority: 'high',
        assignee: 'Research Team',
        progress: 100,
        color: '#3b82f6'
      },
      {
        id: 'design',
        title: 'UI/UX Design',
        description: 'User interface and experience design',
        startDate: new Date('2024-01-10'),
        endDate: new Date('2024-02-05'),
        category: 'Design',
        status: 'completed',
        priority: 'high',
        assignee: 'Design Team',
        progress: 100,
        color: '#10b981',
        dependencies: ['research']
      },
      {
        id: 'frontend',
        title: 'Frontend Development',
        description: 'React application development',
        startDate: new Date('2024-01-25'),
        endDate: new Date('2024-03-15'),
        category: 'Development',
        status: 'in-progress',
        priority: 'high',
        assignee: 'Frontend Team',
        progress: 75,
        color: '#f59e0b',
        dependencies: ['design']
      },
      {
        id: 'backend',
        title: 'Backend Development',
        description: 'API and database development',
        startDate: new Date('2024-02-01'),
        endDate: new Date('2024-03-20'),
        category: 'Development',
        status: 'in-progress',
        priority: 'high',
        assignee: 'Backend Team',
        progress: 60,
        color: '#8b5cf6',
        dependencies: ['research']
      },
      {
        id: 'testing',
        title: 'Quality Assurance',
        description: 'Comprehensive testing and bug fixes',
        startDate: new Date('2024-03-10'),
        endDate: new Date('2024-03-30'),
        category: 'Testing',
        status: 'planned',
        priority: 'medium',
        assignee: 'QA Team',
        progress: 0,
        color: '#ec4899',
        dependencies: ['frontend', 'backend']
      },
      {
        id: 'deployment',
        title: 'Production Deployment',
        description: 'Go-live and production monitoring',
        startDate: new Date('2024-03-25'),
        endDate: new Date('2024-04-05'),
        category: 'Deployment',
        status: 'planned',
        priority: 'critical',
        assignee: 'DevOps Team',
        progress: 0,
        color: '#ef4444',
        dependencies: ['testing']
      }
    ],
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-04-05'),
    groupBy: 'category',
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-25'),
    version: 1
  } as TimelineVisualization,

  kanban: {
    id: 'demo-kanban',
    title: 'Sprint Planning Board',
    description: 'Current sprint tasks and their progress status',
    type: 'kanban',
    columns: [
      {
        id: 'backlog',
        title: 'Backlog',
        color: '#64748b',
        position: 0,
        cards: [
          {
            id: 'task1',
            title: 'Implement search functionality',
            description: 'Add global search with filters and sorting',
            labels: ['Feature', 'Frontend'],
            assignee: 'Alice Johnson',
            priority: 'medium',
            dueDate: new Date('2024-02-20'),
            createdAt: new Date('2024-01-15'),
            color: '#3b82f6',
            attachments: 2,
            comments: 3,
            checklist: { total: 6, completed: 0 }
          },
          {
            id: 'task2',
            title: 'Database optimization',
            description: 'Optimize database queries for better performance',
            labels: ['Performance', 'Backend'],
            assignee: 'Bob Smith',
            priority: 'high',
            createdAt: new Date('2024-01-18'),
            color: '#10b981',
            attachments: 1,
            comments: 1
          }
        ]
      },
      {
        id: 'todo',
        title: 'To Do',
        color: '#6b7280',
        limit: 5,
        position: 1,
        cards: [
          {
            id: 'task3',
            title: 'User authentication',
            description: 'Implement JWT-based authentication system',
            labels: ['Security', 'Backend'],
            assignee: 'Carol Davis',
            priority: 'high',
            dueDate: new Date('2024-02-10'),
            createdAt: new Date('2024-01-12'),
            color: '#f59e0b',
            attachments: 3,
            comments: 5,
            checklist: { total: 8, completed: 2 }
          }
        ]
      },
      {
        id: 'in-progress',
        title: 'In Progress',
        color: '#f59e0b',
        limit: 3,
        position: 2,
        cards: [
          {
            id: 'task4',
            title: 'API documentation',
            description: 'Write comprehensive API documentation',
            labels: ['Documentation'],
            assignee: 'Dave Wilson',
            priority: 'medium',
            dueDate: new Date('2024-02-15'),
            createdAt: new Date('2024-01-10'),
            color: '#8b5cf6',
            attachments: 4,
            comments: 2,
            checklist: { total: 5, completed: 3 }
          },
          {
            id: 'task5',
            title: 'Unit tests',
            description: 'Write unit tests for core components',
            labels: ['Testing'],
            assignee: 'Eve Brown',
            priority: 'high',
            createdAt: new Date('2024-01-14'),
            color: '#ec4899',
            attachments: 0,
            comments: 7,
            checklist: { total: 10, completed: 6 }
          }
        ]
      },
      {
        id: 'review',
        title: 'Code Review',
        color: '#8b5cf6',
        position: 3,
        cards: [
          {
            id: 'task6',
            title: 'UI component library',
            description: 'Create reusable UI component library',
            labels: ['Frontend', 'Components'],
            assignee: 'Frank Miller',
            priority: 'low',
            createdAt: new Date('2024-01-08'),
            color: '#06b6d4',
            attachments: 2,
            comments: 4
          }
        ]
      },
      {
        id: 'done',
        title: 'Done',
        color: '#10b981',
        position: 4,
        cards: [
          {
            id: 'task7',
            title: 'Project setup',
            description: 'Initialize project with build tools and dependencies',
            labels: ['Setup'],
            assignee: 'Grace Lee',
            priority: 'high',
            createdAt: new Date('2024-01-05'),
            color: '#6366f1',
            attachments: 1,
            comments: 2
          }
        ]
      }
    ],
    swimlanes: false,
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-20'),
    version: 1
  } as KanbanVisualization
};

const visualizationTypes = [
  { id: 'chart', label: 'Charts & Analytics', icon: BarChart3, description: 'Data visualization with multiple chart types' },
  { id: 'mindmap', label: 'Mind Maps', icon: GitBranch, description: 'Knowledge organization and brainstorming' },
  { id: 'network', label: 'Network Graphs', icon: Network, description: 'Relationship and network visualization' },
  { id: 'timeline', label: 'Timeline & Gantt', icon: Calendar, description: 'Project planning and scheduling' },
  { id: 'kanban', label: 'Kanban Boards', icon: Kanban, description: 'Task management and workflow' }
];

export const VisualizationDemo: React.FC = () => {
  const [selectedType, setSelectedType] = useState<string>('chart');
  const [theme, setTheme] = useState<VisualizationTheme>(lightTheme);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showToolbar, setShowToolbar] = useState(true);
  const [isEditable, setIsEditable] = useState(true);
  const [isPlaying, setIsPlaying] = useState(false);
  const [events, setEvents] = useState<Array<{ timestamp: Date; event: any }>>([]);

  // Handle visualization updates
  const handleVisualizationUpdate = useCallback((visualization: BaseVisualization) => {
    console.log('Visualization updated:', visualization);
    setEvents(prev => [...prev, {
      timestamp: new Date(),
      event: { type: 'update', data: visualization }
    }]);
  }, []);

  // Handle visualization events
  const handleVisualizationEvent = useCallback((event: any) => {
    console.log('Visualization event:', event);
    setEvents(prev => [...prev, {
      timestamp: new Date(),
      event
    }]);
  }, []);

  // Handle export
  const handleExport = useCallback((options: ExportOptions) => {
    console.log('Export requested:', options);
    setEvents(prev => [...prev, {
      timestamp: new Date(),
      event: { type: 'export', data: options }
    }]);
  }, []);

  // Toggle theme
  const toggleTheme = useCallback(() => {
    setTheme(current => current.name === 'light' ? darkTheme : lightTheme);
  }, []);

  // Simulate real-time updates
  useEffect(() => {
    if (!isPlaying) return;

    const interval = setInterval(() => {
      setEvents(prev => [...prev, {
        timestamp: new Date(),
        event: { 
          type: 'real-time-update',
          data: { 
            metric: Math.random() * 100,
            user: 'System',
            action: 'data_refresh'
          }
        }
      }]);
    }, 3000);

    return () => clearInterval(interval);
  }, [isPlaying]);

  const currentVisualization = sampleVisualizations[selectedType];
  const selectedTypeConfig = visualizationTypes.find(t => t.id === selectedType);

  return (
    <div className={`w-full h-screen flex flex-col ${theme.name === 'dark' ? 'dark' : ''}`}>
      {/* Header */}
      <div className="border-b bg-card p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Advanced Visualization System</h1>
            <p className="text-muted-foreground">
              Interactive data visualization with real-time collaboration
            </p>
          </div>
          
          <div className="flex items-center gap-4">
            {/* Theme Toggle */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={toggleTheme}
                    className="gap-2"
                  >
                    <Palette className="h-4 w-4" />
                    {theme.name === 'light' ? 'Dark' : 'Light'}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Toggle theme</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {/* Real-time Toggle */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsPlaying(!isPlaying)}
                    className="gap-2"
                  >
                    {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                    Real-time
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  {isPlaying ? 'Pause' : 'Start'} real-time updates
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {/* Fullscreen Toggle */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsFullscreen(!isFullscreen)}
                    className="gap-2"
                  >
                    {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
                    {isFullscreen ? 'Exit' : 'Fullscreen'}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Toggle fullscreen mode</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        {/* Controls */}
        <div className="mt-4 flex items-center gap-6">
          <div className="flex items-center gap-2">
            <Label htmlFor="editable">Editable:</Label>
            <Switch
              id="editable"
              checked={isEditable}
              onCheckedChange={setIsEditable}
            />
          </div>
          
          <div className="flex items-center gap-2">
            <Label htmlFor="toolbar">Show Toolbar:</Label>
            <Switch
              id="toolbar"
              checked={showToolbar}
              onCheckedChange={setShowToolbar}
            />
          </div>

          <div className="flex-1" />
          
          <Badge variant={isPlaying ? 'default' : 'secondary'} className="gap-1">
            <div className={`w-2 h-2 rounded-full ${isPlaying ? 'bg-green-500' : 'bg-gray-500'}`} />
            {isPlaying ? 'Live' : 'Paused'}
          </Badge>
        </div>
      </div>

      {/* Main Content */}
      <div className={`flex-1 flex ${isFullscreen ? 'fixed inset-0 z-50 bg-background' : ''}`}>
        {/* Sidebar */}
        {!isFullscreen && (
          <div className="w-80 border-r bg-card/50 overflow-y-auto">
            <div className="p-4">
              <h3 className="font-semibold mb-4">Visualization Types</h3>
              
              <div className="space-y-2">
                {visualizationTypes.map(type => {
                  const Icon = type.icon;
                  return (
                    <button
                      key={type.id}
                      onClick={() => setSelectedType(type.id)}
                      className={`
                        w-full text-left p-3 rounded-lg border transition-all
                        ${selectedType === type.id 
                          ? 'border-primary bg-primary/10 text-primary' 
                          : 'border-border hover:bg-accent'
                        }
                      `}
                    >
                      <div className="flex items-center gap-3">
                        <Icon className="h-5 w-5 flex-shrink-0" />
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-sm">{type.label}</div>
                          <div className="text-xs text-muted-foreground truncate">
                            {type.description}
                          </div>
                        </div>
                      </div>
                    </button>
                  );
                })}
              </div>

              <Separator className="my-6" />

              <h3 className="font-semibold mb-4">Activity Feed</h3>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {events.slice(-10).reverse().map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="p-2 text-xs bg-muted rounded"
                  >
                    <div className="font-mono text-xs text-muted-foreground">
                      {item.timestamp.toLocaleTimeString()}
                    </div>
                    <div className="mt-1">
                      <span className="font-medium">{item.event.type}</span>
                      {item.event.data && (
                        <div className="text-muted-foreground mt-1">
                          {JSON.stringify(item.event.data, null, 2).slice(0, 100)}...
                        </div>
                      )}
                    </div>
                  </motion.div>
                ))}
                
                {events.length === 0 && (
                  <div className="text-center text-muted-foreground text-sm py-8">
                    No events yet. Interact with visualizations to see activity.
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Visualization Area */}
        <div className="flex-1 flex flex-col">
          {/* Visualization Header */}
          {!isFullscreen && (
            <div className="border-b p-4 bg-card/50">
              <div className="flex items-center gap-3">
                {selectedTypeConfig && (
                  <>
                    <selectedTypeConfig.icon className="h-6 w-6 text-primary" />
                    <div>
                      <h2 className="font-semibold">{selectedTypeConfig.label}</h2>
                      <p className="text-sm text-muted-foreground">
                        {selectedTypeConfig.description}
                      </p>
                    </div>
                  </>
                )}
              </div>
            </div>
          )}

          {/* Visualization Engine */}
          <div className="flex-1">
            <AnimatePresence mode="wait">
              <motion.div
                key={selectedType}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="h-full"
              >
                <VisualizationEngine
                  visualization={currentVisualization}
                  theme={theme}
                  editable={isEditable}
                  showToolbar={showToolbar}
                  onUpdate={handleVisualizationUpdate}
                  onExport={handleExport}
                  onEvent={handleVisualizationEvent}
                />
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>
    </div>
  );
};