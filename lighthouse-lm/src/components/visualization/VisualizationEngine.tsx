/**
 * VisualizationEngine - Core visualization framework
 * Provides unified interface for all visualization types
 */

import React, { useState, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Download, 
  Share2, 
  Settings, 
  Maximize2, 
  Minimize2,
  <PERSON>otateCcw,
  Save,
  Eye,
  EyeOff
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

import { 
  BaseVisualization, 
  VisualizationEngineProps,
  VisualizationTheme,
  ExportOptions,
  VisualizationEventData
} from '@/types/visualization';

import { ChartBuilder } from './ChartBuilder';
import { MindMapEditor } from './MindMapEditor';
import { NetworkGraph } from './NetworkGraph';
import { TimelineVisualizer } from './TimelineVisualizer';
import { KanbanBoard } from './KanbanBoard';

// Default theme
const defaultTheme: VisualizationTheme = {
  name: 'default',
  colors: {
    primary: 'hsl(var(--primary))',
    secondary: 'hsl(var(--secondary))',
    background: 'hsl(var(--background))',
    surface: 'hsl(var(--card))',
    text: 'hsl(var(--foreground))',
    border: 'hsl(var(--border))',
    accent: [
      'hsl(var(--chart-1))',
      'hsl(var(--chart-2))',
      'hsl(var(--chart-3))',
      'hsl(var(--chart-4))',
      'hsl(var(--chart-5))'
    ]
  },
  fonts: {
    primary: 'var(--font-sans)',
    monospace: 'var(--font-mono)'
  },
  spacing: {
    small: 8,
    medium: 16,
    large: 24
  }
};

export const VisualizationEngine: React.FC<VisualizationEngineProps> = ({
  visualization,
  theme = defaultTheme,
  editable = true,
  showToolbar = true,
  onUpdate,
  onExport,
  onEvent,
  plugins = []
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Handle visualization updates
  const handleVisualizationUpdate = useCallback((updatedVisualization: BaseVisualization) => {
    onUpdate?.(updatedVisualization);
    setLastSaved(new Date());
  }, [onUpdate]);

  // Handle export functionality
  const handleExport = useCallback(async (format: ExportOptions['format']) => {
    const options: ExportOptions = {
      format,
      quality: 'high',
      background: 'white',
      includeTitle: true,
      includeMetadata: false
    };
    
    onExport?.(options);
    
    // Emit export event
    onEvent?.({
      type: 'data-update',
      data: { action: 'export', format },
      timestamp: new Date()
    });
  }, [onExport, onEvent]);

  // Handle visualization events
  const handleEvent = useCallback((event: VisualizationEventData) => {
    onEvent?.(event);
  }, [onEvent]);

  // Render appropriate visualization component
  const renderVisualization = useMemo(() => {
    if (!visualization) {
      return (
        <div className="flex items-center justify-center h-96 text-muted-foreground">
          <div className="text-center">
            <div className="text-6xl mb-4">📊</div>
            <h3 className="text-lg font-semibold mb-2">No Visualization Selected</h3>
            <p>Choose a visualization type to get started</p>
          </div>
        </div>
      );
    }

    const commonProps = {
      theme,
      editable: editable && !isPreviewMode,
      onUpdate: handleVisualizationUpdate,
      onEvent: handleEvent
    };

    switch (visualization.type) {
      case 'chart':
        return <ChartBuilder {...commonProps} visualization={visualization} />;
      case 'mindmap':
        return <MindMapEditor {...commonProps} visualization={visualization} />;
      case 'network':
        return <NetworkGraph {...commonProps} visualization={visualization} />;
      case 'timeline':
        return <TimelineVisualizer {...commonProps} visualization={visualization} />;
      case 'kanban':
        return <KanbanBoard {...commonProps} visualization={visualization} />;
      default:
        return (
          <div className="flex items-center justify-center h-96 text-muted-foreground">
            <div className="text-center">
              <div className="text-6xl mb-4">🚧</div>
              <h3 className="text-lg font-semibold mb-2">Visualization Type Not Supported</h3>
              <p>The visualization type "{visualization.type}" is not yet implemented</p>
            </div>
          </div>
        );
    }
  }, [visualization, theme, editable, isPreviewMode, handleVisualizationUpdate, handleEvent]);

  // Toolbar component
  const Toolbar = () => (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className="flex items-center gap-2 p-2 border-b bg-card/50 backdrop-blur-sm"
    >
      {/* View Controls */}
      <div className="flex items-center gap-1">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsPreviewMode(!isPreviewMode)}
                className="h-8 w-8 p-0"
              >
                {isPreviewMode ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {isPreviewMode ? 'Edit Mode' : 'Preview Mode'}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsFullscreen(!isFullscreen)}
                className="h-8 w-8 p-0"
              >
                {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <Separator orientation="vertical" className="h-6" />

      {/* Action Controls */}
      <div className="flex items-center gap-1">
        {editable && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleVisualizationUpdate(visualization!)}
                  className="h-8 w-8 p-0"
                >
                  <Save className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                Save Changes {lastSaved && `(${lastSaved.toLocaleTimeString()})`}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.location.reload()}
                className="h-8 w-8 p-0"
              >
                <RotateCcw className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Reset View</TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <Separator orientation="vertical" className="h-6" />

      {/* Export Controls */}
      <div className="flex items-center gap-1">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleExport('png')}
                className="h-8 w-8 p-0"
              >
                <Download className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Export as PNG</TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigator.share?.({ title: visualization?.title })}
                className="h-8 w-8 p-0"
                disabled={!navigator.share}
              >
                <Share2 className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Share Visualization</TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <div className="flex-1" />

      {/* Settings */}
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
              className="h-8 w-8 p-0"
            >
              <Settings className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Settings</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </motion.div>
  );

  return (
    <div 
      className={`w-full h-full ${isFullscreen ? 'fixed inset-0 z-50 bg-background' : 'relative'}`}
      style={{ fontFamily: theme.fonts.primary }}
    >
      <Card className="w-full h-full border-0 rounded-none">
        {showToolbar && <Toolbar />}
        
        <CardContent className={`p-0 ${showToolbar ? 'h-[calc(100%-60px)]' : 'h-full'}`}>
          <AnimatePresence mode="wait">
            <motion.div
              key={visualization?.id || 'empty'}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="w-full h-full"
            >
              {renderVisualization}
            </motion.div>
          </AnimatePresence>
        </CardContent>

        {/* Settings Panel */}
        <AnimatePresence>
          {showSettings && (
            <motion.div
              initial={{ opacity: 0, x: 300 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 300 }}
              className="absolute top-0 right-0 w-80 h-full bg-card border-l shadow-lg z-10"
            >
              <div className="p-4">
                <h3 className="text-lg font-semibold mb-4">Visualization Settings</h3>
                {/* Settings content will be added based on visualization type */}
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Theme</label>
                    <p className="text-sm text-muted-foreground">
                      Currently using: {theme.name}
                    </p>
                  </div>
                  
                  {visualization && (
                    <div>
                      <label className="text-sm font-medium">Type</label>
                      <p className="text-sm text-muted-foreground capitalize">
                        {visualization.type}
                      </p>
                    </div>
                  )}

                  <div>
                    <label className="text-sm font-medium">Last Updated</label>
                    <p className="text-sm text-muted-foreground">
                      {lastSaved ? lastSaved.toLocaleString() : 'Not saved'}
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
    </div>
  );
};