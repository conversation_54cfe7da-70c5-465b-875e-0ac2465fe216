# Advanced Visualization System

A comprehensive visualization framework for the Lighthouse LM project featuring interactive charts, mind maps, network graphs, timelines, and Kanban boards with real-time collaboration support.

## Features

### 🎯 Core Components

- **VisualizationEngine**: Unified framework for all visualization types
- **ChartBuilder**: Interactive chart creation with multiple chart types
- **MindMapEditor**: Mind mapping and knowledge organization 
- **NetworkGraph**: Relationship and network visualization
- **TimelineVisualizer**: Timeline and Gantt chart components
- **KanbanBoard**: Task management and workflow boards

### ⭐ Key Features

- **Interactive Editing**: Drag-and-drop interface for creating and editing visualizations
- **Real-time Collaboration**: Live updates and multi-user support
- **Export Capabilities**: Export to PNG, SVG, PDF, and JSON formats
- **Theme Support**: Light/dark themes with customizable color palettes
- **Mobile Responsive**: Optimized for all device sizes
- **Performance Optimized**: Efficient rendering and data handling
- **Extensible**: Plugin system for custom visualization types

## Installation

The visualization system is already integrated into the Lighthouse LM project. Required dependencies are:

```bash
npm install @dnd-kit/core @dnd-kit/sortable @dnd-kit/utilities reactflow d3 @types/d3 html2canvas jspdf
```

## Usage

### Basic Usage

```tsx
import { VisualizationEngine, ChartVisualization } from '@/components/visualization';

const chartData: ChartVisualization = {
  id: 'my-chart',
  title: 'Sales Data',
  type: 'chart',
  chartType: 'bar',
  data: [
    { month: 'Jan', sales: 4000, profit: 2400 },
    { month: 'Feb', sales: 3000, profit: 1398 },
    // ... more data
  ],
  config: {
    title: 'Monthly Sales',
    xAxis: { label: 'Month' },
    yAxis: { label: 'Amount ($)' }
  },
  createdAt: new Date(),
  updatedAt: new Date(),
  version: 1
};

function MyComponent() {
  return (
    <VisualizationEngine
      visualization={chartData}
      editable={true}
      showToolbar={true}
      onUpdate={(viz) => console.log('Updated:', viz)}
      onExport={(options) => console.log('Export:', options)}
    />
  );
}
```

### Individual Components

Each visualization type can be used independently:

```tsx
import { ChartBuilder, MindMapEditor, NetworkGraph } from '@/components/visualization';

// Chart Builder
<ChartBuilder
  data={chartData}
  chartType="line"
  theme={lightTheme}
  onConfigChange={(config) => updateConfig(config)}
/>

// Mind Map Editor
<MindMapEditor
  nodes={mindMapNodes}
  edges={mindMapEdges}
  editable={true}
  onNodesChange={(nodes) => updateNodes(nodes)}
/>

// Network Graph
<NetworkGraph
  nodes={networkNodes}
  edges={networkEdges}
  layout="force"
  interactive={true}
  onNodeClick={(node) => handleNodeClick(node)}
/>
```

### Demo Component

Use the demo component to explore all features:

```tsx
import { VisualizationDemo } from '@/components/visualization';

function App() {
  return <VisualizationDemo />;
}
```

## Supported Visualization Types

### 📊 Charts
- **Line Charts**: Trend analysis and time series data
- **Bar Charts**: Category comparisons and rankings
- **Area Charts**: Cumulative data and filled line charts
- **Pie Charts**: Part-to-whole relationships
- **Scatter Plots**: Correlation analysis
- **Radar Charts**: Multi-dimensional comparisons

### 🧠 Mind Maps
- **Radial Layout**: Central topic with branching subtopics
- **Tree Layout**: Hierarchical information structure
- **Force Layout**: Dynamic node positioning
- **Interactive Editing**: Add, edit, delete nodes and connections

### 🕸️ Network Graphs
- **Force-Directed**: Physics-based layout simulation
- **Circular Layout**: Nodes arranged in circles
- **Hierarchical Layout**: Tree-like organization
- **Grid Layout**: Structured positioning
- **Clustering**: Automatic grouping of related nodes

### 📅 Timelines
- **Timeline View**: Linear chronological display
- **Gantt Charts**: Project planning with dependencies
- **Milestone Tracking**: Key project events
- **Resource Management**: Assignee and category filtering

### 📋 Kanban Boards
- **Drag-and-Drop**: Move tasks between columns
- **Card Details**: Rich task information with attachments
- **Column Limits**: WIP (Work In Progress) constraints
- **Priority Levels**: Visual priority indicators

## Theming

The system supports comprehensive theming:

```tsx
import { lightTheme, darkTheme } from '@/components/visualization/utils';

const customTheme = {
  name: 'custom',
  colors: {
    primary: '#3b82f6',
    secondary: '#64748b',
    background: '#ffffff',
    surface: '#f8fafc',
    text: '#0f172a',
    border: '#e2e8f0',
    accent: ['#3b82f6', '#10b981', '#f59e0b', '#8b5cf6', '#ec4899']
  },
  fonts: {
    primary: 'Inter, sans-serif',
    monospace: 'Fira Code, monospace'
  },
  spacing: {
    small: 8,
    medium: 16,
    large: 24
  }
};
```

## Export Options

Export visualizations in multiple formats:

```tsx
const exportOptions = {
  format: 'png' | 'svg' | 'pdf' | 'json',
  quality: 'low' | 'medium' | 'high',
  width: 1920,
  height: 1080,
  background: 'transparent' | 'white' | 'dark',
  includeTitle: true,
  includeMetadata: false
};

await exportVisualization('chart-container', exportOptions);
```

## Data Integration

### State Management
The visualization system integrates with existing state management patterns:

```tsx
// With React Query
const { data, isLoading } = useQuery('chart-data', fetchChartData);

// With Context API
const { updateVisualization } = useVisualizationContext();

// With Redux/Zustand
const dispatch = useAppDispatch();
```

### Real-time Updates
Support for live data updates:

```tsx
// WebSocket integration
useEffect(() => {
  const ws = new WebSocket('ws://localhost:8080');
  ws.onmessage = (event) => {
    const newData = JSON.parse(event.data);
    updateVisualization(newData);
  };
}, []);

// Server-sent events
useEffect(() => {
  const eventSource = new EventSource('/api/visualization-updates');
  eventSource.onmessage = (event) => {
    const update = JSON.parse(event.data);
    handleVisualizationUpdate(update);
  };
}, []);
```

## Collaboration Features

### Multi-user Support
- **Live Cursors**: See other users' mouse positions
- **Real-time Edits**: Synchronized editing across users
- **Comments**: Add contextual comments to visualizations
- **Version History**: Track changes and revert if needed

### Permissions
```tsx
const permissions = {
  canEdit: true,
  canComment: true,
  canExport: true,
  canShare: true
};
```

## Performance Optimization

The system includes several performance optimizations:

- **Virtual Rendering**: Large datasets handled efficiently
- **Debounced Updates**: Reduces unnecessary re-renders
- **Lazy Loading**: Components loaded on demand
- **Memoization**: Expensive calculations cached
- **Web Workers**: Heavy computations off the main thread

## Accessibility

Full WCAG 2.1 AA compliance:
- **Keyboard Navigation**: Full keyboard support
- **Screen Reader**: ARIA labels and descriptions
- **High Contrast**: Theme support for visual accessibility
- **Focus Management**: Clear focus indicators
- **Alternative Text**: Descriptive text for visual elements

## Browser Support

- **Modern Browsers**: Chrome, Firefox, Safari, Edge (latest 2 versions)
- **Mobile Browsers**: iOS Safari, Chrome Mobile
- **Performance**: Optimized for 60fps animations
- **Memory**: Efficient memory usage for large datasets

## API Reference

### Components

#### VisualizationEngine
Main component that renders any visualization type.

**Props:**
- `visualization` (BaseVisualization): The visualization data
- `theme` (VisualizationTheme): Theme configuration
- `editable` (boolean): Enable/disable editing
- `showToolbar` (boolean): Show/hide toolbar
- `onUpdate` (function): Callback when visualization changes
- `onExport` (function): Callback when export is requested
- `onEvent` (function): Callback for visualization events

#### ChartBuilder
Interactive chart creation and editing component.

**Props:**
- `data` (ChartDataPoint[]): Chart data
- `chartType` (ChartType): Type of chart to display
- `config` (ChartConfig): Chart configuration
- `theme` (VisualizationTheme): Theme settings
- `onConfigChange` (function): Config change callback
- `onDataChange` (function): Data change callback

### Utilities

#### exportVisualization
Export a visualization to various formats.

```tsx
exportVisualization(elementId: string, options: ExportOptions): Promise<void>
```

#### generateColorPalette
Create a color palette from a base color.

```tsx
generateColorPalette(baseColor: string, count: number): string[]
```

#### validateVisualization
Validate visualization data structure.

```tsx
validateVisualization(visualization: BaseVisualization): boolean
```

## Development

### Adding New Visualization Types

1. Create the component in the visualization directory
2. Define TypeScript interfaces in `types/visualization.ts`
3. Add to the VisualizationEngine switch statement
4. Export from `index.ts`

### Custom Plugins

Create custom visualization plugins:

```tsx
const customPlugin: VisualizationPlugin = {
  id: 'custom-chart',
  name: 'Custom Chart',
  version: '1.0.0',
  description: 'Custom visualization type',
  supportedTypes: ['custom'],
  component: CustomChartComponent,
  config: {
    // Plugin configuration
  }
};
```

## Troubleshooting

### Common Issues

**Performance Issues:**
- Enable virtualization for large datasets
- Use web workers for heavy computations
- Optimize re-render cycles

**Export Problems:**
- Check browser security settings
- Ensure CORS is configured for external resources
- Verify file permissions

**Responsive Issues:**
- Test on multiple screen sizes
- Use CSS container queries
- Implement mobile-specific interactions

### Debug Mode

Enable debug mode for development:

```tsx
<VisualizationEngine
  // ... other props
  debug={process.env.NODE_ENV === 'development'}
/>
```

## Contributing

1. Follow the existing code patterns
2. Add TypeScript types for new features
3. Include unit tests for components
4. Update documentation
5. Test accessibility compliance

## License

This visualization system is part of the Lighthouse LM project and follows the same license terms.