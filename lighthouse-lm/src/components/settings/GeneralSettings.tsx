import React from 'react';
import { <PERSON>, <PERSON>, <PERSON>, Save, Home } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { GeneralSettings as GeneralSettingsType } from './types';

interface GeneralSettingsProps {
  settings: GeneralSettingsType;
  onChange: (updates: Partial<GeneralSettingsType>) => void;
}

const GeneralSettings: React.FC<GeneralSettingsProps> = ({ settings, onChange }) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-4">General Preferences</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="auto-save">Auto-save</Label>
              <p className="text-sm text-muted-foreground">
                Automatically save your work at regular intervals
              </p>
            </div>
            <Switch
              id="auto-save"
              checked={settings.autoSave}
              onCheckedChange={(checked) => onChange({ autoSave: checked })}
            />
          </div>

          {settings.autoSave && (
            <div className="pl-6 space-y-2">
              <Label htmlFor="auto-save-interval" className="text-sm">
                Auto-save interval (minutes)
              </Label>
              <div className="flex items-center gap-4">
                <Slider
                  id="auto-save-interval"
                  min={1}
                  max={30}
                  step={1}
                  value={[settings.autoSaveInterval]}
                  onValueChange={([value]) => onChange({ autoSaveInterval: value })}
                  className="flex-1"
                />
                <span className="w-12 text-sm text-muted-foreground">
                  {settings.autoSaveInterval}m
                </span>
              </div>
            </div>
          )}

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="confirm-delete">Confirm before delete</Label>
              <p className="text-sm text-muted-foreground">
                Show confirmation dialog before deleting items
              </p>
            </div>
            <Switch
              id="confirm-delete"
              checked={settings.confirmBeforeDelete}
              onCheckedChange={(checked) => onChange({ confirmBeforeDelete: checked })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="notifications">Enable notifications</Label>
              <p className="text-sm text-muted-foreground">
                Receive notifications for important events
              </p>
            </div>
            <Switch
              id="notifications"
              checked={settings.enableNotifications}
              onCheckedChange={(checked) => onChange({ enableNotifications: checked })}
            />
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-4">Localization</h3>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="language">Language</Label>
            <Select
              value={settings.language}
              onValueChange={(value) => onChange({ language: value })}
            >
              <SelectTrigger id="language">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">English</SelectItem>
                <SelectItem value="es">Español</SelectItem>
                <SelectItem value="fr">Français</SelectItem>
                <SelectItem value="de">Deutsch</SelectItem>
                <SelectItem value="ja">日本語</SelectItem>
                <SelectItem value="zh">中文</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="timezone">Timezone</Label>
            <Select
              value={settings.timezone}
              onValueChange={(value) => onChange({ timezone: value })}
            >
              <SelectTrigger id="timezone">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="auto">Auto-detect</SelectItem>
                <SelectItem value="UTC">UTC</SelectItem>
                <SelectItem value="America/New_York">Eastern Time</SelectItem>
                <SelectItem value="America/Chicago">Central Time</SelectItem>
                <SelectItem value="America/Denver">Mountain Time</SelectItem>
                <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                <SelectItem value="Europe/London">London</SelectItem>
                <SelectItem value="Europe/Paris">Paris</SelectItem>
                <SelectItem value="Asia/Tokyo">Tokyo</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-4">Startup</h3>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="startup">When Lighthouse starts</Label>
            <Select
              value={settings.startupBehavior}
              onValueChange={(value: any) => onChange({ startupBehavior: value })}
            >
              <SelectTrigger id="startup">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="dashboard">Show dashboard</SelectItem>
                <SelectItem value="last-opened">Open last notebook</SelectItem>
                <SelectItem value="specific-notebook">Open specific notebook</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {settings.startupBehavior === 'specific-notebook' && (
            <div className="pl-6 space-y-2">
              <Label htmlFor="default-notebook" className="text-sm">
                Default notebook
              </Label>
              <Select
                value={settings.defaultNotebook}
                onValueChange={(value) => onChange({ defaultNotebook: value })}
              >
                <SelectTrigger id="default-notebook">
                  <SelectValue placeholder="Select a notebook" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="notebook-1">My Notes</SelectItem>
                  <SelectItem value="notebook-2">Work Projects</SelectItem>
                  <SelectItem value="notebook-3">Personal</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default GeneralSettings;