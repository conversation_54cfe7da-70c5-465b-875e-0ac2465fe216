import React from 'react';
import { Shield, Database, Lock, Eye, AlertTriangle, Download } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { PrivacySettings as PrivacySettingsType } from './types';

interface PrivacySettingsProps {
  settings: PrivacySettingsType;
  onChange: (updates: Partial<PrivacySettingsType>) => void;
}

const PrivacySettings: React.FC<PrivacySettingsProps> = ({ settings, onChange }) => {
  return (
    <div className="space-y-6">
      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          Your privacy is important. These settings control how your data is collected, stored, and used.
        </AlertDescription>
      </Alert>

      <div>
        <h3 className="text-lg font-medium mb-4">Data Collection</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="analytics">Analytics</Label>
              <p className="text-sm text-muted-foreground">
                Help improve Lighthouse by sharing anonymous usage data
              </p>
            </div>
            <Switch
              id="analytics"
              checked={settings.analyticsEnabled}
              onCheckedChange={(checked) => onChange({ analyticsEnabled: checked })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="crash-reporting">Crash Reporting</Label>
              <p className="text-sm text-muted-foreground">
                Automatically send crash reports to help fix issues
              </p>
            </div>
            <Switch
              id="crash-reporting"
              checked={settings.crashReporting}
              onCheckedChange={(checked) => onChange({ crashReporting: checked })}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="telemetry">Telemetry Level</Label>
            <Select
              value={settings.telemetryLevel}
              onValueChange={(value: any) => onChange({ telemetryLevel: value })}
            >
              <SelectTrigger id="telemetry">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None - No data collection</SelectItem>
                <SelectItem value="minimal">Minimal - Basic usage only</SelectItem>
                <SelectItem value="full">Full - Detailed analytics</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              {settings.telemetryLevel === 'none' && 'No data will be collected'}
              {settings.telemetryLevel === 'minimal' && 'Only essential data like app launches and crashes'}
              {settings.telemetryLevel === 'full' && 'Includes feature usage and performance metrics'}
            </p>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-4">Data Storage</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="sync">Data Sync</Label>
              <p className="text-sm text-muted-foreground">
                Sync your data across devices
              </p>
            </div>
            <Switch
              id="sync"
              checked={settings.dataSyncEnabled}
              onCheckedChange={(checked) => onChange({ dataSyncEnabled: checked })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="encryption">Encryption</Label>
              <p className="text-sm text-muted-foreground">
                Encrypt sensitive data at rest
              </p>
            </div>
            <Switch
              id="encryption"
              checked={settings.encryptionEnabled}
              onCheckedChange={(checked) => onChange({ encryptionEnabled: checked })}
            />
          </div>

          {settings.encryptionEnabled && (
            <Alert>
              <Lock className="h-4 w-4" />
              <AlertDescription>
                End-to-end encryption is enabled. Your data is encrypted before leaving your device.
              </AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="retention">Data Retention (days)</Label>
            <div className="flex items-center gap-4">
              <Slider
                id="retention"
                min={7}
                max={365}
                step={7}
                value={[settings.dataRetentionDays]}
                onValueChange={([value]) => onChange({ dataRetentionDays: value })}
                className="flex-1"
              />
              <span className="w-12 text-sm text-muted-foreground">
                {settings.dataRetentionDays}
              </span>
            </div>
            <p className="text-xs text-muted-foreground">
              Deleted data will be permanently removed after this period
            </p>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="export-on-delete">Export on Delete</Label>
              <p className="text-sm text-muted-foreground">
                Automatically export your data before account deletion
              </p>
            </div>
            <Switch
              id="export-on-delete"
              checked={settings.exportDataOnDelete}
              onCheckedChange={(checked) => onChange({ exportDataOnDelete: checked })}
            />
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-4">Privacy Actions</h3>
        <div className="space-y-2">
          <button className="w-full px-4 py-2 text-left border rounded-lg hover:bg-accent transition-colors">
            <div className="flex items-center gap-3">
              <Download className="h-4 w-4" />
              <div>
                <p className="font-medium">Export My Data</p>
                <p className="text-sm text-muted-foreground">
                  Download all your data in a portable format
                </p>
              </div>
            </div>
          </button>

          <button className="w-full px-4 py-2 text-left border rounded-lg hover:bg-accent transition-colors">
            <div className="flex items-center gap-3">
              <Eye className="h-4 w-4" />
              <div>
                <p className="font-medium">View Privacy Policy</p>
                <p className="text-sm text-muted-foreground">
                  Read our complete privacy policy
                </p>
              </div>
            </div>
          </button>

          <button className="w-full px-4 py-2 text-left border border-destructive/50 rounded-lg hover:bg-destructive/10 transition-colors">
            <div className="flex items-center gap-3">
              <AlertTriangle className="h-4 w-4 text-destructive" />
              <div>
                <p className="font-medium text-destructive">Delete All Data</p>
                <p className="text-sm text-muted-foreground">
                  Permanently delete all your data
                </p>
              </div>
            </div>
          </button>
        </div>
      </div>
    </div>
  );
};

export default PrivacySettings;