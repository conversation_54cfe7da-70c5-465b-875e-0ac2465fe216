import React, { useState } from 'react';
import { Settings, Palette, <PERSON>, Flask, Keyboard, X } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/useToast';
import GeneralSettings from './GeneralSettings';
import AppearanceSettings from './AppearanceSettings';
import PrivacySettings from './PrivacySettings';
import ExperimentalSettings from './ExperimentalSettings';
import KeyboardShortcuts from './KeyboardShortcuts';
import { AppSettings } from './types';

interface SettingsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  settings?: AppSettings;
  onSave?: (settings: AppSettings) => void;
}

const defaultSettings: AppSettings = {
  general: {
    defaultWorkspace: 'default',
    autoSave: true,
    autoSaveInterval: 5,
    confirmBeforeDelete: true,
    enableNotifications: true,
    language: 'en',
    timezone: 'auto',
    startupBehavior: 'dashboard',
  },
  appearance: {
    theme: 'system',
    accentColor: '#3b82f6',
    fontSize: 'medium',
    fontFamily: 'system-ui',
    compactMode: false,
    animations: true,
    reducedMotion: false,
    sidebarPosition: 'left',
    sidebarCollapsed: false,
  },
  privacy: {
    analyticsEnabled: false,
    crashReporting: true,
    dataSyncEnabled: true,
    encryptionEnabled: true,
    telemetryLevel: 'minimal',
    dataRetentionDays: 30,
    exportDataOnDelete: true,
  },
  experimental: {
    enableBetaFeatures: false,
    enableDebugMode: false,
    performanceMode: false,
    enableAIFeatures: true,
    cacheStrategy: 'normal',
    experimentalEditor: false,
  },
  shortcuts: {
    'search': 'Cmd+K',
    'new-notebook': 'Cmd+N',
    'save': 'Cmd+S',
    'settings': 'Cmd+,',
    'toggle-sidebar': 'Cmd+B',
  },
};

export const SettingsDialog: React.FC<SettingsDialogProps> = ({
  open,
  onOpenChange,
  settings = defaultSettings,
  onSave,
}) => {
  const [currentSettings, setCurrentSettings] = useState<AppSettings>(settings);
  const [hasChanges, setHasChanges] = useState(false);
  const { toast } = useToast();

  const handleSettingChange = <K extends keyof AppSettings>(
    category: K,
    updates: Partial<AppSettings[K]>
  ) => {
    setCurrentSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        ...updates,
      },
    }));
    setHasChanges(true);
  };

  const handleSave = () => {
    if (onSave) {
      onSave(currentSettings);
    }
    toast({
      title: 'Settings saved',
      description: 'Your preferences have been updated successfully.',
    });
    setHasChanges(false);
    onOpenChange(false);
  };

  const handleReset = () => {
    setCurrentSettings(defaultSettings);
    setHasChanges(true);
  };

  const handleCancel = () => {
    if (hasChanges) {
      const confirmed = window.confirm('You have unsaved changes. Discard them?');
      if (!confirmed) return;
    }
    setCurrentSettings(settings);
    setHasChanges(false);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] p-0">
        <DialogHeader className="px-6 pt-6">
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Settings
          </DialogTitle>
          <DialogDescription>
            Customize your Lighthouse experience
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 px-6">
          <Tabs defaultValue="general" className="flex gap-6 h-[500px]">
            <TabsList className="flex-col h-fit w-48 p-1">
              <TabsTrigger value="general" className="w-full justify-start">
                <Settings className="h-4 w-4 mr-2" />
                General
              </TabsTrigger>
              <TabsTrigger value="appearance" className="w-full justify-start">
                <Palette className="h-4 w-4 mr-2" />
                Appearance
              </TabsTrigger>
              <TabsTrigger value="privacy" className="w-full justify-start">
                <Lock className="h-4 w-4 mr-2" />
                Privacy
              </TabsTrigger>
              <TabsTrigger value="experimental" className="w-full justify-start">
                <Flask className="h-4 w-4 mr-2" />
                Experimental
              </TabsTrigger>
              <TabsTrigger value="shortcuts" className="w-full justify-start">
                <Keyboard className="h-4 w-4 mr-2" />
                Shortcuts
              </TabsTrigger>
            </TabsList>

            <div className="flex-1">
              <ScrollArea className="h-full pr-4">
                <TabsContent value="general" className="mt-0 space-y-4">
                  <GeneralSettings
                    settings={currentSettings.general}
                    onChange={(updates) => handleSettingChange('general', updates)}
                  />
                </TabsContent>

                <TabsContent value="appearance" className="mt-0 space-y-4">
                  <AppearanceSettings
                    settings={currentSettings.appearance}
                    onChange={(updates) => handleSettingChange('appearance', updates)}
                  />
                </TabsContent>

                <TabsContent value="privacy" className="mt-0 space-y-4">
                  <PrivacySettings
                    settings={currentSettings.privacy}
                    onChange={(updates) => handleSettingChange('privacy', updates)}
                  />
                </TabsContent>

                <TabsContent value="experimental" className="mt-0 space-y-4">
                  <ExperimentalSettings
                    settings={currentSettings.experimental}
                    onChange={(updates) => handleSettingChange('experimental', updates)}
                  />
                </TabsContent>

                <TabsContent value="shortcuts" className="mt-0 space-y-4">
                  <KeyboardShortcuts
                    shortcuts={currentSettings.shortcuts}
                    onChange={(shortcuts) => 
                      setCurrentSettings(prev => ({ ...prev, shortcuts }))
                    }
                  />
                </TabsContent>
              </ScrollArea>
            </div>
          </Tabs>
        </div>

        <Separator />

        <div className="px-6 py-4 flex justify-between items-center">
          <Button variant="ghost" onClick={handleReset}>
            Reset to defaults
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={!hasChanges}>
              Save changes
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SettingsDialog;