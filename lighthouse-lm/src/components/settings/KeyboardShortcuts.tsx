import React, { useState } from 'react';
import { Keyboard, Command, Info } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface KeyboardShortcutsProps {
  shortcuts: Record<string, string>;
  onChange: (shortcuts: Record<string, string>) => void;
}

const defaultShortcuts = {
  'search': 'Cmd+K',
  'new-notebook': 'Cmd+N',
  'save': 'Cmd+S',
  'settings': 'Cmd+,',
  'toggle-sidebar': 'Cmd+B',
  'quick-actions': 'Cmd+Shift+P',
  'focus-mode': 'Cmd+Shift+F',
  'export': 'Cmd+E',
  'import': 'Cmd+I',
  'undo': 'Cmd+Z',
  'redo': 'Cmd+Shift+Z',
  'copy': 'Cmd+C',
  'paste': 'Cmd+V',
  'cut': 'Cmd+X',
  'select-all': 'Cmd+A',
};

const shortcutDescriptions: Record<string, string> = {
  'search': 'Open search dialog',
  'new-notebook': 'Create new notebook',
  'save': 'Save current document',
  'settings': 'Open settings',
  'toggle-sidebar': 'Toggle sidebar visibility',
  'quick-actions': 'Open quick actions menu',
  'focus-mode': 'Toggle focus mode',
  'export': 'Export current document',
  'import': 'Import documents',
  'undo': 'Undo last action',
  'redo': 'Redo last action',
  'copy': 'Copy selected text',
  'paste': 'Paste from clipboard',
  'cut': 'Cut selected text',
  'select-all': 'Select all content',
};

const KeyboardShortcuts: React.FC<KeyboardShortcutsProps> = ({ shortcuts, onChange }) => {
  const [editingShortcut, setEditingShortcut] = useState<string | null>(null);
  const [tempValue, setTempValue] = useState('');
  const [recording, setRecording] = useState(false);

  const handleEdit = (key: string) => {
    setEditingShortcut(key);
    setTempValue(shortcuts[key] || defaultShortcuts[key]);
  };

  const handleSave = () => {
    if (editingShortcut && tempValue) {
      onChange({
        ...shortcuts,
        [editingShortcut]: tempValue,
      });
    }
    setEditingShortcut(null);
    setTempValue('');
  };

  const handleCancel = () => {
    setEditingShortcut(null);
    setTempValue('');
    setRecording(false);
  };

  const handleRecord = () => {
    setRecording(true);
    setTempValue('Press keys...');
    
    const handleKeyDown = (e: KeyboardEvent) => {
      e.preventDefault();
      const keys = [];
      if (e.metaKey) keys.push('Cmd');
      if (e.ctrlKey && !e.metaKey) keys.push('Ctrl');
      if (e.altKey) keys.push('Alt');
      if (e.shiftKey) keys.push('Shift');
      
      if (e.key && !['Meta', 'Control', 'Alt', 'Shift'].includes(e.key)) {
        keys.push(e.key.toUpperCase());
      }
      
      if (keys.length > 0) {
        setTempValue(keys.join('+'));
        setRecording(false);
        document.removeEventListener('keydown', handleKeyDown);
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    setTimeout(() => {
      document.removeEventListener('keydown', handleKeyDown);
      setRecording(false);
    }, 5000);
  };

  const handleReset = () => {
    onChange(defaultShortcuts);
  };

  const allShortcuts = { ...defaultShortcuts, ...shortcuts };

  return (
    <div className="space-y-6">
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          Click on a shortcut to edit it. Press the new key combination to record it.
        </AlertDescription>
      </Alert>

      <div>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">Keyboard Shortcuts</h3>
          <Button variant="outline" size="sm" onClick={handleReset}>
            Reset to defaults
          </Button>
        </div>

        <div className="space-y-2">
          {Object.entries(allShortcuts).map(([key, value]) => (
            <div
              key={key}
              className="flex items-center justify-between p-3 border rounded-lg hover:bg-accent/50 transition-colors"
            >
              <div className="flex-1">
                <p className="font-medium">
                  {shortcutDescriptions[key] || key.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </p>
              </div>
              
              {editingShortcut === key ? (
                <div className="flex items-center gap-2">
                  <Input
                    value={tempValue}
                    onChange={(e) => setTempValue(e.target.value)}
                    className="w-32"
                    placeholder="Press keys..."
                    disabled={recording}
                  />
                  <Button
                    size="sm"
                    variant={recording ? 'destructive' : 'outline'}
                    onClick={handleRecord}
                    disabled={recording}
                  >
                    <Keyboard className="h-4 w-4" />
                  </Button>
                  <Button size="sm" onClick={handleSave}>
                    Save
                  </Button>
                  <Button size="sm" variant="ghost" onClick={handleCancel}>
                    Cancel
                  </Button>
                </div>
              ) : (
                <button
                  onClick={() => handleEdit(key)}
                  className="px-3 py-1 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/80 transition-colors font-mono text-sm"
                >
                  {value}
                </button>
              )}
            </div>
          ))}
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-4">Modifier Keys</h3>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <kbd className="px-2 py-1 bg-muted rounded text-xs">Cmd</kbd>
              <span className="text-muted-foreground">Command (macOS)</span>
            </div>
            <div className="flex items-center gap-2">
              <kbd className="px-2 py-1 bg-muted rounded text-xs">Ctrl</kbd>
              <span className="text-muted-foreground">Control</span>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <kbd className="px-2 py-1 bg-muted rounded text-xs">Alt</kbd>
              <span className="text-muted-foreground">Option (macOS)</span>
            </div>
            <div className="flex items-center gap-2">
              <kbd className="px-2 py-1 bg-muted rounded text-xs">Shift</kbd>
              <span className="text-muted-foreground">Shift</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default KeyboardShortcuts;