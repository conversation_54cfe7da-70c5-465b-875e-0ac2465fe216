export interface AppSettings {
  general: GeneralSettings;
  appearance: AppearanceSettings;
  privacy: PrivacySettings;
  experimental: ExperimentalSettings;
  shortcuts: Record<string, string>;
}

export interface GeneralSettings {
  defaultWorkspace: string;
  autoSave: boolean;
  autoSaveInterval: number; // minutes
  confirmBeforeDelete: boolean;
  enableNotifications: boolean;
  language: string;
  timezone: string;
  startupBehavior: 'dashboard' | 'last-opened' | 'specific-notebook';
  defaultNotebook?: string;
}

export interface AppearanceSettings {
  theme: 'light' | 'dark' | 'system';
  accentColor: string;
  fontSize: 'small' | 'medium' | 'large';
  fontFamily: string;
  compactMode: boolean;
  animations: boolean;
  reducedMotion: boolean;
  sidebarPosition: 'left' | 'right';
  sidebarCollapsed: boolean;
}

export interface PrivacySettings {
  analyticsEnabled: boolean;
  crashReporting: boolean;
  dataSyncEnabled: boolean;
  encryptionEnabled: boolean;
  telemetryLevel: 'none' | 'minimal' | 'full';
  dataRetentionDays: number;
  exportDataOnDelete: boolean;
}

export interface ExperimentalSettings {
  enableBetaFeatures: boolean;
  enableDebugMode: boolean;
  performanceMode: boolean;
  enableAIFeatures: boolean;
  cacheStrategy: 'aggressive' | 'normal' | 'minimal';
  experimentalEditor: boolean;
}

export interface SettingsTab {
  id: string;
  label: string;
  icon: React.ReactNode;
  component: React.ComponentType;
}