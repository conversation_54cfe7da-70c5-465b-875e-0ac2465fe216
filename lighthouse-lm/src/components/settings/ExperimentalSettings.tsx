import React from 'react';
import { Flask, Zap, Bug, Cpu, Database, Sparkles } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ExperimentalSettings as ExperimentalSettingsType } from './types';

interface ExperimentalSettingsProps {
  settings: ExperimentalSettingsType;
  onChange: (updates: Partial<ExperimentalSettingsType>) => void;
}

const ExperimentalSettings: React.FC<ExperimentalSettingsProps> = ({ settings, onChange }) => {
  return (
    <div className="space-y-6">
      <Alert>
        <Flask className="h-4 w-4" />
        <AlertDescription>
          These features are experimental and may be unstable. Use at your own risk.
        </AlertDescription>
      </Alert>

      <div>
        <h3 className="text-lg font-medium mb-4">Beta Features</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="beta-features">Enable Beta Features</Label>
              <p className="text-sm text-muted-foreground">
                Access features that are still in development
              </p>
            </div>
            <Switch
              id="beta-features"
              checked={settings.enableBetaFeatures}
              onCheckedChange={(checked) => onChange({ enableBetaFeatures: checked })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="ai-features">AI-Powered Features</Label>
              <p className="text-sm text-muted-foreground">
                Smart suggestions, auto-completion, and AI assistance
              </p>
            </div>
            <Switch
              id="ai-features"
              checked={settings.enableAIFeatures}
              onCheckedChange={(checked) => onChange({ enableAIFeatures: checked })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="experimental-editor">Experimental Editor</Label>
              <p className="text-sm text-muted-foreground">
                New editor with advanced features and performance improvements
              </p>
            </div>
            <Switch
              id="experimental-editor"
              checked={settings.experimentalEditor}
              onCheckedChange={(checked) => onChange({ experimentalEditor: checked })}
            />
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-4">Performance</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="performance-mode">Performance Mode</Label>
              <p className="text-sm text-muted-foreground">
                Optimize for speed over visual effects
              </p>
            </div>
            <Switch
              id="performance-mode"
              checked={settings.performanceMode}
              onCheckedChange={(checked) => onChange({ performanceMode: checked })}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="cache-strategy">Cache Strategy</Label>
            <Select
              value={settings.cacheStrategy}
              onValueChange={(value: any) => onChange({ cacheStrategy: value })}
            >
              <SelectTrigger id="cache-strategy">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="minimal">Minimal - Less memory usage</SelectItem>
                <SelectItem value="normal">Normal - Balanced</SelectItem>
                <SelectItem value="aggressive">Aggressive - Faster but more memory</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              {settings.cacheStrategy === 'minimal' && 'Reduces memory usage but may be slower'}
              {settings.cacheStrategy === 'normal' && 'Balanced between speed and memory'}
              {settings.cacheStrategy === 'aggressive' && 'Faster performance but uses more memory'}
            </p>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-4">Developer</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="debug-mode">Debug Mode</Label>
              <p className="text-sm text-muted-foreground">
                Show detailed logs and debugging information
              </p>
            </div>
            <Switch
              id="debug-mode"
              checked={settings.enableDebugMode}
              onCheckedChange={(checked) => onChange({ enableDebugMode: checked })}
            />
          </div>

          {settings.enableDebugMode && (
            <Alert>
              <Bug className="h-4 w-4" />
              <AlertDescription>
                Debug mode is enabled. Check the console for detailed logs.
              </AlertDescription>
            </Alert>
          )}
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-4">Experimental Actions</h3>
        <div className="space-y-2">
          <button className="w-full px-4 py-2 text-left border rounded-lg hover:bg-accent transition-colors">
            <div className="flex items-center gap-3">
              <Database className="h-4 w-4" />
              <div>
                <p className="font-medium">Rebuild Database</p>
                <p className="text-sm text-muted-foreground">
                  Rebuild search index and optimize database
                </p>
              </div>
            </div>
          </button>

          <button className="w-full px-4 py-2 text-left border rounded-lg hover:bg-accent transition-colors">
            <div className="flex items-center gap-3">
              <Zap className="h-4 w-4" />
              <div>
                <p className="font-medium">Clear Cache</p>
                <p className="text-sm text-muted-foreground">
                  Clear all cached data and temporary files
                </p>
              </div>
            </div>
          </button>

          <button className="w-full px-4 py-2 text-left border rounded-lg hover:bg-accent transition-colors">
            <div className="flex items-center gap-3">
              <Sparkles className="h-4 w-4" />
              <div>
                <p className="font-medium">Reset Experiments</p>
                <p className="text-sm text-muted-foreground">
                  Reset all experimental features to defaults
                </p>
              </div>
            </div>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExperimentalSettings;