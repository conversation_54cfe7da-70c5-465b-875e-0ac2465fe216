import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { Plus, Search, LogOut, HelpCircle, BarChart3 } from 'lucide-react';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { DASHBOARD_CONSTANTS, DASHBOARD_CLASSES } from './constants';
import PerformanceDashboard from '../debug/PerformanceDashboard';
import { useLogout } from '@/services/auth';

interface DashboardHeaderProps {
  children?: React.ReactNode;
  onHelpClick?: () => void;
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({ children, onHelpClick }) => {
  const navigate = useNavigate();
  const { logout, isLoading: isLoggingOut } = useLogout();
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isPerformanceDashboardOpen, setIsPerformanceDashboardOpen] = useState(false);

  return (
    <>
    <motion.header 
      className="bg-background border-b border-border px-4 sm:px-6 py-3 sm:py-4"
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      <div className="w-full">
        <div className="flex items-center justify-between">
          {/* Logo and Brand */}
          <motion.div 
            className="flex items-center space-x-2 sm:space-x-4"
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.4, delay: 0.1 }}
          >
            <motion.button 
              onClick={() => navigate('/lighthouse-lm')}
              className="flex items-center space-x-1 sm:space-x-2 group rounded-lg p-1"
              aria-label="Go to dashboard home"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <motion.div 
                className="w-7 h-7 sm:w-8 sm:h-8 bg-primary rounded-lg flex items-center justify-center"
                whileHover={{ 
                  boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
                  rotate: [0, -1, 1, 0]
                }}
                transition={{ duration: 0.3 }}
              >
                <motion.span 
                  className="text-primary-foreground font-bold text-xs sm:text-sm"
                  whileHover={{ scale: 1.1 }}
                  transition={{ duration: 0.2 }}
                >
                  {DASHBOARD_CONSTANTS.BRAND_INITIALS}
                </motion.span>
              </motion.div>
              <motion.h1 
                className="text-lg sm:text-xl font-semibold text-foreground hidden xs:block"
                whileHover={{ color: "hsl(var(--primary))" }}
                transition={{ duration: 0.2 }}
              >
                {DASHBOARD_CONSTANTS.BRAND_NAME}
              </motion.h1>
            </motion.button>
          </motion.div>

          {/* Desktop Search */}
          <motion.div 
            className="hidden md:flex flex-1 max-w-3xl mx-8"
            initial={{ y: -10, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.4, delay: 0.2 }}
          >
            {children ? (
              children
            ) : (
              <motion.div 
                className="relative group w-full"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                <motion.div
                  animate={{
                    scale: isSearchFocused ? 1.1 : 1,
                    color: isSearchFocused ? "hsl(var(--primary))" : "hsl(var(--muted-foreground))"
                  }}
                  transition={{ duration: 0.2 }}
                >
                  <Search className={DASHBOARD_CLASSES.searchIcon(isSearchFocused)} aria-hidden="true" />
                </motion.div>
                <motion.input
                  type="text"
                  placeholder="Search notebooks..."
                  className={`w-full pl-10 pr-4 py-2 border rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent hover:border-border/80 hover:shadow-sm bg-background text-foreground ${
                    isSearchFocused ? 'border-primary/50 shadow-md' : 'border-border'
                  }`}
                  onFocus={() => setIsSearchFocused(true)}
                  onBlur={() => setIsSearchFocused(false)}
                  whileFocus={{ 
                    boxShadow: "0 0 0 3px rgba(var(--primary), 0.1)",
                    borderColor: "hsl(var(--primary))"
                  }}
                  aria-label="Search notebooks"
                />
              </motion.div>
            )}
          </motion.div>

          {/* Mobile Actions */}
          <motion.div 
            className="flex md:hidden items-center space-x-2"
            initial={{ x: 20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.4, delay: 0.3 }}
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="p-2"
                aria-label="Toggle mobile menu"
              >
                <motion.div
                  whileHover={{ rotate: 90 }}
                  transition={{ duration: 0.2 }}
                >
                  <Search className="w-4 h-4" />
                </motion.div>
              </Button>
            </motion.div>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <Button
                onClick={() => navigate('/lighthouse-lm/new')}
                size="sm"
                className="bg-primary hover:bg-primary/90 text-primary-foreground p-2"
                aria-label="Create a new notebook"
              >
                <motion.div
                  whileHover={{ rotate: 90 }}
                  transition={{ duration: 0.2 }}
                >
                  <Plus className="w-4 h-4" />
                </motion.div>
              </Button>
            </motion.div>
          </motion.div>

          {/* Desktop Actions */}
          <motion.div 
            className="hidden md:flex items-center space-x-4"
            initial={{ x: 20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.4, delay: 0.3 }}
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.3, delay: 0.4, type: "spring", stiffness: 200 }}
            >
              <ThemeToggle />
            </motion.div>
            {process.env.NODE_ENV === 'development' && (
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsPerformanceDashboardOpen(true)}
                  className="flex items-center space-x-2"
                  aria-label="Open performance dashboard"
                >
                  <motion.div
                    whileHover={{ rotate: [0, -10, 10, 0] }}
                    transition={{ duration: 0.4 }}
                  >
                    <BarChart3 className="w-4 h-4" aria-hidden="true" />
                  </motion.div>
                  <span className="hidden lg:inline">Perf</span>
                </Button>
              </motion.div>
            )}
            {onHelpClick && (
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onHelpClick}
                  className="flex items-center space-x-2"
                  aria-label="Open help system"
                >
                  <motion.div
                    whileHover={{ rotate: [0, -10, 10, 0] }}
                    transition={{ duration: 0.4 }}
                  >
                    <HelpCircle className="w-4 h-4" aria-hidden="true" />
                  </motion.div>
                  <span className="hidden lg:inline">Help</span>
                </Button>
              </motion.div>
            )}
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <Button
                variant="outline"
                size="sm"
                onClick={logout}
                disabled={isLoggingOut}
                className="flex items-center space-x-2"
                aria-label="Logout"
              >
                <motion.div
                  whileHover={{ rotate: [0, -10, 10, 0] }}
                  transition={{ duration: 0.4 }}
                >
                  <LogOut className="w-4 h-4" aria-hidden="true" />
                </motion.div>
                <span className="hidden lg:inline">{isLoggingOut ? 'Logging out...' : 'Logout'}</span>
              </Button>
            </motion.div>
            <motion.div
              whileHover={{ 
                scale: 1.05,
                boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
              }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <Button
                onClick={() => navigate('/lighthouse-lm/new')}
                className="flex items-center space-x-2 bg-primary hover:bg-primary/90 text-primary-foreground group relative overflow-hidden"
                aria-label="Create a new notebook"
              >
                <motion.div
                  whileHover={{ rotate: 90, scale: 1.1 }}
                  transition={{ duration: 0.3 }}
                >
                  <Plus className="w-4 h-4 relative z-10" aria-hidden="true" />
                </motion.div>
                <span className="relative z-10 hidden lg:inline">New Notebook</span>
                <motion.div 
                  className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary/40"
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                />
              </Button>
            </motion.div>
          </motion.div>
        </div>
        
        {/* Mobile Search Dropdown */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div 
              className="md:hidden mt-4 pb-4 border-t border-border pt-4"
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
            >
              <motion.div 
                className="relative"
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.2, delay: 0.1 }}
              >
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search notebooks..."
                  className="w-full pl-10 pr-4 py-2 border rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent bg-background text-foreground"
                  aria-label="Search notebooks"
                />
              </motion.div>
              <motion.div 
                className="flex items-center justify-between mt-4 pt-4 border-t border-border"
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.2, delay: 0.2 }}
              >
                <div className="flex items-center space-x-2">
                  <ThemeToggle />
                  {onHelpClick && (
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={onHelpClick}
                        className="flex items-center space-x-2"
                        aria-label="Open help system"
                      >
                        <HelpCircle className="w-4 h-4" />
                        <span>Help</span>
                      </Button>
                    </motion.div>
                  )}
                </div>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={logout}
                    disabled={isLoggingOut}
                    className="flex items-center space-x-2"
                    aria-label="Logout"
                  >
                    <LogOut className="w-4 h-4" />
                    <span>{isLoggingOut ? 'Logging out...' : 'Logout'}</span>
                  </Button>
                </motion.div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      </motion.header>
      
      {/* Performance Dashboard */}
      <PerformanceDashboard 
        isOpen={isPerformanceDashboardOpen}
        onClose={() => setIsPerformanceDashboardOpen(false)}
      />
    </>
  );
};

export default DashboardHeader;