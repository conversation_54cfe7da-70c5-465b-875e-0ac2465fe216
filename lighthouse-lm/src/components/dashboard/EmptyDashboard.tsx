import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Plus, Upload, FileText, Globe, Youtube, Mic } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useNotebooks } from '../../hooks/useNotebooks';
import { useNavigate } from 'react-router-dom';
import { DASHBOARD_CONSTANTS, DASHBOARD_STYLES } from './constants';
import DashboardButton from './DashboardButton';

const EmptyDashboard = () => {
  const navigate = useNavigate();
  const { createNotebook, isCreating } = useNotebooks();
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [notebookTitle, setNotebookTitle] = useState('');
  const [notebookDescription, setNotebookDescription] = useState('');

  const handleCreateNotebook = async () => {
    if (!notebookTitle.trim()) return;
    
    try {
      await createNotebook({
        title: notebookTitle,
        description: notebookDescription || undefined,
      });
      
      setShowCreateDialog(false);
      setNotebookTitle('');
      setNotebookDescription('');
    } catch (error) {
    }
  };

  const quickStartOptions = [
    {
      icon: <FileText className="w-6 h-6" />,
      title: 'Upload Documents',
      description: 'Start with PDFs, Word docs, or text files',
      color: 'bg-accent text-accent-foreground',
    },
    {
      icon: <Globe className="w-6 h-6" />,
      title: 'Add Web Pages',
      description: 'Import content from websites',
      color: 'bg-accent text-accent-foreground',
    },
    {
      icon: <Youtube className="w-6 h-6" />,
      title: 'YouTube Videos',
      description: 'Extract transcripts from videos',
      color: 'bg-accent text-accent-foreground',
    },
    {
      icon: <Mic className="w-6 h-6" />,
      title: 'Audio Files',
      description: 'Transcribe and analyze audio content',
      color: 'bg-accent text-accent-foreground',
    },
  ];

  return (
    <div className="flex flex-col items-center justify-center py-16">
      <div className="text-center max-w-2xl mx-auto">
        <div className="mb-8">
          <div className={`w-24 h-24 bg-accent rounded-full flex items-center justify-center mx-auto mb-4 transition-all duration-500 hover:scale-110 hover:shadow-lg animate-pulse`}>
            <Upload className="w-12 h-12 text-accent-foreground transition-all duration-300 hover:scale-110" />
          </div>
          <h2 className="text-3xl font-bold text-foreground mb-2 transition-colors duration-300 hover:text-primary">
            {DASHBOARD_CONSTANTS.WELCOME_TEXT}
          </h2>
          <p className="text-lg text-muted-foreground transition-colors duration-300 hover:text-foreground">
            Create your first notebook to start organizing and analyzing your content with AI
          </p>
        </div>

        <DashboardButton
          size="lg"
          onClick={() => setShowCreateDialog(true)}
          className="mb-8"
          aria-label="Create your first notebook"
        >
          <Plus className="w-5 h-5 mr-2 transition-transform duration-300 group-hover:rotate-90" aria-hidden="true" />
          <span className="transition-all duration-300 group-hover:tracking-wide">Create Your First Notebook</span>
        </DashboardButton>

        <div className="grid grid-cols-2 gap-4 mt-12">
          {quickStartOptions.map((option, index) => (
            <button
              key={index}
              className="border border-border rounded-lg p-6 hover:shadow-lg hover:border-border/80 transition-all duration-300 cursor-pointer text-left hover:scale-105 active:scale-95 group hover:-translate-y-1 bg-card hover:bg-card/80"
              onClick={() => setShowCreateDialog(true)}
              style={{ animationDelay: `${index * 100}ms` }}
              aria-label={`Quick start with ${option.title}: ${option.description}`}
            >
              <div className={`w-12 h-12 rounded-lg flex items-center justify-center mb-3 transition-all duration-300 group-hover:scale-110 group-hover:shadow-md ${option.color}`}>
                {React.cloneElement(option.icon, { 
                  className: "w-6 h-6 transition-transform duration-300 group-hover:scale-110" 
                })}
              </div>
              <h3 className="font-medium text-card-foreground mb-1 transition-colors duration-300 group-hover:text-primary">{option.title}</h3>
              <p className="text-sm text-muted-foreground transition-colors duration-300 group-hover:text-card-foreground">{option.description}</p>
            </button>
          ))}
        </div>

        <div className="mt-12 p-6 bg-muted/50 rounded-lg transition-all duration-300 hover:bg-muted hover:shadow-md group">
          <h3 className="font-medium text-foreground mb-2 transition-colors duration-300 group-hover:text-primary">
            What can you do with LighthouseLM?
          </h3>
          <ul className="text-left text-sm text-muted-foreground space-y-2">
            <li className="flex items-start transition-all duration-300 hover:text-foreground hover:translate-x-1" style={{ animationDelay: '0ms' }}>
              <span className="text-emerald-500 mr-2 transition-all duration-300 hover:scale-125">✓</span>
              Upload and organize documents, web pages, and media files
            </li>
            <li className="flex items-start transition-all duration-300 hover:text-foreground hover:translate-x-1" style={{ animationDelay: '100ms' }}>
              <span className="text-emerald-500 mr-2 transition-all duration-300 hover:scale-125">✓</span>
              Chat with AI about your content with source citations
            </li>
            <li className="flex items-start transition-all duration-300 hover:text-foreground hover:translate-x-1" style={{ animationDelay: '200ms' }}>
              <span className="text-emerald-500 mr-2 transition-all duration-300 hover:scale-125">✓</span>
              Generate summaries and extract key insights
            </li>
            <li className="flex items-start transition-all duration-300 hover:text-foreground hover:translate-x-1" style={{ animationDelay: '300ms' }}>
              <span className="text-emerald-500 mr-2 transition-all duration-300 hover:scale-125">✓</span>
              Create notes and organize your research
            </li>
          </ul>
        </div>
      </div>

      {/* Create Notebook Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Notebook</DialogTitle>
            <DialogDescription>
              Give your notebook a name and optional description to get started
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 mt-4">
            <div>
              <label className="text-sm font-medium text-foreground mb-1 block transition-colors duration-300 hover:text-primary">
                Notebook Title *
              </label>
              <Input
                placeholder="e.g., Research Notes, Project Documentation"
                value={notebookTitle}
                onChange={(e) => setNotebookTitle(e.target.value)}
                autoFocus
                className="transition-all duration-300 focus:ring-2 focus:ring-primary focus:border-primary hover:border-border/80"
                aria-label="Enter notebook title"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium text-foreground mb-1 block transition-colors duration-300 hover:text-primary">
                Description (optional)
              </label>
              <Textarea
                placeholder="What is this notebook about?"
                value={notebookDescription}
                onChange={(e) => setNotebookDescription(e.target.value)}
                rows={3}
                className="transition-all duration-300 focus:ring-2 focus:ring-primary focus:border-primary hover:border-border/80 resize-none"
                aria-label="Enter notebook description"
              />
            </div>
          </div>
          
          <div className="flex justify-end space-x-2 mt-6">
            <Button
              variant="outline"
              onClick={() => {
                setShowCreateDialog(false);
                setNotebookTitle('');
                setNotebookDescription('');
              }}
              className="transition-all duration-300 hover:scale-105 active:scale-95 focus:ring-2 focus:ring-muted-foreground focus:ring-offset-2"
              aria-label="Cancel notebook creation"
            >
              Cancel
            </Button>
            <DashboardButton
              onClick={handleCreateNotebook}
              disabled={!notebookTitle.trim() || isCreating}
              isLoading={isCreating}
              aria-label={isCreating ? 'Creating notebook...' : 'Create notebook'}
            >
              <span className="transition-all duration-300 group-hover:tracking-wide">
                {isCreating ? 'Creating...' : 'Create Notebook'}
              </span>
            </DashboardButton>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default EmptyDashboard;