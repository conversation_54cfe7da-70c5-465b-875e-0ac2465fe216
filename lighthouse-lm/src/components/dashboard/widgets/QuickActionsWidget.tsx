import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { 
  Plus, 
  FileText, 
  Upload, 
  MessageSquare, 
  Search, 
  Bookmark, 
  Share2, 
  Download, 
  Copy, 
  Link, 
  Zap, 
  Clock, 
  Star, 
  Archive, 
  Trash2, 
  Settings, 
  Palette, 
  Globe, 
  Camera, 
  Mic, 
  Video, 
  Calendar, 
  Users, 
  Bell, 
  Filter, 
  Tag, 
  Folder, 
  Database, 
  Code, 
  Terminal, 
  GitBranch, 
  Layers, 
  Target, 
  Lightbulb, 
  Rocket, 
  Heart, 
  Coffee, 
  Moon, 
  Sun, 
  Wifi, 
  Bluetooth, 
  Volume2, 
  Battery, 
  Smartphone, 
  Monitor, 
  Headphones, 
  Keyboard, 
  Mouse, 
  Printer, 
  HardDrive, 
  Cpu, 
  MemoryStick, 
  Usb, 
  Router, 
  Server, 
  Cloud, 
  Shield, 
  Lock, 
  Key, 
  Eye, 
  EyeOff, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Info, 
  HelpCircle, 
  RefreshCw, 
  RotateCcw, 
  RotateCw, 
  Maximize2, 
  Minimize2, 
  Move, 
  Resize, 
  Crop, 
  Edit, 
  Scissors, 
  PaintBucket, 
  Brush, 
  Eraser, 
  Ruler, 
  Grid, 
  Layout, 
  Sidebar, 
  PanelLeft, 
  PanelRight, 
  PanelTop, 
  PanelBottom, 
  Columns, 
  Rows, 
  Table, 
  List, 
  Grid3X3, 
  MoreHorizontal, 
  MoreVertical, 
  ChevronUp, 
  ChevronDown, 
  ChevronLeft, 
  ChevronRight, 
  ArrowUp, 
  ArrowDown, 
  ArrowLeft, 
  ArrowRight, 
  ExternalLink, 
  Home, 
  Menu, 
  X, 
  Check, 
  Minus, 
  Equal, 
  Slash, 
  Percent, 
  Hash, 
  AtSign, 
  DollarSign, 
  Euro, 
  IndianRupee, 
  Bitcoin, 
  CreditCard, 
  Banknote, 
  Coins, 
  Wallet, 
  ShoppingCart, 
  ShoppingBag, 
  Package, 
  Truck, 
  Plane, 
  Car, 
  Bike, 
  Bus, 
  Train, 
  Ship, 
  Anchor, 
  MapPin, 
  Map, 
  Navigation, 
  Compass, 
  Route, 
  Flag, 
  Mountain, 
  Trees, 
  Flower, 
  Leaf, 
  Snowflake, 
  Sun as SunIcon, 
  Moon as MoonIcon, 
  CloudRain, 
  CloudSnow, 
  CloudLightning, 
  Wind, 
  Thermometer, 
  Droplets, 
  Umbrella, 
  Rainbow, 
  Sunrise, 
  Sunset, 
  Activity, 
  BarChart, 
  BarChart2, 
  BarChart3, 
  LineChart, 
  PieChart, 
  TrendingUp, 
  TrendingDown, 
  Gauge, 
  Timer, 
  AlarmClock, 
  Hourglass, 
  CalendarDays, 
  CalendarCheck, 
  CalendarX, 
  CalendarPlus, 
  CalendarMinus, 
  CalendarClock, 
  CalendarHeart, 
  CalendarRange, 
  Clock1, 
  Clock2, 
  Clock3, 
  Clock4, 
  Clock5, 
  Clock6, 
  Clock7, 
  Clock8, 
  Clock9, 
  Clock10, 
  Clock11, 
  Clock12
} from 'lucide-react';

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  category: 'create' | 'manage' | 'tools' | 'shortcuts' | 'recent';
  shortcut?: string;
  badge?: string;
  color: string;
  onClick: () => void;
  disabled?: boolean;
}

interface QuickActionsWidgetProps {
  className?: string;
  onCreateNotebook?: () => void;
  onUploadSource?: () => void;
  onStartChat?: () => void;
  onOpenSearch?: () => void;
  onImportWeb?: () => void;
  onGenerateDiagram?: () => void;
}

const QuickActionsWidget: React.FC<QuickActionsWidgetProps> = ({
  className,
  onCreateNotebook,
  onUploadSource,
  onStartChat,
  onOpenSearch,
  onImportWeb,
  onGenerateDiagram
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState<string>('all');
  const [recentActions, setRecentActions] = useState<string[]>(['create-notebook', 'upload-source', 'start-chat']);

  const quickActions: QuickAction[] = [
    // Create Actions
    {
      id: 'create-notebook',
      title: 'New Notebook',
      description: 'Create a new notebook for your research',
      icon: <FileText className="w-5 h-5" />,
      category: 'create',
      shortcut: '⌘N',
      color: 'bg-blue-500 hover:bg-blue-600',
      onClick: () => {
        onCreateNotebook?.();
        addToRecent('create-notebook');
      }
    },
    {
      id: 'upload-source',
      title: 'Upload Source',
      description: 'Add documents, PDFs, or files',
      icon: <Upload className="w-5 h-5" />,
      category: 'create',
      shortcut: '⌘U',
      color: 'bg-green-500 hover:bg-green-600',
      onClick: () => {
        onUploadSource?.();
        addToRecent('upload-source');
      }
    },
    {
      id: 'start-chat',
      title: 'Start Chat',
      description: 'Begin a conversation with AI',
      icon: <MessageSquare className="w-5 h-5" />,
      category: 'create',
      shortcut: '⌘T',
      color: 'bg-purple-500 hover:bg-purple-600',
      onClick: () => {
        onStartChat?.();
        addToRecent('start-chat');
      }
    },
    {
      id: 'generate-diagram',
      title: 'Generate Diagram',
      description: 'Create visual representations',
      icon: <Layers className="w-5 h-5" />,
      category: 'create',
      shortcut: '⌘D',
      color: 'bg-orange-500 hover:bg-orange-600',
      onClick: () => {
        onGenerateDiagram?.();
        addToRecent('generate-diagram');
      }
    },
    {
      id: 'import-web',
      title: 'Import from Web',
      description: 'Fetch content from URLs',
      icon: <Globe className="w-5 h-5" />,
      category: 'create',
      shortcut: '⌘I',
      color: 'bg-teal-500 hover:bg-teal-600',
      onClick: () => {
        onImportWeb?.();
        addToRecent('import-web');
      }
    },
    {
      id: 'create-template',
      title: 'Use Template',
      description: 'Start from a predefined template',
      icon: <Copy className="w-5 h-5" />,
      category: 'create',
      color: 'bg-indigo-500 hover:bg-indigo-600',
      onClick: () => {
        console.log('Create from template');
        addToRecent('create-template');
      }
    },
    
    // Manage Actions
    {
      id: 'search-all',
      title: 'Search Everything',
      description: 'Find content across all sources',
      icon: <Search className="w-5 h-5" />,
      category: 'manage',
      shortcut: '⌘K',
      color: 'bg-gray-500 hover:bg-gray-600',
      onClick: () => {
        onOpenSearch?.();
        addToRecent('search-all');
      }
    },
    {
      id: 'manage-sources',
      title: 'Manage Sources',
      description: 'Organize and edit your sources',
      icon: <Folder className="w-5 h-5" />,
      category: 'manage',
      shortcut: '⌘M',
      color: 'bg-yellow-500 hover:bg-yellow-600',
      onClick: () => {
        console.log('Manage sources');
        addToRecent('manage-sources');
      }
    },
    {
      id: 'view-bookmarks',
      title: 'Bookmarks',
      description: 'Access your saved items',
      icon: <Bookmark className="w-5 h-5" />,
      category: 'manage',
      color: 'bg-pink-500 hover:bg-pink-600',
      onClick: () => {
        console.log('View bookmarks');
        addToRecent('view-bookmarks');
      }
    },
    {
      id: 'recent-activity',
      title: 'Recent Activity',
      description: 'See your latest actions',
      icon: <Clock className="w-5 h-5" />,
      category: 'manage',
      color: 'bg-cyan-500 hover:bg-cyan-600',
      onClick: () => {
        console.log('Recent activity');
        addToRecent('recent-activity');
      }
    },
    {
      id: 'manage-tags',
      title: 'Manage Tags',
      description: 'Organize with labels',
      icon: <Tag className="w-5 h-5" />,
      category: 'manage',
      color: 'bg-emerald-500 hover:bg-emerald-600',
      onClick: () => {
        console.log('Manage tags');
        addToRecent('manage-tags');
      }
    },
    {
      id: 'archive-items',
      title: 'Archive',
      description: 'View archived content',
      icon: <Archive className="w-5 h-5" />,
      category: 'manage',
      color: 'bg-slate-500 hover:bg-slate-600',
      onClick: () => {
        console.log('Archive items');
        addToRecent('archive-items');
      }
    },
    
    // Tools
    {
      id: 'export-data',
      title: 'Export Data',
      description: 'Download your content',
      icon: <Download className="w-5 h-5" />,
      category: 'tools',
      color: 'bg-red-500 hover:bg-red-600',
      onClick: () => {
        console.log('Export data');
        addToRecent('export-data');
      }
    },
    {
      id: 'share-content',
      title: 'Share',
      description: 'Share notebooks or sources',
      icon: <Share2 className="w-5 h-5" />,
      category: 'tools',
      color: 'bg-blue-600 hover:bg-blue-700',
      onClick: () => {
        console.log('Share content');
        addToRecent('share-content');
      }
    },
    {
      id: 'generate-link',
      title: 'Generate Link',
      description: 'Create shareable links',
      icon: <Link className="w-5 h-5" />,
      category: 'tools',
      color: 'bg-violet-500 hover:bg-violet-600',
      onClick: () => {
        console.log('Generate link');
        addToRecent('generate-link');
      }
    },
    {
      id: 'backup-data',
      title: 'Backup',
      description: 'Create data backup',
      icon: <Database className="w-5 h-5" />,
      category: 'tools',
      color: 'bg-amber-500 hover:bg-amber-600',
      onClick: () => {
        console.log('Backup data');
        addToRecent('backup-data');
      }
    },
    {
      id: 'analytics',
      title: 'Analytics',
      description: 'View usage statistics',
      icon: <BarChart3 className="w-5 h-5" />,
      category: 'tools',
      color: 'bg-green-600 hover:bg-green-700',
      onClick: () => {
        console.log('View analytics');
        addToRecent('analytics');
      }
    },
    {
      id: 'integrations',
      title: 'Integrations',
      description: 'Connect external services',
      icon: <Zap className="w-5 h-5" />,
      category: 'tools',
      color: 'bg-purple-600 hover:bg-purple-700',
      onClick: () => {
        console.log('Manage integrations');
        addToRecent('integrations');
      }
    },
    
    // Shortcuts
    {
      id: 'keyboard-shortcuts',
      title: 'Shortcuts',
      description: 'View keyboard shortcuts',
      icon: <Keyboard className="w-5 h-5" />,
      category: 'shortcuts',
      shortcut: '⌘?',
      color: 'bg-gray-600 hover:bg-gray-700',
      onClick: () => {
        console.log('Show shortcuts');
        addToRecent('keyboard-shortcuts');
      }
    },
    {
      id: 'settings',
      title: 'Settings',
      description: 'Configure preferences',
      icon: <Settings className="w-5 h-5" />,
      category: 'shortcuts',
      shortcut: '⌘,',
      color: 'bg-stone-500 hover:bg-stone-600',
      onClick: () => {
        console.log('Open settings');
        addToRecent('settings');
      }
    },
    {
      id: 'theme-toggle',
      title: 'Toggle Theme',
      description: 'Switch between light/dark',
      icon: <Palette className="w-5 h-5" />,
      category: 'shortcuts',
      color: 'bg-rose-500 hover:bg-rose-600',
      onClick: () => {
        console.log('Toggle theme');
        addToRecent('theme-toggle');
      }
    },
    {
      id: 'help-support',
      title: 'Help & Support',
      description: 'Get help and documentation',
      icon: <HelpCircle className="w-5 h-5" />,
      category: 'shortcuts',
      color: 'bg-sky-500 hover:bg-sky-600',
      onClick: () => {
        console.log('Help & support');
        addToRecent('help-support');
      }
    }
  ];

  const addToRecent = (actionId: string) => {
    setRecentActions(prev => {
      const filtered = prev.filter(id => id !== actionId);
      return [actionId, ...filtered].slice(0, 6);
    });
  };

  const categories = [
    { id: 'all', label: 'All Actions', icon: <Grid3X3 className="w-4 h-4" /> },
    { id: 'create', label: 'Create', icon: <Plus className="w-4 h-4" /> },
    { id: 'manage', label: 'Manage', icon: <Folder className="w-4 h-4" /> },
    { id: 'tools', label: 'Tools', icon: <Zap className="w-4 h-4" /> },
    { id: 'shortcuts', label: 'Shortcuts', icon: <Keyboard className="w-4 h-4" /> },
    { id: 'recent', label: 'Recent', icon: <Clock className="w-4 h-4" /> }
  ];

  const filteredActions = quickActions.filter(action => {
    const matchesSearch = action.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         action.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    if (activeCategory === 'all') return matchesSearch;
    if (activeCategory === 'recent') {
      return matchesSearch && recentActions.includes(action.id);
    }
    return matchesSearch && action.category === activeCategory;
  });

  const getRecentActions = () => {
    return recentActions.map(id => quickActions.find(action => action.id === id)).filter(Boolean) as QuickAction[];
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3 sm:pb-6">
        <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
          <Zap className="w-4 h-4 sm:w-5 sm:h-5" />
          <span className="hidden sm:inline">Quick Actions</span>
          <span className="sm:hidden">Actions</span>
        </CardTitle>
        <CardDescription className="text-sm">
          <span className="hidden sm:inline">Fast access to common tasks and shortcuts</span>
          <span className="sm:hidden">Common tasks & shortcuts</span>
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-2 sm:space-y-4 p-4 sm:p-6">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-2 sm:left-3 top-1/2 transform -translate-y-1/2 w-3 h-3 sm:w-4 sm:h-4 text-muted-foreground" />
          <Input
            placeholder="Search actions..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8 sm:pl-10 text-xs sm:text-sm h-8 sm:h-10"
          />
        </div>

        {/* Category Tabs */}
        <div className="flex flex-wrap gap-1 sm:gap-2">
          {categories.map((category) => {
            const isActive = activeCategory === category.id;
            const count = category.id === 'all' 
              ? quickActions.length 
              : category.id === 'recent'
                ? recentActions.length
                : quickActions.filter(a => a.category === category.id).length;
            
            return (
              <Button
                key={category.id}
                variant={isActive ? 'default' : 'outline'}
                size="sm"
                onClick={() => setActiveCategory(category.id)}
                className="h-6 sm:h-8 text-xs px-1.5 sm:px-3 flex items-center gap-1"
              >
                <span className="w-3 h-3 sm:w-4 sm:h-4">{category.icon}</span>
                <span className="hidden sm:inline text-xs sm:text-sm">{category.label}</span>
                <Badge variant="secondary" className="h-3 sm:h-4 text-xs px-1 ml-0.5 sm:ml-1">
                  {count}
                </Badge>
              </Button>
            );
          })}
        </div>

        <Separator />

        {/* Recent Actions (when not in recent tab) */}
        {activeCategory !== 'recent' && recentActions.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-xs sm:text-sm font-semibold text-muted-foreground flex items-center gap-2">
              <Clock className="w-3 h-3 sm:w-4 sm:h-4" />
              Recently Used
            </h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              {getRecentActions().slice(0, 4).map((action) => (
                <Button
                  key={action.id}
                  variant="outline"
                  size="sm"
                  onClick={action.onClick}
                  className="h-auto p-2 sm:p-3 justify-start"
                >
                  <div className="flex items-center gap-2 w-full">
                    <div className={`p-1 sm:p-1.5 rounded text-white ${action.color} flex-shrink-0`}>
                      <span className="w-3 h-3 sm:w-4 sm:h-4 block">{action.icon}</span>
                    </div>
                    <div className="text-left flex-1 min-w-0">
                      <div className="font-medium text-xs sm:text-sm truncate">{action.title}</div>
                      {action.shortcut && (
                        <div className="text-xs text-muted-foreground hidden sm:block">{action.shortcut}</div>
                      )}
                    </div>
                  </div>
                </Button>
              ))}
            </div>
            <Separator />
          </div>
        )}

        {/* Actions Grid */}
        <div className="space-y-2">
          {activeCategory !== 'recent' && (
            <h4 className="text-xs sm:text-sm font-semibold text-muted-foreground">
              {categories.find(c => c.id === activeCategory)?.label || 'All Actions'}
            </h4>
          )}
          
          <div className="grid grid-cols-1 gap-2">
            {(activeCategory === 'recent' ? getRecentActions() : filteredActions).map((action) => (
              <Button
                key={action.id}
                variant="outline"
                onClick={action.onClick}
                disabled={action.disabled}
                className="h-auto p-2 sm:p-4 justify-start hover:bg-muted/50"
              >
                <div className="flex items-center gap-2 sm:gap-3 w-full">
                  <div className={`p-1 sm:p-2 rounded text-white ${action.color} flex-shrink-0`}>
                    <span className="w-3 h-3 sm:w-4 sm:h-4 block">{action.icon}</span>
                  </div>
                  <div className="text-left flex-1 min-w-0">
                    <div className="flex items-center gap-1 sm:gap-2 mb-1">
                      <span className="font-medium text-xs sm:text-base">{action.title}</span>
                      {action.badge && (
                        <Badge variant="secondary" className="text-xs hidden sm:inline-flex">
                          {action.badge}
                        </Badge>
                      )}
                      {action.shortcut && (
                        <Badge variant="outline" className="text-xs font-mono hidden md:inline-flex">
                          {action.shortcut}
                        </Badge>
                      )}
                    </div>
                    <p className="text-xs sm:text-sm text-muted-foreground line-clamp-2 hidden sm:block">{action.description}</p>
                  </div>
                  <ChevronRight className="w-3 h-3 sm:w-4 sm:h-4 text-muted-foreground flex-shrink-0" />
                </div>
              </Button>
            ))}
          </div>
        </div>

        {filteredActions.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Search className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p>No actions found matching "{searchQuery}"</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default QuickActionsWidget;