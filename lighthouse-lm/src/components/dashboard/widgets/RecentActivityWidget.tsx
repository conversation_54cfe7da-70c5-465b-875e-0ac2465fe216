import React, { useState, useEffect } from 'react';
import { useAuth } from '@/services/auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Activity, 
  FileText, 
  Upload, 
  MessageSquare, 
  Users, 
  Settings, 
  Download, 
  Share2, 
  Edit3, 
  Trash2, 
  Plus, 
  Eye, 
  Clock, 
  Filter,
  RefreshCw,
  MoreHorizontal
} from 'lucide-react';

interface ActivityItem {
  id: string;
  type: 'notebook' | 'source' | 'chat' | 'collaboration' | 'system';
  action: string;
  description: string;
  user: {
    name: string;
    avatar?: string;
    id: string;
  };
  timestamp: string;
  metadata?: {
    notebookTitle?: string;
    sourceCount?: number;
    collaborators?: string[];
    fileSize?: string;
    duration?: string;
  };
  priority: 'low' | 'medium' | 'high';
  status?: 'completed' | 'in_progress' | 'failed';
}

interface RecentActivityWidgetProps {
  className?: string;
  maxItems?: number;
  onRefresh?: () => void;
}

const RecentActivityWidget: React.FC<RecentActivityWidgetProps> = ({ 
  className, 
  maxItems = 20,
  onRefresh 
}) => {
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const [refreshing, setRefreshing] = useState(false);
  const [filter, setFilter] = useState<string>('all');

  const { currentUser } = useAuth();

  // Fetch activities - in real app, this would come from backend API
  useEffect(() => {
    const fetchActivities = async () => {
      setIsLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Generate sample activities for the current user
      const userActivities: ActivityItem[] = currentUser ? [
        {
          id: '1',
          type: 'notebook',
          action: 'created',
          description: 'Created new research notebook',
          user: { name: currentUser.username, id: currentUser.id },
          timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          metadata: { notebookTitle: 'AI Research Findings' },
          priority: 'medium',
          status: 'completed'
        },
        {
          id: '2',
          type: 'source',
          action: 'uploaded',
          description: 'Uploaded research documents',
          user: { name: currentUser.username, id: currentUser.id },
          timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
          metadata: { sourceCount: 3, fileSize: '8.2 MB' },
          priority: 'high',
          status: 'completed'
        },
        {
          id: '3',
          type: 'chat',
          action: 'started',
          description: 'Started AI conversation about data analysis',
          user: { name: currentUser.username, id: currentUser.id },
          timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          metadata: { duration: '15 minutes' },
          priority: 'low',
          status: 'completed'
        },
        {
          id: '4',
          type: 'system',
          action: 'login',
          description: 'Signed in to Lighthouse LM',
          user: { name: currentUser.username, id: currentUser.id },
          timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
          priority: 'low',
          status: 'completed'
        }
      ] : [];
      
      setActivities(userActivities);
      setIsLoading(false);
    };

    if (currentUser) {
      fetchActivities();
    }
  }, [currentUser]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await new Promise(resolve => setTimeout(resolve, 600));
    onRefresh?.();
    setRefreshing(false);
  };

  const getActivityIcon = (type: string, action: string) => {
    switch (type) {
      case 'notebook':
        return action === 'created' ? <Plus className="w-4 h-4" /> : <Edit3 className="w-4 h-4" />;
      case 'source':
        return action === 'uploaded' ? <Upload className="w-4 h-4" /> : <FileText className="w-4 h-4" />;
      case 'chat':
        return <MessageSquare className="w-4 h-4" />;
      case 'collaboration':
        return action === 'shared' ? <Share2 className="w-4 h-4" /> : <Users className="w-4 h-4" />;
      case 'system':
        return <Settings className="w-4 h-4" />;
      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'notebook': return 'text-blue-500';
      case 'source': return 'text-green-500';
      case 'chat': return 'text-purple-500';
      case 'collaboration': return 'text-orange-500';
      case 'system': return 'text-gray-500';
      default: return 'text-muted-foreground';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'in_progress': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'failed': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const filteredActivities = activities.filter(activity => {
    if (activeTab === 'all') return true;
    return activity.type === activeTab;
  }).slice(0, maxItems);

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5" />
            Recent Activity
          </CardTitle>
          <CardDescription>Loading recent activities...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex items-center gap-3">
                <div className="w-8 h-8 bg-muted/50 rounded-full animate-pulse" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-muted/50 rounded animate-pulse" />
                  <div className="h-3 bg-muted/30 rounded animate-pulse w-2/3" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3 sm:pb-6">
        <div className="flex items-start sm:items-center justify-between flex-col sm:flex-row gap-3 sm:gap-0">
          <div>
            <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
              <Activity className="w-4 h-4 sm:w-5 sm:h-5" />
              <span className="hidden sm:inline">Recent Activity</span>
              <span className="sm:hidden">Activity</span>
            </CardTitle>
            <CardDescription className="text-sm">
              <span className="hidden sm:inline">Latest actions and system events</span>
              <span className="sm:hidden">Latest actions</span>
            </CardDescription>
          </div>
          <div className="flex items-center gap-1 sm:gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
              className="h-7 sm:h-8 px-2 sm:px-3"
            >
              <RefreshCw className={`w-3 h-3 sm:w-4 sm:h-4 ${refreshing ? 'animate-spin' : ''}`} />
              <span className="hidden sm:inline ml-1">Refresh</span>
            </Button>
            <Button variant="outline" size="sm" className="h-7 sm:h-8 px-2 sm:px-3">
              <Filter className="w-3 h-3 sm:w-4 sm:h-4" />
              <span className="hidden sm:inline ml-1">Filter</span>
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-4 sm:p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3 sm:grid-cols-6 h-8 sm:h-10">
            <TabsTrigger value="all" className="text-xs sm:text-sm px-1 sm:px-3">All</TabsTrigger>
            <TabsTrigger value="notebook" className="text-xs sm:text-sm px-1 sm:px-3">
              <span className="hidden sm:inline">Notes</span>
              <span className="sm:hidden">Notes</span>
            </TabsTrigger>
            <TabsTrigger value="source" className="text-xs sm:text-sm px-1 sm:px-3 hidden sm:flex">
              Sources
            </TabsTrigger>
            <TabsTrigger value="chat" className="text-xs sm:text-sm px-1 sm:px-3">
              Chat
            </TabsTrigger>
            <TabsTrigger value="collaboration" className="text-xs sm:text-sm px-1 sm:px-3 hidden sm:flex">
              Collab
            </TabsTrigger>
            <TabsTrigger value="system" className="text-xs sm:text-sm px-1 sm:px-3 hidden sm:flex">
              System
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value={activeTab} className="mt-2 sm:mt-4">
            <ScrollArea className="h-80 sm:h-96">
              <div className="space-y-2 sm:space-y-3">
                {filteredActivities.length === 0 ? (
                  <div className="text-center py-6 sm:py-8 text-muted-foreground">
                    <Activity className="w-8 h-8 sm:w-12 sm:h-12 mx-auto mb-3 sm:mb-4 opacity-50" />
                    <p className="text-sm sm:text-base">No recent activities found</p>
                  </div>
                ) : (
                  filteredActivities.map((activity) => (
                    <div key={activity.id} className="flex items-start gap-2 sm:gap-3 p-2 sm:p-3 bg-muted/30 rounded-lg hover:bg-muted/50 transition-colors">
                      <div className={`p-1.5 sm:p-2 rounded-full bg-background ${getActivityColor(activity.type)}`}>
                        {getActivityIcon(activity.type, activity.action)}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start sm:items-center justify-between mb-1 flex-col sm:flex-row gap-1 sm:gap-0">
                          <div className="flex items-center gap-1 sm:gap-2 flex-wrap">
                            <span className="font-medium text-xs sm:text-sm">{activity.user.name}</span>
                            <span className="text-muted-foreground text-xs sm:text-sm">{activity.action}</span>
                            {activity.priority !== 'low' && (
                              <Badge className={`text-xs h-4 sm:h-5 px-1 sm:px-2 ${getPriorityColor(activity.priority)}`}>
                                {activity.priority}
                              </Badge>
                            )}
                          </div>
                          <div className="flex items-center gap-1 sm:gap-2 flex-wrap">
                            {activity.status && (
                              <Badge className={`text-xs h-4 sm:h-5 px-1 sm:px-2 ${getStatusColor(activity.status)}`}>
                                {activity.status}
                              </Badge>
                            )}
                            <span className="text-xs text-muted-foreground flex items-center gap-1">
                              <Clock className="w-2.5 h-2.5 sm:w-3 sm:h-3" />
                              {formatTimeAgo(activity.timestamp)}
                            </span>
                          </div>
                        </div>
                        
                        <p className="text-xs sm:text-sm text-muted-foreground mb-1 sm:mb-2 line-clamp-2">
                          {activity.description}
                        </p>
                        
                        {activity.metadata && (
                          <div className="flex flex-wrap gap-1 sm:gap-2 text-xs">
                            {activity.metadata.notebookTitle && (
                              <Badge variant="outline" className="text-xs h-4 sm:h-5 px-1 sm:px-2">
                                <FileText className="w-2 h-2 sm:w-3 sm:h-3 mr-0.5 sm:mr-1" />
                                <span className="hidden sm:inline">{activity.metadata.notebookTitle}</span>
                                <span className="sm:hidden truncate max-w-[60px]">{activity.metadata.notebookTitle}</span>
                              </Badge>
                            )}
                            {activity.metadata.sourceCount && (
                              <Badge variant="outline" className="text-xs h-4 sm:h-5 px-1 sm:px-2">
                                <span className="hidden sm:inline">{activity.metadata.sourceCount} files</span>
                                <span className="sm:hidden">{activity.metadata.sourceCount}</span>
                              </Badge>
                            )}
                            {activity.metadata.fileSize && (
                              <Badge variant="outline" className="text-xs h-4 sm:h-5 px-1 sm:px-2">
                                {activity.metadata.fileSize}
                              </Badge>
                            )}
                            {activity.metadata.duration && (
                              <Badge variant="outline" className="text-xs h-4 sm:h-5 px-1 sm:px-2">
                                {activity.metadata.duration}
                              </Badge>
                            )}
                            {activity.metadata.collaborators && (
                              <Badge variant="outline" className="text-xs h-4 sm:h-5 px-1 sm:px-2">
                                <Users className="w-2 h-2 sm:w-3 sm:h-3 mr-0.5 sm:mr-1" />
                                <span className="hidden sm:inline">{activity.metadata.collaborators.length} collaborators</span>
                                <span className="sm:hidden">{activity.metadata.collaborators.length}</span>
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                      
                      <Button variant="ghost" size="sm" className="h-6 w-6 sm:h-8 sm:w-8 p-0 flex-shrink-0">
                        <MoreHorizontal className="w-3 h-3 sm:w-4 sm:h-4" />
                      </Button>
                    </div>
                  ))
                )}
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default RecentActivityWidget;