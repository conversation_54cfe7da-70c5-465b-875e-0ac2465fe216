import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  FileText, 
  Clock, 
  Target,
  Zap,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  MoreHorizontal,
  RefreshCw
} from 'lucide-react';

interface AnalyticsData {
  totalNotebooks: number;
  totalSources: number;
  activeUsers: number;
  avgSessionTime: string;
  completionRate: number;
  weeklyGrowth: number;
  monthlyGrowth: number;
  topCategories: Array<{
    name: string;
    count: number;
    percentage: number;
  }>;
  recentMetrics: Array<{
    date: string;
    notebooks: number;
    sources: number;
    users: number;
  }>;
  productivityScore: number;
  goals: Array<{
    title: string;
    current: number;
    target: number;
    deadline: string;
  }>;
}

interface AnalyticsWidgetProps {
  className?: string;
  onRefresh?: () => void;
}

const AnalyticsWidget: React.FC<AnalyticsWidgetProps> = ({ className, onRefresh }) => {
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [refreshing, setRefreshing] = useState(false);

  // Mock data - in real app, this would come from an API
  useEffect(() => {
    const fetchAnalytics = async () => {
      setIsLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setData({
        totalNotebooks: 127,
        totalSources: 1543,
        activeUsers: 23,
        avgSessionTime: '2h 34m',
        completionRate: 78,
        weeklyGrowth: 12.5,
        monthlyGrowth: 34.2,
        topCategories: [
          { name: 'Research', count: 45, percentage: 35 },
          { name: 'Documentation', count: 38, percentage: 30 },
          { name: 'Analysis', count: 25, percentage: 20 },
          { name: 'Planning', count: 19, percentage: 15 }
        ],
        recentMetrics: [
          { date: '2024-01-15', notebooks: 12, sources: 89, users: 8 },
          { date: '2024-01-14', notebooks: 8, sources: 67, users: 12 },
          { date: '2024-01-13', notebooks: 15, sources: 102, users: 9 },
          { date: '2024-01-12', notebooks: 6, sources: 45, users: 7 },
          { date: '2024-01-11', notebooks: 11, sources: 78, users: 10 }
        ],
        productivityScore: 85,
        goals: [
          { title: 'Monthly Notebooks', current: 89, target: 100, deadline: '2024-01-31' },
          { title: 'Source Integration', current: 1543, target: 2000, deadline: '2024-02-15' },
          { title: 'User Engagement', current: 78, target: 85, deadline: '2024-01-25' }
        ]
      });
      setIsLoading(false);
    };

    fetchAnalytics();
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    await new Promise(resolve => setTimeout(resolve, 800));
    onRefresh?.();
    setRefreshing(false);
  };

  if (isLoading || !data) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                Analytics Dashboard
              </CardTitle>
              <CardDescription>Loading analytics data...</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-16 bg-muted/50 rounded-lg animate-pulse" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3 sm:pb-6">
        <div className="flex items-start sm:items-center justify-between flex-col sm:flex-row gap-3 sm:gap-0">
          <div>
            <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
              <BarChart3 className="w-4 h-4 sm:w-5 sm:h-5" />
              <span className="hidden sm:inline">Analytics Dashboard</span>
              <span className="sm:hidden">Analytics</span>
            </CardTitle>
            <CardDescription className="text-sm">Real-time insights and metrics</CardDescription>
          </div>
          <div className="flex items-center gap-1 sm:gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
              className="h-7 sm:h-8 px-2 sm:px-3"
            >
              <RefreshCw className={`w-3 h-3 sm:w-4 sm:h-4 ${refreshing ? 'animate-spin' : ''}`} />
              <span className="ml-1 hidden sm:inline text-xs">Refresh</span>
            </Button>
            <Button variant="outline" size="sm" className="h-7 sm:h-8 px-2 sm:px-3">
              <MoreHorizontal className="w-3 h-3 sm:w-4 sm:h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3 h-8 sm:h-10">
            <TabsTrigger value="overview" className="text-xs sm:text-sm px-2 sm:px-3">Overview</TabsTrigger>
            <TabsTrigger value="trends" className="text-xs sm:text-sm px-2 sm:px-3">Trends</TabsTrigger>
            <TabsTrigger value="goals" className="text-xs sm:text-sm px-2 sm:px-3">Goals</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-3 sm:space-y-4 mt-3 sm:mt-4">
            {/* Key Metrics */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-4">
              <div className="p-3 sm:p-4 bg-muted/50 rounded-lg">
                <div className="flex items-center justify-between">
                  <FileText className="w-4 h-4 sm:w-5 sm:h-5 text-blue-500" />
                  <Badge variant="secondary" className="text-xs px-1 sm:px-2 hidden sm:inline-flex">
                    {data.weeklyGrowth > 0 ? '+' : ''}{data.weeklyGrowth}%
                  </Badge>
                </div>
                <div className="mt-2">
                  <div className="text-xl sm:text-2xl font-bold">{data.totalNotebooks}</div>
                  <div className="text-xs sm:text-sm text-muted-foreground">Notebooks</div>
                </div>
              </div>
              
              <div className="p-3 sm:p-4 bg-muted/50 rounded-lg">
                <div className="flex items-center justify-between">
                  <Target className="w-4 h-4 sm:w-5 sm:h-5 text-green-500" />
                  <Badge variant="secondary" className="text-xs px-1 sm:px-2 hidden sm:inline-flex">
                    {data.monthlyGrowth > 0 ? '+' : ''}{data.monthlyGrowth}%
                  </Badge>
                </div>
                <div className="mt-2">
                  <div className="text-xl sm:text-2xl font-bold">{data.totalSources}</div>
                  <div className="text-xs sm:text-sm text-muted-foreground">Sources</div>
                </div>
              </div>
              
              <div className="p-3 sm:p-4 bg-muted/50 rounded-lg">
                <div className="flex items-center justify-between">
                  <Users className="w-4 h-4 sm:w-5 sm:h-5 text-purple-500" />
                  <Badge variant="secondary" className="text-xs px-1 sm:px-2 hidden sm:inline-flex">Live</Badge>
                </div>
                <div className="mt-2">
                  <div className="text-xl sm:text-2xl font-bold">{data.activeUsers}</div>
                  <div className="text-xs sm:text-sm text-muted-foreground">Active Users</div>
                </div>
              </div>
              
              <div className="p-3 sm:p-4 bg-muted/50 rounded-lg">
                <div className="flex items-center justify-between">
                  <Clock className="w-4 h-4 sm:w-5 sm:h-5 text-orange-500" />
                  <Badge variant="secondary" className="text-xs px-1 sm:px-2 hidden sm:inline-flex">Avg</Badge>
                </div>
                <div className="mt-2">
                  <div className="text-xl sm:text-2xl font-bold">{data.avgSessionTime}</div>
                  <div className="text-xs sm:text-sm text-muted-foreground">Session Time</div>
                </div>
              </div>
            </div>

            {/* Productivity Score */}
            <div className="p-3 sm:p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <Zap className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-500" />
                  <span className="font-semibold text-sm sm:text-base">Productivity Score</span>
                </div>
                <span className="text-xl sm:text-2xl font-bold text-primary">{data.productivityScore}%</span>
              </div>
              <Progress value={data.productivityScore} className="h-2" />
              <div className="text-xs sm:text-sm text-muted-foreground mt-2">
                Based on completion rate, engagement, and efficiency metrics
              </div>
            </div>

            {/* Top Categories */}
            <div className="space-y-3 sm:space-y-4">
              <h4 className="font-semibold flex items-center gap-2 text-sm sm:text-base">
                <BarChart3 className="w-3 h-3 sm:w-4 sm:h-4" />
                Top Categories
              </h4>
              {data.topCategories.map((category, index) => (
                <div key={category.name} className="flex items-center justify-between p-2 sm:p-3 bg-muted/30 rounded-lg">
                  <div className="flex items-center gap-2 sm:gap-3">
                    <div className="w-6 h-6 sm:w-8 sm:h-8 bg-primary/10 rounded-full flex items-center justify-center text-xs sm:text-sm font-semibold">
                      {index + 1}
                    </div>
                    <span className="font-medium text-sm sm:text-base">{category.name}</span>
                  </div>
                  <div className="flex items-center gap-2 sm:gap-3">
                    <div className="text-right">
                      <div className="font-semibold text-sm sm:text-base">{category.count}</div>
                      <div className="text-xs text-muted-foreground">{category.percentage}%</div>
                    </div>
                    <div className="w-12 sm:w-16">
                      <Progress value={category.percentage} className="h-1" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="trends" className="space-y-3 sm:space-y-4 mt-3 sm:mt-4">
            <div className="space-y-3 sm:space-y-4">
              <h4 className="font-semibold flex items-center gap-2 text-sm sm:text-base">
                <TrendingUp className="w-3 h-3 sm:w-4 sm:h-4" />
                Recent Activity
              </h4>
              {data.recentMetrics.map((metric, index) => (
                <div key={metric.date} className="flex items-center justify-between p-2 sm:p-3 bg-muted/30 rounded-lg">
                  <div className="flex items-center gap-2 sm:gap-3">
                    <Calendar className="w-3 h-3 sm:w-4 sm:h-4 text-muted-foreground" />
                    <span className="font-medium text-xs sm:text-sm">{new Date(metric.date).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center gap-2 sm:gap-4 text-xs sm:text-sm">
                    <div className="flex items-center gap-1">
                      <FileText className="w-3 h-3" />
                      <span className="hidden sm:inline">Notebooks:</span>
                      {metric.notebooks}
                    </div>
                    <div className="flex items-center gap-1 hidden sm:flex">
                      <Target className="w-3 h-3" />
                      <span>Sources:</span>
                      {metric.sources}
                    </div>
                    <div className="flex items-center gap-1 hidden sm:flex">
                      <Users className="w-3 h-3" />
                      <span>Users:</span>
                      {metric.users}
                    </div>
                    {index === 0 && (
                      <Badge variant="secondary" className="text-xs px-1 sm:px-2">
                        Today
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
              <div className="p-3 sm:p-4 bg-green-50 dark:bg-green-950/20 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <ArrowUpRight className="w-3 h-3 sm:w-4 sm:h-4 text-green-600" />
                  <span className="font-semibold text-green-700 dark:text-green-400 text-sm sm:text-base">Weekly Growth</span>
                </div>
                <div className="text-xl sm:text-2xl font-bold text-green-600">{data.weeklyGrowth}%</div>
                <div className="text-xs sm:text-sm text-green-600/70">vs last week</div>
              </div>
              
              <div className="p-3 sm:p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <ArrowUpRight className="w-3 h-3 sm:w-4 sm:h-4 text-blue-600" />
                  <span className="font-semibold text-blue-700 dark:text-blue-400 text-sm sm:text-base">Monthly Growth</span>
                </div>
                <div className="text-xl sm:text-2xl font-bold text-blue-600">{data.monthlyGrowth}%</div>
                <div className="text-xs sm:text-sm text-blue-600/70">vs last month</div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="goals" className="space-y-3 sm:space-y-4 mt-3 sm:mt-4">
            <div className="space-y-3 sm:space-y-4">
              <h4 className="font-semibold flex items-center gap-2 text-sm sm:text-base">
                <Target className="w-3 h-3 sm:w-4 sm:h-4" />
                Current Goals
              </h4>
              {data.goals.map((goal, index) => {
                const progress = (goal.current / goal.target) * 100;
                const isOnTrack = progress >= 70;
                
                return (
                  <div key={index} className="p-3 sm:p-4 bg-muted/30 rounded-lg">
                    <div className="flex items-start sm:items-center justify-between mb-3 flex-col sm:flex-row gap-2 sm:gap-0">
                      <div>
                        <div className="font-semibold text-sm sm:text-base">{goal.title}</div>
                        <div className="text-xs sm:text-sm text-muted-foreground">
                          Due: {new Date(goal.deadline).toLocaleDateString()}
                        </div>
                      </div>
                      <Badge variant={isOnTrack ? "default" : "secondary"} className="text-xs px-1 sm:px-2 self-start sm:self-auto">
                        {Math.round(progress)}%
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      <Progress value={progress} className="h-2" />
                      <div className="flex justify-between text-xs sm:text-sm text-muted-foreground">
                        <span>{goal.current} / {goal.target}</span>
                        <span>{goal.target - goal.current} remaining</span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default AnalyticsWidget;