import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Zap, 
  Target, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  Calendar, 
  Award, 
  Flame, 
  BarChart3, 
  Timer, 
  Focus, 
  Coffee, 
  Moon, 
  Sun, 
  RefreshCw,
  Settings,
  Trophy,
  Star,
  ArrowUp,
  ArrowDown
} from 'lucide-react';

interface ProductivityMetrics {
  dailyScore: number;
  weeklyScore: number;
  monthlyScore: number;
  streak: number;
  totalTasks: number;
  completedTasks: number;
  focusTime: string;
  peakHours: string[];
  efficiency: number;
  goals: Array<{
    id: string;
    title: string;
    progress: number;
    target: number;
    deadline: string;
    category: 'daily' | 'weekly' | 'monthly';
    priority: 'low' | 'medium' | 'high';
  }>;
  achievements: Array<{
    id: string;
    title: string;
    description: string;
    icon: string;
    unlockedAt: string;
    rarity: 'common' | 'rare' | 'epic' | 'legendary';
  }>;
  timeDistribution: Array<{
    activity: string;
    time: number;
    percentage: number;
    color: string;
  }>;
  weeklyTrend: Array<{
    day: string;
    score: number;
    tasks: number;
    focusTime: number;
  }>;
}

interface ProductivityWidgetProps {
  className?: string;
  onRefresh?: () => void;
}

const ProductivityWidget: React.FC<ProductivityWidgetProps> = ({ className, onRefresh }) => {
  const [metrics, setMetrics] = useState<ProductivityMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [refreshing, setRefreshing] = useState(false);

  // Mock data - in real app, this would come from an API
  useEffect(() => {
    const fetchMetrics = async () => {
      setIsLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 900));
      
      setMetrics({
        dailyScore: 87,
        weeklyScore: 82,
        monthlyScore: 79,
        streak: 12,
        totalTasks: 45,
        completedTasks: 38,
        focusTime: '6h 23m',
        peakHours: ['9:00 AM', '2:00 PM', '7:00 PM'],
        efficiency: 84,
        goals: [
          {
            id: '1',
            title: 'Complete 50 tasks this week',
            progress: 38,
            target: 50,
            deadline: '2024-01-21',
            category: 'weekly',
            priority: 'high'
          },
          {
            id: '2',
            title: 'Maintain 8h focus time daily',
            progress: 6.4,
            target: 8,
            deadline: '2024-01-15',
            category: 'daily',
            priority: 'medium'
          },
          {
            id: '3',
            title: 'Achieve 90% efficiency rate',
            progress: 84,
            target: 90,
            deadline: '2024-01-31',
            category: 'monthly',
            priority: 'high'
          }
        ],
        achievements: [
          {
            id: '1',
            title: 'Streak Master',
            description: 'Maintained productivity for 10+ days',
            icon: 'flame',
            unlockedAt: '2024-01-10',
            rarity: 'rare'
          },
          {
            id: '2',
            title: 'Focus Champion',
            description: 'Achieved 8+ hours of focus time',
            icon: 'target',
            unlockedAt: '2024-01-08',
            rarity: 'epic'
          },
          {
            id: '3',
            title: 'Task Crusher',
            description: 'Completed 100+ tasks',
            icon: 'trophy',
            unlockedAt: '2024-01-05',
            rarity: 'legendary'
          }
        ],
        timeDistribution: [
          { activity: 'Deep Work', time: 4.2, percentage: 35, color: 'bg-blue-500' },
          { activity: 'Research', time: 2.8, percentage: 23, color: 'bg-green-500' },
          { activity: 'Writing', time: 2.1, percentage: 18, color: 'bg-purple-500' },
          { activity: 'Meetings', time: 1.5, percentage: 12, color: 'bg-orange-500' },
          { activity: 'Planning', time: 1.4, percentage: 12, color: 'bg-pink-500' }
        ],
        weeklyTrend: [
          { day: 'Mon', score: 85, tasks: 8, focusTime: 7.2 },
          { day: 'Tue', score: 78, tasks: 6, focusTime: 6.8 },
          { day: 'Wed', score: 92, tasks: 10, focusTime: 8.1 },
          { day: 'Thu', score: 88, tasks: 9, focusTime: 7.5 },
          { day: 'Fri', score: 75, tasks: 5, focusTime: 5.9 },
          { day: 'Sat', score: 82, tasks: 7, focusTime: 6.3 },
          { day: 'Sun', score: 87, tasks: 8, focusTime: 7.1 }
        ]
      });
      setIsLoading(false);
    };

    fetchMetrics();
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    await new Promise(resolve => setTimeout(resolve, 700));
    onRefresh?.();
    setRefreshing(false);
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-blue-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBg = (score: number) => {
    if (score >= 90) return 'bg-green-50 dark:bg-green-950/20';
    if (score >= 80) return 'bg-blue-50 dark:bg-blue-950/20';
    if (score >= 70) return 'bg-yellow-50 dark:bg-yellow-950/20';
    return 'bg-red-50 dark:bg-red-950/20';
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return 'text-yellow-500 bg-yellow-50 dark:bg-yellow-950/20';
      case 'epic': return 'text-purple-500 bg-purple-50 dark:bg-purple-950/20';
      case 'rare': return 'text-blue-500 bg-blue-50 dark:bg-blue-950/20';
      default: return 'text-gray-500 bg-gray-50 dark:bg-gray-950/20';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-red-200 bg-red-50 dark:bg-red-950/20';
      case 'medium': return 'border-yellow-200 bg-yellow-50 dark:bg-yellow-950/20';
      default: return 'border-green-200 bg-green-50 dark:bg-green-950/20';
    }
  };

  if (isLoading || !metrics) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="w-5 h-5" />
            Productivity Metrics
          </CardTitle>
          <CardDescription>Loading productivity data...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-20 bg-muted/50 rounded-lg animate-pulse" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-0">
          <div>
            <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
              <Zap className="w-4 h-4 sm:w-5 sm:h-5" />
              <span className="hidden sm:inline">Productivity Metrics</span>
              <span className="sm:hidden">Productivity</span>
            </CardTitle>
            <CardDescription className="text-sm sm:text-base">
              <span className="hidden sm:inline">Track your performance and achieve your goals</span>
              <span className="sm:hidden">Track performance & goals</span>
            </CardDescription>
          </div>
          <div className="flex items-center gap-2 self-start sm:self-auto">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
              className="h-7 sm:h-8 px-2 sm:px-3"
            >
              <RefreshCw className={`w-3 h-3 sm:w-4 sm:h-4 ${refreshing ? 'animate-spin' : ''}`} />
              <span className="hidden sm:inline ml-1">Refresh</span>
            </Button>
            <Button variant="outline" size="sm" className="h-7 sm:h-8 px-2 sm:px-3">
              <Settings className="w-3 h-3 sm:w-4 sm:h-4" />
              <span className="hidden sm:inline ml-1">Settings</span>
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-4 sm:p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4 h-8 sm:h-10">
            <TabsTrigger value="overview" className="text-xs sm:text-sm px-1 sm:px-3">Overview</TabsTrigger>
            <TabsTrigger value="goals" className="text-xs sm:text-sm px-1 sm:px-3">Goals</TabsTrigger>
            <TabsTrigger value="achievements" className="text-xs sm:text-sm px-1 sm:px-3">
              <span className="hidden sm:inline">Awards</span>
              <span className="sm:hidden">Awards</span>
            </TabsTrigger>
            <TabsTrigger value="insights" className="text-xs sm:text-sm px-1 sm:px-3">Insights</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-3 sm:space-y-4 mt-3 sm:mt-4">
            {/* Score Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4">
              <div className={`p-3 sm:p-4 rounded-lg ${getScoreBg(metrics.dailyScore)}`}>
                <div className="flex items-center justify-between mb-2">
                  <Sun className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-500" />
                  <Badge className={`${getScoreColor(metrics.dailyScore)} text-xs sm:text-sm hidden sm:inline-flex`}>
                    {metrics.dailyScore >= 85 ? 'Excellent' : metrics.dailyScore >= 70 ? 'Good' : 'Needs Work'}
                  </Badge>
                </div>
                <div className="text-xl sm:text-2xl font-bold mb-1">{metrics.dailyScore}%</div>
                <div className="text-xs sm:text-sm text-muted-foreground">Daily Score</div>
              </div>
              
              <div className={`p-3 sm:p-4 rounded-lg ${getScoreBg(metrics.weeklyScore)}`}>
                <div className="flex items-center justify-between mb-2">
                  <Calendar className="w-4 h-4 sm:w-5 sm:h-5 text-blue-500" />
                  <Badge className={`${getScoreColor(metrics.weeklyScore)} text-xs sm:text-sm hidden sm:inline-flex`}>
                    {metrics.weeklyScore >= 85 ? 'Excellent' : metrics.weeklyScore >= 70 ? 'Good' : 'Needs Work'}
                  </Badge>
                </div>
                <div className="text-xl sm:text-2xl font-bold mb-1">{metrics.weeklyScore}%</div>
                <div className="text-xs sm:text-sm text-muted-foreground">Weekly Score</div>
              </div>
              
              <div className={`p-3 sm:p-4 rounded-lg ${getScoreBg(metrics.monthlyScore)}`}>
                <div className="flex items-center justify-between mb-2">
                  <BarChart3 className="w-4 h-4 sm:w-5 sm:h-5 text-purple-500" />
                  <Badge className={`${getScoreColor(metrics.monthlyScore)} text-xs sm:text-sm hidden sm:inline-flex`}>
                    {metrics.monthlyScore >= 85 ? 'Excellent' : metrics.monthlyScore >= 70 ? 'Good' : 'Needs Work'}
                  </Badge>
                </div>
                <div className="text-xl sm:text-2xl font-bold mb-1">{metrics.monthlyScore}%</div>
                <div className="text-xs sm:text-sm text-muted-foreground">Monthly Score</div>
              </div>
            </div>

            {/* Key Metrics */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
              <div className="p-3 sm:p-4 bg-muted/30 rounded-lg">
                <div className="flex items-center justify-between mb-2 sm:mb-3">
                  <div className="flex items-center gap-2">
                    <Flame className="w-4 h-4 sm:w-5 sm:h-5 text-orange-500" />
                    <span className="font-semibold text-sm sm:text-base">
                      <span className="hidden sm:inline">Current Streak</span>
                      <span className="sm:hidden">Streak</span>
                    </span>
                  </div>
                  <Badge variant="secondary" className="text-xs sm:text-sm">{metrics.streak} days</Badge>
                </div>
                <div className="text-xl sm:text-2xl font-bold text-orange-600">{metrics.streak}</div>
                <div className="text-xs sm:text-sm text-muted-foreground">
                  <span className="hidden sm:inline">Consecutive productive days</span>
                  <span className="sm:hidden">Productive days</span>
                </div>
              </div>
              
              <div className="p-3 sm:p-4 bg-muted/30 rounded-lg">
                <div className="flex items-center justify-between mb-2 sm:mb-3">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-500" />
                    <span className="font-semibold text-sm sm:text-base">
                      <span className="hidden sm:inline">Task Completion</span>
                      <span className="sm:hidden">Tasks</span>
                    </span>
                  </div>
                  <Badge variant="secondary" className="text-xs sm:text-sm">
                    {Math.round((metrics.completedTasks / metrics.totalTasks) * 100)}%
                  </Badge>
                </div>
                <div className="text-xl sm:text-2xl font-bold text-green-600">
                  {metrics.completedTasks}/{metrics.totalTasks}
                </div>
                <div className="text-xs sm:text-sm text-muted-foreground">
                  <span className="hidden sm:inline">Tasks completed today</span>
                  <span className="sm:hidden">Completed today</span>
                </div>
              </div>
            </div>

            {/* Focus Time and Efficiency */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
              <div className="p-3 sm:p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 rounded-lg">
                <div className="flex items-center gap-2 mb-2 sm:mb-3">
                  <Focus className="w-4 h-4 sm:w-5 sm:h-5 text-blue-500" />
                  <span className="font-semibold text-sm sm:text-base">Focus Time</span>
                </div>
                <div className="text-xl sm:text-2xl font-bold text-blue-600 mb-1">{metrics.focusTime}</div>
                <div className="text-xs sm:text-sm text-muted-foreground">
                  <span className="hidden sm:inline">Deep work sessions today</span>
                  <span className="sm:hidden">Deep work today</span>
                </div>
              </div>
              
              <div className="p-3 sm:p-4 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/20 dark:to-blue-950/20 rounded-lg">
                <div className="flex items-center gap-2 mb-2 sm:mb-3">
                  <Target className="w-4 h-4 sm:w-5 sm:h-5 text-green-500" />
                  <span className="font-semibold text-sm sm:text-base">Efficiency</span>
                </div>
                <div className="text-xl sm:text-2xl font-bold text-green-600 mb-1">{metrics.efficiency}%</div>
                <Progress value={metrics.efficiency} className="h-1.5 sm:h-2 mt-2" />
              </div>
            </div>

            {/* Peak Hours */}
            <div className="p-3 sm:p-4 bg-muted/30 rounded-lg">
              <div className="flex items-center gap-2 mb-2 sm:mb-3">
                <Clock className="w-4 h-4 sm:w-5 sm:h-5 text-purple-500" />
                <span className="font-semibold text-sm sm:text-base">
                  <span className="hidden sm:inline">Peak Performance Hours</span>
                  <span className="sm:hidden">Peak Hours</span>
                </span>
              </div>
              <div className="flex flex-wrap gap-1 sm:gap-2">
                {metrics.peakHours.map((hour, index) => (
                  <Badge key={index} variant="outline" className="text-xs sm:text-sm">
                    {hour}
                  </Badge>
                ))}
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="goals" className="space-y-3 sm:space-y-4 mt-3 sm:mt-4">
            <div className="space-y-3 sm:space-y-4">
              {metrics.goals.map((goal) => {
                const progress = (goal.progress / goal.target) * 100;
                const isOverdue = new Date(goal.deadline) < new Date();
                
                return (
                  <div key={goal.id} className={`p-3 sm:p-4 rounded-lg border-2 ${getPriorityColor(goal.priority)}`}>
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-2 sm:mb-3 gap-2 sm:gap-0">
                      <div className="flex-1">
                        <div className="font-semibold text-sm sm:text-base">{goal.title}</div>
                        <div className="text-xs sm:text-sm text-muted-foreground">
                          {goal.category} • Due: {new Date(goal.deadline).toLocaleDateString()}
                        </div>
                      </div>
                      <div className="flex items-center gap-1 sm:gap-2 self-start sm:self-auto">
                        <Badge variant={goal.priority === 'high' ? 'destructive' : 'secondary'} className="text-xs sm:text-sm">
                          {goal.priority}
                        </Badge>
                        <Badge variant={isOverdue ? 'destructive' : 'default'} className="text-xs sm:text-sm">
                          {Math.round(progress)}%
                        </Badge>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Progress value={progress} className="h-2 sm:h-3" />
                      <div className="flex justify-between text-xs sm:text-sm text-muted-foreground">
                        <span>{goal.progress} / {goal.target}</span>
                        <span className="hidden sm:inline">{goal.target - goal.progress} remaining</span>
                        <span className="sm:hidden">{goal.target - goal.progress} left</span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </TabsContent>
          
          <TabsContent value="achievements" className="space-y-3 sm:space-y-4 mt-3 sm:mt-4">
            <div className="grid gap-3 sm:gap-4">
              {metrics.achievements.map((achievement) => (
                <div key={achievement.id} className={`p-3 sm:p-4 rounded-lg ${getRarityColor(achievement.rarity)}`}>
                  <div className="flex items-start sm:items-center gap-3">
                    <div className="p-2 sm:p-3 bg-background rounded-full flex-shrink-0">
                      {achievement.icon === 'flame' && <Flame className="w-4 h-4 sm:w-6 sm:h-6" />}
                      {achievement.icon === 'target' && <Target className="w-4 h-4 sm:w-6 sm:h-6" />}
                      {achievement.icon === 'trophy' && <Trophy className="w-4 h-4 sm:w-6 sm:h-6" />}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 mb-1">
                        <span className="font-semibold text-sm sm:text-base">{achievement.title}</span>
                        <Badge className={`${getRarityColor(achievement.rarity)} text-xs sm:text-sm self-start sm:self-auto`}>
                          {achievement.rarity}
                        </Badge>
                      </div>
                      <p className="text-xs sm:text-sm text-muted-foreground mb-2">
                        {achievement.description}
                      </p>
                      <div className="text-xs text-muted-foreground">
                        <span className="hidden sm:inline">Unlocked: </span>
                        {new Date(achievement.unlockedAt).toLocaleDateString()}
                      </div>
                    </div>
                    <Award className="w-5 h-5 sm:w-6 sm:h-6 text-yellow-500 flex-shrink-0" />
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="insights" className="space-y-3 sm:space-y-4 mt-3 sm:mt-4">
            {/* Time Distribution */}
            <div className="space-y-2 sm:space-y-3">
              <h4 className="font-semibold flex items-center gap-2 text-sm sm:text-base">
                <Timer className="w-3 h-3 sm:w-4 sm:h-4" />
                Time Distribution
              </h4>
              {metrics.timeDistribution.map((item, index) => (
                <div key={index} className="flex flex-col sm:flex-row sm:items-center justify-between p-2 sm:p-3 bg-muted/30 rounded-lg gap-2 sm:gap-3">
                  <div className="flex items-center gap-2 sm:gap-3">
                    <div className={`w-3 h-3 sm:w-4 sm:h-4 rounded ${item.color} flex-shrink-0`} />
                    <span className="font-medium text-sm sm:text-base">{item.activity}</span>
                  </div>
                  <div className="flex items-center gap-2 sm:gap-3 justify-between sm:justify-end">
                    <div className="text-right">
                      <div className="font-semibold text-sm sm:text-base">{item.time}h</div>
                      <div className="text-xs text-muted-foreground">{item.percentage}%</div>
                    </div>
                    <div className="w-16 sm:w-20">
                      <Progress value={item.percentage} className="h-1.5 sm:h-2" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {/* Weekly Trend */}
            <div className="space-y-2 sm:space-y-3">
              <h4 className="font-semibold flex items-center gap-2 text-sm sm:text-base">
                <TrendingUp className="w-3 h-3 sm:w-4 sm:h-4" />
                Weekly Trend
              </h4>
              <div className="grid grid-cols-7 gap-1 sm:gap-2">
                {metrics.weeklyTrend.map((day, index) => {
                  const isToday = index === 6; // Assuming Sunday is today
                  const trend = index > 0 ? day.score - metrics.weeklyTrend[index - 1].score : 0;
                  
                  return (
                    <div key={day.day} className={`p-2 sm:p-3 rounded-lg text-center ${isToday ? 'bg-primary/10 border-2 border-primary/20' : 'bg-muted/30'}`}>
                      <div className="text-xs font-medium mb-1">{day.day}</div>
                      <div className="text-sm sm:text-lg font-bold mb-1">{day.score}%</div>
                      <div className="flex items-center justify-center gap-1 text-xs">
                        {trend > 0 ? (
                          <ArrowUp className="w-2 h-2 sm:w-3 sm:h-3 text-green-500" />
                        ) : trend < 0 ? (
                          <ArrowDown className="w-2 h-2 sm:w-3 sm:h-3 text-red-500" />
                        ) : null}
                        <span className="text-muted-foreground">
                          <span className="hidden sm:inline">{day.tasks}</span>
                          <span className="sm:hidden">{day.tasks}</span>
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default ProductivityWidget;