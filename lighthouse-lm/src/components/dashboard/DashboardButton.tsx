import React from 'react';
import { Button } from '@/components/ui/button';
import { DASHBOARD_STYLES, DASHBOARD_CLASSES } from './constants';

interface DashboardButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  variant?: 'primary' | 'outline';
  size?: 'default' | 'lg';
  className?: string;
  isLoading?: boolean;
  type?: 'button' | 'submit';
  'aria-label'?: string;
}

const DashboardButton: React.FC<DashboardButtonProps> = ({
  children,
  onClick,
  disabled = false,
  variant = 'primary',
  size = 'default',
  className = '',
  isLoading = false,
  type = 'button',
  'aria-label': ariaLabel,
}) => {
  const baseClasses = variant === 'primary'
    ? DASHBOARD_CLASSES.primaryButton
    : 'border border-border bg-background hover:bg-accent hover:text-accent-foreground';

  const sizeClasses = size === 'lg' ? 'px-6 py-3 text-lg' : '';
  const disabledClasses = disabled ? DASHBOARD_CLASSES.disabledButton : '';
  const combinedClasses = `${baseClasses} ${sizeClasses} ${disabledClasses} ${className}`.trim();

  return (
    <Button
      onClick={onClick}
      disabled={disabled || isLoading}
      className={combinedClasses}
      type={type}
      aria-label={ariaLabel}
    >
      {children}
    </Button>
  );
};

export default DashboardButton;