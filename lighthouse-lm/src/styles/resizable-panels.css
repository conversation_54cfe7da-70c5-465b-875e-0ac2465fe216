/* Resizable Panels Custom Styles */

/* Resize Handle Styles */
.resize-handle-horizontal {
  width: 4px;
  background: transparent;
  position: relative;
  transition: background-color 0.2s ease;
  cursor: col-resize;
}

.resize-handle-horizontal:hover {
  background: hsl(var(--primary) / 0.2);
}

.resize-handle-horizontal:active {
  background: hsl(var(--primary) / 0.3);
}

.resize-handle-horizontal::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2px;
  height: 40px;
  background: hsl(var(--border));
  border-radius: 2px;
  transition: all 0.2s ease;
}

.resize-handle-horizontal:hover::before {
  background: hsl(var(--primary) / 0.5);
  height: 60px;
}

.resize-handle-vertical {
  height: 4px;
  background: transparent;
  position: relative;
  transition: background-color 0.2s ease;
  cursor: row-resize;
}

.resize-handle-vertical:hover {
  background: hsl(var(--primary) / 0.2);
}

.resize-handle-vertical:active {
  background: hsl(var(--primary) / 0.3);
}

.resize-handle-vertical::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  height: 2px;
  width: 40px;
  background: hsl(var(--border));
  border-radius: 2px;
  transition: all 0.2s ease;
}

.resize-handle-vertical:hover::before {
  background: hsl(var(--primary) / 0.5);
  width: 60px;
}

/* Panel Transitions */
[data-panel] {
  transition: flex 0.2s ease;
}

/* Prevent text selection during resize */
[data-panel-group]:has([data-resize-handle]:active) {
  user-select: none;
}

/* Add visual feedback for min/max size reached */
[data-panel][data-panel-size="0"] {
  opacity: 0.5;
}

/* Smooth panel collapse/expand animations */
[data-panel-collapsible="true"] {
  transition: flex 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}