/**
 * Text analysis utilities for extracting concepts and relationships from source content
 */

import { ExtractedConcept, ExtractedRelationship, ConceptType, RelationshipType, TextPosition } from '../types/sourceAnalysis';

export class TextAnalysisUtils {
  private static readonly STOP_WORDS = new Set([
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
    'from', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below',
    'between', 'under', 'over', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
    'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
    'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he',
    'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'
  ]);

  private static readonly CONCEPT_PATTERNS = {
    entity: /\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g,
    process: /\b(?:process|procedure|method|approach|technique|strategy)\b/gi,
    decision: /\b(?:if|when|whether|decide|choose|select|determine)\b/gi,
    outcome: /\b(?:result|outcome|output|conclusion|effect|impact)\b/gi,
    person: /\b(?:user|customer|client|manager|developer|analyst|admin|person|individual)\b/gi,
    organization: /\b(?:company|organization|department|team|group|division)\b/gi,
    date: /\b(?:\d{1,2}\/\d{1,2}\/\d{4}|\d{4}-\d{2}-\d{2}|(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4})\b/gi,
    number: /\b\d+(?:\.\d+)?(?:%|percent|dollars?|euros?|pounds?)?\b/gi
  };

  private static readonly RELATIONSHIP_PATTERNS = {
    depends_on: /\b(?:depends on|requires|needs|relies on|based on)\b/gi,
    leads_to: /\b(?:leads to|results in|causes|triggers|produces)\b/gi,
    contains: /\b(?:contains|includes|comprises|consists of|has)\b/gi,
    references: /\b(?:references|refers to|mentions|cites|points to)\b/gi,
    similar_to: /\b(?:similar to|like|resembles|comparable to|analogous to)\b/gi,
    part_of: /\b(?:part of|component of|element of|section of|belongs to)\b/gi
  };

  /**
   * Extract concepts from text content
   */
  static extractConcepts(content: string, sourceId: string): ExtractedConcept[] {
    const concepts: ExtractedConcept[] = [];
    let conceptId = 0;

    // Extract different types of concepts
    for (const [type, pattern] of Object.entries(this.CONCEPT_PATTERNS)) {
      const matches = content.matchAll(pattern);
      
      for (const match of matches) {
        if (match.index !== undefined && match[0]) {
          const text = match[0].trim();
          
          // Skip if it's a stop word or too short
          if (this.STOP_WORDS.has(text.toLowerCase()) || text.length < 2) {
            continue;
          }

          const concept: ExtractedConcept = {
            id: `concept_${sourceId}_${conceptId++}`,
            text,
            type: type as ConceptType,
            position: {
              start: match.index,
              end: match.index + text.length
            },
            confidence: this.calculateConceptConfidence(text, type as ConceptType, content),
            context: this.extractContext(content, match.index, text.length),
            sourceIds: [sourceId]
          };

          concepts.push(concept);
        }
      }
    }

    // Extract noun phrases as general topics
    const nounPhrases = this.extractNounPhrases(content);
    nounPhrases.forEach((phrase, index) => {
      if (phrase.text.length > 3 && !this.STOP_WORDS.has(phrase.text.toLowerCase())) {
        concepts.push({
          id: `topic_${sourceId}_${index}`,
          text: phrase.text,
          type: 'topic',
          position: phrase.position,
          confidence: 0.6,
          context: this.extractContext(content, phrase.position.start, phrase.text.length),
          sourceIds: [sourceId]
        });
      }
    });

    return this.deduplicateConcepts(concepts);
  }

  /**
   * Extract relationships between concepts in text
   */
  static extractRelationships(
    content: string, 
    concepts: ExtractedConcept[], 
    sourceId: string
  ): ExtractedRelationship[] {
    const relationships: ExtractedRelationship[] = [];
    let relationshipId = 0;

    // Look for explicit relationship patterns
    for (const [relType, pattern] of Object.entries(this.RELATIONSHIP_PATTERNS)) {
      const matches = content.matchAll(pattern);
      
      for (const match of matches) {
        if (match.index !== undefined) {
          const relationshipText = match[0];
          const position = match.index;
          
          // Find concepts near this relationship indicator
          const nearConcepts = this.findNearConcepts(concepts, position, 100);
          
          if (nearConcepts.length >= 2) {
            // Create relationships between nearby concepts
            for (let i = 0; i < nearConcepts.length - 1; i++) {
              for (let j = i + 1; j < nearConcepts.length; j++) {
                const relationship: ExtractedRelationship = {
                  id: `rel_${sourceId}_${relationshipId++}`,
                  from_concept: nearConcepts[i].id,
                  to_concept: nearConcepts[j].id,
                  relationship_type: relType as RelationshipType,
                  evidence: this.extractContext(content, position, relationshipText.length),
                  confidence: this.calculateRelationshipConfidence(
                    nearConcepts[i], 
                    nearConcepts[j], 
                    relationshipText,
                    position
                  ),
                  sourceIds: [sourceId]
                };
                
                relationships.push(relationship);
              }
            }
          }
        }
      }
    }

    // Infer relationships from proximity and co-occurrence
    const proximityRelationships = this.inferProximityRelationships(concepts, content, sourceId);
    relationships.push(...proximityRelationships);

    return this.deduplicateRelationships(relationships);
  }

  /**
   * Calculate confidence score for a concept
   */
  private static calculateConceptConfidence(text: string, type: ConceptType, content: string): number {
    let confidence = 0.5; // Base confidence
    
    // Boost confidence based on capitalization
    if (/^[A-Z]/.test(text)) confidence += 0.1;
    
    // Boost confidence based on frequency
    const frequency = (content.match(new RegExp(text, 'gi')) || []).length;
    confidence += Math.min(frequency * 0.05, 0.3);
    
    // Boost confidence based on type-specific patterns
    switch (type) {
      case 'entity':
        if (/^[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*$/.test(text)) confidence += 0.2;
        break;
      case 'process':
        if (text.includes('process') || text.includes('method')) confidence += 0.2;
        break;
      case 'person':
        if (/\b(?:Mr|Mrs|Ms|Dr|Prof)\.?\s/.test(text)) confidence += 0.3;
        break;
    }
    
    return Math.min(confidence, 1.0);
  }

  /**
   * Calculate confidence score for a relationship
   */
  private static calculateRelationshipConfidence(
    conceptA: ExtractedConcept,
    conceptB: ExtractedConcept,
    evidence: string,
    position: number
  ): number {
    let confidence = 0.4; // Base confidence
    
    // Boost confidence based on concept confidence
    confidence += (conceptA.confidence + conceptB.confidence) * 0.2;
    
    // Boost confidence based on proximity
    const distance = Math.abs(conceptA.position.start - conceptB.position.start);
    if (distance < 50) confidence += 0.2;
    else if (distance < 100) confidence += 0.1;
    
    // Boost confidence based on evidence strength
    if (evidence.length > 10) confidence += 0.1;
    
    return Math.min(confidence, 1.0);
  }

  /**
   * Extract context around a position in text
   */
  private static extractContext(content: string, position: number, length: number, contextSize = 50): string {
    const start = Math.max(0, position - contextSize);
    const end = Math.min(content.length, position + length + contextSize);
    return content.substring(start, end).trim();
  }

  /**
   * Extract noun phrases from text
   */
  private static extractNounPhrases(content: string): Array<{ text: string; position: TextPosition }> {
    const phrases: Array<{ text: string; position: TextPosition }> = [];
    
    // Simple noun phrase pattern: adjective* noun+
    const nounPhrasePattern = /\b(?:[A-Z][a-z]*\s+)*[A-Z][a-z]*\b/g;
    const matches = content.matchAll(nounPhrasePattern);
    
    for (const match of matches) {
      if (match.index !== undefined && match[0]) {
        const text = match[0].trim();
        if (text.length > 3) {
          phrases.push({
            text,
            position: {
              start: match.index,
              end: match.index + text.length
            }
          });
        }
      }
    }
    
    return phrases;
  }

  /**
   * Find concepts near a given position
   */
  private static findNearConcepts(
    concepts: ExtractedConcept[], 
    position: number, 
    maxDistance: number
  ): ExtractedConcept[] {
    return concepts.filter(concept => {
      const distance = Math.abs(concept.position.start - position);
      return distance <= maxDistance;
    }).sort((a, b) => {
      const distA = Math.abs(a.position.start - position);
      const distB = Math.abs(b.position.start - position);
      return distA - distB;
    });
  }

  /**
   * Infer relationships based on concept proximity
   */
  private static inferProximityRelationships(
    concepts: ExtractedConcept[],
    content: string,
    sourceId: string
  ): ExtractedRelationship[] {
    const relationships: ExtractedRelationship[] = [];
    let relationshipId = 1000; // Start with high ID to avoid conflicts
    
    for (let i = 0; i < concepts.length - 1; i++) {
      for (let j = i + 1; j < concepts.length; j++) {
        const conceptA = concepts[i];
        const conceptB = concepts[j];
        const distance = Math.abs(conceptA.position.start - conceptB.position.start);
        
        // Only create relationships for nearby concepts
        if (distance < 200) {
          const relationship: ExtractedRelationship = {
            id: `prox_rel_${sourceId}_${relationshipId++}`,
            from_concept: conceptA.id,
            to_concept: conceptB.id,
            relationship_type: 'similar_to', // Default relationship type
            evidence: this.extractContext(
              content, 
              Math.min(conceptA.position.start, conceptB.position.start),
              distance
            ),
            confidence: Math.max(0.2, 0.8 - (distance / 200) * 0.6),
            sourceIds: [sourceId]
          };
          
          relationships.push(relationship);
        }
      }
    }
    
    return relationships;
  }

  /**
   * Remove duplicate concepts based on text similarity
   */
  private static deduplicateConcepts(concepts: ExtractedConcept[]): ExtractedConcept[] {
    const unique: ExtractedConcept[] = [];
    const seen = new Set<string>();
    
    for (const concept of concepts) {
      const key = concept.text.toLowerCase().trim();
      if (!seen.has(key)) {
        seen.add(key);
        unique.push(concept);
      } else {
        // Merge with existing concept if found
        const existing = unique.find(c => c.text.toLowerCase().trim() === key);
        if (existing) {
          existing.confidence = Math.max(existing.confidence, concept.confidence);
          existing.sourceIds = [...new Set([...existing.sourceIds, ...concept.sourceIds])];
        }
      }
    }
    
    return unique;
  }

  /**
   * Remove duplicate relationships
   */
  private static deduplicateRelationships(relationships: ExtractedRelationship[]): ExtractedRelationship[] {
    const unique: ExtractedRelationship[] = [];
    const seen = new Set<string>();
    
    for (const relationship of relationships) {
      const key = `${relationship.from_concept}-${relationship.to_concept}-${relationship.relationship_type}`;
      if (!seen.has(key)) {
        seen.add(key);
        unique.push(relationship);
      } else {
        // Merge with existing relationship if found
        const existing = unique.find(r => 
          r.from_concept === relationship.from_concept &&
          r.to_concept === relationship.to_concept &&
          r.relationship_type === relationship.relationship_type
        );
        if (existing) {
          existing.confidence = Math.max(existing.confidence, relationship.confidence);
          existing.sourceIds = [...new Set([...existing.sourceIds, ...relationship.sourceIds])];
        }
      }
    }
    
    return unique;
  }

  /**
   * Clean and normalize text for analysis
   */
  static cleanText(text: string): string {
    return text
      .replace(/\s+/g, ' ') // Normalize whitespace
      .replace(/[^\w\s.,!?;:()\-]/g, '') // Remove special characters except basic punctuation
      .trim();
  }

  /**
   * Calculate text similarity between two strings
   */
  static calculateSimilarity(text1: string, text2: string): number {
    const words1 = new Set(text1.toLowerCase().split(/\s+/));
    const words2 = new Set(text2.toLowerCase().split(/\s+/));
    
    const intersection = new Set([...words1].filter(word => words2.has(word)));
    const union = new Set([...words1, ...words2]);
    
    return intersection.size / union.size;
  }
}