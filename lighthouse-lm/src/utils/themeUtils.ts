import { useTheme } from '@/contexts/ThemeContext';

/**
 * Theme utility functions for lighthouse-lm components
 */

/**
 * Get theme-aware color classes
 */
export const getThemeColorClasses = (baseColor: string, variant: 'light' | 'dark' = 'light') => {
  // This function returns CSS classes that work with both light and dark themes
  // Using Tailwind's dark: prefix for automatic dark mode support
  return `${baseColor} dark:${baseColor.replace('bg-', 'dark:bg-').replace('text-', 'dark:text-')}`;
};

/**
 * Get theme-aware status color classes
 */
export const getThemeStatusColor = (status: string) => {
  const statusMap: Record<string, string> = {
    success: 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-200',
    error: 'bg-destructive/10 text-destructive dark:bg-destructive/20 dark:text-destructive-foreground',
    warning: 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-200',
    info: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200',
    pending: 'bg-muted text-muted-foreground',
    active: 'bg-primary/10 text-primary',
  };
  
  return statusMap[status] || statusMap.pending;
};

/**
 * Get theme-aware icon color classes
 */
export const getThemeIconColor = (type: 'success' | 'error' | 'warning' | 'info' | 'default') => {
  const colorMap: Record<string, string> = {
    success: 'text-emerald-500 dark:text-emerald-400',
    error: 'text-destructive dark:text-red-400',
    warning: 'text-amber-500 dark:text-amber-400',
    info: 'text-primary dark:text-blue-400',
    default: 'text-foreground dark:text-foreground',
  };
  
  return colorMap[type] || colorMap.default;
};

/**
 * Theme-aware skeleton loading classes
 */
export const getSkeletonClasses = () => {
  return 'animate-pulse bg-muted dark:bg-muted/50';
};

/**
 * Theme-aware card classes
 */
export const getCardClasses = (interactive = false) => {
  let classes = 'bg-card border border-border rounded-xl shadow-sm';
  
  if (interactive) {
    classes += ' hover:shadow-md transition-all duration-200 cursor-pointer';
  }
  
  return classes;
};

/**
 * Theme-aware button variant classes
 */
export const getButtonVariantClasses = (variant: 'primary' | 'secondary' | 'destructive' | 'ghost' | 'outline') => {
  const variantMap: Record<string, string> = {
    primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
    destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
    ghost: 'hover:bg-accent hover:text-accent-foreground',
    outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
  };
  
  return variantMap[variant] || variantMap.primary;
};

export { useTheme };