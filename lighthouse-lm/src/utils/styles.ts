import { clsx } from 'clsx';
import { useThemeContext } from '@/contexts/ThemeContext';

/**
 * Status color mappings for badges and indicators using theme variables
 */
export const statusColors = {
  completed: 'bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300',
  success: 'bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300',
  processing: 'bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300',
  pending: 'bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300',
  loading: 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300',
  failed: 'bg-destructive/10 text-destructive dark:bg-destructive/20',
  error: 'bg-destructive/10 text-destructive dark:bg-destructive/20',
  default: 'bg-muted text-muted-foreground',
  inactive: 'bg-muted text-muted-foreground',
  active: 'bg-primary/10 text-primary',
} as const;

/**
 * Get status color classes
 */
export const getStatusColor = (status: string): string => {
  return statusColors[status as keyof typeof statusColors] || statusColors.default;
};

/**
 * Common animation classes
 */
export const animations = {
  pulse: 'animate-pulse',
  fadeIn: 'animate-in fade-in-0 duration-700',
  fadeInUp: 'animate-in fade-in-0 slide-in-from-bottom-2 duration-500',
  slideIn: 'animate-in slide-in-from-left duration-300',
  scaleIn: 'animate-in zoom-in-95 duration-300',
  spin: 'animate-spin',
} as const;

/**
 * Common transition classes
 */
export const transitions = {
  all: 'transition-all duration-300',
  colors: 'transition-colors duration-200',
  transform: 'transition-transform duration-200',
  opacity: 'transition-opacity duration-300',
  shadow: 'transition-shadow duration-200',
} as const;

/**
 * Common hover effects
 */
export const hoverEffects = {
  scale: 'hover:scale-105',
  scaleSmall: 'hover:scale-102',
  scaleLarge: 'hover:scale-110',
  shadow: 'hover:shadow-lg',
  shadowSmall: 'hover:shadow-md',
  opacity: 'hover:opacity-80',
  brightness: 'hover:brightness-110',
} as const;

/**
 * Combine hover and transition effects
 */
export const interactiveCard = clsx(
  transitions.all,
  hoverEffects.scale,
  hoverEffects.shadow,
  'cursor-pointer'
);

/**
 * Skeleton loading classes using theme variables
 */
export const skeleton = {
  base: 'animate-pulse bg-muted',
  text: 'h-4 rounded',
  title: 'h-6 rounded',
  button: 'h-10 w-24 rounded',
  avatar: 'w-10 h-10 rounded-full',
  card: 'bg-card border border-border rounded-xl p-6 shadow-sm animate-pulse',
} as const;

/**
 * Page transition variants for framer-motion
 */
export const pageTransitions = {
  initial: {
    opacity: 0,
    x: -20,
    scale: 0.95,
  },
  animate: {
    opacity: 1,
    x: 0,
    scale: 1,
    transition: {
      duration: 0.4,
      ease: "easeInOut",
    },
  },
  exit: {
    opacity: 0,
    x: 20,
    scale: 0.95,
    transition: {
      duration: 0.3,
      ease: "easeInOut",
    },
  },
};

/**
 * Modal/Dialog transition variants
 */
export const modalTransitions = {
  initial: {
    opacity: 0,
    scale: 0.9,
    y: 20,
  },
  animate: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: "easeOut",
    },
  },
  exit: {
    opacity: 0,
    scale: 0.9,
    y: 20,
    transition: {
      duration: 0.2,
      ease: "easeIn",
    },
  },
};

/**
 * Create staggered animation delay
 */
export const staggerDelay = (index: number, baseDelay = 100): React.CSSProperties => ({
  animationDelay: `${index * baseDelay}ms`,
});

/**
 * Badge style variations using theme variables
 */
export const badgeVariants = {
  default: 'bg-secondary text-secondary-foreground',
  primary: 'bg-primary text-primary-foreground',
  success: 'bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-300',
  warning: 'bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300',
  error: 'bg-destructive/10 text-destructive dark:bg-destructive/20',
  info: 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300',
} as const;

/**
 * Get badge variant classes
 */
export const getBadgeVariant = (variant: keyof typeof badgeVariants): string => {
  return badgeVariants[variant] || badgeVariants.default;
};