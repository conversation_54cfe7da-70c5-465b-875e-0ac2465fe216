/**
 * Content extraction utilities for different source types
 */

import { Source } from '@/services/insights-api';

export interface ExtractedContent {
  text: string;
  structure: ContentStructure;
  metadata: ContentMetadata;
}

export interface ContentStructure {
  sections: ContentSection[];
  headings: Heading[];
  lists: ListItem[];
  tables: TableData[];
  links: LinkData[];
}

export interface ContentSection {
  id: string;
  title: string;
  content: string;
  level: number;
  start_position: number;
  end_position: number;
}

export interface Heading {
  id: string;
  text: string;
  level: number;
  position: number;
}

export interface ListItem {
  id: string;
  text: string;
  level: number;
  type: 'ordered' | 'unordered';
  position: number;
}

export interface TableData {
  id: string;
  headers: string[];
  rows: string[][];
  position: number;
}

export interface LinkData {
  id: string;
  text: string;
  url: string;
  position: number;
}

export interface ContentMetadata {
  word_count: number;
  character_count: number;
  paragraph_count: number;
  sentence_count: number;
  reading_time_minutes: number;
  language?: string;
  content_type: string;
}

export class ContentExtractionUtils {
  /**
   * Extract content from a source based on its type
   */
  static extractFromSource(source: Source): ExtractedContent {
    const content = source.content || '';
    const sourceType = source.source_type || 'text';

    switch (sourceType.toLowerCase()) {
      case 'markdown':
      case 'md':
        return this.extractFromMarkdown(content);
      case 'html':
        return this.extractFromHtml(content);
      case 'pdf':
        return this.extractFromPdf(content);
      case 'json':
        return this.extractFromJson(content);
      case 'csv':
        return this.extractFromCsv(content);
      default:
        return this.extractFromPlainText(content);
    }
  }

  /**
   * Extract content from markdown text
   */
  static extractFromMarkdown(content: string): ExtractedContent {
    const text = this.stripMarkdownSyntax(content);
    const structure = this.parseMarkdownStructure(content);
    const metadata = this.generateMetadata(text, 'markdown');

    return { text, structure, metadata };
  }

  /**
   * Extract content from HTML text
   */
  static extractFromHtml(content: string): ExtractedContent {
    const text = this.stripHtmlTags(content);
    const structure = this.parseHtmlStructure(content);
    const metadata = this.generateMetadata(text, 'html');

    return { text, structure, metadata };
  }

  /**
   * Extract content from PDF text (assuming it's already extracted)
   */
  static extractFromPdf(content: string): ExtractedContent {
    const text = this.cleanPdfText(content);
    const structure = this.parsePlainTextStructure(text);
    const metadata = this.generateMetadata(text, 'pdf');

    return { text, structure, metadata };
  }

  /**
   * Extract content from JSON data
   */
  static extractFromJson(content: string): ExtractedContent {
    try {
      const jsonData = JSON.parse(content);
      const text = this.extractTextFromJson(jsonData);
      const structure = this.parseJsonStructure(jsonData);
      const metadata = this.generateMetadata(text, 'json');

      return { text, structure, metadata };
    } catch (error) {
      // Fallback to plain text if JSON parsing fails
      return this.extractFromPlainText(content);
    }
  }

  /**
   * Extract content from CSV data
   */
  static extractFromCsv(content: string): ExtractedContent {
    const text = this.extractTextFromCsv(content);
    const structure = this.parseCsvStructure(content);
    const metadata = this.generateMetadata(text, 'csv');

    return { text, structure, metadata };
  }

  /**
   * Extract content from plain text
   */
  static extractFromPlainText(content: string): ExtractedContent {
    const text = content.trim();
    const structure = this.parsePlainTextStructure(text);
    const metadata = this.generateMetadata(text, 'text');

    return { text, structure, metadata };
  }

  /**
   * Strip markdown syntax and return clean text
   */
  private static stripMarkdownSyntax(content: string): string {
    return content
      .replace(/^#{1,6}\s+/gm, '') // Remove headers
      .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
      .replace(/\*(.*?)\*/g, '$1') // Remove italic
      .replace(/`(.*?)`/g, '$1') // Remove inline code
      .replace(/```[\s\S]*?```/g, '') // Remove code blocks
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Remove links, keep text
      .replace(/!\[([^\]]*)\]\([^)]+\)/g, '$1') // Remove images, keep alt text
      .replace(/^\s*[-*+]\s+/gm, '') // Remove list markers
      .replace(/^\s*\d+\.\s+/gm, '') // Remove numbered list markers
      .replace(/^\s*>\s+/gm, '') // Remove blockquotes
      .replace(/\n{3,}/g, '\n\n') // Normalize line breaks
      .trim();
  }

  /**
   * Parse markdown structure
   */
  private static parseMarkdownStructure(content: string): ContentStructure {
    const sections: ContentSection[] = [];
    const headings: Heading[] = [];
    const lists: ListItem[] = [];
    const tables: TableData[] = [];
    const links: LinkData[] = [];

    // Extract headings
    const headingMatches = content.matchAll(/^(#{1,6})\s+(.+)$/gm);
    for (const match of headingMatches) {
      if (match.index !== undefined) {
        headings.push({
          id: `heading_${headings.length}`,
          text: match[2],
          level: match[1].length,
          position: match.index
        });
      }
    }

    // Extract lists
    const listMatches = content.matchAll(/^(\s*)([-*+]|\d+\.)\s+(.+)$/gm);
    for (const match of listMatches) {
      if (match.index !== undefined) {
        const level = Math.floor(match[1].length / 2);
        const type = /\d+\./.test(match[2]) ? 'ordered' : 'unordered';
        
        lists.push({
          id: `list_${lists.length}`,
          text: match[3],
          level,
          type,
          position: match.index
        });
      }
    }

    // Extract links
    const linkMatches = content.matchAll(/\[([^\]]+)\]\(([^)]+)\)/g);
    for (const match of linkMatches) {
      if (match.index !== undefined) {
        links.push({
          id: `link_${links.length}`,
          text: match[1],
          url: match[2],
          position: match.index
        });
      }
    }

    // Create sections based on headings
    for (let i = 0; i < headings.length; i++) {
      const heading = headings[i];
      const nextHeading = headings[i + 1];
      const endPosition = nextHeading ? nextHeading.position : content.length;
      
      const sectionContent = content.substring(heading.position, endPosition);
      
      sections.push({
        id: `section_${i}`,
        title: heading.text,
        content: this.stripMarkdownSyntax(sectionContent),
        level: heading.level,
        start_position: heading.position,
        end_position: endPosition
      });
    }

    return { sections, headings, lists, tables, links };
  }

  /**
   * Strip HTML tags and return clean text
   */
  private static stripHtmlTags(content: string): string {
    return content
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove scripts
      .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '') // Remove styles
      .replace(/<[^>]+>/g, ' ') // Remove all HTML tags
      .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
      .replace(/&amp;/g, '&') // Replace HTML entities
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
  }

  /**
   * Parse HTML structure
   */
  private static parseHtmlStructure(content: string): ContentStructure {
    const sections: ContentSection[] = [];
    const headings: Heading[] = [];
    const lists: ListItem[] = [];
    const tables: TableData[] = [];
    const links: LinkData[] = [];

    // Extract headings
    const headingMatches = content.matchAll(/<h([1-6])[^>]*>([^<]+)<\/h[1-6]>/gi);
    for (const match of headingMatches) {
      if (match.index !== undefined) {
        headings.push({
          id: `heading_${headings.length}`,
          text: match[2].trim(),
          level: parseInt(match[1]),
          position: match.index
        });
      }
    }

    // Extract links
    const linkMatches = content.matchAll(/<a[^>]+href="([^"]+)"[^>]*>([^<]+)<\/a>/gi);
    for (const match of linkMatches) {
      if (match.index !== undefined) {
        links.push({
          id: `link_${links.length}`,
          text: match[2].trim(),
          url: match[1],
          position: match.index
        });
      }
    }

    return { sections, headings, lists, tables, links };
  }

  /**
   * Clean PDF text (remove common PDF artifacts)
   */
  private static cleanPdfText(content: string): string {
    return content
      .replace(/\f/g, '\n') // Replace form feeds with newlines
      .replace(/\r\n/g, '\n') // Normalize line endings
      .replace(/\r/g, '\n')
      .replace(/\n{3,}/g, '\n\n') // Normalize multiple line breaks
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
  }

  /**
   * Parse plain text structure
   */
  private static parsePlainTextStructure(content: string): ContentStructure {
    const sections: ContentSection[] = [];
    const headings: Heading[] = [];
    const lists: ListItem[] = [];
    const tables: TableData[] = [];
    const links: LinkData[] = [];

    // Split into paragraphs and treat as sections
    const paragraphs = content.split(/\n\s*\n/);
    
    paragraphs.forEach((paragraph, index) => {
      if (paragraph.trim()) {
        const startPosition = content.indexOf(paragraph);
        
        sections.push({
          id: `section_${index}`,
          title: `Paragraph ${index + 1}`,
          content: paragraph.trim(),
          level: 1,
          start_position: startPosition,
          end_position: startPosition + paragraph.length
        });
      }
    });

    return { sections, headings, lists, tables, links };
  }

  /**
   * Extract text from JSON object
   */
  private static extractTextFromJson(obj: any, path = ''): string {
    let text = '';
    
    if (typeof obj === 'string') {
      text += obj + ' ';
    } else if (typeof obj === 'number' || typeof obj === 'boolean') {
      text += obj.toString() + ' ';
    } else if (Array.isArray(obj)) {
      obj.forEach((item, index) => {
        text += this.extractTextFromJson(item, `${path}[${index}]`);
      });
    } else if (obj && typeof obj === 'object') {
      Object.entries(obj).forEach(([key, value]) => {
        text += key + ': ';
        text += this.extractTextFromJson(value, `${path}.${key}`);
      });
    }
    
    return text;
  }

  /**
   * Parse JSON structure
   */
  private static parseJsonStructure(obj: any): ContentStructure {
    const sections: ContentSection[] = [];
    const headings: Heading[] = [];
    const lists: ListItem[] = [];
    const tables: TableData[] = [];
    const links: LinkData[] = [];

    // Create sections for top-level properties
    if (obj && typeof obj === 'object' && !Array.isArray(obj)) {
      Object.entries(obj).forEach(([key, value], index) => {
        sections.push({
          id: `section_${index}`,
          title: key,
          content: this.extractTextFromJson(value),
          level: 1,
          start_position: 0,
          end_position: 0
        });
      });
    }

    return { sections, headings, lists, tables, links };
  }

  /**
   * Extract text from CSV content
   */
  private static extractTextFromCsv(content: string): string {
    const lines = content.split('\n');
    const text = lines
      .map(line => line.split(',').join(' '))
      .join(' ')
      .replace(/"/g, '')
      .trim();
    
    return text;
  }

  /**
   * Parse CSV structure
   */
  private static parseCsvStructure(content: string): ContentStructure {
    const sections: ContentSection[] = [];
    const headings: Heading[] = [];
    const lists: ListItem[] = [];
    const tables: TableData[] = [];
    const links: LinkData[] = [];

    const lines = content.split('\n').filter(line => line.trim());
    
    if (lines.length > 0) {
      const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());
      const rows = lines.slice(1).map(line => 
        line.split(',').map(cell => cell.replace(/"/g, '').trim())
      );

      tables.push({
        id: 'csv_table_0',
        headers,
        rows,
        position: 0
      });

      // Create a section for the table
      sections.push({
        id: 'section_0',
        title: 'CSV Data',
        content: this.extractTextFromCsv(content),
        level: 1,
        start_position: 0,
        end_position: content.length
      });
    }

    return { sections, headings, lists, tables, links };
  }

  /**
   * Generate content metadata
   */
  private static generateMetadata(text: string, contentType: string): ContentMetadata {
    const words = text.split(/\s+/).filter(word => word.length > 0);
    const sentences = text.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0);
    const paragraphs = text.split(/\n\s*\n/).filter(paragraph => paragraph.trim().length > 0);
    
    // Estimate reading time (average 200 words per minute)
    const readingTimeMinutes = Math.ceil(words.length / 200);

    return {
      word_count: words.length,
      character_count: text.length,
      paragraph_count: paragraphs.length,
      sentence_count: sentences.length,
      reading_time_minutes: readingTimeMinutes,
      content_type: contentType
    };
  }

  /**
   * Get content summary
   */
  static getContentSummary(extractedContent: ExtractedContent): string {
    const { text, structure, metadata } = extractedContent;
    
    // Take first few sentences as summary
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const summaryLength = Math.min(3, sentences.length);
    
    let summary = sentences.slice(0, summaryLength).join('. ').trim();
    if (summary && !summary.endsWith('.')) {
      summary += '.';
    }
    
    // If no good summary from sentences, take first paragraph
    if (!summary || summary.length < 50) {
      const firstParagraph = text.split('\n\n')[0];
      summary = firstParagraph.substring(0, 200);
      if (summary.length === 200) {
        summary += '...';
      }
    }
    
    return summary || 'No content summary available.';
  }

  /**
   * Extract key phrases from content
   */
  static extractKeyPhrases(extractedContent: ExtractedContent, maxPhrases = 10): string[] {
    const { text } = extractedContent;
    const phrases: string[] = [];
    
    // Extract noun phrases (simple pattern)
    const nounPhrasePattern = /\b(?:[A-Z][a-z]*\s+)*[A-Z][a-z]*\b/g;
    const matches = text.matchAll(nounPhrasePattern);
    
    const phraseFrequency = new Map<string, number>();
    
    for (const match of matches) {
      if (match[0] && match[0].length > 3) {
        const phrase = match[0].trim().toLowerCase();
        phraseFrequency.set(phrase, (phraseFrequency.get(phrase) || 0) + 1);
      }
    }
    
    // Sort by frequency and return top phrases
    const sortedPhrases = Array.from(phraseFrequency.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, maxPhrases)
      .map(([phrase]) => phrase);
    
    return sortedPhrases;
  }
}