import React from 'react';
import { FileText, Globe, Youtube, Mic, FileCode, File } from 'lucide-react';
import { toast } from '../hooks/useToast';
import { copyToClipboard as notificationCopyToClipboard } from '../services/notification';

/**
 * Format file size from bytes to human-readable format
 */
export const formatFileSize = (bytes: number): string => {
  if (!bytes || bytes === 0) return '0 B';
  if (bytes < 1024) return `${bytes} B`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
  if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
};

/**
 * Format date to relative or absolute format
 */
export const formatDate = (date: string | Date | undefined): string => {
  if (!date) return 'Unknown';
  
  const d = new Date(date);
  const now = new Date();
  const diffMs = now.getTime() - d.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) {
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    if (diffHours === 0) {
      const diffMinutes = Math.floor(diffMs / (1000 * 60));
      if (diffMinutes === 0) return 'Just now';
      if (diffMinutes === 1) return '1 minute ago';
      return `${diffMinutes} minutes ago`;
    }
    if (diffHours === 1) return '1 hour ago';
    return `${diffHours} hours ago`;
  }
  if (diffDays === 1) return 'Yesterday';
  if (diffDays < 7) return `${diffDays} days ago`;
  if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
  if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
  
  // For older dates, show actual date
  return d.toLocaleDateString();
};

/**
 * Get icon component based on source type
 */
export const getSourceIcon = (
  sourceType: string,
  className = 'w-5 h-5'
): React.ReactElement => {
  const iconProps = { className };
  
  switch (sourceType?.toLowerCase()) {
    case 'pdf':
      return React.createElement(FileText, iconProps);
    case 'website':
    case 'url':
    case 'web':
      return React.createElement(Globe, iconProps);
    case 'youtube':
    case 'video':
      return React.createElement(Youtube, iconProps);
    case 'audio':
    case 'mp3':
    case 'wav':
      return React.createElement(Mic, iconProps);
    case 'code':
    case 'json':
    case 'xml':
      return React.createElement(FileCode, iconProps);
    case 'text':
    case 'txt':
    case 'md':
    case 'markdown':
    default:
      return React.createElement(File, iconProps);
  }
};

/**
 * Copy text to clipboard
 * @deprecated Use copyToClipboard from notificationService instead
 */
export const copyToClipboard = notificationCopyToClipboard;

/**
 * Export content to file
 */
export const exportToFile = (
  content: string,
  filename: string,
  mimeType = 'text/plain'
): void => {
  try {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast({
      title: 'Exported',
      description: `File "${filename}" has been downloaded`,
    });
  } catch (error) {
    toast({
      title: 'Export Failed',
      description: 'Failed to export file',
      variant: 'destructive',
    });
  }
};

/**
 * Validate URL format
 */
export const isValidUrl = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
  } catch {
    return false;
  }
};

/**
 * Validate YouTube URL
 */
export const isValidYouTubeUrl = (url: string): boolean => {
  const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/.+$/;
  return youtubeRegex.test(url);
};

/**
 * Extract YouTube video ID from URL
 */
export const extractYouTubeVideoId = (url: string): string | null => {
  const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
  return match ? match[1] : null;
};

/**
 * Truncate text with ellipsis
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + '...';
};

/**
 * Generate unique ID
 */
export const generateUniqueId = (prefix = 'id'): string => {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Debounce function
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Format duration in seconds to human-readable format
 */
export const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`;
};

/**
 * Check if running in Tauri environment
 */
export const isTauriEnvironment = (): boolean => {
  return typeof window !== 'undefined' && !!(window as any).__TAURI__;
};