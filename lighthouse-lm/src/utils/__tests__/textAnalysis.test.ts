/**
 * Unit tests for text analysis utilities
 */

import { TextAnalysisUtils } from '../textAnalysis';
import { ConceptType, RelationshipType } from '../../types/sourceAnalysis';

describe('TextAnalysisUtils', () => {
  describe('extractConcepts', () => {
    it('should extract entity concepts from text', () => {
      const content = 'The User Management System handles user authentication and authorization.';
      const concepts = TextAnalysisUtils.extractConcepts(content, 'test-source-1');
      
      const entityConcepts = concepts.filter(c => c.type === 'entity');
      expect(entityConcepts.length).toBeGreaterThan(0);
      
      const userManagementConcept = entityConcepts.find(c => c.text.includes('User Management'));
      expect(userManagementConcept).toBeDefined();
      expect(userManagementConcept?.sourceIds).toContain('test-source-1');
    });

    it('should extract process concepts from text', () => {
      const content = 'The authentication process requires user credentials and validates them against the database.';
      const concepts = TextAnalysisUtils.extractConcepts(content, 'test-source-2');
      
      const processConcepts = concepts.filter(c => c.type === 'process');
      expect(processConcepts.length).toBeGreaterThan(0);
      
      const authProcess = processConcepts.find(c => c.text.toLowerCase().includes('process'));
      expect(authProcess).toBeDefined();
    });

    it('should extract person concepts from text', () => {
      const content = 'The user logs in and the admin manages the system settings.';
      const concepts = TextAnalysisUtils.extractConcepts(content, 'test-source-3');
      
      const personConcepts = concepts.filter(c => c.type === 'person');
      expect(personConcepts.length).toBeGreaterThan(0);
      
      const userConcept = personConcepts.find(c => c.text.toLowerCase() === 'user');
      const adminConcept = personConcepts.find(c => c.text.toLowerCase() === 'admin');
      
      expect(userConcept).toBeDefined();
      expect(adminConcept).toBeDefined();
    });

    it('should extract date concepts from text', () => {
      const content = 'The project started on January 15, 2024 and will end on 2024-12-31.';
      const concepts = TextAnalysisUtils.extractConcepts(content, 'test-source-4');
      
      const dateConcepts = concepts.filter(c => c.type === 'date');
      expect(dateConcepts.length).toBe(2);
      
      const januaryDate = dateConcepts.find(c => c.text.includes('January'));
      const isoDate = dateConcepts.find(c => c.text.includes('2024-12-31'));
      
      expect(januaryDate).toBeDefined();
      expect(isoDate).toBeDefined();
    });

    it('should calculate confidence scores for concepts', () => {
      const content = 'The Database Management System is a critical component. The Database Management System handles all data operations.';
      const concepts = TextAnalysisUtils.extractConcepts(content, 'test-source-5');
      
      const dbConcept = concepts.find(c => c.text.includes('Database Management'));
      expect(dbConcept).toBeDefined();
      expect(dbConcept?.confidence).toBeGreaterThan(0.5); // Should have higher confidence due to repetition
    });

    it('should deduplicate similar concepts', () => {
      const content = 'User authentication and user authentication are important. User Authentication is critical.';
      const concepts = TextAnalysisUtils.extractConcepts(content, 'test-source-6');
      
      const authConcepts = concepts.filter(c => c.text.toLowerCase().includes('authentication'));
      // Should be deduplicated to fewer concepts than the number of occurrences
      expect(authConcepts.length).toBeLessThan(3);
    });
  });

  describe('extractRelationships', () => {
    it('should extract dependency relationships', () => {
      const content = 'The authentication process depends on the user database. Login requires valid credentials.';
      const concepts = TextAnalysisUtils.extractConcepts(content, 'test-source-7');
      const relationships = TextAnalysisUtils.extractRelationships(content, concepts, 'test-source-7');
      
      const dependencyRels = relationships.filter(r => r.relationship_type === 'depends_on');
      expect(dependencyRels.length).toBeGreaterThan(0);
      
      const authDependency = dependencyRels.find(r => r.evidence.toLowerCase().includes('depends on'));
      expect(authDependency).toBeDefined();
    });

    it('should extract containment relationships', () => {
      const content = 'The system contains multiple modules including authentication and authorization.';
      const concepts = TextAnalysisUtils.extractConcepts(content, 'test-source-8');
      const relationships = TextAnalysisUtils.extractRelationships(content, concepts, 'test-source-8');
      
      const containmentRels = relationships.filter(r => r.relationship_type === 'contains');
      expect(containmentRels.length).toBeGreaterThan(0);
    });

    it('should infer proximity relationships', () => {
      const content = 'User authentication and password validation work together seamlessly.';
      const concepts = TextAnalysisUtils.extractConcepts(content, 'test-source-9');
      const relationships = TextAnalysisUtils.extractRelationships(content, concepts, 'test-source-9');
      
      // Should create relationships between nearby concepts
      expect(relationships.length).toBeGreaterThan(0);
      
      const proximityRel = relationships.find(r => r.id.includes('prox_rel'));
      expect(proximityRel).toBeDefined();
    });

    it('should calculate relationship confidence scores', () => {
      const content = 'Authentication directly leads to authorization in the security workflow.';
      const concepts = TextAnalysisUtils.extractConcepts(content, 'test-source-10');
      const relationships = TextAnalysisUtils.extractRelationships(content, concepts, 'test-source-10');
      
      const leadToRel = relationships.find(r => r.relationship_type === 'leads_to');
      expect(leadToRel).toBeDefined();
      expect(leadToRel?.confidence).toBeGreaterThan(0.3);
    });

    it('should deduplicate similar relationships', () => {
      const content = 'A depends on B. A relies on B. A requires B.';
      const concepts = [
        {
          id: 'concept_a',
          text: 'A',
          type: 'entity' as ConceptType,
          position: { start: 0, end: 1 },
          confidence: 0.8,
          context: 'A depends',
          sourceIds: ['test-source-11']
        },
        {
          id: 'concept_b',
          text: 'B',
          type: 'entity' as ConceptType,
          position: { start: 12, end: 13 },
          confidence: 0.8,
          context: 'on B',
          sourceIds: ['test-source-11']
        }
      ];
      
      const relationships = TextAnalysisUtils.extractRelationships(content, concepts, 'test-source-11');
      
      // Should deduplicate similar dependency relationships
      const dependencyRels = relationships.filter(r => r.relationship_type === 'depends_on');
      expect(dependencyRels.length).toBeLessThanOrEqual(1);
    });
  });

  describe('utility methods', () => {
    it('should clean text properly', () => {
      const dirtyText = '  This   has    extra   spaces  and @#$% special chars!  ';
      const cleanText = TextAnalysisUtils.cleanText(dirtyText);
      
      expect(cleanText).toBe('This has extra spaces and special chars!');
      expect(cleanText).not.toMatch(/\s{2,}/); // No multiple spaces
      expect(cleanText.trim()).toBe(cleanText); // No leading/trailing spaces
    });

    it('should calculate text similarity correctly', () => {
      const text1 = 'user authentication system';
      const text2 = 'authentication system for users';
      const text3 = 'completely different content';
      
      const similarity1 = TextAnalysisUtils.calculateSimilarity(text1, text2);
      const similarity2 = TextAnalysisUtils.calculateSimilarity(text1, text3);
      
      expect(similarity1).toBeGreaterThan(similarity2);
      expect(similarity1).toBeGreaterThan(0.5); // Should have high similarity
      expect(similarity2).toBeLessThan(0.3); // Should have low similarity
    });

    it('should handle empty or invalid input gracefully', () => {
      const emptyConcepts = TextAnalysisUtils.extractConcepts('', 'empty-source');
      const emptyRelationships = TextAnalysisUtils.extractRelationships('', [], 'empty-source');
      
      expect(emptyConcepts).toEqual([]);
      expect(emptyRelationships).toEqual([]);
      
      const cleanEmpty = TextAnalysisUtils.cleanText('');
      const similarityEmpty = TextAnalysisUtils.calculateSimilarity('', 'test');
      
      expect(cleanEmpty).toBe('');
      expect(similarityEmpty).toBe(0);
    });
  });

  describe('edge cases', () => {
    it('should handle very short text', () => {
      const shortText = 'A B';
      const concepts = TextAnalysisUtils.extractConcepts(shortText, 'short-source');
      
      // Should handle short text without errors
      expect(Array.isArray(concepts)).toBe(true);
    });

    it('should handle text with only punctuation', () => {
      const punctuationText = '!@#$%^&*()_+-=[]{}|;:,.<>?';
      const concepts = TextAnalysisUtils.extractConcepts(punctuationText, 'punct-source');
      
      expect(concepts).toEqual([]);
    });

    it('should handle very long text', () => {
      const longText = 'word '.repeat(1000) + 'important concept';
      const concepts = TextAnalysisUtils.extractConcepts(longText, 'long-source');
      
      // Should process without errors and find the important concept
      expect(Array.isArray(concepts)).toBe(true);
      expect(concepts.length).toBeGreaterThan(0);
    });

    it('should handle text with mixed languages', () => {
      const mixedText = 'The système de authentication handles 用户 login processes';
      const concepts = TextAnalysisUtils.extractConcepts(mixedText, 'mixed-source');
      
      // Should extract what it can without errors
      expect(Array.isArray(concepts)).toBe(true);
    });
  });
});