/**
 * Unit tests for content extraction utilities
 */

import { ContentExtractionUtils } from '../contentExtraction';
import { Source } from '@/services/insights-api';

describe('ContentExtractionUtils', () => {
  describe('extractFromMarkdown', () => {
    it('should extract clean text from markdown', () => {
      const markdown = `
# Main Title

This is a **bold** text with *italic* and \`code\`.

## Subtitle

- List item 1
- List item 2

[Link text](https://example.com)

\`\`\`javascript
\`\`\`
      `;

      const result = ContentExtractionUtils.extractFromMarkdown(markdown);
      
      expect(result.text).toContain('Main Title');
      expect(result.text).toContain('bold text with italic and code');
      expect(result.text).toContain('List item 1');
      expect(result.text).toContain('Link text');
      expect(result.text).not.toContain('**');
      expect(result.text).not.toContain('```');
      expect(result.text).not.toContain('https://example.com');
    });

    it('should parse markdown structure correctly', () => {
      const markdown = `
# Main Title

Content under main title.

## Subtitle

Content under subtitle.

### Sub-subtitle

More content.
      `;

      const result = ContentExtractionUtils.extractFromMarkdown(markdown);
      
      expect(result.structure.headings).toHaveLength(3);
      expect(result.structure.headings[0].text).toBe('Main Title');
      expect(result.structure.headings[0].level).toBe(1);
      expect(result.structure.headings[1].text).toBe('Subtitle');
      expect(result.structure.headings[1].level).toBe(2);
      
      expect(result.structure.sections).toHaveLength(3);
      expect(result.structure.sections[0].title).toBe('Main Title');
    });

    it('should extract lists from markdown', () => {
      const markdown = `
# List Examples

- Unordered item 1
- Unordered item 2
  - Nested item

1. Ordered item 1
2. Ordered item 2
      `;

      const result = ContentExtractionUtils.extractFromMarkdown(markdown);
      
      expect(result.structure.lists.length).toBeGreaterThan(0);
      
      const unorderedItems = result.structure.lists.filter(item => item.type === 'unordered');
      const orderedItems = result.structure.lists.filter(item => item.type === 'ordered');
      
      expect(unorderedItems.length).toBeGreaterThan(0);
      expect(orderedItems.length).toBeGreaterThan(0);
      
      const nestedItem = result.structure.lists.find(item => item.level > 0);
      expect(nestedItem).toBeDefined();
    });

    it('should extract links from markdown', () => {
      const markdown = `
# Links

Check out [Google](https://google.com) and [GitHub](https://github.com).
      `;

      const result = ContentExtractionUtils.extractFromMarkdown(markdown);
      
      expect(result.structure.links).toHaveLength(2);
      expect(result.structure.links[0].text).toBe('Google');
      expect(result.structure.links[0].url).toBe('https://google.com');
      expect(result.structure.links[1].text).toBe('GitHub');
      expect(result.structure.links[1].url).toBe('https://github.com');
    });
  });

  describe('extractFromHtml', () => {
    it('should extract clean text from HTML', () => {
      const html = `
<html>
<head><title>Test</title></head>
<body>
  <h1>Main Title</h1>
  <p>This is a <strong>bold</strong> text with <em>italic</em>.</p>
  <script></script>
  <style>body { color: red; }</style>
  <a href="https://example.com">Link text</a>
</body>
</html>
      `;

      const result = ContentExtractionUtils.extractFromHtml(html);
      
      expect(result.text).toContain('Main Title');
      expect(result.text).toContain('bold text with italic');
      expect(result.text).toContain('Link text');
      expect(result.text).not.toContain('<h1>');
      expect(result.text).not.toContain('<script>');
      expect(result.text).not.toContain('console.log');
      expect(result.text).not.toContain('color: red');
    });

    it('should parse HTML structure correctly', () => {
      const html = `
<h1>Main Title</h1>
<h2>Subtitle</h2>
<h3>Sub-subtitle</h3>
<a href="https://example.com">Example Link</a>
<a href="https://test.com">Test Link</a>
      `;

      const result = ContentExtractionUtils.extractFromHtml(html);
      
      expect(result.structure.headings).toHaveLength(3);
      expect(result.structure.headings[0].text).toBe('Main Title');
      expect(result.structure.headings[0].level).toBe(1);
      
      expect(result.structure.links).toHaveLength(2);
      expect(result.structure.links[0].text).toBe('Example Link');
      expect(result.structure.links[0].url).toBe('https://example.com');
    });
  });

  describe('extractFromJson', () => {
    it('should extract text from JSON object', () => {
      const jsonContent = JSON.stringify({
        title: 'Test Document',
        description: 'This is a test document',
        author: 'John Doe',
        tags: ['test', 'document', 'example'],
        metadata: {
          created: '2024-01-01',
          version: 1.0
        }
      });

      const result = ContentExtractionUtils.extractFromJson(jsonContent);
      
      expect(result.text).toContain('Test Document');
      expect(result.text).toContain('This is a test document');
      expect(result.text).toContain('John Doe');
      expect(result.text).toContain('test');
      expect(result.text).toContain('2024-01-01');
    });

    it('should create sections from JSON properties', () => {
      const jsonContent = JSON.stringify({
        introduction: 'This is the introduction',
        methodology: 'This describes the methodology',
        results: 'These are the results'
      });

      const result = ContentExtractionUtils.extractFromJson(jsonContent);
      
      expect(result.structure.sections).toHaveLength(3);
      expect(result.structure.sections[0].title).toBe('introduction');
      expect(result.structure.sections[1].title).toBe('methodology');
      expect(result.structure.sections[2].title).toBe('results');
    });

    it('should handle invalid JSON gracefully', () => {
      const invalidJson = '{ invalid json content }';
      
      const result = ContentExtractionUtils.extractFromJson(invalidJson);
      
      // Should fallback to plain text extraction
      expect(result.text).toBe(invalidJson.trim());
      expect(result.metadata.content_type).toBe('text');
    });
  });

  describe('extractFromCsv', () => {
    it('should extract text from CSV content', () => {
      const csvContent = `
Name,Age,City
"John Doe",30,"New York"
"Jane Smith",25,"Los Angeles"
"Bob Johnson",35,"Chicago"
      `.trim();

      const result = ContentExtractionUtils.extractFromCsv(csvContent);
      
      expect(result.text).toContain('John Doe');
      expect(result.text).toContain('New York');
      expect(result.text).toContain('Jane Smith');
      expect(result.text).not.toContain('"');
      expect(result.text).not.toContain(',');
    });

    it('should parse CSV structure into tables', () => {
      const csvContent = `
Name,Age,City
John,30,NYC
Jane,25,LA
      `.trim();

      const result = ContentExtractionUtils.extractFromCsv(csvContent);
      
      expect(result.structure.tables).toHaveLength(1);
      expect(result.structure.tables[0].headers).toEqual(['Name', 'Age', 'City']);
      expect(result.structure.tables[0].rows).toHaveLength(2);
      expect(result.structure.tables[0].rows[0]).toEqual(['John', '30', 'NYC']);
    });
  });

  describe('extractFromSource', () => {
    it('should route to correct extraction method based on source type', () => {
      const markdownSource: Source = {
        id: '1',
        title: 'Test Markdown',
        content: '# Title\n\nContent here.',
        source_type: 'markdown',
        created_at: '2024-01-01',
        updated_at: '2024-01-01'
      };

      const result = ContentExtractionUtils.extractFromSource(markdownSource);
      
      expect(result.metadata.content_type).toBe('markdown');
      expect(result.structure.headings).toHaveLength(1);
      expect(result.structure.headings[0].text).toBe('Title');
    });

    it('should handle unknown source types as plain text', () => {
      const unknownSource: Source = {
        id: '1',
        title: 'Unknown Type',
        content: 'Some content here.',
        source_type: 'unknown',
        created_at: '2024-01-01',
        updated_at: '2024-01-01'
      };

      const result = ContentExtractionUtils.extractFromSource(unknownSource);
      
      expect(result.metadata.content_type).toBe('text');
      expect(result.text).toBe('Some content here.');
    });
  });

  describe('metadata generation', () => {
    it('should generate accurate metadata', () => {
      const content = `
This is a test document with multiple sentences. It has several paragraphs to test the metadata generation.

This is the second paragraph. It contains more text for testing purposes.

Final paragraph here.
      `.trim();

      const result = ContentExtractionUtils.extractFromPlainText(content);
      
      expect(result.metadata.word_count).toBeGreaterThan(0);
      expect(result.metadata.character_count).toBe(content.length);
      expect(result.metadata.paragraph_count).toBe(3);
      expect(result.metadata.sentence_count).toBeGreaterThan(3);
      expect(result.metadata.reading_time_minutes).toBeGreaterThan(0);
      expect(result.metadata.content_type).toBe('text');
    });
  });

  describe('utility methods', () => {
    it('should generate content summary', () => {
      const extractedContent = {
        text: 'This is the first sentence. This is the second sentence. This is the third sentence. This is the fourth sentence.',
        structure: {
          sections: [],
          headings: [],
          lists: [],
          tables: [],
          links: []
        },
        metadata: {
          word_count: 20,
          character_count: 100,
          paragraph_count: 1,
          sentence_count: 4,
          reading_time_minutes: 1,
          content_type: 'text'
        }
      };

      const summary = ContentExtractionUtils.getContentSummary(extractedContent);
      
      expect(summary).toContain('This is the first sentence');
      expect(summary).toContain('This is the second sentence');
      expect(summary).toContain('This is the third sentence');
      expect(summary.split('.').length - 1).toBeLessThanOrEqual(3); // Should limit to 3 sentences
    });

    it('should extract key phrases', () => {
      const extractedContent = {
        text: 'User Authentication System manages User Login Process. The Authentication Module handles User Credentials and User Authentication.',
        structure: {
          sections: [],
          headings: [],
          lists: [],
          tables: [],
          links: []
        },
        metadata: {
          word_count: 15,
          character_count: 120,
          paragraph_count: 1,
          sentence_count: 2,
          reading_time_minutes: 1,
          content_type: 'text'
        }
      };

      const keyPhrases = ContentExtractionUtils.extractKeyPhrases(extractedContent, 5);
      
      expect(keyPhrases.length).toBeGreaterThan(0);
      expect(keyPhrases.length).toBeLessThanOrEqual(5);
      
      // Should include repeated important phrases
      const authenticationPhrases = keyPhrases.filter(phrase => 
        phrase.includes('authentication') || phrase.includes('user')
      );
      expect(authenticationPhrases.length).toBeGreaterThan(0);
    });
  });

  describe('edge cases', () => {
    it('should handle empty content', () => {
      const result = ContentExtractionUtils.extractFromPlainText('');
      
      expect(result.text).toBe('');
      expect(result.metadata.word_count).toBe(0);
      expect(result.metadata.character_count).toBe(0);
      expect(result.structure.sections).toHaveLength(0);
    });

    it('should handle content with only whitespace', () => {
      const result = ContentExtractionUtils.extractFromPlainText('   \n\n   \t   ');
      
      expect(result.text).toBe('');
      expect(result.metadata.word_count).toBe(0);
    });

    it('should handle very long content', () => {
      const longContent = 'word '.repeat(10000) + 'end';
      const result = ContentExtractionUtils.extractFromPlainText(longContent);
      
      expect(result.metadata.word_count).toBe(10001);
      expect(result.metadata.reading_time_minutes).toBeGreaterThan(50); // Should be around 50 minutes
    });
  });
});