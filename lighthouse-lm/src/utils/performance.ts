import { useEffect, useRef } from 'react';

// Performance monitoring utilities
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number[]> = new Map();
  private observers: PerformanceObserver[] = [];

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  // Track component render times
  trackRender(componentName: string, renderTime: number): void {
    if (!this.metrics.has(componentName)) {
      this.metrics.set(componentName, []);
    }
    this.metrics.get(componentName)!.push(renderTime);
    
    // Keep only last 100 measurements
    const measurements = this.metrics.get(componentName)!;
    if (measurements.length > 100) {
      measurements.shift();
    }
  }

  // Get average render time for a component
  getAverageRenderTime(componentName: string): number {
    const measurements = this.metrics.get(componentName);
    if (!measurements || measurements.length === 0) return 0;
    
    const sum = measurements.reduce((acc, time) => acc + time, 0);
    return sum / measurements.length;
  }

  // Get all performance metrics
  getAllMetrics(): Record<string, { average: number; count: number; latest: number }> {
    const result: Record<string, { average: number; count: number; latest: number }> = {};
    
    this.metrics.forEach((measurements, componentName) => {
      if (measurements.length > 0) {
        const sum = measurements.reduce((acc, time) => acc + time, 0);
        result[componentName] = {
          average: sum / measurements.length,
          count: measurements.length,
          latest: measurements[measurements.length - 1]
        };
      }
    });
    
    return result;
  }

  // Initialize performance observers
  initializeObservers(): void {
    if (typeof window === 'undefined') return;

    // Observe long tasks
    if ('PerformanceObserver' in window) {
      const longTaskObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.duration > 50) {
            console.warn(`Long task detected: ${entry.duration}ms`, entry);
          }
        });
      });
      
      try {
        longTaskObserver.observe({ entryTypes: ['longtask'] });
        this.observers.push(longTaskObserver);
      } catch (e) {
        // Long task API not supported
      }

      // Observe layout shifts
      const layoutShiftObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry: any) => {
          if (entry.value > 0.1) {
            console.warn(`Layout shift detected: ${entry.value}`, entry);
          }
        });
      });
      
      try {
        layoutShiftObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.push(layoutShiftObserver);
      } catch (e) {
        // Layout shift API not supported
      }
    }
  }

  // Clean up observers
  cleanup(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// React hook for tracking component render performance
export function useRenderPerformance(componentName: string) {
  const renderStartTime = useRef<number>(0);
  const monitor = PerformanceMonitor.getInstance();

  useEffect(() => {
    renderStartTime.current = performance.now();
  });

  useEffect(() => {
    const renderTime = performance.now() - renderStartTime.current;
    monitor.trackRender(componentName, renderTime);
  });

  return {
    getAverageRenderTime: () => monitor.getAverageRenderTime(componentName),
    getAllMetrics: () => monitor.getAllMetrics()
  };
}

// Bundle size analyzer
export class BundleAnalyzer {
  static analyzeChunkSizes(): void {
    if (typeof window === 'undefined') return;

    // Get all script tags
    const scripts = Array.from(document.querySelectorAll('script[src]'));
    const chunks: { name: string; size: number }[] = [];

    scripts.forEach(script => {
      const src = script.getAttribute('src');
      if (src && src.includes('/js/')) {
        // Extract chunk name from filename
        const filename = src.split('/').pop() || '';
        const chunkName = filename.split('-')[0] || 'unknown';
        
        // Estimate size (this is approximate)
        fetch(src, { method: 'HEAD' })
          .then(response => {
            const contentLength = response.headers.get('content-length');
            if (contentLength) {
              chunks.push({
                name: chunkName,
                size: parseInt(contentLength, 10)
              });
            }
          })
          .catch(() => {
            // Ignore fetch errors
          });
      }
    });

    // Log chunk sizes after a delay to allow fetches to complete
    setTimeout(() => {
      if (chunks.length > 0) {
        console.table(chunks.sort((a, b) => b.size - a.size));
      }
    }, 2000);
  }

  static getMemoryUsage(): any | null {
    if (typeof window !== 'undefined' && 'memory' in performance) {
      return (performance as any).memory;
    }
    return null;
  }
}

// Memory usage tracker
export function trackMemoryUsage(): void {
  if (typeof window === 'undefined') return;

  const logMemoryUsage = () => {
    const memory = BundleAnalyzer.getMemoryUsage();
    if (memory) {
      console.log('Memory Usage:', {
        used: `${Math.round(memory.usedJSHeapSize / 1024 / 1024)} MB`,
        total: `${Math.round(memory.totalJSHeapSize / 1024 / 1024)} MB`,
        limit: `${Math.round(memory.jsHeapSizeLimit / 1024 / 1024)} MB`
      });
    }
  };

  // Log memory usage every 30 seconds in development
  if (process.env.NODE_ENV === 'development') {
    setInterval(logMemoryUsage, 30000);
  }
}

// Initialize performance monitoring
export function initializePerformanceMonitoring(): void {
  if (typeof window === 'undefined') return;

  const monitor = PerformanceMonitor.getInstance();
  monitor.initializeObservers();
  
  // Track memory usage
  trackMemoryUsage();
  
  // Analyze bundle sizes
  BundleAnalyzer.analyzeChunkSizes();
  
  // Clean up on page unload
  window.addEventListener('beforeunload', () => {
    monitor.cleanup();
  });
}