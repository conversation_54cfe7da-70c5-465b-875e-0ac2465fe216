import { Citation } from '../types/message';
import { Source } from '@/services/insights-api';

/**
 * Convert a Source object to a Citation object
 * This utility handles the transformation between different data structures
 * used in the application for source and citation management.
 */
export const sourceToCitation = (source: Source): Citation => {
  return {
    citation_id: parseInt(source.id) || 0,
    source_id: source.id,
    source_title: source.title || 'Untitled',
    source_type: source.source_type || 'document',
    chunk_lines_from: 0,
    chunk_lines_to: 0,
    chunk_index: 0,
    excerpt: source.content || ''
  };
};

/**
 * Convert multiple Source objects to Citation objects
 */
export const sourcesToCitations = (sources: Source[]): Citation[] => {
  return sources.map(sourceToCitation);
};

/**
 * Extract citation information from a source for display purposes
 */
export const getCitationDisplayInfo = (citation: Citation) => {
  return {
    title: citation.source_title,
    type: citation.source_type,
    excerpt: citation.excerpt ? citation.excerpt.substring(0, 200) + '...' : '',
    hasContent: Boolean(citation.excerpt && citation.excerpt.length > 0)
  };
};

/**
 * Validate if a citation has the required fields
 */
export const isValidCitation = (citation: Citation): boolean => {
  return Boolean(
    citation.citation_id &&
    citation.source_id &&
    citation.source_title &&
    citation.source_type
  );
};

/**
 * Create a default citation object
 */
export const createDefaultCitation = (sourceId: string): Citation => {
  return {
    citation_id: 0,
    source_id: sourceId,
    source_title: 'Untitled',
    source_type: 'document',
    chunk_lines_from: 0,
    chunk_lines_to: 0,
    chunk_index: 0,
    excerpt: ''
  };
};