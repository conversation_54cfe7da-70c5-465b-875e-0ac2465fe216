import { useState, useCallback } from 'react';
import { useNotificationService as useNotification } from '../services/notification';
import { useErrorHandler } from './error';

/**
 * Consolidated bulk operations utilities
 */

export interface BulkOperationOptions {
  showProgress?: boolean;
  batchSize?: number;
  continueOnError?: boolean;
  onProgress?: (completed: number, total: number) => void;
  onItemSuccess?: (item: any, index: number) => void;
  onItemError?: (item: any, index: number, error: Error) => void;
}

export interface BulkOperationResult {
  success: boolean;
  successful: any[];
  failed: { item: any; error: Error }[];
  total: number;
  completed: number;
}

/**
 * Hook for consolidated bulk operations
 */
export const useBulkOperations = () => {
  const { success, error } = useNotification();
  const { handleError } = useErrorHandler();

  const executeBulkOperation = useCallback(async <T, R>(
    items: T[],
    operation: (item: T, index: number) => Promise<R>,
    options: BulkOperationOptions = {}
  ): Promise<BulkOperationResult> => {
    const {
      showProgress = true,
      batchSize = 5,
      continueOnError = true,
      onProgress,
      onItemSuccess,
      onItemError,
    } = options;

    const results: R[] = [];
    const failures: { item: T; error: Error }[] = [];
    let completed = 0;

    // Process items in batches
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchPromises = batch.map(async (item, batchIndex) => {
        const globalIndex = i + batchIndex;

        try {
          const result = await operation(item, globalIndex);
          results.push(result);
          onItemSuccess?.(item, globalIndex);
          return { success: true, result, item, index: globalIndex };
        } catch (err) {
          const error = err as Error;
          failures.push({ item, error });
          onItemError?.(item, globalIndex, error);

          if (!continueOnError) {
            throw error;
          }

          return { success: false, error, item, index: globalIndex };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      completed += batch.length;

      if (showProgress && onProgress) {
        onProgress(completed, items.length);
      }
    }

    const operationResult: BulkOperationResult = {
      success: failures.length === 0,
      successful: results,
      failed: failures,
      total: items.length,
      completed,
    };

    // Show notification based on results
    if (operationResult.success) {
      success.bulkOperation(items.length, 'item', 'Processed');
    } else if (failures.length === items.length) {
      error.bulkOperationFailed('process all items', `${failures.length} items failed`);
    } else {
      // Partial success
    }

    return operationResult;
  }, [success, error]);

  const bulkDelete = useCallback(async (
    items: any[],
    deleteFn: (item: any) => Promise<void>,
    itemType: string = 'item',
    options: BulkOperationOptions = {}
  ): Promise<BulkOperationResult> => {
    const result = await executeBulkOperation(
      items,
      deleteFn,
      {
        ...options,
        onItemSuccess: (item) => console.log(`Successfully processed ${itemType}:`, item),
        onItemError: (item, _, err) => console.error(`Failed to process ${itemType}:`, item, err)
    });

    if (result.success) {
      success.bulkOperation(result.successful.length, itemType, 'Deleted');
    } else {
      error.bulkOperationFailed('delete', `${result.failed.length} items failed to delete`);
    }

    return result;
  }, [executeBulkOperation, success, error]);

  const bulkExport = useCallback(async (
    items: any[],
    exportFn: (item: any) => Promise<any>,
    itemType: string = 'item',
    options: BulkOperationOptions = {}
  ): Promise<BulkOperationResult> => {
    const result = await executeBulkOperation(
      items,
      exportFn,
      {
        ...options,
        onItemSuccess: (item) => console.log(`Successfully exported ${itemType}:`, item),
        onItemError: (item, _, err) => console.error(`Failed to export ${itemType}:`, item, err)
      }
    );

    if (result.success) {
      success.bulkOperation(result.successful.length, itemType, 'Exported');
    } else {
      error.bulkOperationFailed('export', `${result.failed.length} items failed to export`);
    }

    return result;
  }, [executeBulkOperation, success, error]);

  const bulkArchive = useCallback(async (
    items: any[],
    archiveFn: (item: any) => Promise<void>,
    itemType: string = 'item',
    options: BulkOperationOptions = {}
  ): Promise<BulkOperationResult> => {
    const result = await executeBulkOperation(
      items,
      archiveFn,
      {
        ...options,
        onItemSuccess: (item) => console.log(`Successfully archived ${itemType}:`, item),
        onItemError: (item, _, err) => console.error(`Failed to archive ${itemType}:`, item, err)
      }
    );

    if (result.success) {
      success.bulkOperation(result.successful.length, itemType, 'Archived');
    } else {
      error.bulkOperationFailed('archive', `${result.failed.length} items failed to archive`);
    }

    return result;
  }, [executeBulkOperation, success, error]);

  return {
    executeBulkOperation,
    bulkDelete,
    bulkExport,
    bulkArchive,
  };
};

/**
 * Hook for managing bulk selection state
 */
export const useBulkSelection = <T>(items: T[] = []) => {
  const [selectedItems, setSelectedItems] = useState<Set<T>>(new Set());

  const toggleItem = useCallback((item: T) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev);
      const itemKey = JSON.stringify(item); // Simple key generation

      if (newSet.has(item)) {
        newSet.delete(item);
      } else {
        newSet.add(item);
      }

      return newSet;
    });
  }, []);

  const selectAll = useCallback(() => {
    setSelectedItems(new Set(items));
  }, [items]);

  const deselectAll = useCallback(() => {
    setSelectedItems(new Set());
  }, []);

  const selectRange = useCallback((startItem: T, endItem: T) => {
    const startIndex = items.indexOf(startItem);
    const endIndex = items.indexOf(endItem);

    if (startIndex !== -1 && endIndex !== -1) {
      const [from, to] = startIndex < endIndex ? [startIndex, endIndex] : [endIndex, startIndex];
      const rangeItems = items.slice(from, to + 1);
      setSelectedItems(new Set(rangeItems));
    }
  }, [items]);

  const isSelected = useCallback((item: T) => {
    return selectedItems.has(item);
  }, [selectedItems]);

  const selectedCount = selectedItems.size;
  const hasSelection = selectedCount > 0;
  const isAllSelected = selectedCount === items.length;

  return {
    selectedItems: Array.from(selectedItems),
    selectedCount,
    hasSelection,
    isAllSelected,
    toggleItem,
    selectAll,
    deselectAll,
    selectRange,
    isSelected,
  };
};

/**
 * Common bulk operation patterns
 */
export const BulkOperationPatterns = {
  // Source operations
  deleteSources: async (sourceIds: string[], deleteFn: (id: string) => Promise<void>) => {
    const { bulkDelete } = useBulkOperations();
    return bulkDelete(sourceIds, deleteFn, 'source');
  },

  exportSources: async (sources: any[], exportFn: (source: any) => Promise<any>) => {
    const { bulkExport } = useBulkOperations();
    return bulkExport(sources, exportFn, 'source');
  },

  archiveSources: async (sourceIds: string[], archiveFn: (id: string) => Promise<void>) => {
    const { bulkArchive } = useBulkOperations();
    return bulkArchive(sourceIds, archiveFn, 'source');
  },

  // Diagram operations
  deleteDiagrams: async (diagramIds: number[], deleteFn: (id: number) => Promise<void>) => {
    const { bulkDelete } = useBulkOperations();
    return bulkDelete(diagramIds, deleteFn, 'diagram');
  },

  exportDiagrams: async (diagrams: any[], exportFn: (diagram: any) => Promise<any>) => {
    const { bulkExport } = useBulkOperations();
    return bulkExport(diagrams, exportFn, 'diagram');
  },

  // Generic operations
  processItems: async <T>(
    items: T[],
    processFn: (item: T) => Promise<any>,
    itemType: string
  ) => {
    const { executeBulkOperation } = useBulkOperations();
    return executeBulkOperation(items, processFn, {
      onItemSuccess: (item) => console.log(`Successfully processed ${itemType}:`, item),
      onItemError: (item, _, err) => console.error(`Failed to process ${itemType}:`, item, err)
    });
  },
};

/**
 * Progress tracking utilities
 */
export const ProgressUtils = {
  createProgressTracker: (total: number, onProgress?: (progress: number) => void) => {
    let completed = 0;

    return {
      increment: () => {
        completed++;
        const progress = Math.round((completed / total) * 100);
        onProgress?.(progress);
      },
      getProgress: () => Math.round((completed / total) * 100),
      isComplete: () => completed === total,
    };
  },

  formatProgress: (completed: number, total: number): string => {
    return `${completed}/${total} (${Math.round((completed / total) * 100)}%)`;
  },
};