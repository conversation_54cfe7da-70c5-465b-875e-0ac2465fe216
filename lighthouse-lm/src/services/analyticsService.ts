import { 
  AnalyticsDashboardData, 
  AnalyticsTimeRange, 
  SessionMetrics,
  ContentMetrics,
  ProductivityMetrics,
  FeatureUsage,
  SearchAnalytics,
  CollaborationMetrics,
  GoalMetrics,
  InsightData,
  AnalyticsConfig,
  ChartDataPoint,
  HeatmapDataPoint,
  AnalyticsFilters,
  ExportOptions 
} from '../types/analytics';
import { tauriService } from './tauriService';

class AnalyticsService {
  private readonly STORAGE_PREFIX = 'analytics_';
  private sessionStartTime: Date | null = null;
  private currentSessionId: string | null = null;
  private activityBuffer: any[] = [];
  private lastActivityTime: Date = new Date();

  constructor() {
    this.initializeSession();
    this.setupActivityTracking();
  }

  // Session Management
  private initializeSession() {
    this.sessionStartTime = new Date();
    this.currentSessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.trackEvent('session_start', { sessionId: this.currentSessionId });
  }

  private setupActivityTracking() {
    // Track user activity
    ['click', 'keydown', 'mousemove', 'scroll'].forEach(event => {
      document.addEventListener(event, () => {
        this.lastActivityTime = new Date();
        this.flushActivityBuffer();
      }, { passive: true });
    });

    // Track window focus/blur
    window.addEventListener('focus', () => {
      this.trackEvent('window_focus');
    });
    
    window.addEventListener('blur', () => {
      this.trackEvent('window_blur');
    });

    // Flush activity buffer every 30 seconds
    setInterval(() => {
      this.flushActivityBuffer();
    }, 30000);

    // Track session end on page unload
    window.addEventListener('beforeunload', () => {
      this.endSession();
    });
  }

  private flushActivityBuffer() {
    if (this.activityBuffer.length === 0) return;
    
    // Store activity data
    this.persistData(`${this.STORAGE_PREFIX}activity_${Date.now()}`, this.activityBuffer);
    this.activityBuffer = [];
  }

  // Core Analytics Methods
  async getDashboardData(filters: AnalyticsFilters): Promise<AnalyticsDashboardData> {
    try {
      const [
        sessions,
        contentMetrics,
        productivityMetrics,
        featureUsage,
        searchAnalytics,
        collaborationMetrics,
        goals,
        insights,
        config
      ] = await Promise.all([
        this.getSessionMetrics(filters.timeRange),
        this.getContentMetrics(filters.timeRange, filters.notebooks),
        this.getProductivityMetrics(filters.timeRange),
        this.getFeatureUsage(filters.timeRange),
        this.getSearchAnalytics(filters.timeRange),
        this.getCollaborationMetrics(filters.timeRange),
        this.getGoals(),
        this.getInsights(filters.timeRange),
        this.getConfig()
      ]);

      return {
        timeRange: filters.timeRange,
        sessions,
        contentMetrics,
        productivityMetrics,
        featureUsage,
        searchAnalytics,
        collaborationMetrics,
        goals,
        insights,
        config
      };
    } catch (error) {
      console.error('Failed to load analytics dashboard data:', error);
      throw error;
    }
  }

  async getSessionMetrics(timeRange: AnalyticsTimeRange): Promise<SessionMetrics[]> {
    const sessions = await this.getStoredData('sessions', timeRange);
    
    return sessions.map((session: any) => ({
      sessionId: session.id,
      startTime: new Date(session.startTime),
      endTime: session.endTime ? new Date(session.endTime) : undefined,
      duration: session.duration || 0,
      notebookId: session.notebookId || '',
      userId: session.userId,
      activeTime: session.activeTime || 0,
      idleTime: session.idleTime || 0,
      focusMode: session.focusMode || 'split',
      devices: session.devices || []
    }));
  }

  async getContentMetrics(timeRange: AnalyticsTimeRange, notebooks?: string[]): Promise<ContentMetrics[]> {
    const data = await this.getStoredData('content', timeRange);
    
    // Filter by notebooks if specified
    const filtered = notebooks?.length 
      ? data.filter((item: any) => notebooks.includes(item.notebookId))
      : data;

    // Aggregate metrics
    const grouped = this.groupByNotebook(filtered);
    
    return Object.entries(grouped).map(([notebookId, items]: [string, any]) => ({
      notebookId,
      notesCreated: items.filter((i: any) => i.type === 'note_created').length,
      notesModified: items.filter((i: any) => i.type === 'note_modified').length,
      sourcesAdded: items.filter((i: any) => i.type === 'source_added').length,
      sourcesAnalyzed: items.filter((i: any) => i.type === 'source_analyzed').length,
      chatMessages: items.filter((i: any) => i.type === 'chat_message').length,
      studioDocuments: items.filter((i: any) => i.type === 'studio_document').length,
      totalWords: items.reduce((sum: number, i: any) => sum + (i.wordCount || 0), 0),
      totalCharacters: items.reduce((sum: number, i: any) => sum + (i.charCount || 0), 0),
      contentTypes: this.aggregateContentTypes(items)
    }));
  }

  async getProductivityMetrics(timeRange: AnalyticsTimeRange): Promise<ProductivityMetrics> {
    const sessions = await this.getSessionMetrics(timeRange);
    const activities = await this.getStoredData('activities', timeRange);

    const totalActiveTime = sessions.reduce((sum, s) => sum + s.activeTime, 0);
    const totalSessions = sessions.length;
    const contextSwitches = activities.filter((a: any) => a.type === 'context_switch').length;
    const completedTasks = activities.filter((a: any) => a.type === 'task_completed').length;

    return {
      focusScore: this.calculateFocusScore(sessions, activities),
      productivityScore: this.calculateProductivityScore(activities),
      distractionCount: activities.filter((a: any) => a.type === 'distraction').length,
      deepWorkMinutes: this.calculateDeepWorkTime(activities),
      shallowWorkMinutes: totalActiveTime - this.calculateDeepWorkTime(activities),
      contextSwitches,
      averageTaskDuration: this.calculateAverageTaskDuration(activities),
      completedTasks,
      efficiency: totalActiveTime > 0 ? completedTasks / (totalActiveTime / 60) : 0
    };
  }

  async getFeatureUsage(timeRange: AnalyticsTimeRange): Promise<FeatureUsage[]> {
    const data = await this.getStoredData('feature_usage', timeRange);
    const grouped = this.groupBy(data, 'feature');

    return Object.entries(grouped).map(([featureName, items]: [string, any]) => ({
      featureName,
      usageCount: items.length,
      timeSpent: items.reduce((sum: number, i: any) => sum + (i.duration || 0), 0),
      lastUsed: new Date(Math.max(...items.map((i: any) => new Date(i.timestamp).getTime()))),
      userSatisfaction: this.calculateAverageSatisfaction(items)
    }));
  }

  async getSearchAnalytics(timeRange: AnalyticsTimeRange): Promise<SearchAnalytics[]> {
    const data = await this.getStoredData('search', timeRange);
    
    return data.map((item: any) => ({
      query: item.query,
      timestamp: new Date(item.timestamp),
      resultCount: item.resultCount || 0,
      clickedResults: item.clickedResults || 0,
      searchType: item.searchType || 'global',
      timeToFirstClick: item.timeToFirstClick,
      sessionId: item.sessionId || ''
    }));
  }

  async getCollaborationMetrics(timeRange: AnalyticsTimeRange): Promise<CollaborationMetrics> {
    const data = await this.getStoredData('collaboration', timeRange);
    
    return {
      sharedNotebooks: data.filter((d: any) => d.type === 'shared_notebook').length,
      collaborators: new Set(data.map((d: any) => d.collaboratorId).filter(Boolean)).size,
      commentsGiven: data.filter((d: any) => d.type === 'comment_given').length,
      commentsReceived: data.filter((d: any) => d.type === 'comment_received').length,
      reviewsCompleted: data.filter((d: any) => d.type === 'review_completed').length,
      sharingFrequency: this.calculateSharingFrequency(data, timeRange)
    };
  }

  async getGoals(): Promise<GoalMetrics[]> {
    const stored = await this.getStoredValue('goals') || [];
    return stored.map((goal: any) => ({
      goalId: goal.id,
      title: goal.title,
      targetValue: goal.targetValue,
      currentValue: goal.currentValue || 0,
      unit: goal.unit,
      deadline: goal.deadline ? new Date(goal.deadline) : undefined,
      category: goal.category,
      status: goal.status || 'active',
      progress: Math.min(100, (goal.currentValue / goal.targetValue) * 100)
    }));
  }

  async getInsights(timeRange: AnalyticsTimeRange): Promise<InsightData[]> {
    // Generate AI-powered insights based on analytics data
    const dashboardData = await this.getDashboardData({ timeRange });
    return this.generateInsights(dashboardData);
  }

  async getConfig(): Promise<AnalyticsConfig> {
    const config = await this.getStoredValue('config');
    return {
      trackingEnabled: config?.trackingEnabled ?? true,
      anonymousMode: config?.anonymousMode ?? false,
      dataRetentionDays: config?.dataRetentionDays ?? 365,
      reportingFrequency: config?.reportingFrequency ?? 'weekly',
      goalReminders: config?.goalReminders ?? true,
      insightNotifications: config?.insightNotifications ?? true,
      performanceAlerts: config?.performanceAlerts ?? false
    };
  }

  // Chart Data Methods
  async getTimeSeriesData(
    metric: string, 
    timeRange: AnalyticsTimeRange, 
    interval: 'hour' | 'day' | 'week' | 'month' = 'day'
  ): Promise<ChartDataPoint[]> {
    const data = await this.getStoredData(metric, timeRange);
    return this.aggregateByTimeInterval(data, interval);
  }

  async getHeatmapData(timeRange: AnalyticsTimeRange): Promise<HeatmapDataPoint[]> {
    const sessions = await this.getSessionMetrics(timeRange);
    const heatmapData: HeatmapDataPoint[] = [];

    // Create 24x7 grid (hours x days of week)
    for (let day = 0; day < 7; day++) {
      for (let hour = 0; hour < 24; hour++) {
        const sessionsAtTime = sessions.filter(s => {
          const sessionStart = new Date(s.startTime);
          return sessionStart.getDay() === day && sessionStart.getHours() === hour;
        });

        const totalActivity = sessionsAtTime.reduce((sum, s) => sum + s.activeTime, 0);
        const intensity = totalActivity > 60 ? 'high' : totalActivity > 20 ? 'medium' : 'low';

        heatmapData.push({
          hour,
          day,
          value: totalActivity,
          intensity
        });
      }
    }

    return heatmapData;
  }

  // Event Tracking
  trackEvent(eventType: string, data: any = {}) {
    if (!this.isTrackingEnabled()) return;

    const event = {
      type: eventType,
      timestamp: new Date().toISOString(),
      sessionId: this.currentSessionId,
      data: { ...data }
    };

    this.activityBuffer.push(event);

    // Immediate persist for critical events
    if (['session_start', 'session_end', 'error'].includes(eventType)) {
      this.persistEvent(event);
    }
  }

  trackSessionActivity(notebookId: string, focusMode: string) {
    this.trackEvent('session_activity', { notebookId, focusMode });
  }

  trackContentCreation(type: string, notebookId: string, metadata: any = {}) {
    this.trackEvent('content_created', { type, notebookId, ...metadata });
  }

  trackFeatureUsage(feature: string, duration?: number, satisfaction?: number) {
    this.trackEvent('feature_usage', { feature, duration, satisfaction });
  }

  trackSearch(query: string, resultCount: number, searchType: string) {
    this.trackEvent('search', { query, resultCount, searchType });
  }

  // Goal Management
  async createGoal(goal: Omit<GoalMetrics, 'goalId' | 'progress'>): Promise<string> {
    const goals = await this.getGoals();
    const newGoal: GoalMetrics = {
      ...goal,
      goalId: `goal_${Date.now()}`,
      progress: 0
    };
    
    goals.push(newGoal);
    await this.storeValue('goals', goals);
    
    this.trackEvent('goal_created', { goalId: newGoal.goalId, category: goal.category });
    return newGoal.goalId;
  }

  async updateGoalProgress(goalId: string, currentValue: number): Promise<void> {
    const goals = await this.getGoals();
    const goalIndex = goals.findIndex(g => g.goalId === goalId);
    
    if (goalIndex !== -1) {
      goals[goalIndex].currentValue = currentValue;
      goals[goalIndex].progress = Math.min(100, (currentValue / goals[goalIndex].targetValue) * 100);
      
      if (goals[goalIndex].progress >= 100 && goals[goalIndex].status === 'active') {
        goals[goalIndex].status = 'completed';
        this.trackEvent('goal_completed', { goalId });
      }
      
      await this.storeValue('goals', goals);
    }
  }

  // Export Functionality
  async exportData(options: ExportOptions): Promise<Blob> {
    const data = await this.getDashboardData({ 
      timeRange: options.dateRange,
      notebooks: options.sections.includes('notebooks') ? undefined : []
    });

    switch (options.format) {
      case 'json':
        return new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      
      case 'csv':
        const csvData = this.convertToCSV(data, options);
        return new Blob([csvData], { type: 'text/csv' });
      
      case 'pdf':
        // This would require a PDF generation library
        throw new Error('PDF export not implemented yet');
      
      default:
        throw new Error(`Unsupported export format: ${options.format}`);
    }
  }

  // Private Helper Methods
  private async isTrackingEnabled(): Promise<boolean> {
    const config = await this.getConfig();
    return config.trackingEnabled;
  }

  private endSession() {
    if (this.sessionStartTime && this.currentSessionId) {
      const duration = (new Date().getTime() - this.sessionStartTime.getTime()) / (1000 * 60); // minutes
      this.trackEvent('session_end', { 
        duration,
        sessionId: this.currentSessionId 
      });
      this.flushActivityBuffer();
    }
  }

  private async getStoredData(type: string, timeRange: AnalyticsTimeRange): Promise<any[]> {
    try {
      const key = `${this.STORAGE_PREFIX}${type}`;
      const data = await tauriService.getAppState(key) || [];
      
      // Filter by time range
      return data.filter((item: any) => {
        const timestamp = new Date(item.timestamp);
        return timestamp >= timeRange.start && timestamp <= timeRange.end;
      });
    } catch (error) {
      console.warn(`Failed to get stored data for ${type}:`, error);
      return [];
    }
  }

  private async getStoredValue(key: string): Promise<any> {
    try {
      return await tauriService.getAppState(`${this.STORAGE_PREFIX}${key}`);
    } catch (error) {
      console.warn(`Failed to get stored value for ${key}:`, error);
      return null;
    }
  }

  private async storeValue(key: string, value: any): Promise<void> {
    try {
      await tauriService.setAppState(`${this.STORAGE_PREFIX}${key}`, value);
    } catch (error) {
      console.warn(`Failed to store value for ${key}:`, error);
    }
  }

  private async persistData(key: string, data: any): Promise<void> {
    try {
      const existing = await tauriService.getAppState(key) || [];
      const combined = Array.isArray(existing) ? [...existing, ...data] : data;
      await tauriService.setAppState(key, combined);
    } catch (error) {
      console.warn(`Failed to persist data for ${key}:`, error);
    }
  }

  private async persistEvent(event: any): Promise<void> {
    const key = `${this.STORAGE_PREFIX}events`;
    await this.persistData(key, [event]);
  }

  private groupByNotebook(data: any[]): Record<string, any[]> {
    return data.reduce((acc, item) => {
      const key = item.notebookId || 'unknown';
      if (!acc[key]) acc[key] = [];
      acc[key].push(item);
      return acc;
    }, {});
  }

  private groupBy(data: any[], key: string): Record<string, any[]> {
    return data.reduce((acc, item) => {
      const groupKey = item[key] || 'unknown';
      if (!acc[groupKey]) acc[groupKey] = [];
      acc[groupKey].push(item);
      return acc;
    }, {});
  }

  private aggregateContentTypes(items: any[]): Record<string, number> {
    return items.reduce((acc, item) => {
      const type = item.contentType || 'unknown';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});
  }

  private calculateFocusScore(sessions: SessionMetrics[], activities: any[]): number {
    const totalActiveTime = sessions.reduce((sum, s) => sum + s.activeTime, 0);
    const distractions = activities.filter(a => a.type === 'distraction').length;
    const contextSwitches = activities.filter(a => a.type === 'context_switch').length;
    
    if (totalActiveTime === 0) return 0;
    
    const distractionPenalty = Math.min(50, (distractions + contextSwitches) * 2);
    return Math.max(0, 100 - distractionPenalty);
  }

  private calculateProductivityScore(activities: any[]): number {
    const completedTasks = activities.filter(a => a.type === 'task_completed').length;
    const totalTasks = activities.filter(a => ['task_started', 'task_completed'].includes(a.type)).length;
    
    if (totalTasks === 0) return 0;
    
    return Math.round((completedTasks / totalTasks) * 100);
  }

  private calculateDeepWorkTime(activities: any[]): number {
    return activities
      .filter(a => a.type === 'deep_work_session')
      .reduce((sum, a) => sum + (a.duration || 0), 0);
  }

  private calculateAverageTaskDuration(activities: any[]): number {
    const taskDurations = activities
      .filter(a => a.type === 'task_completed' && a.duration)
      .map(a => a.duration);
    
    return taskDurations.length > 0 
      ? taskDurations.reduce((sum, d) => sum + d, 0) / taskDurations.length 
      : 0;
  }

  private calculateAverageSatisfaction(items: any[]): number | undefined {
    const ratings = items.map(i => i.satisfaction).filter(Boolean);
    return ratings.length > 0 
      ? ratings.reduce((sum, r) => sum + r, 0) / ratings.length 
      : undefined;
  }

  private calculateSharingFrequency(data: any[], timeRange: AnalyticsTimeRange): number {
    const shares = data.filter(d => d.type === 'shared_notebook');
    const days = Math.ceil((timeRange.end.getTime() - timeRange.start.getTime()) / (1000 * 60 * 60 * 24));
    return days > 0 ? shares.length / days : 0;
  }

  private aggregateByTimeInterval(data: any[], interval: string): ChartDataPoint[] {
    // Implementation for time series aggregation
    const grouped = this.groupBy(data, 'timestamp');
    // ... aggregation logic based on interval
    return Object.entries(grouped).map(([timestamp, items]) => ({
      timestamp: new Date(timestamp),
      value: items.length,
      metadata: { items }
    }));
  }

  private generateInsights(data: AnalyticsDashboardData): InsightData[] {
    const insights: InsightData[] = [];
    
    // Productivity insights
    if (data.productivityMetrics.focusScore < 60) {
      insights.push({
        id: `insight_focus_${Date.now()}`,
        type: 'suggestion',
        title: 'Low Focus Score Detected',
        description: `Your focus score is ${data.productivityMetrics.focusScore}%. Consider reducing distractions during work sessions.`,
        category: 'productivity',
        impact: 'medium',
        confidence: 85,
        generatedAt: new Date(),
        actionable: true,
        actions: [{
          label: 'Enable Focus Mode',
          action: 'enable_focus_mode',
          data: { duration: 60 }
        }]
      });
    }

    // Usage pattern insights
    const sessionsToday = data.sessions.filter(s => {
      const today = new Date();
      return s.startTime.toDateString() === today.toDateString();
    });

    if (sessionsToday.length === 0) {
      insights.push({
        id: `insight_usage_${Date.now()}`,
        type: 'suggestion',
        title: 'No Activity Today',
        description: 'You haven\'t used the app today. Consider setting a daily usage goal.',
        category: 'usage',
        impact: 'low',
        confidence: 100,
        generatedAt: new Date(),
        actionable: true,
        actions: [{
          label: 'Set Daily Goal',
          action: 'create_goal',
          data: { type: 'daily_usage', target: 30 }
        }]
      });
    }

    // Goal completion insights
    data.goals.filter(g => g.progress >= 100 && g.status === 'active').forEach(goal => {
      insights.push({
        id: `insight_goal_${goal.goalId}`,
        type: 'achievement',
        title: 'Goal Completed!',
        description: `Congratulations! You've completed your goal: ${goal.title}`,
        category: 'productivity',
        impact: 'high',
        confidence: 100,
        generatedAt: new Date(),
        actionable: false
      });
    });

    return insights;
  }

  private convertToCSV(data: AnalyticsDashboardData, options: ExportOptions): string {
    // Implementation for CSV conversion
    const headers = ['Date', 'Sessions', 'Active Time', 'Focus Score'];
    const rows = data.sessions.map(session => [
      session.startTime.toISOString().split('T')[0],
      '1',
      session.activeTime.toString(),
      data.productivityMetrics.focusScore.toString()
    ]);

    return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
  }
}

export const analyticsService = new AnalyticsService();
export default analyticsService;