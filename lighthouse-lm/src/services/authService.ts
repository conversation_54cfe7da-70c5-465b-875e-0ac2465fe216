// Authentication Service - Frontend integration with backend auth commands

import { tauriService } from './tauriService';
import { AuthUser } from './tauriService';

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
}

export interface AuthState {
  user: AuthUser | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

class AuthService {
  private static instance: AuthService;
  private authState: AuthState = {
    user: null,
    token: null,
    isAuthenticated: false,
    isLoading: false,
  };
  private listeners: Set<(state: AuthState) => void> = new Set();

  private constructor() {
    this.initializeAuth();
  }

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  // ============= Public API =============

  async register(data: RegisterData): Promise<AuthUser> {
    this.setLoading(true);
    try {
      const user = await tauriService.registerUser(data.username, data.email, data.password);
      await this.logError('AUTH_SUCCESS', `User registered successfully: ${user.username}`, 'Low', 'Authentication');
      return user;
    } catch (error) {
      await this.logError('AUTH_REGISTER_FAILED', `Registration failed: ${error}`, 'High', 'Authentication');
      throw error;
    } finally {
      this.setLoading(false);
    }
  }

  async login(credentials: LoginCredentials): Promise<void> {
    this.setLoading(true);
    try {
      const result = await tauriService.loginUser(credentials.username, credentials.password);
      
      // Store auth data
      this.authState.user = result.user;
      this.authState.token = result.token;
      this.authState.isAuthenticated = true;
      
      // Store token in backend API client
      await tauriService.setAuthToken(result.token);
      
      // Persist auth state
      await tauriService.setAppState('auth_user', result.user);
      await tauriService.setAppState('auth_token', result.token);
      
      await this.logError('AUTH_SUCCESS', `User logged in successfully: ${result.user.username}`, 'Low', 'Authentication');
      this.notifyListeners();
    } catch (error) {
      await this.logError('AUTH_LOGIN_FAILED', `Login failed: ${error}`, 'High', 'Authentication');
      throw error;
    } finally {
      this.setLoading(false);
    }
  }

  async logout(): Promise<void> {
    this.setLoading(true);
    try {
      await tauriService.logoutUser();
      await tauriService.clearAuthToken();
      
      // Clear persisted auth state
      await tauriService.deleteAppState('auth_user');
      await tauriService.deleteAppState('auth_token');
      
      // Reset local state
      this.authState.user = null;
      this.authState.token = null;
      this.authState.isAuthenticated = false;
      
      await this.logError('AUTH_SUCCESS', 'User logged out successfully', 'Low', 'Authentication');
      this.notifyListeners();
    } catch (error) {
      await this.logError('AUTH_LOGOUT_FAILED', `Logout failed: ${error}`, 'Medium', 'Authentication');
      throw error;
    } finally {
      this.setLoading(false);
    }
  }

  async validateSession(): Promise<boolean> {
    if (!this.authState.token) {
      return false;
    }
    
    try {
      const isValid = await tauriService.validateUserSession(this.authState.token);
      if (!isValid) {
        await this.logout();
      }
      return isValid;
    } catch (error) {
      await this.logError('AUTH_VALIDATION_FAILED', `Session validation failed: ${error}`, 'Medium', 'Authentication');
      await this.logout();
      return false;
    }
  }

  async updateProfile(updates: Partial<AuthUser>): Promise<AuthUser> {
    if (!this.authState.user) {
      throw new Error('No authenticated user');
    }
    
    this.setLoading(true);
    try {
      const updatedUser = await tauriService.updateUserProfile(this.authState.user.id, updates);
      this.authState.user = updatedUser;
      
      // Update persisted state
      await tauriService.setAppState('auth_user', updatedUser);
      
      await this.logError('AUTH_SUCCESS', `Profile updated successfully: ${updatedUser.username}`, 'Low', 'Authentication');
      this.notifyListeners();
      return updatedUser;
    } catch (error) {
      await this.logError('AUTH_UPDATE_FAILED', `Profile update failed: ${error}`, 'Medium', 'Authentication');
      throw error;
    } finally {
      this.setLoading(false);
    }
  }

  async changePassword(oldPassword: string, newPassword: string): Promise<void> {
    if (!this.authState.user) {
      throw new Error('No authenticated user');
    }
    
    this.setLoading(true);
    try {
      await tauriService.changeUserPassword(this.authState.user.id, oldPassword, newPassword);
      await this.logError('AUTH_SUCCESS', 'Password changed successfully', 'Low', 'Authentication');
    } catch (error) {
      await this.logError('AUTH_PASSWORD_CHANGE_FAILED', `Password change failed: ${error}`, 'High', 'Authentication');
      throw error;
    } finally {
      this.setLoading(false);
    }
  }

  // ============= State Management =============

  getAuthState(): AuthState {
    return { ...this.authState };
  }

  subscribe(listener: (state: AuthState) => void): () => void {
    this.listeners.add(listener);
    return () => {
      this.listeners.delete(listener);
    };
  }

  // ============= Private Methods =============

  private async initializeAuth(): Promise<void> {
    try {
      // Try to restore auth state from backend
      const [storedUser, storedToken] = await Promise.all([
        tauriService.getAppState('auth_user').catch(() => null),
        tauriService.getAppState('auth_token').catch(() => null),
      ]);
      
      if (storedUser && storedToken) {
        this.authState.user = storedUser;
        this.authState.token = storedToken;
        this.authState.isAuthenticated = true;
        
        // Set token in API client
        await tauriService.setAuthToken(storedToken);
        
        // Validate the session
        const isValid = await this.validateSession();
        if (!isValid) {
          return; // validateSession will handle cleanup
        }
      }
      
      this.notifyListeners();
    } catch (error) {
      await this.logError('AUTH_INIT_FAILED', `Auth initialization failed: ${error}`, 'Medium', 'Authentication');
    }
  }

  private setLoading(loading: boolean): void {
    this.authState.isLoading = loading;
    this.notifyListeners();
  }

  private notifyListeners(): void {
    const state = this.getAuthState();
    this.listeners.forEach(listener => {
      try {
        listener(state);
      } catch (error) {
        console.error('Error in auth state listener:', error);
      }
    });
  }

  private async logError(code: string, message: string, severity: 'Low' | 'Medium' | 'High' | 'Critical', category: string): Promise<void> {
    try {
      await tauriService.logError(code, message, severity, category);
    } catch (error) {
      console.error('Failed to log error:', error);
    }
  }
}

// ============= Export =============

export const authService = AuthService.getInstance();

export function useAuthService() {
  return authService;
}

export default authService;