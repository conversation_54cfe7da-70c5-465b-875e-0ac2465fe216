
import { useState, useEffect } from 'react';
import { useToast } from '../hooks/useToast';
import { authService, AuthState, LoginCredentials, RegisterData } from './authService';

// Hook for authentication state
export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>(authService.getAuthState());

  useEffect(() => {
    const unsubscribe = authService.subscribe(setAuthState);
    return unsubscribe;
  }, []);

  return authState;
};

// Hook for login functionality
export const useLogin = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const login = async (credentials: LoginCredentials) => {
    setIsLoading(true);
    try {
      await authService.login(credentials);
      toast({
        title: "Welcome back!",
        description: "You have been successfully logged in.",
        variant: "default"
      });
    } catch (error) {
      toast({
        title: "Login failed",
        description: error instanceof Error ? error.message : "An error occurred during login.",
        variant: "destructive"
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return { login, isLoading };
};

// Hook for registration functionality
export const useRegister = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const register = async (data: RegisterData) => {
    setIsLoading(true);
    try {
      await authService.register(data);
      toast({
        title: "Account created!",
        description: "Your account has been successfully created. You can now log in.",
        variant: "default"
      });
    } catch (error) {
      toast({
        title: "Registration failed",
        description: error instanceof Error ? error.message : "An error occurred during registration.",
        variant: "destructive"
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return { register, isLoading };
};

// Hook for logout functionality
export const useLogout = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const logout = async () => {
    setIsLoading(true);
    try {
      await authService.logout();
      toast({
        title: "Goodbye!",
        description: "You have been successfully logged out.",
        variant: "default"
      });
    } catch (error) {
      toast({
        title: "Logout failed",
        description: error instanceof Error ? error.message : "An error occurred during logout.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return { logout, isLoading };
};

// Hook for profile management
export const useProfile = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const updateProfile = async (updates: any) => {
    setIsLoading(true);
    try {
      await authService.updateProfile(updates);
      toast({
        title: "Profile updated",
        description: "Your profile has been successfully updated.",
        variant: "default"
      });
    } catch (error) {
      toast({
        title: "Update failed",
        description: error instanceof Error ? error.message : "An error occurred while updating your profile.",
        variant: "destructive"
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const changePassword = async (oldPassword: string, newPassword: string) => {
    setIsLoading(true);
    try {
      await authService.changePassword(oldPassword, newPassword);
      toast({
        title: "Password changed",
        description: "Your password has been successfully changed.",
        variant: "default"
      });
    } catch (error) {
      toast({
        title: "Password change failed",
        description: error instanceof Error ? error.message : "An error occurred while changing your password.",
        variant: "destructive"
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return { updateProfile, changePassword, isLoading };
};

// Hook for session validation
export const useSessionValidation = () => {
  const validateSession = async () => {
    return await authService.validateSession();
  };

  return { validateSession };
};
