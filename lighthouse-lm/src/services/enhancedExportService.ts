import { invoke } from '@/lib/tauri-mock';
// Mermaid-related imports removed
// import { SourceDiagram, SourceAttribution } from './sourceDiagramService';
import { exportToFile } from '../utils/common';
import { toast } from '../hooks/useToast';

// Temporary type definitions to prevent errors
interface SourceDiagram {
  id?: number;
  notebook_id?: string;
  diagram_content: string;
  diagram_type: string;
  created_at?: string;
  updated_at?: string;
  sourceAttributions?: SourceAttribution[];
  source_ids: string[];
  analysis_metadata?: any;
  interactive_metadata?: {
    source_attribution?: SourceAttribution[];
    node_source_map?: { [key: string]: any };
  };
}

interface SourceAttribution {
  id?: number;
  diagram_id?: number;
  node_id: string;
  source_ids: string[];
  source_id?: string;
  source_content?: string;
  content_excerpt?: string;
  confidence?: number;
  metadata?: any;
}

export interface ExportOptions {
  includeSourceMetadata?: boolean;
  includeAttribution?: boolean;
  includeComments?: boolean;
  format?: 'svg' | 'png' | 'json' | 'mmd';
  quality?: number; // For PNG exports (0.1 to 1.0)
  backgroundColor?: string;
  embedSourceLinks?: boolean;
}

export interface ShareableLink {
  id: string;
  url: string;
  expires_at?: string;
  access_token?: string;
  source_references: string[];
}

/**
 * Enhanced export service for Mermaid diagrams with source metadata
 */
export class EnhancedExportService {
  /**
   * Export diagram as SVG with source attribution embedded as metadata
   */
  static async exportSVGWithAttribution(
    diagram: SourceDiagram,
    options: ExportOptions = {}
  ): Promise<string> {
    try {
      const {
        includeSourceMetadata = true,
        includeAttribution = true,
        backgroundColor = 'white'
      } = options;

      // Get the base SVG content
      const baseSvg = await invoke<string>('export_diagram_as_svg', {
        diagramContent: diagram.diagram_content
      });

      if (!includeSourceMetadata && !includeAttribution) {
        return baseSvg;
      }

      // Parse the SVG to add metadata
      const parser = new DOMParser();
      const svgDoc = parser.parseFromString(baseSvg, 'image/svg+xml');
      const svgElement = svgDoc.documentElement;

      // Add background if specified
      if (backgroundColor && backgroundColor !== 'transparent') {
        const rect = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'rect');
        rect.setAttribute('width', '100%');
        rect.setAttribute('height', '100%');
        rect.setAttribute('fill', backgroundColor);
        svgElement.insertBefore(rect, svgElement.firstChild);
      }

      // Add source metadata as SVG metadata elements
      if (includeSourceMetadata) {
        const metadataElement = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'metadata');
        
        // Add source IDs
        const sourceIds = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'source-ids');
        sourceIds.textContent = diagram.source_ids.join(',');
        metadataElement.appendChild(sourceIds);

        // Add generation metadata
        const generationMeta = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'generation-metadata');
        generationMeta.textContent = JSON.stringify(diagram.analysis_metadata);
        metadataElement.appendChild(generationMeta);

        // Add creation timestamp
        const timestamp = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'created-at');
        timestamp.textContent = diagram.created_at || '';
        metadataElement.appendChild(timestamp);

        svgElement.appendChild(metadataElement);
      }

      // Add source attribution as invisible text elements
      if (includeAttribution && diagram.interactive_metadata?.source_attribution) {
        const attributionGroup = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'g');
        attributionGroup.setAttribute('id', 'source-attribution');
        attributionGroup.setAttribute('style', 'display: none;');

        diagram.interactive_metadata.source_attribution.forEach((attr, index) => {
          const attrElement = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'text');
          attrElement.setAttribute('id', `attribution-${index}`);
          attrElement.setAttribute('data-node-id', attr.node_id);
          attrElement.setAttribute('data-source-id', attr.source_id || '');
          attrElement.setAttribute('data-confidence', (attr.confidence || 0).toString());
          attrElement.textContent = attr.content_excerpt || '';
          attributionGroup.appendChild(attrElement);
        });

        svgElement.appendChild(attributionGroup);
      }

      // Add visual attribution watermark in corner
      if (includeAttribution && diagram.source_ids.length > 0) {
        const watermarkGroup = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'g');
        watermarkGroup.setAttribute('id', 'attribution-watermark');
        watermarkGroup.setAttribute('transform', 'translate(10, 20)');

        const watermarkText = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'text');
        watermarkText.setAttribute('font-size', '10');
        watermarkText.setAttribute('fill', '#666');
        watermarkText.setAttribute('opacity', '0.7');
        watermarkText.textContent = `Generated from ${diagram.source_ids.length} source${diagram.source_ids.length > 1 ? 's' : ''}`;
        
        watermarkGroup.appendChild(watermarkText);
        svgElement.appendChild(watermarkGroup);
      }

      return new XMLSerializer().serializeToString(svgDoc);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Export diagram as PNG with source attribution
   */
  static async exportPNGWithAttribution(
    diagram: SourceDiagram,
    options: ExportOptions = {}
  ): Promise<Blob> {
    try {
      const {
        quality = 1.0,
        backgroundColor = 'white'
      } = options;

      // First get the enhanced SVG
      const svgString = await this.exportSVGWithAttribution(diagram, options);
      
      // Convert SVG to PNG
      return new Promise((resolve, reject) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();
        
        const svgBlob = new Blob([svgString], { type: 'image/svg+xml;charset=utf-8' });
        const url = URL.createObjectURL(svgBlob);
        
        img.onload = () => {
          // Set canvas size with higher resolution for better quality
          const scale = 2; // 2x resolution
          canvas.width = img.width * scale;
          canvas.height = img.height * scale;
          
          if (ctx) {
            ctx.scale(scale, scale);
            
            // Set background
            if (backgroundColor && backgroundColor !== 'transparent') {
              ctx.fillStyle = backgroundColor;
              ctx.fillRect(0, 0, img.width, img.height);
            }
            
            ctx.drawImage(img, 0, 0);
          }
          
          canvas.toBlob((blob) => {
            URL.revokeObjectURL(url);
            if (blob) {
              resolve(blob);
            } else {
              reject(new Error('Failed to create PNG blob'));
            }
          }, 'image/png', quality);
        };
        
        img.onerror = () => {
          URL.revokeObjectURL(url);
          reject(new Error('Failed to load SVG for PNG conversion'));
        };
        
        img.src = url;
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Export diagram code with source comments
   */
  static exportCodeWithComments(
    diagram: SourceDiagram,
    options: ExportOptions = {}
  ): string {
    const { includeComments = true, includeSourceMetadata = true } = options;
    
    let output = '';
    
    // Add header comments
    if (includeComments) {
      output += `%% Mermaid Diagram Generated from Sources\n`;
      output += `%% Created: ${new Date(diagram.created_at || Date.now()).toLocaleString()}\n`;
      output += `%% Diagram Type: ${diagram.diagram_type}\n`;
      
      if (includeSourceMetadata) {
        output += `%% Source Count: ${diagram.source_ids.length}\n`;
        output += `%% Concepts Extracted: ${diagram.analysis_metadata.concepts_extracted}\n`;
        output += `%% Relationships Found: ${diagram.analysis_metadata.relationships_found}\n`;
        output += `%% Analysis Confidence: ${Math.round(diagram.analysis_metadata.analysis_confidence * 100)}%\n`;
      }
      
      output += `%%\n`;
      
      // Add source references
      if (diagram.source_ids.length > 0) {
        output += `%% Source References:\n`;
        diagram.source_ids.forEach((sourceId, index) => {
          output += `%% ${index + 1}. Source ID: ${sourceId}\n`;
        });
        output += `%%\n`;
      }
      
      // Add node-to-source mapping
      if (diagram.interactive_metadata?.node_source_map) {
        output += `%% Node-to-Source Mapping:\n`;
        Object.entries(diagram.interactive_metadata.node_source_map).forEach(([nodeId, sourceIds]) => {
          output += `%% ${nodeId} -> Sources: ${sourceIds.join(', ')}\n`;
        });
        output += `%%\n`;
      }
    }
    
    // Add the actual diagram content
    output += diagram.diagram_content;
    
    // Add attribution comments at the end
    if (includeComments && diagram.interactive_metadata?.source_attribution) {
      output += `\n\n%% Source Attribution Details:\n`;
      diagram.interactive_metadata.source_attribution.forEach((attr, index) => {
        output += `%% ${index + 1}. Node "${attr.node_id}" (${Math.round((attr.confidence || 0) * 100)}% confidence)\n`;
        output += `%%    Source: ${attr.source_id || 'Unknown'}\n`;
        output += `%%    Content: "${(attr.content_excerpt || '').substring(0, 100)}${(attr.content_excerpt || '').length > 100 ? '...' : ''}"\n`;
      });
    }
    
    return output;
  }

  /**
   * Export diagram as JSON with full metadata
   */
  static exportJSONWithMetadata(diagram: SourceDiagram): string {
    const exportData = {
      diagram: {
        id: diagram.id,
        type: diagram.diagram_type,
        content: diagram.diagram_content,
        created_at: diagram.created_at,
        updated_at: diagram.updated_at
      },
      sources: {
        source_ids: diagram.source_ids,
        count: diagram.source_ids.length
      },
      analysis: diagram.analysis_metadata,
      interactivity: diagram.interactive_metadata,
      export_metadata: {
        exported_at: new Date().toISOString(),
        export_version: '1.0',
        format: 'enhanced-mermaid-export'
      }
    };
    
    return JSON.stringify(exportData, null, 2);
  }

  /**
   * Create shareable diagram link with source references
   */
  static async createShareableLink(
    diagram: SourceDiagram,
    options: {
      expiresIn?: number; // hours
      includeSourceAccess?: boolean;
      publicAccess?: boolean;
    } = {}
  ): Promise<ShareableLink> {
    try {
      const {
        expiresIn = 24, // 24 hours default
        includeSourceAccess = false,
        publicAccess = false
      } = options;

      const shareData = {
        diagram_id: diagram.id,
        diagram_content: diagram.diagram_content,
        diagram_type: diagram.diagram_type,
        source_references: diagram.source_ids,
        metadata: {
          title: `Diagram from ${diagram.source_ids.length} sources`,
          created_at: diagram.created_at,
          concepts_count: diagram.analysis_metadata.concepts_extracted,
          relationships_count: diagram.analysis_metadata.relationships_found
        },
        access_settings: {
          expires_in_hours: expiresIn,
          include_source_access: includeSourceAccess,
          public_access: publicAccess
        }
      };

      return await invoke<ShareableLink>('create_shareable_diagram_link', shareData);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Download diagram with enhanced export options
   */
  static async downloadDiagramWithAttribution(
    diagram: SourceDiagram,
    format: 'svg' | 'png' | 'json' | 'mmd',
    options: ExportOptions = {}
  ): Promise<void> {
    try {
      const filename = `diagram-${diagram.id}-${Date.now()}`;
      
      switch (format) {
        case 'svg': {
          const svgContent = await this.exportSVGWithAttribution(diagram, options);
          exportToFile(svgContent, `${filename}.svg`, 'image/svg+xml');
          break;
        }
        
        case 'png': {
          const pngBlob = await this.exportPNGWithAttribution(diagram, options);
          const url = URL.createObjectURL(pngBlob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `${filename}.png`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
          
          toast({
            title: 'Exported',
            description: 'Diagram exported as PNG with source attribution',
          });
          break;
        }
        
        case 'json': {
          const jsonContent = this.exportJSONWithMetadata(diagram);
          exportToFile(jsonContent, `${filename}.json`, 'application/json');
          break;
        }
        
        case 'mmd': {
          const mmdContent = this.exportCodeWithComments(diagram, options);
          exportToFile(mmdContent, `${filename}.mmd`, 'text/plain');
          break;
        }
        
        default:
          throw new Error(`Unsupported export format: ${format}`);
      }
    } catch (error) {
      toast({
        title: 'Export Failed',
        description: `Failed to export diagram as ${format.toUpperCase()}`,
        variant: 'destructive',
      });
      throw error;
    }
  }

  /**
   * Bulk export multiple diagrams
   */
  static async bulkExportDiagrams(
    diagrams: SourceDiagram[],
    format: 'svg' | 'png' | 'json' | 'mmd',
    options: ExportOptions = {}
  ): Promise<void> {
    try {
      const timestamp = Date.now();
      const exportPromises = diagrams.map(async (diagram, index) => {
        const filename = `diagram-${diagram.id}-${timestamp}-${index + 1}`;
        
        switch (format) {
          case 'svg': {
            const svgContent = await this.exportSVGWithAttribution(diagram, options);
            return { filename: `${filename}.svg`, content: svgContent, type: 'image/svg+xml' };
          }
          
          case 'png': {
            const pngBlob = await this.exportPNGWithAttribution(diagram, options);
            return { filename: `${filename}.png`, blob: pngBlob };
          }
          
          case 'json': {
            const jsonContent = this.exportJSONWithMetadata(diagram);
            return { filename: `${filename}.json`, content: jsonContent, type: 'application/json' };
          }
          
          case 'mmd': {
            const mmdContent = this.exportCodeWithComments(diagram, options);
            return { filename: `${filename}.mmd`, content: mmdContent, type: 'text/plain' };
          }
          
          default:
            throw new Error(`Unsupported export format: ${format}`);
        }
      });

      const results = await Promise.all(exportPromises);
      
      // Download all files
      results.forEach(result => {
        if ('blob' in result && result.blob) {
          const url = URL.createObjectURL(result.blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = result.filename;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        } else if ('content' in result && result.content) {
          exportToFile(result.content, result.filename, result.type || 'text/plain');
        }
      });

      toast({
        title: 'Bulk Export Complete',
        description: `Successfully exported ${diagrams.length} diagrams as ${format.toUpperCase()}`,
      });
    } catch (error) {
      toast({
        title: 'Bulk Export Failed',
        description: 'Failed to export some or all diagrams',
        variant: 'destructive',
      });
      throw error;
    }
  }

  /**
   * Get export preview for a diagram
   */
  static async getExportPreview(
    diagram: SourceDiagram,
    format: 'svg' | 'png' | 'json' | 'mmd'
  ): Promise<{ preview: string; size: number }> {
    try {
      let preview: string;
      let size: number;

      switch (format) {
        case 'svg': {
          const svgContent = await this.exportSVGWithAttribution(diagram);
          preview = svgContent.substring(0, 500) + (svgContent.length > 500 ? '...' : '');
          size = new Blob([svgContent]).size;
          break;
        }
        
        case 'json': {
          const jsonContent = this.exportJSONWithMetadata(diagram);
          preview = jsonContent.substring(0, 500) + (jsonContent.length > 500 ? '...' : '');
          size = new Blob([jsonContent]).size;
          break;
        }
        
        case 'mmd': {
          const mmdContent = this.exportCodeWithComments(diagram);
          preview = mmdContent.substring(0, 500) + (mmdContent.length > 500 ? '...' : '');
          size = new Blob([mmdContent]).size;
          break;
        }
        
        case 'png': {
          preview = 'PNG image with embedded source attribution';
          // Estimate PNG size (rough approximation)
          size = Math.round(diagram.diagram_content.length * 0.8);
          break;
        }
        
        default:
          throw new Error(`Unsupported preview format: ${format}`);
      }

      return { preview, size };
    } catch (error) {
      throw error;
    }
  }
}