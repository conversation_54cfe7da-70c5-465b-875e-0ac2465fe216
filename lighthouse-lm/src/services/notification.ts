import { useToast } from '../hooks/useToast';

/**
 * Unified Notification Service
 * Consolidates notification and error handling utilities
 */

export interface NotificationOptions {
  title: string;
  description?: string;
  variant?: 'default' | 'destructive' | null | undefined;
  duration?: number;
}

/**
 * Standard notification messages for common operations
 */
export const NOTIFICATION_MESSAGES = {
  // Success messages
  SUCCESS: {
    SOURCE_UPLOADED: (count: number) => `Uploaded ${count} file(s) successfully`,
    SOURCE_DELETED: 'Source deleted successfully',
    SOURCE_RENAMED: 'Source renamed successfully',
    SOURCE_UPDATED: 'Source updated successfully',
    URL_IMPORTED: 'URL imported successfully',
    DIAGRAM_GENERATED: 'Diagram generated and saved successfully',
    DIAGRAM_DELETED: 'Diagram deleted successfully',
    DIAGRAM_REGENERATED: 'Diagram regenerated successfully',
    DIAGRAM_EXPORTED: (format: string) => `Diagram exported as ${format.toUpperCase()}`,
    BULK_DELETED: (count: number, type: string) => `Deleted ${count} ${type}(s) successfully`,
    BULK_EXPORTED: (count: number, type: string, format?: string) =>
      `Exported ${count} ${type}(s)${format ? ` as ${format.toUpperCase()}` : ''}`,
    BULK_ARCHIVED: (count: number, type: string) => `Archived ${count} ${type}(s) successfully`,
    CHAT_CLEARED: 'Chat history cleared',
    COPIED_TO_CLIPBOARD: 'Copied to clipboard',
    SETTINGS_SAVED: 'Settings saved successfully',
    PRESENTATION_SAVED: 'Presentation saved successfully',
    SLIDES_GENERATED: 'Slides generated successfully',
    AUDIO_GENERATED: 'Audio overview generated successfully',
    NOTEBOOK_CREATED: 'Notebook created successfully',
    NOTEBOOK_UPDATED: 'Notebook updated successfully',
    NOTEBOOK_DELETED: 'Notebook deleted successfully',
  },

  // Error messages
  ERROR: {
    SOURCE_UPLOAD_FAILED: 'Failed to upload files',
    SOURCE_DELETE_FAILED: 'Failed to delete source',
    SOURCE_RENAME_FAILED: 'Failed to rename source',
    SOURCE_UPDATE_FAILED: 'Failed to update source',
    URL_IMPORT_FAILED: 'Failed to import URL',
    DIAGRAM_GENERATION_FAILED: 'Failed to generate diagram',
    DIAGRAM_DELETE_FAILED: 'Failed to delete diagram',
    DIAGRAM_REGENERATION_FAILED: 'Failed to regenerate diagram',
    DIAGRAM_EXPORT_FAILED: (format?: string) => `Failed to export diagram${format ? ` as ${format}` : ''}`,
    BULK_OPERATION_FAILED: (operation: string) => `Failed to ${operation}`,
    CHAT_LOAD_FAILED: 'Failed to load chat messages',
    CHAT_DELETE_FAILED: 'Failed to delete chat history',
    SETTINGS_SAVE_FAILED: 'Failed to save settings',
    PRESENTATION_LOAD_FAILED: 'Failed to load presentations',
    PRESENTATION_SAVE_FAILED: 'Failed to save presentation',
    EXPORT_FAILED: 'Export failed',
    GENERATION_FAILED: 'Generation failed',
    LOAD_FAILED: 'Failed to load data',
    SAVE_FAILED: 'Save failed',
    DELETE_FAILED: 'Delete failed',
    NETWORK_ERROR: 'Network error occurred',
    PERMISSION_DENIED: 'Permission denied',
    NOTEBOOK_CREATE_FAILED: 'Failed to create notebook',
    NOTEBOOK_UPDATE_FAILED: 'Failed to update notebook',
    NOTEBOOK_DELETE_FAILED: 'Failed to delete notebook',
  },

  // Warning messages
  WARNING: {
    NO_SOURCES_SELECTED: 'Please select sources to continue',
    NO_SOURCES_AVAILABLE: 'No sources available for this operation',
    INVALID_FILE_TYPE: 'Invalid file type selected',
    FILE_TOO_LARGE: 'File is too large',
    MISSING_REQUIRED: 'Required information is missing',
    OPERATION_IN_PROGRESS: 'Operation already in progress',
    UNSAVED_CHANGES: 'You have unsaved changes',
  },

  // Info messages
  INFO: {
    LOADING: 'Loading...',
    PROCESSING: 'Processing...',
    SAVING: 'Saving...',
    UPLOADING: 'Uploading...',
    GENERATING: 'Generating...',
    EXPORTING: 'Exporting...',
  },
} as const;

/**
 * Error classification utilities
 */
export const ErrorClassifier = {
  isNetworkError: (error: Error): boolean => {
    return error.message.toLowerCase().includes('fetch') ||
           error.message.toLowerCase().includes('network') ||
           error.message.toLowerCase().includes('connection');
  },

  isPermissionError: (error: Error): boolean => {
    return error.message.toLowerCase().includes('permission') ||
           error.message.toLowerCase().includes('unauthorized') ||
           error.message.toLowerCase().includes('forbidden');
  },

  isValidationError: (error: Error): boolean => {
    return error.message.toLowerCase().includes('validation') ||
           error.message.toLowerCase().includes('invalid');
  },

  isFileError: (error: Error): boolean => {
    return error.message.toLowerCase().includes('file') ||
           error.message.toLowerCase().includes('upload');
  },

  classifyError: (error: Error): string => {
    if (ErrorClassifier.isNetworkError(error)) return 'network';
    if (ErrorClassifier.isPermissionError(error)) return 'permission';
    if (ErrorClassifier.isValidationError(error)) return 'validation';
    if (ErrorClassifier.isFileError(error)) return 'file';
    return 'unknown';
  },

  getUserFriendlyMessage: (error: Error): string => {
    const errorType = ErrorClassifier.classifyError(error);

    switch (errorType) {
      case 'network':
        return 'Unable to connect to the server. Please check your internet connection and try again.';
      case 'permission':
        return 'You don\'t have permission to perform this action. Please contact your administrator.';
      case 'validation':
        return 'The provided information is invalid. Please check your input and try again.';
      case 'file':
        return 'There was an issue with the file. Please check the file format and size.';
      default:
        return error.message || 'Something went wrong. Please try again.';
    }
  },
};

/**
 * Unified notification hook
 */
export const useNotificationService = () => {
  const { toast } = useToast();

  const showNotification = (options: NotificationOptions) => {
    toast(options);
  };

  const showSuccess = (title: string, description?: string) => {
    showNotification({
      title,
      description,
      variant: 'default',
    });
  };

  const showError = (error: Error | string, customMessage?: string) => {
    const errorMessage = error instanceof Error 
      ? ErrorClassifier.getUserFriendlyMessage(error)
      : error;

    showNotification({
      title: 'Error',
      description: customMessage || errorMessage,
      variant: 'destructive',
    });
  };

  const showWarning = (title: string, description?: string) => {
    showNotification({
      title,
      description,
      variant: 'default',
    });
  };

  const showInfo = (title: string, description?: string) => {
    showNotification({
      title,
      description,
      variant: 'default',
    });
  };

  // Pre-configured notification helpers
  const notifications = {
    success: {
      sourceUploaded: (count: number) =>
        showSuccess(NOTIFICATION_MESSAGES.SUCCESS.SOURCE_UPLOADED(count)),
      sourceDeleted: () =>
        showSuccess(NOTIFICATION_MESSAGES.SUCCESS.SOURCE_DELETED),
      sourceRenamed: () =>
        showSuccess(NOTIFICATION_MESSAGES.SUCCESS.SOURCE_RENAMED),
      diagramGenerated: () =>
        showSuccess(NOTIFICATION_MESSAGES.SUCCESS.DIAGRAM_GENERATED),
      diagramExported: (format: string) =>
        showSuccess(NOTIFICATION_MESSAGES.SUCCESS.DIAGRAM_EXPORTED(format)),
      bulkOperation: (count: number, type: string, operation: string) =>
        showSuccess(NOTIFICATION_MESSAGES.SUCCESS.BULK_DELETED(count, type).replace('Deleted', operation)),
      copiedToClipboard: () =>
        showSuccess(NOTIFICATION_MESSAGES.SUCCESS.COPIED_TO_CLIPBOARD),
      notebookCreated: () =>
        showSuccess(NOTIFICATION_MESSAGES.SUCCESS.NOTEBOOK_CREATED),
      notebookUpdated: () =>
        showSuccess(NOTIFICATION_MESSAGES.SUCCESS.NOTEBOOK_UPDATED),
      notebookDeleted: () =>
        showSuccess(NOTIFICATION_MESSAGES.SUCCESS.NOTEBOOK_DELETED),
    },
    error: {
      sourceUploadFailed: (description?: string) =>
        showError(NOTIFICATION_MESSAGES.ERROR.SOURCE_UPLOAD_FAILED, description),
      sourceDeleteFailed: (description?: string) =>
        showError(NOTIFICATION_MESSAGES.ERROR.SOURCE_DELETE_FAILED, description),
      diagramGenerationFailed: (description?: string) =>
        showError(NOTIFICATION_MESSAGES.ERROR.DIAGRAM_GENERATION_FAILED, description),
      bulkOperationFailed: (operation: string, description?: string) =>
        showError(NOTIFICATION_MESSAGES.ERROR.BULK_OPERATION_FAILED(operation), description),
      networkError: () =>
        showError(NOTIFICATION_MESSAGES.ERROR.NETWORK_ERROR),
      notebookCreateFailed: (description?: string) =>
        showError(NOTIFICATION_MESSAGES.ERROR.NOTEBOOK_CREATE_FAILED, description),
      notebookUpdateFailed: (description?: string) =>
        showError(NOTIFICATION_MESSAGES.ERROR.NOTEBOOK_UPDATE_FAILED, description),
      notebookDeleteFailed: (description?: string) =>
        showError(NOTIFICATION_MESSAGES.ERROR.NOTEBOOK_DELETE_FAILED, description),
    },
    warning: {
      noSourcesSelected: () =>
        showWarning(NOTIFICATION_MESSAGES.WARNING.NO_SOURCES_SELECTED),
      invalidFileType: () =>
        showWarning(NOTIFICATION_MESSAGES.WARNING.INVALID_FILE_TYPE),
      unsavedChanges: () =>
        showWarning(NOTIFICATION_MESSAGES.WARNING.UNSAVED_CHANGES),
    },
    info: {
      loading: () => showInfo(NOTIFICATION_MESSAGES.INFO.LOADING),
      processing: () => showInfo(NOTIFICATION_MESSAGES.INFO.PROCESSING),
      saving: () => showInfo(NOTIFICATION_MESSAGES.INFO.SAVING),
      uploading: () => showInfo(NOTIFICATION_MESSAGES.INFO.UPLOADING),
      generating: () => showInfo(NOTIFICATION_MESSAGES.INFO.GENERATING),
      exporting: () => showInfo(NOTIFICATION_MESSAGES.INFO.EXPORTING),
    },
  };

  return {
    showNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    notifications,
    ErrorClassifier,
  };
};

/**
 * Copy to clipboard with notification
 */
export const copyToClipboard = async (
  text: string,
  showNotification = true
): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text);
    if (showNotification) {
      const { notifications } = useNotificationService();
      notifications.success.copiedToClipboard();
    }
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * File operation error handling
 */
export const FileErrorHandler = {
  handleFileUploadError: (error: Error): string => {
    if (error.message.includes('size')) {
      return 'File is too large. Please choose a smaller file.';
    }
    if (error.message.includes('type') || error.message.includes('format')) {
      return 'File format not supported. Please choose a different file.';
    }
    if (error.message.includes('permission')) {
      return 'Permission denied. Please check file permissions.';
    }
    return 'Failed to upload file. Please try again.';
  },

  handleFileReadError: (error: Error): string => {
    if (error.message.includes('encoding')) {
      return 'File encoding not supported. Please save the file with UTF-8 encoding.';
    }
    if (error.message.includes('corrupt')) {
      return 'File appears to be corrupted. Please choose a different file.';
    }
    return 'Unable to read file. Please check the file and try again.';
  },
};

/**
 * API error handling
 */
export const ApiErrorHandler = {
  handleApiError: (error: any): string => {
    if (error?.status) {
      switch (error.status) {
        case 400:
          return 'Invalid request. Please check your input.';
        case 401:
          return 'Authentication required. Please log in again.';
        case 403:
          return 'Access denied. You don\'t have permission for this action.';
        case 404:
          return 'Resource not found. It may have been deleted.';
        case 429:
          return 'Too many requests. Please wait a moment and try again.';
        case 500:
          return 'Server error. Please try again later.';
        case 503:
          return 'Service temporarily unavailable. Please try again later.';
        default:
          return `Server error (${error.status}). Please try again.`;
      }
    }

    if (error?.code) {
      switch (error.code) {
        case 'NETWORK_ERROR':
          return 'Network connection error. Please check your internet connection.';
        case 'TIMEOUT':
          return 'Request timed out. Please try again.';
        case 'CANCELLED':
          return 'Request was cancelled.';
        default:
          return `Error (${error.code}). Please try again.`;
      }
    }

    return ErrorClassifier.getUserFriendlyMessage(error);
  },
};