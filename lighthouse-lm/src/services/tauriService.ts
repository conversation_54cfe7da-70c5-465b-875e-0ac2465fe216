// Tauri Service Layer - TypeScript bindings for Rust backend commands

import { invoke } from '@tauri-apps/api/core';
import { listen, emit, UnlistenFn } from '@tauri-apps/api/event';

// Check if we're running in Tauri environment
const isTauriEnvironment = () => {
  return typeof window !== 'undefined' && window.__TAURI__ !== undefined;
};

// ============= Types =============

export interface Notebook {
  id: string;
  title: string;
  description?: string;
  created_at: string;
  updated_at: string;
  owner_id: string;
  is_public: boolean;
  tags: string[];
  metadata: Record<string, any>;
}

export interface Source {
  id: string;
  notebook_id: string;
  title: string;
  content: string;
  source_type: SourceType;
  url?: string;
  file_path?: string;
  metadata: SourceMetadata;
  created_at: string;
  updated_at: string;
  processed: boolean;
  embeddings?: number[];
  tags: string[];
}

export type SourceType = 'PDF' | 'Web' | 'Document' | 'Code' | 'Media' | 'Note' | 'External';

export interface SourceMetadata {
  size?: number;
  page_count?: number;
  word_count?: number;
  language?: string;
  author?: string;
  published_date?: string;
  last_accessed?: string;
  extraction_method?: string;
  confidence_score?: number;
}

export interface ChatMessage {
  id: string;
  notebook_id: string;
  role: MessageRole;
  content: string;
  model?: string;
  temperature?: number;
  max_tokens?: number;
  sources: string[];
  citations: Citation[];
  metadata: MessageMetadata;
  parent_id?: string;
  created_at: string;
  edited_at?: string;
  reactions: Reaction[];
}

export type MessageRole = 'user' | 'assistant' | 'system';

export interface MessageMetadata {
  tokens_used?: number;
  processing_time_ms?: number;
  confidence?: number;
  model_version?: string;
  attachments: Attachment[];
}

export interface Citation {
  source_id: string;
  source_title: string;
  excerpt: string;
  page_number?: number;
  relevance_score: number;
}

export interface Attachment {
  id: string;
  name: string;
  mime_type: string;
  size: number;
  url: string;
}

export interface Reaction {
  user_id: string;
  emoji: string;
  created_at: string;
}

export interface AuthUser {
  id: string;
  username: string;
  email: string;
  created_at: string;
  updated_at: string;
}

export interface ProxySettings {
  enabled: boolean;
  http_proxy?: string;
  https_proxy?: string;
  no_proxy?: string;
  all_proxy?: string;
}

export interface CacheStats {
  total_entries: number;
  cache_size_bytes: number;
  hit_rate: number;
  miss_rate: number;
}

export interface ErrorInfo {
  id: string;
  error_code: string;
  message: string;
  severity: 'Low' | 'Medium' | 'High' | 'Critical';
  category: string;
  timestamp: string;
  context?: Record<string, any>;
  stack_trace?: string;
}

// ============= Tauri Service Class =============

class TauriService {
  private static instance: TauriService;
  private listeners: Map<string, UnlistenFn> = new Map();
  private initialized = false;

  private constructor() {}

  static getInstance(): TauriService {
    if (!TauriService.instance) {
      TauriService.instance = new TauriService();
    }
    return TauriService.instance;
  }

  // ============= Initialization =============

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // Check if we're in Tauri environment
      if (isTauriEnvironment()) {
        // Initialize all backend systems
        await Promise.all([
          invoke('init_auth_system'),
          invoke('init_state_manager'),
          invoke('init_data_fetcher'),
          invoke('init_error_handler'),
          invoke('init_api_client'),
        ]);
        this.setupEventListeners();
      } else {
        // Web mode - skip Tauri initialization
        console.log('Running in web mode - Tauri services disabled');
      }

      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize Tauri service:', error);
      throw error;
    }
  }

  // ============= Authentication Commands =============

  async registerUser(username: string, email: string, password: string): Promise<AuthUser> {
    if (!isTauriEnvironment()) {
      throw new Error('Authentication not available in web mode');
    }
    return await invoke('register_user', { username, email, password });
  }

  async loginUser(username: string, password: string): Promise<{ user: AuthUser; token: string }> {
    if (!isTauriEnvironment()) {
      throw new Error('Authentication not available in web mode');
    }
    return await invoke('login_user', { username, password });
  }

  async logoutUser(): Promise<void> {
    if (!isTauriEnvironment()) {
      throw new Error('Authentication not available in web mode');
    }
    return await invoke('logout_user');
  }

  async validateUserSession(token: string): Promise<boolean> {
    if (!isTauriEnvironment()) {
      return false;
    }
    return await invoke('validate_user_session', { token });
  }

  async getUserById(id: string): Promise<AuthUser> {
    if (!isTauriEnvironment()) {
      throw new Error('getUserById is not available in web mode');
    }
    return invoke('get_user_by_id', { id });
  }

  async updateUserProfile(id: string, updates: Partial<AuthUser>): Promise<AuthUser> {
    if (!isTauriEnvironment()) {
      throw new Error('updateUserProfile is not available in web mode');
    }
    return invoke('update_user_profile', { id, updates });
  }

  async changeUserPassword(id: string, oldPassword: string, newPassword: string): Promise<void> {
    if (!isTauriEnvironment()) {
      throw new Error('changeUserPassword is not available in web mode');
    }
    return invoke('change_user_password', { id, oldPassword, newPassword });
  }

  // ============= State Management Commands =============

  async setAppState(key: string, value: any): Promise<void> {
    if (!isTauriEnvironment()) {
      // Use localStorage as fallback in web mode
      localStorage.setItem(`app_state_${key}`, JSON.stringify(value));
      return;
    }
    return invoke('set_app_state', { key, value });
  }

  async getAppState(key: string): Promise<any> {
    if (!isTauriEnvironment()) {
      // Use localStorage as fallback in web mode
      const item = localStorage.getItem(`app_state_${key}`);
      return item ? JSON.parse(item) : null;
    }
    return invoke('get_app_state', { key });
  }

  async deleteAppState(key: string): Promise<void> {
    if (!isTauriEnvironment()) {
      // Use localStorage as fallback in web mode
      localStorage.removeItem(`app_state_${key}`);
      return;
    }
    return invoke('delete_app_state', { key });
  }

  async queryAppState(pattern: string): Promise<Record<string, any>> {
    if (!isTauriEnvironment()) {
      // Simple pattern matching in localStorage for web mode
      const result: Record<string, any> = {};
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('app_state_') && key.includes(pattern)) {
          const stateKey = key.replace('app_state_', '');
          const item = localStorage.getItem(key);
          result[stateKey] = item ? JSON.parse(item) : null;
        }
      }
      return result;
    }
    return invoke('query_app_state', { pattern });
  }

  async batchStateOperations(operations: Array<{ action: 'set' | 'delete'; key: string; value?: any }>): Promise<void> {
    if (!isTauriEnvironment()) {
      // Execute operations in localStorage for web mode
      operations.forEach(op => {
        if (op.action === 'set') {
          localStorage.setItem(`app_state_${op.key}`, JSON.stringify(op.value));
        } else if (op.action === 'delete') {
          localStorage.removeItem(`app_state_${op.key}`);
        }
      });
      return;
    }
    return invoke('batch_state_operations', { operations });
  }

  async getStateStats(): Promise<{ total_keys: number; total_size_bytes: number }> {
    if (!isTauriEnvironment()) {
      // Calculate stats from localStorage for web mode
      let totalKeys = 0;
      let totalSizeBytes = 0;
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('app_state_')) {
          totalKeys++;
          const item = localStorage.getItem(key);
          totalSizeBytes += (key.length + (item?.length || 0)) * 2; // Rough estimate
        }
      }
      return { total_keys: totalKeys, total_size_bytes: totalSizeBytes };
    }
    return invoke('get_state_stats');
  }

  // ============= Data Fetcher Commands =============

  async fetchData(url: string, options?: { method?: string; headers?: Record<string, string>; body?: string }): Promise<any> {
    if (!isTauriEnvironment()) {
      // Use fetch API in web mode
      const response = await fetch(url, {
        method: options?.method || 'GET',
        headers: options?.headers,
        body: options?.body
      });
      return response.json();
    }
    return invoke('fetch_data', { url, options });
  }

  async clearDataCache(): Promise<void> {
    if (!isTauriEnvironment()) {
      // No-op in web mode
      return;
    }
    return invoke('clear_data_cache');
  }

  async getCacheStats(): Promise<CacheStats> {
    if (!isTauriEnvironment()) {
      // Return mock stats in web mode
      return {
        total_entries: 0,
        cache_size_bytes: 0,
        hit_rate: 0,
        miss_rate: 0
      };
    }
    return invoke('get_cache_stats');
  }

  async setCacheConfig(config: { max_size_mb: number; ttl_seconds: number }): Promise<void> {
    if (!isTauriEnvironment()) {
      // No-op in web mode
      return;
    }
    return invoke('set_cache_config', { config });
  }

  async processOfflineQueue(): Promise<void> {
    if (!isTauriEnvironment()) {
      // No-op in web mode
      return;
    }
    return invoke('process_offline_queue');
  }

  async getOfflineQueueStatus(): Promise<{ pending_requests: number; failed_requests: number }> {
    if (!isTauriEnvironment()) {
      // Return mock status in web mode
      return { pending_requests: 0, failed_requests: 0 };
    }
    return invoke('get_offline_queue_status');
  }

  // ============= Error Handler Commands =============

  async logError(errorCode: string, message: string, severity: 'Low' | 'Medium' | 'High' | 'Critical', category: string, context?: Record<string, any>): Promise<string> {
    if (!isTauriEnvironment()) {
      // Log to console in web mode
      console.error(`[${severity}] ${category}: ${errorCode} - ${message}`, context);
      return `web_error_${Date.now()}`;
    }
    return invoke('log_error_info', { errorCode, message, severity, category, context });
  }

  async getErrorById(id: string): Promise<ErrorInfo> {
    if (!isTauriEnvironment()) {
      throw new Error('getErrorById is not available in web mode');
    }
    return invoke('get_error_by_id', { id });
  }

  async getErrorsByCategory(category: string): Promise<ErrorInfo[]> {
    if (!isTauriEnvironment()) {
      // Return empty array in web mode
      return [];
    }
    return invoke('get_errors_by_category', { category });
  }

  async getErrorsBySeverity(severity: 'Low' | 'Medium' | 'High' | 'Critical'): Promise<ErrorInfo[]> {
    if (!isTauriEnvironment()) {
      // Return empty array in web mode
      return [];
    }
    return invoke('get_errors_by_severity', { severity });
  }

  async getRecentErrors(limit?: number): Promise<ErrorInfo[]> {
    if (!isTauriEnvironment()) {
      // Return empty array in web mode
      return [];
    }
    return invoke('get_recent_errors', { limit });
  }

  async getErrorStatistics(): Promise<{ total_errors: number; by_severity: Record<string, number>; by_category: Record<string, number> }> {
    if (!isTauriEnvironment()) {
      // Return empty stats in web mode
      return {
        total_errors: 0,
        by_severity: {},
        by_category: {}
      };
    }
    return invoke('get_error_statistics');
  }

  async clearErrorHistory(): Promise<void> {
    if (!isTauriEnvironment()) {
      // No-op in web mode
      return;
    }
    return invoke('clear_error_history');
  }

  // ============= Database Commands =============

  async executeDatabaseQuery(query: string, params?: any[]): Promise<any> {
    if (!isTauriEnvironment()) {
      throw new Error('Database operations are not available in web mode');
    }
    return invoke('execute_database_query', { query, params });
  }

  async fetchDatabaseRows(query: string, params?: any[]): Promise<any[]> {
    if (!isTauriEnvironment()) {
      throw new Error('Database operations are not available in web mode');
    }
    return invoke('fetch_database_rows', { query, params });
  }

  async getDatabaseStatistics(): Promise<{ total_tables: number; total_rows: number; database_size_bytes: number }> {
    if (!isTauriEnvironment()) {
      return { total_tables: 0, total_rows: 0, database_size_bytes: 0 };
    }
    return invoke('get_database_statistics');
  }

  async getDatabaseSchema(): Promise<any> {
    if (!isTauriEnvironment()) {
      return {};
    }
    return invoke('get_database_schema');
  }

  async createDatabaseBackup(path: string): Promise<void> {
    if (!isTauriEnvironment()) {
      throw new Error('Database backup is not available in web mode');
    }
    return invoke('create_database_backup', { path });
  }

  async optimizeDatabase(): Promise<void> {
    if (!isTauriEnvironment()) {
      // No-op in web mode
      return;
    }
    return invoke('optimize_database');
  }

  async checkDatabaseIntegrity(): Promise<{ is_valid: boolean; errors?: string[] }> {
    if (!isTauriEnvironment()) {
      return { is_valid: true };
    }
    return invoke('check_database_integrity');
  }

  // ============= API Client Commands =============

  async getApiConfig(): Promise<any> {
    if (!isTauriEnvironment()) {
      // Get from localStorage in web mode
      const stored = localStorage.getItem('api_config');
      return stored ? JSON.parse(stored) : {};
    }
    return invoke('get_api_config');
  }

  async setAuthToken(token: string): Promise<void> {
    if (!isTauriEnvironment()) {
      // Store in localStorage in web mode
      localStorage.setItem('auth_token', token);
      return;
    }
    return invoke('set_auth_token', { token });
  }

  async clearAuthToken(): Promise<void> {
    if (!isTauriEnvironment()) {
      // Remove from localStorage in web mode
      localStorage.removeItem('auth_token');
      return;
    }
    return invoke('clear_auth_token');
  }

  async isAuthenticated(): Promise<boolean> {
    if (!isTauriEnvironment()) {
      // Check localStorage in web mode
      return localStorage.getItem('auth_token') !== null;
    }
    return invoke('is_authenticated');
  }

  async cleanupApiCache(): Promise<void> {
    if (!isTauriEnvironment()) {
      // No-op in web mode
      return;
    }
    return invoke('cleanup_api_cache');
  }

  async apiGet(url: string, headers?: Record<string, string>): Promise<any> {
    if (!isTauriEnvironment()) {
      // Use fetch API in web mode
      const response = await fetch(url, {
        method: 'GET',
        headers
      });
      return response.json();
    }
    return invoke('api_get', { url, headers });
  }

  async apiPost(url: string, data?: any, headers?: Record<string, string>): Promise<any> {
    if (!isTauriEnvironment()) {
      // Use fetch API in web mode
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...headers
        },
        body: data ? JSON.stringify(data) : undefined
      });
      return response.json();
    }
    return invoke('api_post', { url, data, headers });
  }

  async apiPut(url: string, data?: any, headers?: Record<string, string>): Promise<any> {
    if (!isTauriEnvironment()) {
      // Use fetch API in web mode
      const response = await fetch(url, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          ...headers
        },
        body: data ? JSON.stringify(data) : undefined
      });
      return response.json();
    }
    return invoke('api_put', { url, data, headers });
  }

  async apiDelete(url: string, headers?: Record<string, string>): Promise<any> {
    if (!isTauriEnvironment()) {
      // Use fetch API in web mode
      const response = await fetch(url, {
        method: 'DELETE',
        headers
      });
      return response.json();
    }
    return invoke('api_delete', { url, headers });
  }

  async makeApiRequest(method: string, url: string, headers?: Record<string, string>, body?: any): Promise<any> {
    if (!isTauriEnvironment()) {
      // Use fetch API in web mode
      const response = await fetch(url, {
        method,
        headers,
        body: body ? JSON.stringify(body) : undefined
      });
      return response.json();
    }
    return invoke('make_api_request', { method, url, headers, body });
  }

  async setApiClientConfig(config: { base_url?: string; timeout?: number; default_headers?: Record<string, string> }): Promise<void> {
    if (!isTauriEnvironment()) {
      // Store in localStorage in web mode
      localStorage.setItem('api_client_config', JSON.stringify(config));
      return;
    }
    return invoke('set_api_client_config', { config });
  }

  async getApiClientConfig(): Promise<{ base_url?: string; timeout?: number; default_headers?: Record<string, string> }> {
    if (!isTauriEnvironment()) {
      // Get from localStorage in web mode
      const stored = localStorage.getItem('api_client_config');
      return stored ? JSON.parse(stored) : {};
    }
    return invoke('get_api_client_config');
  }

  async getApiRequestHistory(limit?: number): Promise<any[]> {
    if (!isTauriEnvironment()) {
      // Return empty array in web mode
      return [];
    }
    return invoke('get_api_request_history', { limit });
  }

  async clearApiRequestHistory(): Promise<void> {
    if (!isTauriEnvironment()) {
      // No-op in web mode
      return;
    }
    return invoke('clear_api_request_history');
  }

  // ============= Chat Message Commands =============

  async getChatMessages(notebookId: string): Promise<ChatMessage[]> {
    if (!isTauriEnvironment()) {
      // Return empty array in web mode
      return [];
    }
    return invoke('get_chat_messages', { notebookId });
  }

  async sendChatMessage(notebookId: string, message: string): Promise<ChatMessage> {
    if (!isTauriEnvironment()) {
      throw new Error('Chat functionality is not available in web mode');
    }
    return invoke('send_chat_message', { notebookId, message });
  }

  async clearChatHistory(notebookId: string): Promise<void> {
    if (!isTauriEnvironment()) {
      // No-op in web mode
      return;
    }
    return invoke('clear_chat_history', { notebookId });
  }

  async updateChatMessage(messageId: string, content: string): Promise<ChatMessage> {
    if (!isTauriEnvironment()) {
      throw new Error('Chat functionality is not available in web mode');
    }
    return invoke('update_chat_message', { messageId, content });
  }

  async deleteChatMessage(messageId: string): Promise<void> {
    if (!isTauriEnvironment()) {
      // No-op in web mode
      return;
    }
    return invoke('delete_chat_message', { messageId });
  }

  // ============= Notebook Commands =============

  async getNotebooks(): Promise<Notebook[]> {
    if (!isTauriEnvironment()) {
      // Return empty array in web mode
      return [];
    }
    return invoke('get_notebooks');
  }

  async getNotebook(notebookId: string): Promise<Notebook> {
    if (!isTauriEnvironment()) {
      throw new Error('Notebook functionality is not available in web mode');
    }
    return invoke('get_notebook', { notebookId });
  }

  async createNotebook(title: string, description?: string): Promise<Notebook> {
    if (!isTauriEnvironment()) {
      throw new Error('Notebook functionality is not available in web mode');
    }
    return invoke('create_notebook', { title, description });
  }

  async updateNotebook(notebookId: string, updates: Partial<Notebook>): Promise<Notebook> {
    if (!isTauriEnvironment()) {
      throw new Error('Notebook functionality is not available in web mode');
    }
    return invoke('update_notebook', { notebookId, updates });
  }

  async deleteNotebook(notebookId: string): Promise<void> {
    if (!isTauriEnvironment()) {
      // No-op in web mode
      return;
    }
    return invoke('delete_notebook', { notebookId });
  }

  // ============= Source Commands =============

  async getSources(notebookId: string): Promise<Source[]> {
    if (!isTauriEnvironment()) {
      // Return empty array in web mode
      return [];
    }
    return invoke('get_sources', { notebookId });
  }

  async getSource(sourceId: string): Promise<Source> {
    if (!isTauriEnvironment()) {
      throw new Error('Source functionality is not available in web mode');
    }
    return invoke('get_source', { sourceId });
  }

  async createSource(notebookId: string, source: Partial<Source>): Promise<Source> {
    if (!isTauriEnvironment()) {
      throw new Error('Source functionality is not available in web mode');
    }
    return invoke('create_source', { notebookId, source });
  }

  async updateSource(sourceId: string, updates: Partial<Source>): Promise<Source> {
    if (!isTauriEnvironment()) {
      throw new Error('Source functionality is not available in web mode');
    }
    return invoke('update_source', { sourceId, updates });
  }

  async deleteSource(sourceId: string): Promise<void> {
    if (!isTauriEnvironment()) {
      // No-op in web mode
      return;
    }
    return invoke('delete_source', { sourceId });
  }

  async uploadFile(notebookId: string, filePath: string): Promise<Source> {
    if (!isTauriEnvironment()) {
      throw new Error('File upload is not available in web mode');
    }
    return invoke('upload_file', { notebookId, filePath });
  }

  async processDocument(sourceId: string): Promise<void> {
    if (!isTauriEnvironment()) {
      // No-op in web mode
      return;
    }
    return invoke('process_document', { sourceId });
  }

  // ============= Proxy Commands =============

  async getProxySettings(): Promise<ProxySettings> {
    if (!isTauriEnvironment()) {
      // Return default proxy settings in web mode
      return {
        enabled: false,
        http_proxy: undefined,
        https_proxy: undefined,
        no_proxy: undefined,
        all_proxy: undefined
      };
    }
    return invoke('get_proxy_settings');
  }

  async saveProxySettings(settings: ProxySettings): Promise<void> {
    if (!isTauriEnvironment()) {
      // No-op in web mode
      return;
    }
    return invoke('save_proxy_settings', { settings });
  }

  // ============= Storage Commands =============

  async storageListTables(): Promise<string[]> {
    if (!isTauriEnvironment()) {
      // Return empty array in web mode
      return [];
    }
    return invoke('storage_list_tables');
  }

  async storageReadTable(tableName: string): Promise<any[]> {
    if (!isTauriEnvironment()) {
      // Return empty array in web mode
      return [];
    }
    return invoke('storage_read_table', { tableName });
  }

  async storageUpdateRow(tableName: string, id: string, data: Record<string, any>): Promise<void> {
    if (!isTauriEnvironment()) {
      // No-op in web mode
      return;
    }
    return invoke('storage_update_row', { tableName, id, data });
  }

  async storageDeleteRow(tableName: string, id: string): Promise<void> {
    if (!isTauriEnvironment()) {
      // No-op in web mode
      return;
    }
    return invoke('storage_delete_row', { tableName, id });
  }

  async storageInsertRow(tableName: string, data: Record<string, any>): Promise<string> {
    if (!isTauriEnvironment()) {
      // Return mock ID in web mode
      return `web_id_${Date.now()}`;
    }
    return invoke('storage_insert_row', { tableName, data });
  }

  async storageExecuteSql(sql: string, params?: any[]): Promise<any> {
    if (!isTauriEnvironment()) {
      // Return empty result in web mode
      return [];
    }
    return invoke('storage_execute_sql', { sql, params });
  }

  async storageResetDatabase(): Promise<void> {
    if (!isTauriEnvironment()) {
      // No-op in web mode
      return;
    }
    return invoke('storage_reset_database');
  }

  // ============= Event Handling =============

  async subscribeToEvents(eventName: string, handler: (event: any) => void): Promise<UnlistenFn> {
    if (!isTauriEnvironment()) {
      // Return a no-op function in web mode
      return () => {};
    }
    const unlisten = await listen(eventName, handler);
    this.listeners.set(eventName, unlisten);
    return unlisten;
  }

  async emitEvent(eventName: string, payload?: any): Promise<void> {
    if (!isTauriEnvironment()) {
      // No-op in web mode
      return;
    }
    return emit(eventName, payload);
  }

  private setupEventListeners(): void {
    if (!isTauriEnvironment()) {
      // Skip event listener setup in web mode
      return;
    }
    
    // Set up global event listeners for system events
    this.subscribeToEvents('sync-status-changed', (event) => {
      console.log('Sync status changed:', event.payload);
    });

    this.subscribeToEvents('error', (event) => {
      console.error('System error:', event.payload);
    });

    this.subscribeToEvents('cache-updated', (event) => {
      console.log('Cache updated:', event.payload);
    });
  }

  // ============= Cleanup =============

  destroy(): void {
    // Clean up all listeners
    this.listeners.forEach((unlisten) => {
      unlisten();
    });
    this.listeners.clear();
    this.initialized = false;
  }
}

// ============= Export =============

export const tauriService = TauriService.getInstance();

export function useTauriService() {
  return tauriService;
}

export default tauriService;