// Mock Insights API for web environment

export interface Note {
  id: string;
  title: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  tags?: string[];
}

export interface Source {
  id: string;
  title: string;
  type: 'document' | 'url' | 'file';
  content: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

// Mock data
const mockNotes: Note[] = [
  {
    id: '1',
    title: 'Sample Note',
    content: 'This is a sample note for the web environment.',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    tags: ['sample', 'demo']
  }
];

const mockSources: Source[] = [
  {
    id: '1',
    title: 'Sample Document',
    type: 'document',
    content: 'This is a sample document for the web environment.',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    metadata: { author: 'Demo User' }
  }
];

// Mock API functions
export const notesApi = {
  async getNotes(): Promise<Note[]> {
    console.log('Mock: Getting notes');
    return Promise.resolve([...mockNotes]);
  },

  async createNote(note: Omit<Note, 'id' | 'createdAt' | 'updatedAt'>): Promise<Note> {
    console.log('Mock: Creating note', note);
    const newNote: Note = {
      ...note,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    mockNotes.push(newNote);
    return Promise.resolve(newNote);
  },

  async updateNote(id: string, updates: Partial<Note>): Promise<Note> {
    console.log('Mock: Updating note', id, updates);
    const noteIndex = mockNotes.findIndex(n => n.id === id);
    if (noteIndex === -1) {
      throw new Error('Note not found');
    }
    const updatedNote = {
      ...mockNotes[noteIndex],
      ...updates,
      updatedAt: new Date().toISOString()
    };
    mockNotes[noteIndex] = updatedNote;
    return Promise.resolve(updatedNote);
  },

  async deleteNote(id: string): Promise<void> {
    console.log('Mock: Deleting note', id);
    const noteIndex = mockNotes.findIndex(n => n.id === id);
    if (noteIndex !== -1) {
      mockNotes.splice(noteIndex, 1);
    }
    return Promise.resolve();
  }
};

export const sourcesApi = {
  async getSources(): Promise<Source[]> {
    console.log('Mock: Getting sources');
    return Promise.resolve([...mockSources]);
  },

  async createSource(source: Omit<Source, 'id' | 'createdAt' | 'updatedAt'>): Promise<Source> {
    console.log('Mock: Creating source', source);
    const newSource: Source = {
      ...source,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    mockSources.push(newSource);
    return Promise.resolve(newSource);
  },

  async updateSource(id: string, updates: Partial<Source>): Promise<Source> {
    console.log('Mock: Updating source', id, updates);
    const sourceIndex = mockSources.findIndex(s => s.id === id);
    if (sourceIndex === -1) {
      throw new Error('Source not found');
    }
    const updatedSource = {
      ...mockSources[sourceIndex],
      ...updates,
      updatedAt: new Date().toISOString()
    };
    mockSources[sourceIndex] = updatedSource;
    return Promise.resolve(updatedSource);
  },

  async deleteSource(id: string): Promise<void> {
    console.log('Mock: Deleting source', id);
    const sourceIndex = mockSources.findIndex(s => s.id === id);
    if (sourceIndex !== -1) {
      mockSources.splice(sourceIndex, 1);
    }
    return Promise.resolve();
  }
};

// Source interface and API
export interface Source {
  id: string;
  title: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  created_at?: string;
  file_size?: number;
  source_type?: string;
  processing_status?: 'pending' | 'processing' | 'completed' | 'failed';
  file_path?: string;
}

// Notebook interface and API
export interface Notebook {
  id: string;
  title: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  notes?: Note[];
  generation_status?: 'pending' | 'processing' | 'completed' | 'failed';
}

export interface UpdateNotebookRequest {
  title?: string;
  content?: string;
  notes?: Note[];
  generation_status?: 'pending' | 'processing' | 'completed' | 'failed';
}

const mockNotebooks: Notebook[] = [
  {
    id: '1',
    title: 'Sample Notebook',
    content: 'This is a sample notebook for the web environment.',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    notes: []
  }
];

export const notebooksApi = {
  async getNotebook(id: string): Promise<Notebook | null> {
    console.log('Mock: Getting notebook', id);
    const notebook = mockNotebooks.find(n => n.id === id);
    return Promise.resolve(notebook || null);
  },

  async getNotebooks(): Promise<Notebook[]> {
    console.log('Mock: Getting notebooks');
    return Promise.resolve([...mockNotebooks]);
  },

  async createNotebook(notebook: Omit<Notebook, 'id' | 'createdAt' | 'updatedAt'>): Promise<Notebook> {
    console.log('Mock: Creating notebook', notebook);
    const newNotebook: Notebook = {
      ...notebook,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    mockNotebooks.push(newNotebook);
    return Promise.resolve(newNotebook);
  },

  async updateNotebook(id: string, updates: Partial<Notebook>): Promise<Notebook> {
    console.log('Mock: Updating notebook', id, updates);
    const notebookIndex = mockNotebooks.findIndex(n => n.id === id);
    if (notebookIndex === -1) {
      throw new Error('Notebook not found');
    }
    const updatedNotebook = {
      ...mockNotebooks[notebookIndex],
      ...updates,
      updatedAt: new Date().toISOString()
    };
    mockNotebooks[notebookIndex] = updatedNotebook;
    return Promise.resolve(updatedNotebook);
  },

  async deleteNotebook(id: string): Promise<void> {
    console.log('Mock: Deleting notebook', id);
    const notebookIndex = mockNotebooks.findIndex(n => n.id === id);
    if (notebookIndex !== -1) {
      mockNotebooks.splice(notebookIndex, 1);
    }
    return Promise.resolve();
  }
};

// Mock processSource function
const processSource = async (sourceId: string): Promise<Notebook> => {
  console.log('Mock: Processing source', sourceId);
  // Create a mock notebook from the source
  const notebook: Notebook = {
    id: Date.now().toString(),
    title: `Notebook from Source ${sourceId}`,
    content: 'Generated notebook content from source processing.',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    generation_status: 'completed'
  };
  return Promise.resolve(notebook);
};

// Default export for backward compatibility
export default {
  notes: notesApi,
  sources: sourcesApi,
  notebooks: notebooksApi,
  getNotebooks: notebooksApi.getNotebooks,
  getNotebook: notebooksApi.getNotebook,
  createNotebook: notebooksApi.createNotebook,
  updateNotebook: notebooksApi.updateNotebook,
  deleteNotebook: notebooksApi.deleteNotebook,
  getNotes: notesApi.getNotes,
  createNote: notesApi.createNote,
  updateNote: notesApi.updateNote,
  deleteNote: notesApi.deleteNote,
  getSources: sourcesApi.getSources,
  createSource: sourcesApi.createSource,
  updateSource: sourcesApi.updateSource,
  deleteSource: sourcesApi.deleteSource,
  processSource
};