import React from 'react';
import { tauriService } from './tauriService';
import { ErrorClassifier } from '../utils/error';
import { useNotificationService } from './notification';

/**
 * Centralized error handler that integrates frontend error handling
 * with backend error logging system
 */
export class ErrorHandler {
  private static instance: <PERSON>rror<PERSON>andler;
  private notificationService: any;

  private constructor() {
    // Initialize notification service
    this.notificationService = null;
  }

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * Set notification service instance (called from React components)
   */
  setNotificationService(notificationService: any) {
    this.notificationService = notificationService;
  }

  /**
   * Handle and log errors with backend integration
   */
  async handleError(
    error: Error,
    context: {
      component?: string;
      operation?: string;
      severity?: 'Low' | 'Medium' | 'High' | 'Critical';
      category?: string;
      showNotification?: boolean;
      additionalContext?: Record<string, any>;
    } = {}
  ): Promise<void> {
    const {
      component = 'Unknown',
      operation = 'Unknown',
      severity = this.determineSeverity(error),
      category = this.determineCategory(error),
      showNotification = true,
      additionalContext = {}
    } = context;

    // Create error code
    const errorCode = `${category.toUpperCase()}_${operation.toUpperCase()}_ERROR`;

    // Prepare context for backend logging
    const errorContext = {
      component,
      operation,
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'Unknown',
      timestamp: new Date().toISOString(),
      url: typeof window !== 'undefined' ? window.location.href : 'Unknown',
      stackTrace: error.stack,
      ...additionalContext
    };

    try {
      // Log to backend error system
      await tauriService.logError(
        errorCode,
        error.message,
        severity,
        category,
        errorContext
      );
    } catch (backendError) {
      // Fallback to console logging if backend fails
      console.error('Failed to log error to backend:', backendError);
      console.error('Original error:', error);
    }

    // Show user notification if requested
    if (showNotification && this.notificationService) {
      this.showUserNotification(error, severity, category);
    }

    // Console log for development
    if (process.env.NODE_ENV === 'development') {
      console.group(`🚨 Error in ${component}`);
      console.error('Error:', error);
      console.log('Context:', errorContext);
      console.log('Severity:', severity);
      console.log('Category:', category);
      console.groupEnd();
    }
  }

  /**
   * Handle async operation errors with loading states
   */
  async handleAsyncError<T>(
    operation: () => Promise<T>,
    context: {
      component?: string;
      operationName?: string;
      loadingMessage?: string;
      successMessage?: string;
      onSuccess?: (result: T) => void;
      onError?: (error: Error) => void;
      showLoadingNotification?: boolean;
    } = {}
  ): Promise<T | null> {
    const {
      component = 'Unknown',
      operationName = 'Operation',
      loadingMessage,
      successMessage,
      onSuccess,
      onError,
      showLoadingNotification = false
    } = context;

    let loadingNotificationId: string | null = null;

    try {
      // Show loading notification if requested
      if (showLoadingNotification && loadingMessage && this.notificationService) {
        loadingNotificationId = this.notificationService.showInfo({
          title: loadingMessage,
          duration: 0 // Keep until dismissed
        });
      }

      // Execute operation
      const result = await operation();

      // Dismiss loading notification
      if (loadingNotificationId && this.notificationService) {
        this.notificationService.dismiss(loadingNotificationId);
      }

      // Show success notification
      if (successMessage && this.notificationService) {
        this.notificationService.showSuccess({
          title: successMessage
        });
      }

      // Call success callback
      if (onSuccess) {
        onSuccess(result);
      }

      return result;
    } catch (error) {
      // Dismiss loading notification
      if (loadingNotificationId && this.notificationService) {
        this.notificationService.dismiss(loadingNotificationId);
      }

      // Handle the error
      await this.handleError(error as Error, {
        component,
        operation: operationName,
        showNotification: true
      });

      // Call error callback
      if (onError) {
        onError(error as Error);
      }

      return null;
    }
  }

  /**
   * Handle React component errors (for error boundaries)
   */
  async handleComponentError(
    error: Error,
    errorInfo: { componentStack: string },
    componentName: string
  ): Promise<void> {
    await this.handleError(error, {
      component: componentName,
      operation: 'Render',
      severity: 'High',
      category: 'UI',
      additionalContext: {
        componentStack: errorInfo.componentStack
      }
    });
  }

  /**
   * Determine error severity based on error characteristics
   */
  private determineSeverity(error: Error): 'Low' | 'Medium' | 'High' | 'Critical' {
    const message = error.message.toLowerCase();

    // Critical errors
    if (message.includes('crash') || message.includes('fatal') || message.includes('system')) {
      return 'Critical';
    }

    // High severity errors
    if (message.includes('auth') || message.includes('permission') || message.includes('security')) {
      return 'High';
    }

    // Medium severity errors
    if (message.includes('network') || message.includes('timeout') || message.includes('failed')) {
      return 'Medium';
    }

    // Default to Low
    return 'Low';
  }

  /**
   * Determine error category based on error characteristics
   */
  private determineCategory(error: Error): string {
    const errorType = ErrorClassifier.classifyError(error);
    
    switch (errorType) {
      case 'network':
        return 'Network';
      case 'permission':
        return 'Authentication';
      case 'validation':
        return 'Validation';
      case 'file':
        return 'FileSystem';
      case 'diagram':
        return 'Diagram';
      default:
        return 'General';
    }
  }

  /**
   * Show user-friendly notification based on error type
   */
  private showUserNotification(error: Error, severity: string, category: string): void {
    if (!this.notificationService) return;

    const message = this.getUserFriendlyMessage(error, category);
    const variant = severity === 'Critical' || severity === 'High' ? 'destructive' : 'default';

    this.notificationService.showError({
      title: 'Error',
      description: message,
      variant
    });
  }

  /**
   * Get user-friendly error message
   */
  private getUserFriendlyMessage(error: Error, category: string): string {
    switch (category) {
      case 'Network':
        return 'Unable to connect to the server. Please check your internet connection and try again.';
      case 'Authentication':
        return 'Authentication failed. Please check your credentials and try again.';
      case 'Validation':
        return 'The provided information is invalid. Please check your input and try again.';
      case 'FileSystem':
        return 'File operation failed. Please check file permissions and try again.';
      case 'Diagram':
        return 'Diagram operation failed. Please try again or contact support.';
      default:
        return error.message || 'An unexpected error occurred. Please try again.';
    }
  }

  /**
   * Get error statistics from backend
   */
  async getErrorStatistics(): Promise<{
    total_errors: number;
    by_severity: Record<string, number>;
    by_category: Record<string, number>;
  }> {
    try {
      return await tauriService.getErrorStatistics();
    } catch (error) {
      console.error('Failed to get error statistics:', error);
      return {
        total_errors: 0,
        by_severity: {},
        by_category: {}
      };
    }
  }

  /**
   * Get recent errors from backend
   */
  async getRecentErrors(limit = 50): Promise<any[]> {
    try {
      return await tauriService.getRecentErrors(limit);
    } catch (error) {
      console.error('Failed to get recent errors:', error);
      return [];
    }
  }

  /**
   * Clear error history
   */
  async clearErrorHistory(): Promise<void> {
    try {
      await tauriService.clearErrorHistory();
    } catch (error) {
      console.error('Failed to clear error history:', error);
    }
  }
}

// Export singleton instance
export const errorHandler = ErrorHandler.getInstance();

// React hook for using error handler
export function useErrorHandler() {
  const notificationService = useNotificationService();
  
  // Set notification service on the error handler
  React.useEffect(() => {
    errorHandler.setNotificationService(notificationService);
  }, [notificationService]);

  return {
    handleError: errorHandler.handleError.bind(errorHandler),
    handleAsyncError: errorHandler.handleAsyncError.bind(errorHandler),
    handleComponentError: errorHandler.handleComponentError.bind(errorHandler),
    getErrorStatistics: errorHandler.getErrorStatistics.bind(errorHandler),
    getRecentErrors: errorHandler.getRecentErrors.bind(errorHandler),
    clearErrorHistory: errorHandler.clearErrorHistory.bind(errorHandler)
  };
}

export default errorHandler;