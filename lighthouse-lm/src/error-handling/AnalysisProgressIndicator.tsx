import React, { useEffect, useState } from 'react';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { 
  FileText, 
  Brain, 
  Network, 
  Zap, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  X,
  Pause,
  Play
} from 'lucide-react';

export interface AnalysisStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'error' | 'skipped';
  progress: number;
  estimatedTime?: number;
  actualTime?: number;
  error?: string;
  details?: string;
}

export interface AnalysisProgressProps {
  steps: AnalysisStep[];
  currentStep?: string;
  overallProgress: number;
  isRunning: boolean;
  canCancel?: boolean;
  canPause?: boolean;
  isPaused?: boolean;
  onCancel?: () => void;
  onPause?: () => void;
  onResume?: () => void;
  estimatedTimeRemaining?: number;
  sourcesProcessed?: number;
  totalSources?: number;
  conceptsFound?: number;
  relationshipsFound?: number;
}

const stepIcons: Record<string, React.ComponentType<{ className?: string }>> = {
  'content-extraction': FileText,
  'concept-analysis': Brain,
  'relationship-mapping': Network,
  'diagram-generation': Zap,
  'validation': CheckCircle,
  'default': Clock
};

/**
 * Progress indicator for long-running source analysis operations
 * Shows detailed progress with step-by-step breakdown and metrics
 */
export const AnalysisProgressIndicator: React.FC<AnalysisProgressProps> = ({
  steps,
  currentStep,
  overallProgress,
  isRunning,
  canCancel = true,
  canPause = false,
  isPaused = false,
  onCancel,
  onPause,
  onResume,
  estimatedTimeRemaining,
  sourcesProcessed = 0,
  totalSources = 0,
  conceptsFound = 0,
  relationshipsFound = 0
}) => {
  const [elapsedTime, setElapsedTime] = useState(0);
  const [startTime] = useState(Date.now());

  useEffect(() => {
    if (!isRunning || isPaused) return;

    const interval = setInterval(() => {
      setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);

    return () => clearInterval(interval);
  }, [isRunning, isPaused, startTime]);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStepIcon = (stepId: string) => {
    const IconComponent = stepIcons[stepId] || stepIcons.default;
    return IconComponent;
  };

  const getStatusColor = (status: AnalysisStep['status']) => {
    switch (status) {
      case 'completed': return 'text-green-600';
      case 'running': return 'text-blue-600';
      case 'error': return 'text-red-600';
      case 'skipped': return 'text-yellow-600';
      default: return 'text-muted-foreground';
    }
  };

  const getStatusBadge = (status: AnalysisStep['status']) => {
    switch (status) {
      case 'completed': return <Badge variant="default" className="bg-green-100 text-green-800">Complete</Badge>;
      case 'running': return <Badge variant="default" className="bg-blue-100 text-blue-800">Running</Badge>;
      case 'error': return <Badge variant="destructive">Error</Badge>;
      case 'skipped': return <Badge variant="secondary">Skipped</Badge>;
      default: return <Badge variant="outline">Pending</Badge>;
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              Analyzing Sources
              {isPaused && <Badge variant="secondary">Paused</Badge>}
            </CardTitle>
            <CardDescription>
              Processing {totalSources} source{totalSources !== 1 ? 's' : ''} for diagram generation
            </CardDescription>
          </div>
          <div className="flex gap-2">
            {canPause && isRunning && (
              <Button
                variant="outline"
                size="sm"
                onClick={isPaused ? onResume : onPause}
              >
                {isPaused ? <Play className="h-4 w-4" /> : <Pause className="h-4 w-4" />}
              </Button>
            )}
            {canCancel && (
              <Button
                variant="outline"
                size="sm"
                onClick={onCancel}
                disabled={!isRunning}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Overall Progress</span>
            <span>{Math.round(overallProgress)}%</span>
          </div>
          <Progress value={overallProgress} className="h-2" />
        </div>

        {/* Time and Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className="space-y-1">
            <div className="text-muted-foreground">Elapsed</div>
            <div className="font-mono">{formatTime(elapsedTime)}</div>
          </div>
          {estimatedTimeRemaining !== undefined && (
            <div className="space-y-1">
              <div className="text-muted-foreground">Remaining</div>
              <div className="font-mono">{formatTime(estimatedTimeRemaining)}</div>
            </div>
          )}
          <div className="space-y-1">
            <div className="text-muted-foreground">Sources</div>
            <div className="font-mono">{sourcesProcessed}/{totalSources}</div>
          </div>
          <div className="space-y-1">
            <div className="text-muted-foreground">Concepts</div>
            <div className="font-mono">{conceptsFound}</div>
          </div>
        </div>

        {/* Step Details */}
        <div className="space-y-3">
          <h4 className="font-medium">Analysis Steps</h4>
          <div className="space-y-2">
            {steps.map((step) => {
              const IconComponent = getStepIcon(step.id);
              const isCurrentStep = currentStep === step.id;
              
              return (
                <div
                  key={step.id}
                  className={`flex items-center gap-3 p-3 rounded-lg border ${
                    isCurrentStep ? 'bg-blue-50 border-blue-200' : 'bg-background'
                  }`}
                >
                  <div className={`flex-shrink-0 ${getStatusColor(step.status)}`}>
                    {step.status === 'error' ? (
                      <AlertCircle className="h-4 w-4" />
                    ) : (
                      <IconComponent className="h-4 w-4" />
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <div className="font-medium">{step.name}</div>
                      {getStatusBadge(step.status)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {step.description}
                    </div>
                    {step.details && (
                      <div className="text-xs text-muted-foreground mt-1">
                        {step.details}
                      </div>
                    )}
                    {step.error && (
                      <div className="text-xs text-red-600 mt-1">
                        Error: {step.error}
                      </div>
                    )}
                  </div>
                  
                  {step.status === 'running' && (
                    <div className="flex-shrink-0 w-20">
                      <Progress value={step.progress} className="h-1" />
                    </div>
                  )}
                  
                  {step.actualTime && (
                    <div className="flex-shrink-0 text-xs text-muted-foreground font-mono">
                      {formatTime(step.actualTime)}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Additional Metrics */}
        {relationshipsFound > 0 && (
          <div className="pt-4 border-t">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="space-y-1">
                <div className="text-muted-foreground">Relationships Found</div>
                <div className="font-mono text-lg">{relationshipsFound}</div>
              </div>
              <div className="space-y-1">
                <div className="text-muted-foreground">Avg. Confidence</div>
                <div className="font-mono text-lg">
                  {conceptsFound > 0 ? '85%' : '0%'}
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

/**
 * Hook for managing analysis progress state
 */
export const useAnalysisProgress = () => {
  const [steps, setSteps] = useState<AnalysisStep[]>([]);
  const [currentStep, setCurrentStep] = useState<string>();
  const [overallProgress, setOverallProgress] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [metrics, setMetrics] = useState({
    sourcesProcessed: 0,
    totalSources: 0,
    conceptsFound: 0,
    relationshipsFound: 0,
    estimatedTimeRemaining: 0
  });

  const initializeSteps = (stepDefinitions: Omit<AnalysisStep, 'status' | 'progress'>[]) => {
    const initialSteps: AnalysisStep[] = stepDefinitions.map(step => ({
      ...step,
      status: 'pending',
      progress: 0
    }));
    setSteps(initialSteps);
    setOverallProgress(0);
  };

  const updateStep = (stepId: string, updates: Partial<AnalysisStep>) => {
    setSteps(prev => prev.map(step => 
      step.id === stepId ? { ...step, ...updates } : step
    ));
  };

  const setStepStatus = (stepId: string, status: AnalysisStep['status'], details?: string) => {
    updateStep(stepId, { status, details });
    if (status === 'running') {
      setCurrentStep(stepId);
    }
  };

  const setStepProgress = (stepId: string, progress: number) => {
    updateStep(stepId, { progress });
    
    // Calculate overall progress
    setSteps(prev => {
      const updatedSteps = prev.map(step => 
        step.id === stepId ? { ...step, progress } : step
      );
      const totalProgress = updatedSteps.reduce((sum, step) => sum + step.progress, 0);
      const avgProgress = totalProgress / updatedSteps.length;
      setOverallProgress(avgProgress);
      return updatedSteps;
    });
  };

  const completeStep = (stepId: string, actualTime?: number) => {
    updateStep(stepId, { 
      status: 'completed', 
      progress: 100,
      actualTime 
    });
  };

  const errorStep = (stepId: string, error: string) => {
    updateStep(stepId, { 
      status: 'error', 
      error 
    });
  };

  const updateMetrics = (newMetrics: Partial<typeof metrics>) => {
    setMetrics(prev => ({ ...prev, ...newMetrics }));
  };

  const start = () => {
    setIsRunning(true);
    setIsPaused(false);
  };

  const pause = () => {
    setIsPaused(true);
  };

  const resume = () => {
    setIsPaused(false);
  };

  const stop = () => {
    setIsRunning(false);
    setIsPaused(false);
    setCurrentStep(undefined);
  };

  const reset = () => {
    setSteps(prev => prev.map(step => ({
      ...step,
      status: 'pending',
      progress: 0,
      error: undefined,
      actualTime: undefined
    })));
    setCurrentStep(undefined);
    setOverallProgress(0);
    setIsRunning(false);
    setIsPaused(false);
    setMetrics({
      sourcesProcessed: 0,
      totalSources: 0,
      conceptsFound: 0,
      relationshipsFound: 0,
      estimatedTimeRemaining: 0
    });
  };

  return {
    steps,
    currentStep,
    overallProgress,
    isRunning,
    isPaused,
    metrics,
    initializeSteps,
    updateStep,
    setStepStatus,
    setStepProgress,
    completeStep,
    errorStep,
    updateMetrics,
    start,
    pause,
    resume,
    stop,
    reset
  };
};