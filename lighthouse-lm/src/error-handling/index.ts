// Error Boundary Components
export { DiagramErrorBoundary } from './DiagramErrorBoundary';

// Progress Indicators
export { 
  AnalysisProgressIndicator, 
  useAnalysisProgress,
  type AnalysisStep,
  type AnalysisProgressProps 
} from './AnalysisProgressIndicator';

// Validation Services
export { 
  ValidationService,
  type ValidationResult,
  type ValidationError,
  type ValidationWarning,
  type ContentComplexityMetrics
} from './ValidationService';

// Error Notification System
export { 
  ErrorNotificationService,
  useErrorNotifications,
  type ErrorNotification,
  type ErrorAction,
  type ErrorRecoveryOption
} from './ErrorNotificationService';

// Comprehensive Error Handler
export { 
  DiagramGenerationErrorHandler,
  default as DiagramGenerationErrorHandlerDefault
} from './DiagramGenerationErrorHandler';

// Enhanced Services
export { 
  EnhancedSourceDiagramService,
  type EnhancedGenerationOptions,
  type GenerationProgress,
  type GenerationResult
} from '../../../services/enhancedSourceDiagramService';

// Utility functions for error handling
export const ErrorHandlingUtils = {
  /**
   * Create a standardized error message for diagram operations
   */
  createDiagramError: (
    operation: string,
    context: string,
    originalError: Error
  ): Error => {
    return new Error(`${operation} failed in ${context}: ${originalError.message}`);
  },

  /**
   * Check if an error is recoverable
   */
  isRecoverableError: (error: Error): boolean => {
    const recoverablePatterns = [
      /timeout/i,
      /network/i,
      /temporary/i,
      /retry/i,
      /rate limit/i
    ];
    
    return recoverablePatterns.some(pattern => pattern.test(error.message));
  },

  /**
   * Get error severity level
   */
  getErrorSeverity: (error: Error): 'low' | 'medium' | 'high' | 'critical' => {
    const message = error.message.toLowerCase();
    
    if (message.includes('critical') || message.includes('fatal')) {
      return 'critical';
    }
    if (message.includes('memory') || message.includes('crash')) {
      return 'high';
    }
    if (message.includes('timeout') || message.includes('network')) {
      return 'medium';
    }
    return 'low';
  },

  /**
   * Format error for user display
   */
  formatErrorForUser: (error: Error, context?: string): string => {
    const contextPrefix = context ? `[${context}] ` : '';
    
    // Remove technical stack traces and internal details
    let message = error.message
      .replace(/at\s+.*\n?/g, '') // Remove stack trace lines
      .replace(/Error:\s*/g, '') // Remove "Error:" prefix
      .trim();
    
    // Capitalize first letter
    message = message.charAt(0).toUpperCase() + message.slice(1);
    
    return `${contextPrefix}${message}`;
  },

  /**
   * Create recovery suggestions based on error type
   */
  getRecoverySuggestions: (error: Error): string[] => {
    const message = error.message.toLowerCase();
    const suggestions: string[] = [];
    
    if (message.includes('timeout')) {
      suggestions.push('Try reducing the number of sources');
      suggestions.push('Increase the timeout setting');
      suggestions.push('Check your internet connection');
    }
    
    if (message.includes('memory')) {
      suggestions.push('Reduce the maximum concepts setting');
      suggestions.push('Split large sources into smaller parts');
      suggestions.push('Close other applications to free memory');
    }
    
    if (message.includes('network') || message.includes('connection')) {
      suggestions.push('Check your internet connection');
      suggestions.push('Try again in a few moments');
      suggestions.push('Verify server availability');
    }
    
    if (message.includes('validation') || message.includes('invalid')) {
      suggestions.push('Check source content format');
      suggestions.push('Verify all required fields are filled');
      suggestions.push('Review generation options');
    }
    
    if (suggestions.length === 0) {
      suggestions.push('Try the operation again');
      suggestions.push('Contact support if the issue persists');
    }
    
    return suggestions;
  }
};