import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ErrorNotificationService } from '../ErrorNotificationService';

// Mock the toast function
vi.mock('@/components/lighthouse-lm/hooks/use-toast', () => ({
  toast: vi.fn()
}));

describe('ErrorNotificationService', () => {
  beforeEach(() => {
    // Clear all notifications before each test
    ErrorNotificationService.dismissAll();
    vi.clearAllMocks();
  });

  describe('showError', () => {
    it('should create and store error notification', () => {
      const id = ErrorNotificationService.showError(
        'Test Error',
        'This is a test error message',
        'generation'
      );

      expect(id).toBeDefined();
      expect(typeof id).toBe('string');

      const notifications = ErrorNotificationService.getNotifications();
      expect(notifications).toHaveLength(1);
      expect(notifications[0].type).toBe('error');
      expect(notifications[0].title).toBe('Test Error');
      expect(notifications[0].message).toBe('This is a test error message');
      expect(notifications[0].context).toBe('generation');
    });

    it('should include recovery options in actions', () => {
      const recoveryOptions = [
        {
          id: 'retry',
          label: 'Retry',
          description: 'Try again',
          action: vi.fn(),
          confidence: 0.8
        },
        {
          id: 'reduce',
          label: 'Reduce Complexity',
          description: 'Simplify options',
          action: vi.fn(),
          confidence: 0.9
        }
      ];

      ErrorNotificationService.showError(
        'Test Error',
        'Error with recovery',
        'analysis',
        recoveryOptions
      );

      const notifications = ErrorNotificationService.getNotifications();
      const notification = notifications[0];
      
      expect(notification.actions).toBeDefined();
      expect(notification.actions!.length).toBeGreaterThan(2); // Recovery options + dismiss
      expect(notification.actions![0].label).toBe('Reduce Complexity'); // Highest confidence first
      expect(notification.actions![1].label).toBe('Retry');
    });

    it('should set persistent flag for errors', () => {
      ErrorNotificationService.showError('Test', 'Message');
      
      const notifications = ErrorNotificationService.getNotifications();
      expect(notifications[0].persistent).toBe(true);
      expect(notifications[0].autoHide).toBe(false);
    });
  });

  describe('showWarning', () => {
    it('should create warning notification with auto-hide', () => {
      const id = ErrorNotificationService.showWarning(
        'Test Warning',
        'This is a warning',
        'validation'
      );

      const notifications = ErrorNotificationService.getNotifications();
      const notification = notifications[0];
      
      expect(notification.type).toBe('warning');
      expect(notification.persistent).toBe(false);
      expect(notification.autoHide).toBe(true);
      expect(notification.duration).toBe(8000);
    });
  });

  describe('showSuccess', () => {
    it('should create success notification with shorter duration', () => {
      ErrorNotificationService.showSuccess(
        'Success',
        'Operation completed',
        'export'
      );

      const notifications = ErrorNotificationService.getNotifications();
      const notification = notifications[0];
      
      expect(notification.type).toBe('success');
      expect(notification.duration).toBe(4000);
    });
  });

  describe('showInfo', () => {
    it('should create info notification', () => {
      ErrorNotificationService.showInfo(
        'Information',
        'This is info',
        'generation'
      );

      const notifications = ErrorNotificationService.getNotifications();
      expect(notifications[0].type).toBe('info');
      expect(notifications[0].duration).toBe(6000);
    });
  });

  describe('showValidationErrors', () => {
    it('should format validation errors correctly', () => {
      const errors = [
        { message: 'Field is required', field: 'name' },
        { message: 'Invalid format', field: 'email' }
      ];
      const suggestions = ['Check all fields', 'Verify format'];

      ErrorNotificationService.showValidationErrors(errors, suggestions);

      const notifications = ErrorNotificationService.getNotifications();
      const notification = notifications[0];
      
      expect(notification.title).toBe('Validation Failed');
      expect(notification.message).toContain('• Field is required');
      expect(notification.message).toContain('• Invalid format');
      expect(notification.message).toContain('Suggestions:');
      expect(notification.message).toContain('• Check all fields');
    });
  });

  describe('showAnalysisError', () => {
    it('should provide timeout-specific recovery options', () => {
      const timeoutError = new Error('Operation timed out after 30 seconds');
      
      ErrorNotificationService.showAnalysisError(timeoutError, 5, 'concept-extraction');

      const notifications = ErrorNotificationService.getNotifications();
      const notification = notifications[0];
      
      expect(notification.title).toBe('Analysis Failed');
      expect(notification.message).toContain('5 sources');
      expect(notification.message).toContain('concept-extraction');
      
      expect(notification.actions!.some(a => a.label === 'Reduce Sources')).toBe(true);
      expect(notification.actions!.some(a => a.label === 'Increase Timeout')).toBe(true);
    });

    it('should provide memory-specific recovery options', () => {
      const memoryError = new Error('Out of memory during processing');
      
      ErrorNotificationService.showAnalysisError(memoryError, 3);

      const notifications = ErrorNotificationService.getNotifications();
      const notification = notifications[0];
      
      expect(notification.actions!.some(a => a.label === 'Reduce Complexity')).toBe(true);
    });
  });

  describe('showGenerationError', () => {
    it('should provide generation-specific recovery options', () => {
      const genError = new Error('Failed to generate flowchart');
      
      ErrorNotificationService.showGenerationError(genError, 'flowchart');

      const notifications = ErrorNotificationService.getNotifications();
      const notification = notifications[0];
      
      expect(notification.title).toBe('Diagram Generation Failed');
      expect(notification.message).toContain('flowchart');
      expect(notification.actions!.some(a => a.label === 'Try Different Type')).toBe(true);
      expect(notification.actions!.some(a => a.label === 'Simplify Options')).toBe(true);
    });
  });

  describe('showExportError', () => {
    it('should provide export-specific recovery options', () => {
      const exportError = new Error('Failed to export as PNG');
      
      ErrorNotificationService.showExportError(exportError, 'png');

      const notifications = ErrorNotificationService.getNotifications();
      const notification = notifications[0];
      
      expect(notification.title).toBe('Export Failed');
      expect(notification.message).toContain('PNG');
      expect(notification.actions!.some(a => a.label === 'Try Different Format')).toBe(true);
    });
  });

  describe('dismiss', () => {
    it('should remove specific notification', () => {
      const id1 = ErrorNotificationService.showError('Error 1', 'Message 1');
      const id2 = ErrorNotificationService.showError('Error 2', 'Message 2');

      expect(ErrorNotificationService.getNotifications()).toHaveLength(2);

      ErrorNotificationService.dismiss(id1);
      
      const remaining = ErrorNotificationService.getNotifications();
      expect(remaining).toHaveLength(1);
      expect(remaining[0].id).toBe(id2);
    });
  });

  describe('dismissAll', () => {
    it('should remove all notifications', () => {
      ErrorNotificationService.showError('Error 1', 'Message 1');
      ErrorNotificationService.showWarning('Warning 1', 'Message 1');
      ErrorNotificationService.showSuccess('Success 1', 'Message 1');

      expect(ErrorNotificationService.getNotifications()).toHaveLength(3);

      ErrorNotificationService.dismissAll();
      
      expect(ErrorNotificationService.getNotifications()).toHaveLength(0);
    });
  });

  describe('dismissByContext', () => {
    it('should remove notifications by context', () => {
      ErrorNotificationService.showError('Error 1', 'Message 1', 'generation');
      ErrorNotificationService.showError('Error 2', 'Message 2', 'analysis');
      ErrorNotificationService.showError('Error 3', 'Message 3', 'generation');

      expect(ErrorNotificationService.getNotifications()).toHaveLength(3);

      ErrorNotificationService.dismissByContext('generation');
      
      const remaining = ErrorNotificationService.getNotifications();
      expect(remaining).toHaveLength(1);
      expect(remaining[0].context).toBe('analysis');
    });
  });

  describe('getErrorStats', () => {
    it('should calculate error statistics correctly', () => {
      ErrorNotificationService.showError('Error 1', 'Message 1', 'generation');
      ErrorNotificationService.showError('Error 2', 'Message 2', 'analysis');
      ErrorNotificationService.showError('Error 3', 'Message 3', 'generation');
      ErrorNotificationService.showWarning('Warning 1', 'Message 1', 'validation');

      const stats = ErrorNotificationService.getErrorStats();
      
      expect(stats.totalErrors).toBe(3);
      expect(stats.errorsByContext.generation).toBe(2);
      expect(stats.errorsByContext.analysis).toBe(1);
      expect(stats.errorsByType.error).toBe(3);
      expect(stats.recentErrors).toHaveLength(3);
    });
  });

  describe('subscribe', () => {
    it('should notify listeners of changes', () => {
      const listener = vi.fn();
      const unsubscribe = ErrorNotificationService.subscribe(listener);

      ErrorNotificationService.showError('Test', 'Message');
      
      expect(listener).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            title: 'Test',
            message: 'Message'
          })
        ])
      );

      unsubscribe();
      
      ErrorNotificationService.showError('Test 2', 'Message 2');
      
      // Should not be called again after unsubscribe
      expect(listener).toHaveBeenCalledTimes(1);
    });
  });
});