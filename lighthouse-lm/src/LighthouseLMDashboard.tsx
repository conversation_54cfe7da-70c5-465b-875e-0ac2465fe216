import React, { useState, Suspense, lazy } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ErrorBoundary } from 'react-error-boundary';
import LoadingSkeleton from './components/layout/LoadingSkeleton';

// Lazy load main components for better performance
const Dashboard = lazy(() => import('./components/dashboard/Dashboard'));
const NotebookPage = lazy(() => import('./components/notebook/NotebookPage'));

// Error fallback component
const ErrorFallback = ({ error, resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void }) => (
  <div className="flex items-center justify-center h-screen">
    <div className="text-center">
      <h2 className="text-lg font-semibold mb-2">Something went wrong</h2>
      <p className="text-muted-foreground mb-4">{error.message}</p>
      <button 
        onClick={resetErrorBoundary}
        className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
      >
        Try again
      </button>
    </div>
  </div>
);

// Create a separate query client for LighthouseLM to avoid conflicts
const lighthouseLmQueryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

const LighthouseLMDashboard = () => {
  const [selectedNotebookId, setSelectedNotebookId] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'dashboard' | 'notebook'>('dashboard');

  const handleNotebookSelect = (notebookId: string) => {
    // 
    setSelectedNotebookId(notebookId);
    setViewMode('notebook');
  };

  const handleBackToDashboard = () => {
    // 
    setViewMode('dashboard');
    setSelectedNotebookId(null);
  };

  return (
    <div className="h-full w-full overflow-hidden bg-background">
      <ErrorBoundary FallbackComponent={ErrorFallback}>
        <QueryClientProvider client={lighthouseLmQueryClient}>
          <Suspense fallback={<LoadingSkeleton />}>
            {viewMode === 'dashboard' ? (
              <Dashboard onNotebookSelect={handleNotebookSelect} />
            ) : selectedNotebookId ? (
              <NotebookPage 
                notebookId={selectedNotebookId} 
                onBack={handleBackToDashboard}
              />
            ) : (
              <Dashboard onNotebookSelect={handleNotebookSelect} />
            )}
          </Suspense>
        </QueryClientProvider>
      </ErrorBoundary>
    </div>
  );
};

export default LighthouseLMDashboard;