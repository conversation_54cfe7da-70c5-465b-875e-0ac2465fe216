/**
 * Analytics type definitions for lighthouse-lm
 */

export interface AnalyticsTimeRange {
  start: Date;
  end: Date;
  preset?: 'today' | '7days' | '30days' | '90days' | 'custom';
}

export interface SessionMetrics {
  sessionId: string;
  startTime: Date;
  endTime?: Date;
  duration: number; // in minutes
  notebookId: string;
  userId?: string;
  activeTime: number; // actual active time in minutes
  idleTime: number;
  focusMode: 'chat' | 'sources' | 'studio' | 'split';
  devices: string[];
}

export interface ContentMetrics {
  notebookId: string;
  notesCreated: number;
  notesModified: number;
  sourcesAdded: number;
  sourcesAnalyzed: number;
  chatMessages: number;
  studioDocuments: number;
  totalWords: number;
  totalCharacters: number;
  contentTypes: Record<string, number>;
}

export interface ProductivityMetrics {
  focusScore: number; // 0-100
  productivityScore: number; // 0-100
  distractionCount: number;
  deepWorkMinutes: number;
  shallowWorkMinutes: number;
  contextSwitches: number;
  averageTaskDuration: number;
  completedTasks: number;
  efficiency: number; // tasks completed per hour
}

export interface FeatureUsage {
  featureName: string;
  usageCount: number;
  timeSpent: number;
  lastUsed: Date;
  userSatisfaction?: number; // 1-5 rating
}

export interface SearchAnalytics {
  query: string;
  timestamp: Date;
  resultCount: number;
  clickedResults: number;
  searchType: 'source' | 'chat' | 'notebook' | 'global';
  timeToFirstClick?: number;
  sessionId: string;
}

export interface CollaborationMetrics {
  sharedNotebooks: number;
  collaborators: number;
  commentsGiven: number;
  commentsReceived: number;
  reviewsCompleted: number;
  sharingFrequency: number;
}

export interface GoalMetrics {
  goalId: string;
  title: string;
  targetValue: number;
  currentValue: number;
  unit: string;
  deadline?: Date;
  category: 'productivity' | 'content' | 'learning' | 'collaboration';
  status: 'active' | 'completed' | 'paused' | 'missed';
  progress: number; // 0-100
}

export interface InsightData {
  id: string;
  type: 'suggestion' | 'warning' | 'achievement' | 'trend';
  title: string;
  description: string;
  category: 'productivity' | 'usage' | 'content' | 'performance';
  impact: 'low' | 'medium' | 'high';
  confidence: number; // 0-100
  generatedAt: Date;
  actionable: boolean;
  actions?: {
    label: string;
    action: string;
    data?: any;
  }[];
}

export interface AnalyticsConfig {
  trackingEnabled: boolean;
  anonymousMode: boolean;
  dataRetentionDays: number;
  reportingFrequency: 'daily' | 'weekly' | 'monthly';
  goalReminders: boolean;
  insightNotifications: boolean;
  performanceAlerts: boolean;
}

export interface AnalyticsDashboardData {
  timeRange: AnalyticsTimeRange;
  sessions: SessionMetrics[];
  contentMetrics: ContentMetrics[];
  productivityMetrics: ProductivityMetrics;
  featureUsage: FeatureUsage[];
  searchAnalytics: SearchAnalytics[];
  collaborationMetrics: CollaborationMetrics;
  goals: GoalMetrics[];
  insights: InsightData[];
  config: AnalyticsConfig;
}

export interface ChartDataPoint {
  timestamp: Date;
  value: number;
  label?: string;
  category?: string;
  metadata?: Record<string, any>;
}

export interface HeatmapDataPoint {
  hour: number;
  day: number;
  value: number;
  intensity: 'low' | 'medium' | 'high';
}

export interface ExportOptions {
  format: 'pdf' | 'csv' | 'json' | 'xlsx';
  includeCharts: boolean;
  includeRawData: boolean;
  dateRange: AnalyticsTimeRange;
  sections: string[];
  customFields?: string[];
}

export interface AnalyticsFilters {
  timeRange: AnalyticsTimeRange;
  notebooks?: string[];
  features?: string[];
  contentTypes?: string[];
  focusModes?: string[];
  minSessionDuration?: number;
  maxSessionDuration?: number;
}