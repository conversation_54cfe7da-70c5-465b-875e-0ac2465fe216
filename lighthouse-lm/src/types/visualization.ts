/**
 * Type definitions for the Advanced Visualization System
 */

export type VisualizationType = 
  | 'chart' 
  | 'mindmap' 
  | 'network' 
  | 'timeline' 
  | 'kanban'
  | 'flowchart'
  | 'gantt';

export type ChartType = 
  | 'line' 
  | 'bar' 
  | 'area' 
  | 'pie' 
  | 'scatter' 
  | 'radar'
  | 'treemap'
  | 'funnel';

export interface BaseVisualization {
  id: string;
  title: string;
  description?: string;
  type: VisualizationType;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;
  tags?: string[];
  isPublic?: boolean;
  version: number;
}

// Chart Data Structures
export interface ChartDataPoint {
  [key: string]: any;
}

export interface ChartSeries {
  name: string;
  data: ChartDataPoint[];
  color?: string;
  type?: ChartType;
}

export interface ChartVisualization extends BaseVisualization {
  type: 'chart';
  chartType: ChartType;
  data: ChartDataPoint[];
  series?: ChartSeries[];
  xAxisKey?: string;
  yAxisKey?: string;
  config: ChartConfig;
}

export interface ChartConfig {
  title?: string;
  subtitle?: string;
  xAxis?: {
    label?: string;
    type?: 'category' | 'number' | 'time';
    tickFormat?: string;
  };
  yAxis?: {
    label?: string;
    type?: 'number' | 'percentage';
    tickFormat?: string;
  };
  legend?: {
    show?: boolean;
    position?: 'top' | 'bottom' | 'left' | 'right';
  };
  grid?: boolean;
  colors?: string[];
  responsive?: boolean;
}

// Mind Map Structures
export interface MindMapNode {
  id: string;
  label: string;
  x: number;
  y: number;
  level: number;
  parentId?: string;
  children?: string[];
  color?: string;
  shape?: 'rectangle' | 'circle' | 'ellipse';
  size?: 'small' | 'medium' | 'large';
  data?: Record<string, any>;
}

export interface MindMapEdge {
  id: string;
  source: string;
  target: string;
  label?: string;
  type?: 'straight' | 'bezier' | 'step';
  animated?: boolean;
  style?: Record<string, any>;
}

export interface MindMapVisualization extends BaseVisualization {
  type: 'mindmap';
  nodes: MindMapNode[];
  edges: MindMapEdge[];
  centralNode?: string;
  layout?: 'radial' | 'tree' | 'force';
}

// Network Graph Structures
export interface NetworkNode {
  id: string;
  label: string;
  group?: string;
  size?: number;
  color?: string;
  shape?: 'circle' | 'square' | 'triangle' | 'diamond';
  data?: Record<string, any>;
}

export interface NetworkEdge {
  id: string;
  source: string;
  target: string;
  weight?: number;
  label?: string;
  color?: string;
  width?: number;
  type?: 'solid' | 'dashed' | 'dotted';
}

export interface NetworkVisualization extends BaseVisualization {
  type: 'network';
  nodes: NetworkNode[];
  edges: NetworkEdge[];
  layout?: 'force' | 'circular' | 'hierarchical' | 'grid';
  clustering?: boolean;
}

// Timeline Structures
export interface TimelineItem {
  id: string;
  title: string;
  description?: string;
  startDate: Date;
  endDate?: Date;
  category?: string;
  color?: string;
  status?: 'planned' | 'in-progress' | 'completed' | 'cancelled';
  priority?: 'low' | 'medium' | 'high' | 'critical';
  assignee?: string;
  progress?: number; // 0-100
  dependencies?: string[];
  data?: Record<string, any>;
}

export interface TimelineVisualization extends BaseVisualization {
  type: 'timeline';
  items: TimelineItem[];
  viewType?: 'timeline' | 'gantt';
  startDate?: Date;
  endDate?: Date;
  groupBy?: 'category' | 'assignee' | 'status';
}

// Kanban Structures
export interface KanbanCard {
  id: string;
  title: string;
  description?: string;
  labels?: string[];
  assignee?: string;
  priority?: 'low' | 'medium' | 'high' | 'critical';
  dueDate?: Date;
  createdAt: Date;
  color?: string;
  attachments?: number;
  comments?: number;
  checklist?: {
    total: number;
    completed: number;
  };
  data?: Record<string, any>;
}

export interface KanbanColumn {
  id: string;
  title: string;
  color?: string;
  limit?: number;
  cards: KanbanCard[];
  position: number;
}

export interface KanbanVisualization extends BaseVisualization {
  type: 'kanban';
  columns: KanbanColumn[];
  swimlanes?: boolean;
  cardTemplate?: Partial<KanbanCard>;
}

// Export Options
export interface ExportOptions {
  format: 'png' | 'svg' | 'pdf' | 'json';
  quality?: 'low' | 'medium' | 'high';
  width?: number;
  height?: number;
  background?: 'transparent' | 'white' | 'dark';
  includeTitle?: boolean;
  includeMetadata?: boolean;
}

// Collaboration Features
export interface CollaborationCursor {
  userId: string;
  userName: string;
  color: string;
  position: { x: number; y: number };
  timestamp: Date;
}

export interface Comment {
  id: string;
  userId: string;
  userName: string;
  content: string;
  position?: { x: number; y: number };
  timestamp: Date;
  replies?: Comment[];
}

// Theme Support
export interface VisualizationTheme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    border: string;
    accent: string[];
  };
  fonts: {
    primary: string;
    monospace: string;
  };
  spacing: {
    small: number;
    medium: number;
    large: number;
  };
}

// Plugin System
export interface VisualizationPlugin {
  id: string;
  name: string;
  version: string;
  description: string;
  supportedTypes: VisualizationType[];
  component: React.ComponentType<any>;
  config?: Record<string, any>;
}

// Event System
export type VisualizationEvent = 
  | 'node-click'
  | 'node-hover'
  | 'edge-click'
  | 'canvas-click'
  | 'zoom-change'
  | 'selection-change'
  | 'data-update';

export interface VisualizationEventData {
  type: VisualizationEvent;
  target?: string;
  data?: any;
  timestamp: Date;
}

// Integration Types
export interface DataSource {
  id: string;
  name: string;
  type: 'api' | 'file' | 'database' | 'static';
  config: Record<string, any>;
  lastSync?: Date;
}

export interface VisualizationTemplate {
  id: string;
  name: string;
  description: string;
  type: VisualizationType;
  thumbnail?: string;
  config: Record<string, any>;
  sampleData?: any;
  category?: string;
}

// State Management
export interface VisualizationState {
  activeVisualization?: BaseVisualization;
  visualizations: BaseVisualization[];
  templates: VisualizationTemplate[];
  themes: VisualizationTheme[];
  plugins: VisualizationPlugin[];
  collaborators: CollaborationCursor[];
  comments: Comment[];
  isLoading: boolean;
  error?: string;
}

// Props Interfaces
export interface VisualizationEngineProps {
  visualization?: BaseVisualization;
  theme?: VisualizationTheme;
  editable?: boolean;
  showToolbar?: boolean;
  onUpdate?: (visualization: BaseVisualization) => void;
  onExport?: (options: ExportOptions) => void;
  onEvent?: (event: VisualizationEventData) => void;
  plugins?: VisualizationPlugin[];
}

export interface ChartBuilderProps {
  data?: ChartDataPoint[];
  chartType?: ChartType;
  config?: ChartConfig;
  theme?: VisualizationTheme;
  onConfigChange?: (config: ChartConfig) => void;
  onDataChange?: (data: ChartDataPoint[]) => void;
}

export interface MindMapEditorProps {
  nodes?: MindMapNode[];
  edges?: MindMapEdge[];
  theme?: VisualizationTheme;
  editable?: boolean;
  onNodesChange?: (nodes: MindMapNode[]) => void;
  onEdgesChange?: (edges: MindMapEdge[]) => void;
  onNodeClick?: (node: MindMapNode) => void;
}

export interface NetworkGraphProps {
  nodes?: NetworkNode[];
  edges?: NetworkEdge[];
  layout?: 'force' | 'circular' | 'hierarchical' | 'grid';
  theme?: VisualizationTheme;
  interactive?: boolean;
  onNodeClick?: (node: NetworkNode) => void;
  onEdgeClick?: (edge: NetworkEdge) => void;
}

export interface TimelineVisualizerProps {
  items?: TimelineItem[];
  viewType?: 'timeline' | 'gantt';
  theme?: VisualizationTheme;
  editable?: boolean;
  onItemsChange?: (items: TimelineItem[]) => void;
  onItemClick?: (item: TimelineItem) => void;
}

export interface KanbanBoardProps {
  columns?: KanbanColumn[];
  theme?: VisualizationTheme;
  editable?: boolean;
  onColumnsChange?: (columns: KanbanColumn[]) => void;
  onCardMove?: (cardId: string, sourceColumn: string, targetColumn: string) => void;
  onCardClick?: (card: KanbanCard) => void;
}