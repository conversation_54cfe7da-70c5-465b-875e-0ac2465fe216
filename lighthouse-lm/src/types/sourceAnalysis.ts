/**
 * Core interfaces for source analysis and diagram generation
 */

export interface SourceAnalysisResult {
  source_id: string;
  concepts: ExtractedConcept[];
  relationships: ExtractedRelationship[];
  hierarchy: HierarchyNode[];
  metadata: AnalysisMetadata;
}

export interface ExtractedConcept {
  id: string;
  text: string;
  type: ConceptType;
  position: TextPosition;
  confidence: number;
  context: string;
  sourceIds: string[];
}

export interface ExtractedRelationship {
  id: string;
  from_concept: string;
  to_concept: string;
  relationship_type: RelationshipType;
  evidence: string;
  confidence: number;
  sourceIds: string[];
}

export interface HierarchyNode {
  id: string;
  concept_id: string;
  parent_id?: string;
  children: string[];
  level: number;
  weight: number;
}

export interface AnalysisMetadata {
  analysis_timestamp: string;
  processing_time_ms: number;
  concepts_found: number;
  relationships_found: number;
  confidence_average: number;
  content_length: number;
  language?: string;
}

export interface TextPosition {
  start: number;
  end: number;
  line?: number;
  column?: number;
}

export type ConceptType = 
  | 'entity'
  | 'process' 
  | 'decision'
  | 'outcome'
  | 'topic'
  | 'person'
  | 'organization'
  | 'location'
  | 'date'
  | 'number';

export type RelationshipType = 
  | 'depends_on'
  | 'leads_to'
  | 'contains'
  | 'references'
  | 'similar_to'
  | 'part_of'
  | 'causes'
  | 'enables'
  | 'requires';

export interface MultiSourceAnalysisResult {
  sources: SourceAnalysisResult[];
  cross_source_relationships: CrossSourceRelationship[];
  concept_clusters: ConceptCluster[];
  similarity_matrix: SimilarityMatrix;
  global_metadata: GlobalAnalysisMetadata;
}

export interface CrossSourceRelationship {
  id: string;
  source_a: string;
  source_b: string;
  relationship_type: RelationshipType;
  strength: number;
  evidence: string[];
  shared_concepts: string[];
}

export interface ConceptCluster {
  id: string;
  name: string;
  concepts: string[];
  sources: string[];
  centrality: number;
  coherence: number;
}

export interface SimilarityMatrix {
  sources: string[];
  matrix: number[][];
  threshold: number;
}

export interface GlobalAnalysisMetadata {
  total_sources: number;
  total_concepts: number;
  total_relationships: number;
  analysis_timestamp: string;
  processing_time_ms: number;
  average_confidence: number;
}

export interface DiagramGenerationOptions {
  diagram_type: DiagramType;
  max_concepts: number;
  relationship_depth: number;
  include_metadata: boolean;
  filter_by_relevance: boolean;
  minimum_confidence: number;
  layout_algorithm?: 'hierarchical' | 'force_directed' | 'circular';
  color_scheme?: 'default' | 'categorical' | 'confidence_based';
}

export type DiagramType = 
  | 'flowchart'
  | 'mindmap'
  | 'relationship'
  | 'hierarchy'
  | 'timeline'
  | 'concept_map';

export interface SourceDiagramMetadata {
  source_ids: string[];
  generation_options: DiagramGenerationOptions;
  node_source_map: Record<string, string[]>;
  clickable_nodes: string[];
  source_attribution: SourceAttribution[];
  analysis_confidence: number;
  generation_timestamp: string;
}

export interface SourceAttribution {
  node_id: string;
  source_id: string;
  content_excerpt: string;
  confidence: number;
  position?: TextPosition;
}