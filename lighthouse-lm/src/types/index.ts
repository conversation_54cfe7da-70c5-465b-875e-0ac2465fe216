/**
 * Consolidated type definitions for lighthouse-lm
 * Single source of truth for all types
 */

// Re-export from message types
export type { 
  MessageSegment,
  Citation,
  EnhancedChatMessage 
} from './message';

// Re-export from chat types (removing duplicates)
export type {
  MessageContent,
  ChatMessage,
  ExampleQuestion,
  Source,
  ChatAreaProps,
  ChatMessagesProps,
  ChatInputProps,
  ChatHeaderProps,
  EmptyChatStateProps,
  SourceDiagram,
  ChatDiagramIntegration,
  MessageType,
  ProcessingStatus,
  DiagramType
} from './chat';

// Re-export from sourceAnalysis types
export type {
  SourceAnalysisResult,
  AnalysisError,
  AnalysisProgress
} from './sourceAnalysis';

// Re-export from analytics types
export type {
  AnalyticsTimeRange,
  SessionMetrics,
  ContentMetrics,
  ProductivityMetrics,
  FeatureUsage,
  SearchAnalytics,
  CollaborationMetrics,
  GoalMetrics,
  InsightData,
  AnalyticsConfig,
  AnalyticsDashboardData,
  ChartDataPoint,
  HeatmapDataPoint,
  ExportOptions,
  AnalyticsFilters
} from './analytics';

// Re-export from visualization types
export type {
  VisualizationType,
  ChartType,
  BaseVisualization,
  ChartVisualization,
  ChartConfig,
  ChartSeries,
  MindMapNode,
  MindMapEdge,
  MindMapVisualization,
  NetworkNode,
  NetworkEdge,
  NetworkVisualization,
  TimelineItem,
  TimelineVisualization,
  KanbanCard,
  KanbanColumn,
  KanbanVisualization,
  VisualizationTheme,
  VisualizationPlugin,
  VisualizationState,
  VisualizationEngineProps,
  ChartBuilderProps,
  MindMapEditorProps,
  NetworkGraphProps,
  TimelineVisualizerProps,
  KanbanBoardProps,
  CollaborationCursor,
  Comment,
  DataSource,
  VisualizationTemplate
} from './visualization';

// Common diagram suggestion type (unified definition)
export interface DiagramSuggestion {
  id: string;
  title: string;
  description: string;
  type: 'flowchart' | 'sequence' | 'mindmap' | 'timeline' | 'gantt';
  priority?: 'high' | 'medium' | 'low';
  confidence?: number;
  relevantSources?: any[]; // Replace 'any' with proper Source type
  suggestedReason?: string;
}

// Refinement command type (from components/chat/types.ts)
export interface RefinementCommand {
  id: string;
  label: string;
  description: string;
  icon: React.ReactNode;
  action: string;
  parameters?: Record<string, any>;
}