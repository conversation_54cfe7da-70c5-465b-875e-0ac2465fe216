import { useState, useCallback } from 'react';
import { Citation } from '../types/message';

/**
 * Shared hook for notebook layout logic
 * Eliminates duplication between desktop and mobile layouts
 */
export interface NotebookLayoutState {
  activeTab: string;
  selectedCitation: Citation | null;
}

export interface NotebookLayoutActions {
  setActiveTab: (tab: string) => void;
  setSelectedCitation: (citation: Citation | null) => void;
  handleCitationClick: (citation: Citation) => void;
  handleCitationClose: () => void;
  handleTabChange: (tab: string) => void;
}

export interface UseNotebookLayoutReturn extends NotebookLayoutState, NotebookLayoutActions {
  isTabActive: (tab: string) => boolean;
}

export const useNotebookLayout = (initialTab = 'chat'): UseNotebookLayoutReturn => {
  const [activeTab, setActiveTab] = useState(initialTab);
  const [selectedCitation, setSelectedCitation] = useState<Citation | null>(null);

  const handleCitationClick = useCallback((citation: Citation) => {
    setSelectedCitation(citation);
    // Optionally switch to sources tab when citation is clicked
    if (activeTab === 'chat') {
      setActiveTab('sources');
    }
  }, [activeTab]);

  const handleCitationClose = useCallback(() => {
    setSelectedCitation(null);
  }, []);

  const handleTabChange = useCallback((tab: string) => {
    setActiveTab(tab);
    // Clear citation when switching away from sources
    if (tab !== 'sources') {
      setSelectedCitation(null);
    }
  }, []);

  const isTabActive = useCallback((tab: string) => {
    return activeTab === tab;
  }, [activeTab]);

  return {
    // State
    activeTab,
    selectedCitation,
    
    // Actions
    setActiveTab,
    setSelectedCitation,
    handleCitationClick,
    handleCitationClose,
    handleTabChange,
    isTabActive,
  };
};

/**
 * Common tab configuration for notebooks
 */
export const NOTEBOOK_TABS = {
  CHAT: {
    id: 'chat',
    label: 'Chat With Anything',
    icon: 'MessageCircle',
  },
  SOURCES: {
    id: 'sources',
    label: 'Sources',
    icon: 'BookOpen',
  },
  STUDIO: {
    id: 'studio',
    label: 'Studio',
    icon: 'Edit',
  },
  NOTES: {
    id: 'notes',
    label: 'Notes',
    icon: 'FileText',
  },
  MERMAID: {
    id: 'mermaid',
    label: 'Diagrams',
    icon: 'GitBranch',
  },
  SLIDEV: {
    id: 'slidev',
    label: 'Slides',
    icon: 'Presentation',
  },
} as const;

/**
 * Hook for managing tab visibility based on features
 */
export const useTabVisibility = (features?: {
  hasNotes?: boolean;
  hasDiagrams?: boolean;
  hasSlides?: boolean;
  hasStudio?: boolean;
}) => {
  const visibleTabs = [
    NOTEBOOK_TABS.CHAT,
    NOTEBOOK_TABS.SOURCES,
  ];

  if (features?.hasStudio) {
    visibleTabs.push(NOTEBOOK_TABS.STUDIO);
  }

  if (features?.hasNotes) {
    visibleTabs.push(NOTEBOOK_TABS.NOTES);
  }

  if (features?.hasDiagrams) {
    visibleTabs.push(NOTEBOOK_TABS.MERMAID);
  }

  if (features?.hasSlides) {
    visibleTabs.push(NOTEBOOK_TABS.SLIDEV);
  }

  return visibleTabs;
};