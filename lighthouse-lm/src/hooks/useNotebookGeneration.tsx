import { useState } from 'react';
import { invoke } from '@/lib/tauri-mock';
import { useToast } from './useToast';

interface NotebookGenerationParams {
  notebookId: string;
  filePath?: string;
  sourceType: string;
}

export const useNotebookGeneration = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const { toast } = useToast();

  const generateNotebookContent = async ({
    notebookId,
    filePath,
    sourceType,
  }: NotebookGenerationParams) => {
    try {
      setIsGenerating(true);

      // Generate content using <PERSON>
      const generatedContent = await invoke('generate_notebook_content', {
        notebookId,
        filePath,
        sourceType,
      });

      
      toast({
        title: "Content Generated",
        description: "Notebook title and description have been generated successfully.",
      });

      return generatedContent;
    } catch (error) {
      toast({
        title: "Generation Failed",
        description: "Failed to generate notebook content. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsGenerating(false);
    }
  };

  const generateNotebookContentAsync = async (params: NotebookGenerationParams) => {
    return generateNotebookContent(params);
  };

  return {
    generateNotebookContent,
    generateNotebookContentAsync,
    isGenerating,
  };
};
