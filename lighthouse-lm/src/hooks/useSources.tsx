import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { tauriService } from '@/services/tauriService';
import { useNotebookGeneration } from './useNotebookGeneration';

export const useSources = (notebookId?: string) => {
  const queryClient = useQueryClient();
  const { generateNotebookContentAsync } = useNotebookGeneration();
  
  // Debug logging (commented out)
  // 
  // 
  // 
  // 
  // 

  const {
    data: sources = [],
    isLoading,
    error,
  } = useQuery({
    queryKey: ['sources', notebookId],
    queryFn: async () => {
      if (!notebookId) {
        // 
        // 
        // 
        return [];
      }
      
      // 
      // 
      try {
        const data = await tauriService.getSources(notebookId);
        // 
        return data || [];
      } catch (err) {
        throw err;
      }
    },
    enabled: !!notebookId,
    // Disable automatic refetch to prevent loops
    refetchInterval: false
  });

  const addSource = useMutation({
    mutationFn: async (sourceData: {
      notebookId: string;
      title: string;
      type: 'pdf' | 'text' | 'website' | 'youtube' | 'audio';
      content?: string;
      url?: string;
      file_path?: string;
      file_size?: number;
      processing_status?: string;
      metadata?: any;
    }) => {
      const newSource = await tauriService.createSource(sourceData.notebookId, {
        title: sourceData.title,
        type: sourceData.type,
        content: sourceData.content,
        url: sourceData.url,
        file_path: sourceData.file_path,
        file_size: sourceData.file_size,
        processing_status: sourceData.processing_status || 'pending',
        metadata: sourceData.metadata
      });

      // If source was created with pending status and we have content, process it immediately
      if (newSource.processing_status === 'pending' && (sourceData.content || sourceData.file_path)) {
        try {
          const processedSource = await tauriService.processDocument(newSource.id);
          return newSource; // Return the original source since processDocument doesn't return the updated source
        } catch (error) {
          // Return the source even if processing failed
          return newSource;
        }
      }

      return newSource;
    },
    onSuccess: async (newSource, variables) => {
      
      // Invalidate queries to refetch
      queryClient.invalidateQueries({ queryKey: ['sources', variables.notebookId] });
      
      // Check if this is the first source
      const currentSources = queryClient.getQueryData(['sources', variables.notebookId]) as any[] || [];
      const isFirstSource = currentSources.length === 0;
      
      if (isFirstSource && notebookId) {
        
        // Check notebook generation status
        const notebook = await tauriService.getNotebook(notebookId);
        
        if (notebook?.generation_status === 'pending') {
          await generateNotebookContentAsync({ notebookId, sourceType: 'auto' });
        }
      }
    },
  });

  const deleteSource = useMutation({
    mutationFn: async (sourceId: string) => {
      await tauriService.deleteSource(sourceId);
      return sourceId;
    },
    onSuccess: () => {
      if (notebookId) {
        queryClient.invalidateQueries({ queryKey: ['sources', notebookId] });
      }
    },
  });

  const updateSource = useMutation({
    mutationFn: async ({
      sourceId,
      updates
    }: {
      sourceId: string;
      updates: Partial<{
        title: string;
        processing_status: string;
        summary: string;
        content: string;
      }>
    }) => {
      // Call the update_source command
      const result = await tauriService.updateSource(sourceId, updates);
      return result;
    },
    onSuccess: () => {
      if (notebookId) {
        queryClient.invalidateQueries({ queryKey: ['sources', notebookId] });
      }
    },
  });

  return {
    sources,
    isLoading,
    error: error?.message || null,
    addSource: addSource.mutate,
    isAddingSource: addSource.isPending,
    deleteSource: deleteSource.mutate,
    isDeletingSource: deleteSource.isPending,
    updateSource: updateSource.mutate,
    isUpdatingSource: updateSource.isPending,
  };
};