import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { lighthouseService, Source, SourceType } from '../services/lighthouseService';
import { useNotebookGeneration } from './useNotebookGeneration';

export const useSourcesWithBackend = (notebookId?: string) => {
  const queryClient = useQueryClient();
  const { generateNotebookContentAsync } = useNotebookGeneration();

  const {
    data: sources = [],
    isLoading,
    error,
  } = useQuery({
    queryKey: ['sources', notebookId],
    queryFn: async () => {
      if (!notebookId) {
        return [];
      }
      
      try {
        const data = await lighthouseService.getSources(notebookId);
        return data || [];
      } catch (err) {
        throw err;
      }
    },
    enabled: !!notebookId,
    refetchInterval: false
  });

  const addSource = useMutation({
    mutationFn: async (sourceData: {
      notebookId: string;
      title: string;
      type: SourceType;
      content?: string;
      url?: string;
      file?: File;
    }) => {
      // Handle file upload if present
      if (sourceData.file) {
        return await lighthouseService.uploadFile(sourceData.notebookId, sourceData.file);
      }
      
      // Otherwise add source directly
      return await lighthouseService.addSource(
        sourceData.notebookId,
        sourceData.title,
        sourceData.content || '',
        sourceData.type
      );
    },
    onSuccess: async (newSource, variables) => {
      
      // Invalidate queries to refetch
      queryClient.invalidateQueries({ queryKey: ['sources', variables.notebookId] });
      
      // Check if this is the first source
      const currentSources = queryClient.getQueryData(['sources', variables.notebookId]) as Source[] || [];
      const isFirstSource = currentSources.length === 0;
      
      if (isFirstSource && notebookId) {
        await generateNotebookContentAsync({ notebookId, sourceType: 'auto' });
      }
    },
  });

  const deleteSource = useMutation({
    mutationFn: async (sourceId: string) => {
      await lighthouseService.deleteSource(sourceId);
      return sourceId;
    },
    onSuccess: () => {
      if (notebookId) {
        queryClient.invalidateQueries({ queryKey: ['sources', notebookId] });
      }
    },
  });

  const searchSources = useMutation({
    mutationFn: async ({ query, limit }: { query: string; limit?: number }) => {
      if (!notebookId) throw new Error('Notebook ID required for search');
      return await lighthouseService.searchSources(notebookId, query, limit);
    },
  });

  // Real-time subscription
  const subscribeToUpdates = async (onUpdate: (event: any) => void) => {
    if (!notebookId) return () => {};
    
    return await lighthouseService.subscribeToNotebook(notebookId, (event) => {
      onUpdate(event);
      
      // Invalidate queries based on event type
      if (event.type === 'source_added' || event.type === 'source_updated' || event.type === 'source_deleted') {
        queryClient.invalidateQueries({ queryKey: ['sources', notebookId] });
      }
    });
  };

  return {
    sources,
    isLoading,
    error: error?.message || null,
    addSource: addSource.mutate,
    isAddingSource: addSource.isPending,
    deleteSource: deleteSource.mutate,
    isDeletingSource: deleteSource.isPending,
    searchSources: searchSources.mutateAsync,
    isSearching: searchSources.isPending,
    subscribeToUpdates,
  };
};