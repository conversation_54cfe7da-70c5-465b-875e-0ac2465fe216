import { useState } from 'react';
import { invoke, open } from '@/lib/tauri-mock';
import { useToast } from './useToast';

declare global {
  interface Window {
    __TAURI__?: any;
  }
}

interface FileUploadResult {
  filePath: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
}

export const useFileUpload = (notebookId?: string) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const { toast } = useToast();

  const uploadFile = async (file: File): Promise<FileUploadResult> => {
    if (!notebookId) {
      throw new Error('Notebook ID is required for file upload');
    }
    
    try {
      setIsUploading(true);
      setUploadProgress(0);

      // Convert file to array buffer
      
      // Check file size (warn if over 10MB)
      if (file.size > 10 * 1024 * 1024) {
      }
      
      const arrayBuffer = await file.arrayBuffer();
      const bytes = new Uint8Array(arrayBuffer);

      // Simulate progress
      setUploadProgress(30);

      // Generate source ID
      const sourceId = crypto.randomUUID();

      // Log upload details (commented out incomplete code)
      // console.log({
      //   notebookId,
      //   sourceId,
      //   fileDataLength: bytes.length,
      //   fileName: file.name
      // });

      // Upload file through Tauri - returns the file path as a string
      
      // Create a timeout promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Upload timeout after 30 seconds')), 30000);
      });
      
      // Race between the upload and timeout
      const filePath = await Promise.race([
        invoke<string>('upload_file', {
          notebookId: notebookId,
          sourceId: sourceId,
          fileData: Array.from(bytes),
          fileName: file.name,
        }),
        timeoutPromise
      ]);
      

      setUploadProgress(100);

      toast({
        title: "File Uploaded",
        description: `${file.name} has been uploaded successfully.`,
      });

      // Create the result object with the returned file path
      const result: FileUploadResult = {
        filePath: filePath,
        fileName: file.name,
        fileSize: file.size,
        mimeType: file.type || 'application/octet-stream',
      };

      return result;
    } catch (error) {
      toast({
        title: "Upload Failed",
        description: `Failed to upload ${file.name}. Please try again.`,
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const uploadMultipleFiles = async (files: FileList): Promise<FileUploadResult[]> => {
    const results: FileUploadResult[] = [];
    
    for (let i = 0; i < files.length; i++) {
      try {
        const result = await uploadFile(files[i]);
        results.push(result);
      } catch (error) {
      }
    }
    
    return results;
  };

  const validateFile = (file: File, maxSizeMB = 50): boolean => {
    const maxSize = maxSizeMB * 1024 * 1024; // Convert MB to bytes
    
    if (file.size > maxSize) {
      toast({
        title: "File Too Large",
        description: `${file.name} exceeds the maximum size of ${maxSizeMB}MB.`,
        variant: "destructive",
      });
      return false;
    }
    
    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'text/plain',
      'text/markdown',
      'text/csv',
      'application/json',
      'audio/mpeg',
      'audio/wav',
      'audio/mp3',
      'audio/ogg',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ];
    
    if (!allowedTypes.includes(file.type) && !file.type.startsWith('text/')) {
      toast({
        title: "Invalid File Type",
        description: `${file.name} is not a supported file type.`,
        variant: "destructive",
      });
      return false;
    }
    
    return true;
  };

  /**
   * Extract text from uploaded file
   */
  const extractText = async (filePath: string, fileType: string): Promise<string | null> => {
    try {
      const result = await invoke<string>('extract_text_rust', {
        filePath: filePath,
        fileType: fileType,
      });
      
      return result;
    } catch (error) {
      // Fallback to Python extraction if Rust fails
      
      try {
        const result = await invoke<string>('extract_document_text', {
          filePath: filePath,
          sourceType: fileType,
        });
        return result;
      } catch (pythonError) {
        toast({
          title: "Extraction Failed",
          description: "Failed to extract text from file",
          variant: "destructive",
        });
        return null;
      }
    }
  };

  /**
   * Show native file dialog and upload selected file
   */
  const selectAndUploadFile = async (): Promise<FileUploadResult | null> => {
    if (!notebookId) {
      throw new Error('Notebook ID is required for file upload');
    }
    
    try {
      setIsUploading(true);
      setUploadProgress(0);

      // Check if we're in a Tauri app environment
      let selected: string | null = null;
      
      if (window.__TAURI__) {
        // Open native file dialog in Tauri app
        selected = await open({
          multiple: false,
          filters: [
            {
              name: 'Documents',
              extensions: ['pdf', 'txt', 'md', 'csv', 'json', 'doc', 'docx']
            },
            {
              name: 'Audio Files',
              extensions: ['mp3', 'wav', 'ogg', 'mpeg']
            },
            {
              name: 'All Files',
              extensions: ['*']
            }
          ]
        });
      } else {
        // Fallback for web development - create file input element
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.pdf,.txt,.md,.csv,.json,.doc,.docx,.mp3,.wav,.ogg';
        
        selected = await new Promise<string | null>((resolve) => {
          input.onchange = (e) => {
            const file = (e.target as HTMLInputElement).files?.[0];
            if (file) {
              // In web mode, we can't get the full path, so we use the file name
              resolve(file.name);
            } else {
              resolve(null);
            }
          };
          input.click();
        });
      }

      if (!selected) {
        return null;
      }

      const sourceId = crypto.randomUUID();
      const fileName = selected.split('/').pop() || selected.split('\\').pop() || 'Unknown';


      // Upload using the file path
      const filePath = await invoke<string>('upload_file_rust', {
        filePath: selected,
        notebookId: notebookId,
        sourceId: sourceId,
      });

      setUploadProgress(100);

      toast({
        title: "File Uploaded",
        description: `${fileName} has been uploaded successfully.`,
      });

      return {
        filePath: filePath,
        fileName: fileName,
        fileSize: 0, // File size not available in this method
        mimeType: 'application/octet-stream', // Default mime type
      };
    } catch (error) {
      toast({
        title: "Upload Failed",
        description: error instanceof Error ? error.message : "Failed to upload file",
        variant: "destructive",
      });
      return null;
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  return {
    uploadFile,
    uploadMultipleFiles,
    validateFile,
    extractText,
    selectAndUploadFile,
    isUploading,
    uploadProgress,
  };
};