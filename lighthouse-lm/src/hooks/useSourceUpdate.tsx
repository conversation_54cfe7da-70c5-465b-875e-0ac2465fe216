import InsightsAPI from '@/services/insights-api';
import { useMutationWithToast, mutationPresets } from './useMutationWithToast';

export const useSourceUpdate = () => {
  const mutation = useMutationWithToast({
    mutationFn: async ({ 
      sourceId, 
      title,
      summary,
      processingStatus,
      metadata
    }: { 
      sourceId: string;
      title?: string;
      summary?: string;
      processingStatus?: string;
      metadata?: any;
    }) => {
      const updatedSource = await InsightsAPI.updateSource(
        sourceId,
        title,
        summary,
        processingStatus,
        metadata
      );
      
      return updatedSource;
    },
    invalidateQueries: [['sources']],
    ...mutationPresets.update('Source'),
  });

  const renameSource = async (sourceId: string, newTitle: string) => {
    return mutation.mutateAsync({
      sourceId,
      title: newTitle,
    });
  };

  const updateSourceStatus = async (sourceId: string, status: string) => {
    return mutation.mutateAsync({
      sourceId,
      processingStatus: status,
    });
  };

  return {
    updateSource: mutation.mutate,
    updateSourceAsync: mutation.mutateAsync,
    renameSource,
    updateSourceStatus,
    isUpdating: mutation.isPending,
  };
};