import { useState, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { lighthouseService, Attachment } from '../services/lighthouseService';
import { useToast } from './useToast';

export const useChatMessagesWithBackend = (notebookId?: string) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [isSending, setIsSending] = useState(false);

  // Fetch messages
  const {
    data: messages = [],
    isLoading,
    error,
  } = useQuery({
    queryKey: ['messages', notebookId],
    queryFn: async () => {
      if (!notebookId) return [];
      return await lighthouseService.getMessages(notebookId);
    },
    enabled: !!notebookId,
  });

  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: async ({
      content,
      options,
    }: {
      content: string;
      options?: {
        sources?: string[];
        model?: string;
        temperature?: number;
        maxTokens?: number;
        attachments?: Attachment[];
      };
    }) => {
      if (!notebookId) throw new Error('Notebook ID required');
      return await lighthouseService.sendMessage(notebookId, content, options);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['messages', notebookId] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Edit message mutation
  const editMessageMutation = useMutation({
    mutationFn: async ({ messageId, content }: { messageId: string; content: string }) => {
      return await lighthouseService.editMessage(messageId, content);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['messages', notebookId] });
      toast({
        title: "Message edited",
        description: "Your message has been updated.",
      });
    },
  });

  // Delete message mutation
  const deleteMessageMutation = useMutation({
    mutationFn: async (messageId: string) => {
      await lighthouseService.deleteMessage(messageId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['messages', notebookId] });
      toast({
        title: "Message deleted",
        description: "The message has been removed.",
      });
    },
  });

  // Regenerate message mutation
  const regenerateMessageMutation = useMutation({
    mutationFn: async (messageId: string) => {
      return await lighthouseService.regenerateMessage(messageId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['messages', notebookId] });
      toast({
        title: "Message regenerated",
        description: "A new response has been generated.",
      });
    },
  });

  // Add reaction mutation
  const addReactionMutation = useMutation({
    mutationFn: async ({ messageId, emoji }: { messageId: string; emoji: string }) => {
      await lighthouseService.addReaction(messageId, emoji);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['messages', notebookId] });
    },
  });

  // Clear chat history
  const clearHistory = useCallback(async () => {
    if (!notebookId) return;
    
    try {
      // Delete all messages
      for (const message of messages) {
        await lighthouseService.deleteMessage(message.id);
      }
      
      queryClient.invalidateQueries({ queryKey: ['messages', notebookId] });
      toast({
        title: "Chat history cleared",
        description: "All messages have been deleted successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to clear chat history. Please try again.",
        variant: "destructive",
      });
    }
  }, [notebookId, messages, queryClient, toast]);

  // Send message wrapper
  const sendMessage = useCallback(async (
    content: string,
    options?: {
      sources?: string[];
      model?: string;
      temperature?: number;
      maxTokens?: number;
      attachments?: Attachment[];
    }
  ) => {
    if (!notebookId || !content.trim()) return null;
    
    setIsSending(true);
    try {
      const result = await sendMessageMutation.mutateAsync({ content, options });
      return result;
    } finally {
      setIsSending(false);
    }
  }, [notebookId, sendMessageMutation]);

  // Real-time subscription
  const subscribeToUpdates = useCallback(async (onUpdate: (event: any) => void) => {
    if (!notebookId) return () => {};
    
    return await lighthouseService.subscribeToNotebook(notebookId, (event) => {
      onUpdate(event);
      
      // Invalidate queries based on event type
      if (event.type === 'message_added' || event.type === 'message_updated' || event.type === 'message_deleted') {
        queryClient.invalidateQueries({ queryKey: ['messages', notebookId] });
      }
    });
  }, [notebookId, queryClient]);

  // Generate suggestions
  const generateSuggestions = useCallback(async (context: string) => {
    if (!notebookId) return [];
    return await lighthouseService.generateSuggestions(notebookId, context);
  }, [notebookId]);

  return {
    messages,
    isLoading,
    isSending,
    error: error?.message || null,
    sendMessage,
    editMessage: editMessageMutation.mutate,
    deleteMessage: deleteMessageMutation.mutate,
    regenerateMessage: regenerateMessageMutation.mutate,
    addReaction: addReactionMutation.mutate,
    clearHistory,
    subscribeToUpdates,
    generateSuggestions,
    isEditingMessage: editMessageMutation.isPending,
    isDeletingMessage: deleteMessageMutation.isPending,
    isRegenerating: regenerateMessageMutation.isPending,
  };
};