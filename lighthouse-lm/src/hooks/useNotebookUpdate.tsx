import InsightsAPI, { UpdateNotebookRequest } from '@/services/insights-api';
import { useMutationWithToast, mutationPresets } from './useMutationWithToast';

export const useNotebookUpdate = () => {
  const mutation = useMutationWithToast({
    mutationFn: async ({ 
      notebookId, 
      updates 
    }: { 
      notebookId: string; 
      updates: UpdateNotebookRequest 
    }) => {
      const updatedNotebook = await InsightsAPI.updateNotebook(notebookId, updates);
      return updatedNotebook;
    },
    invalidateQueries: [
      ['notebooks'],
      // Dynamic query key will be handled in onSuccess
    ],
    ...mutationPresets.update('Notebook'),
    onSuccess: (data, variables) => {
      // Additional query invalidation with specific notebook ID
      const queryClient = (window as any).__queryClient;
      if (queryClient) {
        queryClient.invalidateQueries({ queryKey: ['notebook', variables.notebookId] });
      }
    },
  });

  return {
    updateNotebook: mutation.mutate,
    updateNotebookAsync: mutation.mutateAsync,
    isUpdating: mutation.isPending,
  };
};