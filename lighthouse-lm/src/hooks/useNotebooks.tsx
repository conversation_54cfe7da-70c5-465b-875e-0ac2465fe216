import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import InsightsAPI from '@/services/insights-api';

export const useNotebooks = () => {
  const queryClient = useQueryClient();

  const {
    data: notebooks = [],
    isLoading,
    error,
    isError,
  } = useQuery({
    queryKey: ['notebooks'],
    queryFn: async () => {
      // 
      
      try {
        const notebooksData = await InsightsAPI.getNotebooks();
        // 
        return notebooksData || [];
      } catch (err) {
        throw err;
      }
    },
    enabled: true,
    retry: (failureCount, error) => {
      // Don't retry on auth errors
      if (error?.message?.includes('JWT') || error?.message?.includes('auth')) {
        return false;
      }
      return failureCount < 3;
    },
  });

  const createNotebook = useMutation({
    mutationFn: async (notebookData: { title: string; description?: string }) => {

      try {
        const data = await InsightsAPI.createNotebook(notebookData);
        return data;
      } catch (err) {
        throw err;
      }
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['notebooks'] });
    },
    onError: (error) => {
    },
  });

  return {
    notebooks,
    isLoading,
    error: error?.message || null,
    isError,
    createNotebook: createNotebook.mutate,
    isCreating: createNotebook.isPending,
  };
};