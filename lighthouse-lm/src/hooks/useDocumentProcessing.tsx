import * as React from 'react';
import { invoke } from '@/lib/tauri-mock';
import { useToast } from './useToast';

// Text chunking utility
const chunkText = (text: string, chunkSize = 1000): string[] => {
  if (!text) return [];
  
  const chunks: string[] = [];
  let currentPosition = 0;
  
  while (currentPosition < text.length) {
    let endPosition = Math.min(currentPosition + chunkSize, text.length);
    
    // Try to break at sentence boundaries when possible
    if (endPosition < text.length) {
      const lastPeriod = text.lastIndexOf('.', endPosition);
      const lastExclamation = text.lastIndexOf('!', endPosition);
      const lastQuestion = text.lastIndexOf('?', endPosition);
      
      const lastSentenceEnd = Math.max(lastPeriod, lastExclamation, lastQuestion);
      
      // If we found a sentence boundary within a reasonable distance, use it
      if (lastSentenceEnd > currentPosition + chunkSize * 0.8) {
        endPosition = lastSentenceEnd + 1;
      }
    }
    
    const chunk = text.substring(currentPosition, endPosition).trim();
    if (chunk) {
      chunks.push(chunk);
    }
    
    currentPosition = endPosition;
  }
  
  return chunks;
};

interface ProcessingResult {
  content: string;
  summary: string;
  metadata: any;
  chunks: string[];
}

export const useDocumentProcessing = () => {
  const [isUploading, setIsUploading] = React.useState(false);
  const [uploadProgress, setUploadProgress] = React.useState(0);
  const { toast } = useToast();

  const processDocument = async (
    filePath: string,
    fileType: string
  ): Promise<ProcessingResult> => {
    try {
      setIsUploading(true);
      setUploadProgress(0);


      // Extract text from document using the existing command
      setUploadProgress(20);
      const content = await invoke<string>('extract_document_text', {
        filePath: filePath,
        sourceType: fileType,
      });

      setUploadProgress(100);

      // Create a basic ProcessingResult structure
      const result: ProcessingResult = {
        content: content,
        summary: content.substring(0, 200) + '...', // Basic summary
        metadata: {
          fileType: fileType,
          extractedAt: new Date().toISOString(),
        },
        chunks: chunkText(content, 1000), // Implement chunking
      };

      toast({
        title: "Document Processed",
        description: "The document has been processed successfully.",
      });

      return result;
    } catch (error) {
      toast({
        title: "Processing Failed",
        description: "Failed to process the document. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const extractTextFromPDF = async (filePath: string): Promise<string> => {
    try {
      const text = await invoke<string>('extract_document_text', {
        filePath: filePath,
        sourceType: 'pdf',
      });
      return text;
    } catch (error) {
      throw error;
    }
  };

  const transcribeAudio = async (filePath: string): Promise<string> => {
    try {
      setIsUploading(true);
      setUploadProgress(0);
      
      
      // Update progress
      setUploadProgress(30);
      
      // Call the backend to transcribe audio
      const transcription = await invoke<string>('transcribe_audio', {
        filePath: filePath,
      });
      
      setUploadProgress(100);
      
      toast({
        title: "Audio Transcribed",
        description: "The audio file has been transcribed successfully.",
      });
      
      return transcription;
    } catch (error) {
      
      // If the backend doesn't support it yet, try extract_document_text as fallback
      try {
        const text = await invoke<string>('extract_document_text', {
          filePath: filePath,
          sourceType: 'audio',
        });
        
        toast({
          title: "Audio Processed",
          description: "The audio file has been processed.",
        });
        
        return text;
      } catch (fallbackError) {
        toast({
          title: "Transcription Failed",
          description: "Failed to transcribe the audio file. Please ensure the file is valid.",
          variant: "destructive",
        });
        throw error;
      }
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const processWebContent = async (url: string): Promise<ProcessingResult> => {
    try {
      setIsUploading(true);
      setUploadProgress(0);
      
      
      // Update progress
      setUploadProgress(20);
      
      // Call the backend to fetch and process web content
      const content = await invoke<string>('process_website', {
        url: url,
      });
      
      setUploadProgress(80);
      
      // Try to extract metadata from the content
      let title = url;
      let summary = content.substring(0, 300) + '...';
      
      // Extract title if present in HTML
      const titleMatch = content.match(/<title>(.*?)<\/title>/i);
      if (titleMatch) {
        title = titleMatch[1];
      }
      
      // Extract meta description if present
      const descMatch = content.match(/<meta\s+name=["']description["']\s+content=["'](.*?)["']/i);
      if (descMatch) {
        summary = descMatch[1];
      }
      
      setUploadProgress(100);
      
      const result: ProcessingResult = {
        content: content,
        summary: summary,
        metadata: {
          url: url,
          title: title,
          fetchedAt: new Date().toISOString(),
        },
        chunks: chunkText(content),
      };
      
      toast({
        title: "Website Processed",
        description: "The website content has been extracted successfully.",
      });
      
      return result;
    } catch (error) {
      
      // Try alternative approach using extract_document_text
      try {
        const content = await invoke<string>('extract_document_text', {
          filePath: url,
          sourceType: 'website',
        });
        
        const result: ProcessingResult = {
          content: content,
          summary: content.substring(0, 300) + '...',
          metadata: {
            url: url,
            fetchedAt: new Date().toISOString(),
          },
          chunks: chunkText(content),
        };
        
        toast({
          title: "Website Processed",
          description: "The website content has been extracted.",
        });
        
        return result;
      } catch (fallbackError) {
        toast({
          title: "Processing Failed",
          description: "Failed to process the website. Please check the URL and try again.",
          variant: "destructive",
        });
        throw error;
      }
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const processYouTubeVideo = async (url: string): Promise<ProcessingResult> => {
    try {
      setIsUploading(true);
      setUploadProgress(0);
      
      
      // Validate YouTube URL
      const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/.+$/;
      if (!youtubeRegex.test(url)) {
        throw new Error('Invalid YouTube URL');
      }
      
      // Update progress
      setUploadProgress(20);
      
      // Call the backend to process YouTube video
      const content = await invoke<string>('process_youtube', {
        url: url,
      });
      
      setUploadProgress(80);
      
      // Extract video ID from URL
      let videoId = '';
      const videoIdMatch = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
      if (videoIdMatch) {
        videoId = videoIdMatch[1];
      }
      
      // Parse the response if it's JSON
      let videoTitle = url;
      let transcript = content;
      let metadata: any = {
        url: url,
        videoId: videoId,
        processedAt: new Date().toISOString(),
      };
      
      try {
        const parsed = JSON.parse(content);
        if (parsed.title) videoTitle = parsed.title;
        if (parsed.transcript) transcript = parsed.transcript;
        if (parsed.metadata) metadata = { ...metadata, ...parsed.metadata };
      } catch {
        // Content is not JSON, use as-is
      }
      
      setUploadProgress(100);
      
      const result: ProcessingResult = {
        content: transcript,
        summary: `YouTube Video: ${videoTitle}`,
        metadata: metadata,
        chunks: chunkText(transcript),
      };
      
      toast({
        title: "YouTube Video Processed",
        description: "The video transcript has been extracted successfully.",
      });
      
      return result;
    } catch (error) {
      
      // Try alternative approach using extract_document_text
      try {
        const content = await invoke<string>('extract_document_text', {
          filePath: url,
          sourceType: 'youtube',
        });
        
        const result: ProcessingResult = {
          content: content,
          summary: `YouTube Video: ${url}`,
          metadata: {
            url: url,
            processedAt: new Date().toISOString(),
          },
          chunks: chunkText(content),
        };
        
        toast({
          title: "YouTube Video Processed",
          description: "The video content has been extracted.",
        });
        
        return result;
      } catch (fallbackError) {
        toast({
          title: "Processing Failed",
          description: "Failed to process the YouTube video. Please check the URL and try again.",
          variant: "destructive",
        });
        throw error;
      }
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  return {
    processDocument,
    extractTextFromPDF,
    transcribeAudio,
    processWebContent,
    processYouTubeVideo,
    isUploading,
    uploadProgress,
  };
};