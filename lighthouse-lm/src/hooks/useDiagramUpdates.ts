import { useState, useEffect, useCallback, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { SourceDiagramMonitor, DiagramFreshnessStatus, DiagramRegenerationResult } from '../../../services/sourceDiagramMonitor';
import { DiagramNotificationService, DiagramNotification } from '../../../services/diagramNotificationService';
import { useToast } from './useToast';

export interface UseDiagramUpdatesOptions {
  notebookId?: string;
  autoMonitoring?: boolean;
  monitoringInterval?: number;
  enableNotifications?: boolean;
}

export interface UseDiagramUpdatesReturn {
  // Freshness status
  freshnessStatuses: DiagramFreshnessStatus[];
  isCheckingFreshness: boolean;
  checkFreshness: () => Promise<void>;
  
  // Regeneration
  isRegenerating: boolean;
  regenerateDiagram: (diagramId: number, options?: any) => Promise<DiagramRegenerationResult | null>;
  batchRegenerate: (diagramIds: number[], options?: any) => Promise<DiagramRegenerationResult[]>;
  
  // Notifications
  notifications: DiagramNotification[];
  dismissNotification: (notificationId: string) => void;
  clearAllNotifications: () => void;
  
  // Monitoring
  isMonitoring: boolean;
  startMonitoring: () => void;
  stopMonitoring: () => void;
  
  // Outdated diagrams
  outdatedDiagrams: number[];
  getOutdatedCount: () => number;
}

export function useDiagramUpdates(options: UseDiagramUpdatesOptions = {}): UseDiagramUpdatesReturn {
  const {
    notebookId,
    autoMonitoring = true,
    monitoringInterval = 30000,
    enableNotifications = true
  } = options;

  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // State
  const [freshnessStatuses, setFreshnessStatuses] = useState<DiagramFreshnessStatus[]>([]);
  const [isCheckingFreshness, setIsCheckingFreshness] = useState(false);
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [notifications, setNotifications] = useState<DiagramNotification[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  
  // Refs
  const notificationListenerRef = useRef<string | null>(null);

  // Derived state
  const outdatedDiagrams = freshnessStatuses
    .filter(status => !status.is_fresh)
    .map(status => status.diagram_id);

  // Initialize notification service
  useEffect(() => {
    if (enableNotifications) {
      DiagramNotificationService.initialize();
      
      const listenerId = `diagram-updates-${Date.now()}`;
      notificationListenerRef.current = listenerId;
      
      DiagramNotificationService.addListener(listenerId, (notification) => {
        setNotifications(prev => [...prev, notification]);
      });

      return () => {
        if (notificationListenerRef.current) {
          DiagramNotificationService.removeListener(notificationListenerRef.current);
        }
      };
    }
  }, [enableNotifications]);

  // Auto-start monitoring
  useEffect(() => {
    if (autoMonitoring && notebookId) {
      startMonitoring();
    }

    return () => {
      if (isMonitoring) {
        SourceDiagramMonitor.stopMonitoring();
      }
    };
  }, [autoMonitoring, notebookId]);

  // Check freshness for notebook diagrams
  const checkFreshness = useCallback(async () => {
    if (!notebookId) return;

    setIsCheckingFreshness(true);
    try {
      const statuses = await SourceDiagramMonitor.getNotebookDiagramFreshness(notebookId);
      setFreshnessStatuses(statuses);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to check diagram freshness',
        variant: 'destructive'
      });
    } finally {
      setIsCheckingFreshness(false);
    }
  }, [notebookId, toast]);

  // Start monitoring
  const startMonitoring = useCallback(() => {
    if (isMonitoring) return;

    SourceDiagramMonitor.startMonitoring(monitoringInterval);
    setIsMonitoring(true);
    
    // Initial freshness check
    checkFreshness();
  }, [isMonitoring, monitoringInterval, checkFreshness]);

  // Stop monitoring
  const stopMonitoring = useCallback(() => {
    if (!isMonitoring) return;

    SourceDiagramMonitor.stopMonitoring();
    setIsMonitoring(false);
  }, [isMonitoring]);

  // Regenerate single diagram
  const regenerateDiagram = useCallback(async (
    diagramId: number,
    options?: any
  ): Promise<DiagramRegenerationResult | null> => {
    setIsRegenerating(true);
    try {
      const result = await SourceDiagramMonitor.regenerateDiagram(diagramId, options);
      
      // Invalidate queries to refresh UI
      queryClient.invalidateQueries({ queryKey: ['source-diagrams'] });
      queryClient.invalidateQueries({ queryKey: ['source-diagram', diagramId] });
      
      // Refresh freshness status
      await checkFreshness();
      
      if (result.success) {
        toast({
          title: 'Success',
          description: result.changes_detected 
            ? `Diagram regenerated with ${result.regeneration_metadata.concepts_changed} changes`
            : 'Diagram regenerated (no changes detected)'
        });
      } else {
        toast({
          title: 'Error',
          description: `Failed to regenerate diagram: ${result.error}`,
          variant: 'destructive'
        });
      }
      
      return result;
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to regenerate diagram',
        variant: 'destructive'
      });
      return null;
    } finally {
      setIsRegenerating(false);
    }
  }, [queryClient, checkFreshness, toast]);

  // Batch regenerate diagrams
  const batchRegenerate = useCallback(async (
    diagramIds: number[],
    options?: any
  ): Promise<DiagramRegenerationResult[]> => {
    setIsRegenerating(true);
    try {
      const results = await SourceDiagramMonitor.batchRegenerateDiagrams(diagramIds, options);
      
      // Invalidate queries
      queryClient.invalidateQueries({ queryKey: ['source-diagrams'] });
      diagramIds.forEach(id => {
        queryClient.invalidateQueries({ queryKey: ['source-diagram', id] });
      });
      
      // Refresh freshness status
      await checkFreshness();
      
      const successful = results.filter(r => r.success).length;
      const failed = results.filter(r => !r.success).length;
      
      toast({
        title: 'Batch Regeneration Complete',
        description: `${successful} successful, ${failed} failed`,
        variant: failed > 0 ? 'destructive' : 'default'
      });
      
      return results;
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to batch regenerate diagrams',
        variant: 'destructive'
      });
      return [];
    } finally {
      setIsRegenerating(false);
    }
  }, [queryClient, checkFreshness, toast]);

  // Dismiss notification
  const dismissNotification = useCallback((notificationId: string) => {
    DiagramNotificationService.dismissNotification(notificationId);
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
  }, []);

  // Clear all notifications
  const clearAllNotifications = useCallback(() => {
    DiagramNotificationService.clearAllNotifications();
    setNotifications([]);
  }, []);

  // Get outdated count
  const getOutdatedCount = useCallback(() => {
    return outdatedDiagrams.length;
  }, [outdatedDiagrams]);

  return {
    freshnessStatuses,
    isCheckingFreshness,
    checkFreshness,
    isRegenerating,
    regenerateDiagram,
    batchRegenerate,
    notifications,
    dismissNotification,
    clearAllNotifications,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    outdatedDiagrams,
    getOutdatedCount
  };
}