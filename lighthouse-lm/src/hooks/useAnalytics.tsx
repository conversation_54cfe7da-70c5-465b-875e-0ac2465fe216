import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { analyticsService } from '@/services/analyticsService';
import { useToast } from './useToast';
import { 
  AnalyticsDashboardData, 
  AnalyticsFilters, 
  AnalyticsTimeRange,
  InsightData,
  ExportOptions 
} from '@/types/analytics';

interface UseAnalyticsOptions {
  timeRange: AnalyticsTimeRange;
  notebooks?: string[];
  features?: string[];
  contentTypes?: string[];
  focusModes?: string[];
  refreshInterval?: number;
  enabled?: boolean;
}

interface UseAnalyticsReturn {
  data: AnalyticsDashboardData | undefined;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
  updateGoalProgress: (goalId: string, currentValue: number) => Promise<void>;
  createGoal: (goal: any) => Promise<string>;
  handleInsightAction: (insight: InsightData, actionIndex: number) => Promise<void>;
  exportData: (options: ExportOptions) => Promise<Blob>;
  trackEvent: (eventType: string, data?: any) => void;
}

export const useAnalytics = (options: UseAnalyticsOptions): UseAnalyticsReturn => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const filters: AnalyticsFilters = React.useMemo(() => ({
    timeRange: options.timeRange,
    notebooks: options.notebooks,
    features: options.features,
    contentTypes: options.contentTypes,
    focusModes: options.focusModes
  }), [
    options.timeRange,
    options.notebooks,
    options.features,
    options.contentTypes,
    options.focusModes
  ]);

  // Main analytics data query
  const {
    data,
    isLoading,
    isError,
    error,
    refetch
  } = useQuery({
    queryKey: ['analytics-dashboard', filters],
    queryFn: () => analyticsService.getDashboardData(filters),
    refetchInterval: options.refreshInterval || 0,
    enabled: options.enabled ?? true,
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry on client errors, only server errors
      if (error && 'status' in error && typeof error.status === 'number') {
        return error.status >= 500 && failureCount < 2;
      }
      return failureCount < 2;
    },
    onError: (error: Error) => {
      console.error('Analytics data fetch error:', error);
      toast({
        title: "Analytics Error",
        description: "Failed to load analytics data. Please try again.",
        variant: "destructive",
      });
    }
  });

  // Goal progress update mutation
  const goalProgressMutation = useMutation({
    mutationFn: ({ goalId, currentValue }: { goalId: string; currentValue: number }) => 
      analyticsService.updateGoalProgress(goalId, currentValue),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['analytics-dashboard'] });
      toast({
        title: "Goal Updated",
        description: "Your goal progress has been updated successfully.",
      });
    },
    onError: (error: Error) => {
      console.error('Goal update error:', error);
      toast({
        title: "Update Failed",
        description: "Failed to update goal progress. Please try again.",
        variant: "destructive",
      });
    }
  });

  // Goal creation mutation
  const createGoalMutation = useMutation({
    mutationFn: (goal: any) => analyticsService.createGoal(goal),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['analytics-dashboard'] });
      toast({
        title: "Goal Created",
        description: "Your new goal has been created successfully.",
      });
    },
    onError: (error: Error) => {
      console.error('Goal creation error:', error);
      toast({
        title: "Creation Failed",
        description: "Failed to create goal. Please try again.",
        variant: "destructive",
      });
    }
  });

  // Export data mutation
  const exportMutation = useMutation({
    mutationFn: (options: ExportOptions) => analyticsService.exportData(options),
    onSuccess: () => {
      toast({
        title: "Export Complete",
        description: "Your analytics data has been exported successfully.",
      });
    },
    onError: (error: Error) => {
      console.error('Export error:', error);
      toast({
        title: "Export Failed",
        description: "Failed to export analytics data. Please try again.",
        variant: "destructive",
      });
    }
  });

  // Update goal progress function
  const updateGoalProgress = React.useCallback(async (goalId: string, currentValue: number) => {
    try {
      await goalProgressMutation.mutateAsync({ goalId, currentValue });
    } catch (error) {
      // Error handling is done in the mutation
    }
  }, [goalProgressMutation]);

  // Create goal function
  const createGoal = React.useCallback(async (goal: any) => {
    try {
      return await createGoalMutation.mutateAsync(goal);
    } catch (error) {
      throw error;
    }
  }, [createGoalMutation]);

  // Handle insight actions
  const handleInsightAction = React.useCallback(async (insight: InsightData, actionIndex: number) => {
    if (!insight.actions || actionIndex >= insight.actions.length) return;

    const action = insight.actions[actionIndex];
    
    try {
      switch (action.action) {
        case 'enable_focus_mode':
          // Implement focus mode enabling
          analyticsService.trackEvent('focus_mode_enabled', {
            source: 'insight',
            insightId: insight.id,
            duration: action.data?.duration
          });
          toast({
            title: "Focus Mode Enabled",
            description: `Focus mode has been enabled for ${action.data?.duration || 60} minutes.`,
          });
          break;

        case 'create_goal':
          // Create a new goal based on insight
          const goalData = {
            title: action.data?.title || 'New Productivity Goal',
            category: action.data?.category || 'productivity',
            targetValue: action.data?.target || 100,
            unit: action.data?.unit || 'minutes',
            currentValue: 0,
            status: 'active'
          };
          
          await createGoal(goalData);
          break;

        case 'view_analysis':
          // Track that user viewed detailed analysis
          analyticsService.trackEvent('insight_analysis_viewed', {
            insightId: insight.id,
            category: insight.category
          });
          break;

        default:
          console.warn(`Unknown insight action: ${action.action}`);
      }

      // Track insight action taken
      analyticsService.trackEvent('insight_action_taken', {
        insightId: insight.id,
        action: action.action,
        actionIndex
      });

    } catch (error) {
      console.error('Insight action error:', error);
      toast({
        title: "Action Failed",
        description: "Failed to perform the requested action. Please try again.",
        variant: "destructive",
      });
    }
  }, [createGoal, toast]);

  // Export data function
  const exportData = React.useCallback(async (options: ExportOptions): Promise<Blob> => {
    try {
      return await exportMutation.mutateAsync(options);
    } catch (error) {
      throw error;
    }
  }, [exportMutation]);

  // Event tracking function
  const trackEvent = React.useCallback((eventType: string, data?: any) => {
    analyticsService.trackEvent(eventType, data);
  }, []);

  // Track page view when component mounts
  React.useEffect(() => {
    trackEvent('analytics_dashboard_viewed', {
      timeRange: options.timeRange,
      filters: {
        notebooks: options.notebooks?.length || 0,
        features: options.features?.length || 0,
        contentTypes: options.contentTypes?.length || 0,
        focusModes: options.focusModes?.length || 0
      }
    });
  }, [trackEvent, options]);

  // Track filter changes
  React.useEffect(() => {
    trackEvent('analytics_filter_changed', {
      timeRange: options.timeRange.preset,
      hasNotebookFilter: Boolean(options.notebooks?.length),
      hasFeatureFilter: Boolean(options.features?.length),
      hasContentTypeFilter: Boolean(options.contentTypes?.length),
      hasFocusModeFilter: Boolean(options.focusModes?.length)
    });
  }, [trackEvent, filters]);

  return {
    data,
    isLoading: isLoading || goalProgressMutation.isPending || createGoalMutation.isPending || exportMutation.isPending,
    isError,
    error: error as Error | null,
    refetch,
    updateGoalProgress,
    createGoal,
    handleInsightAction,
    exportData,
    trackEvent
  };
};

export default useAnalytics;