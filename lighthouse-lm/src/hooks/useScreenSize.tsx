import { useState, useEffect } from 'react';

/**
 * Unified hook for screen size detection
 * Replaces both useIsDesktop and useIsMobile hooks
 */

export interface ScreenSize {
  width: number;
  height: number;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isLargeDesktop: boolean;
}

export interface UseScreenSizeOptions {
  mobileBreakpoint?: number;
  tabletBreakpoint?: number;
  desktopBreakpoint?: number;
  largeDesktopBreakpoint?: number;
  debounceDelay?: number;
}

const DEFAULT_BREAKPOINTS = {
  mobile: 768,
  tablet: 1024,
  desktop: 1280,
  largeDesktop: 1920,
};

export function useScreenSize(options: UseScreenSizeOptions = {}): ScreenSize {
  const {
    mobileBreakpoint = DEFAULT_BREAKPOINTS.mobile,
    tabletBreakpoint = DEFAULT_BREAKPOINTS.tablet,
    desktopBreakpoint = DEFAULT_BREAKPOINTS.desktop,
    largeDesktopBreakpoint = DEFAULT_BREAKPOINTS.largeDesktop,
    debounceDelay = 100,
  } = options;

  const getScreenSize = (): ScreenSize => {
    const width = window.innerWidth;
    const height = window.innerHeight;

    return {
      width,
      height,
      isMobile: width < mobileBreakpoint,
      isTablet: width >= mobileBreakpoint && width < tabletBreakpoint,
      isDesktop: width >= tabletBreakpoint,
      isLargeDesktop: width >= largeDesktopBreakpoint,
    };
  };

  const [screenSize, setScreenSize] = useState<ScreenSize>(getScreenSize);

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleResize = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setScreenSize(getScreenSize());
      }, debounceDelay);
    };

    window.addEventListener('resize', handleResize);
    
    // Set initial size
    setScreenSize(getScreenSize());

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('resize', handleResize);
    };
  }, [mobileBreakpoint, tabletBreakpoint, desktopBreakpoint, largeDesktopBreakpoint, debounceDelay]);

  return screenSize;
}

/**
 * Convenience hooks for common use cases
 */
export function useIsMobile(): boolean {
  const { isMobile } = useScreenSize();
  return isMobile;
}

export function useIsTablet(): boolean {
  const { isTablet } = useScreenSize();
  return isTablet;
}

export function useIsDesktop(): boolean {
  const { isDesktop } = useScreenSize();
  return isDesktop;
}

/**
 * Legacy compatibility exports
 * These maintain backward compatibility with existing code
 */
export { useIsDesktop as default } from './useScreenSize';