import InsightsAPI from '@/services/insights-api';
import { useMutationWithToast, mutationPresets } from './useMutationWithToast';

export const useNotebookDelete = (onDeleteSuccess?: () => void) => {
  const mutation = useMutationWithToast({
    mutationFn: async (notebookId: string) => {
      const result = await InsightsAPI.deleteNotebook(notebookId);
      return result;
    },
    invalidateQueries: [['notebooks']],
    ...mutationPresets.delete('Notebook'),
    onSuccess: (data, notebookId) => {
      // Call the success callback if provided
      onDeleteSuccess?.();
    },
  });

  return {
    deleteNotebook: mutation.mutate,
    deleteNotebookAsync: mutation.mutateAsync,
    isDeleting: mutation.isPending,
  };
};