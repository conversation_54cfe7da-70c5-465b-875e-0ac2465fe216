import { useState, useEffect, useCallback } from 'react';
import { tauriService } from '@/services/tauriService';
import { useToast } from './useToast';

import { ChatMessage } from '@/services/tauriService';

export const useChatMessages = (notebookId?: string) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [isDeletingChatHistory, setIsDeletingChatHistory] = useState(false);
  const { toast } = useToast();

  // Load messages when notebook changes
  useEffect(() => {
    if (!notebookId) {
      setMessages([]);
      return;
    }

    const loadMessages = async () => {
      try {
        setIsLoading(true);
        const chatMessages = await tauriService.getChatMessages(notebookId);
        setMessages(chatMessages);
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load chat history",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadMessages();
  }, [notebookId, toast]);

  const sendMessage = useCallback(async (message: string) => {
    if (!notebookId || !message.trim()) return;

    try {
      setIsSending(true);
      
      // Send message with a timeout to prevent UI freezing
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Request timed out')), 15000); // 15 second timeout
      });
      
      const response = await Promise.race([
        tauriService.sendChatMessage(notebookId, message),
        timeoutPromise
      ]) as ChatMessage;

      // Reload messages to get both user and assistant messages
      const updatedMessages = await tauriService.getChatMessages(notebookId);
      setMessages(updatedMessages);

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error && error.message === 'Request timed out'
        ? "The request took too long. Please try again with a simpler question."
        : "Failed to send message. Please try again.";
      
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      
      // Don't throw the error to prevent UI from breaking
      return null;
    } finally {
      setIsSending(false);
    }
  }, [notebookId, toast]);

  const sendMessageAsync = async (messageData: {
    notebookId: string;
    role: string;
    content: string;
  }) => {
    return sendMessage(messageData.content);
  };

  const deleteChatHistory = useCallback(async (nbId?: string) => {
    const targetNotebookId = nbId || notebookId;
    if (!targetNotebookId) return;

    try {
      setIsDeletingChatHistory(true);
      await tauriService.clearChatHistory(targetNotebookId);
      setMessages([]);
      toast({
        title: "Chat history cleared",
        description: "All messages have been deleted successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to clear chat history. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeletingChatHistory(false);
    }
  }, [notebookId, toast]);

  return {
    messages,
    isLoading,
    isSending,
    isDeletingChatHistory,
    sendMessage,
    sendMessageAsync,
    deleteChatHistory,
    error: null,
  };
};
