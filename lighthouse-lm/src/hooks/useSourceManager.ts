import { useState, useCallback, useMemo, useRef } from 'react';
import { useToast } from '../../hooks/useToast';
import { useQueryClient } from '@tanstack/react-query';
import { Source } from '@/services/insights-api';
import {
  useNotificationService as useNotification,
  NOTIFICATION_MESSAGES
} from '../services/notification';
import {
  useErrorH<PERSON><PERSON>,
  useAsyncOperation
} from '../utils/error';
import { Citation } from '../types/message';

export interface SourceFilters {
  searchTerm: string;
  filterType: string;
  filterTags: string[];
  showArchived: boolean;
  showStarredOnly: boolean;
}

export interface SourceSorting {
  sortField: 'name' | 'type' | 'date' | 'size' | 'starred';
  sortOrder: 'asc' | 'desc';
}

export interface SourceManagerState {
  selectedSources: Set<string>;
  filters: SourceFilters;
  sorting: SourceSorting;
  viewMode: 'list' | 'grid' | 'compact';
  processingIds: Set<string>;
  showBulkActions: boolean;
}

export interface SourceManagerActions {
  // Selection management
  toggleSourceSelection: (sourceId: string, event?: React.MouseEvent) => void;
  selectAllSources: () => void;
  deselectAllSources: () => void;
  selectRange: (startId: string, endId: string) => void;
  
  // Filtering and sorting
  updateFilters: (filters: Partial<SourceFilters>) => void;
  updateSorting: (sorting: Partial<SourceSorting>) => void;
  clearFilters: () => void;
  
  // View management
  setViewMode: (mode: 'list' | 'grid' | 'compact') => void;
  toggleBulkActions: () => void;
  
  // Source operations
  handleSourceClick: (source: Source, setSelectedCitation?: (citation: Citation | null) => void) => void;
  handleSourceDelete: (sourceId: string, onSourceDelete: (id: string) => Promise<void>) => Promise<void>;
  handleSourceRename: (source: Source, onSourceRename: (source: Source) => Promise<void>) => Promise<void>;
  
  // Bulk operations
  handleBulkDelete: (sourceIds: string[], onBulkDelete: (ids: string[]) => Promise<void>) => Promise<void>;
  handleBulkExport: (sourceIds: string[], onBulkExport: (ids: string[]) => Promise<void>) => Promise<void>;
  handleBulkArchive: (sourceIds: string[], onBulkArchive: (ids: string[]) => Promise<void>) => Promise<void>;
}

export interface UseSourceManagerReturn {
  state: SourceManagerState;
  actions: SourceManagerActions;
  filteredAndSortedSources: Source[];
  sourceTypes: string[];
  allTags: string[];
  hasSelectedSources: boolean;
  selectedSourcesCount: number;
}

const defaultFilters: SourceFilters = {
  searchTerm: '',
  filterType: 'all',
  filterTags: [],
  showArchived: false,
  showStarredOnly: false,
};

const defaultSorting: SourceSorting = {
  sortField: 'date',
  sortOrder: 'desc',
};

export const useSourceManager = (sources: Source[], notebookId?: string): UseSourceManagerReturn => {
  const { success, error } = useNotification();
   const { handleAsyncError } = useErrorHandler();
  const queryClient = useQueryClient();
  
  // State management
  const [selectedSources, setSelectedSources] = useState<Set<string>>(new Set());
  const [filters, setFilters] = useState<SourceFilters>(defaultFilters);
  const [sorting, setSorting] = useState<SourceSorting>(defaultSorting);
  const [viewMode, setViewMode] = useState<'list' | 'grid' | 'compact'>('list');
  const [processingIds, setProcessingIds] = useState<Set<string>>(new Set());
  const [showBulkActions, setShowBulkActions] = useState(false);

  // Computed values
  const filteredAndSortedSources = useMemo(() => {
    const filtered = sources.filter(source => {
      const matchesSearch = 
        source.title?.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
        source.display_name?.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
        source.content?.toLowerCase().includes(filters.searchTerm.toLowerCase());
      
      const matchesType = 
        filters.filterType === 'all' || 
        source.source_type === filters.filterType;
      
      // Note: Source interface doesn't have tags, archived, or starred fields
      // These filters are disabled for now but can be implemented when the backend supports them
      const matchesTags = filters.filterTags.length === 0; // Always true since no tags field
      const matchesArchived = true; // Always true since no archived field
      const matchesStarred = !filters.showStarredOnly; // Always true since no starred field
      
      return matchesSearch && matchesType && matchesTags && matchesArchived && matchesStarred;
    });

    return filtered.sort((a, b) => {
      let compareValue = 0;
      
      switch (sorting.sortField) {
        case 'name':
          compareValue = (a.title || a.display_name || '').localeCompare(b.title || b.display_name || '');
          break;
        case 'type':
          compareValue = (a.source_type || '').localeCompare(b.source_type || '');
          break;
        case 'date':
          const dateA = new Date(a.updated_at || a.created_at || 0).getTime();
          const dateB = new Date(b.updated_at || b.created_at || 0).getTime();
          compareValue = dateB - dateA;
          break;
        case 'size':
          compareValue = (b.file_size || b.content?.length || 0) - (a.file_size || a.content?.length || 0);
          break;
        case 'starred':
          // Since Source doesn't have starred field, treat all as equal
          compareValue = 0;
          break;
      }
      
      return sorting.sortOrder === 'asc' ? compareValue : -compareValue;
    });
  }, [sources, filters, sorting]);

  const sourceTypes = useMemo(() => {
    const types = new Set(sources.map(s => s.source_type).filter(Boolean));
    return Array.from(types);
  }, [sources]);

  const allTags = useMemo(() => {
    // Source interface doesn't have tags field, return empty array for now
    // This can be implemented when the backend supports tags
    return [];
  }, [sources]);

  // Selection management
  const toggleSourceSelection = useCallback((sourceId: string, event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation();
    }
    
    setSelectedSources(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sourceId)) {
        newSet.delete(sourceId);
      } else {
        newSet.add(sourceId);
      }
      return newSet;
    });
  }, []);

  const selectAllSources = useCallback(() => {
    if (selectedSources.size === filteredAndSortedSources.length) {
      setSelectedSources(new Set());
    } else {
      setSelectedSources(new Set(filteredAndSortedSources.map(s => s.id)));
    }
  }, [selectedSources.size, filteredAndSortedSources]);

  const deselectAllSources = useCallback(() => {
    setSelectedSources(new Set());
  }, []);

  const selectRange = useCallback((startId: string, endId: string) => {
    const startIndex = filteredAndSortedSources.findIndex(s => s.id === startId);
    const endIndex = filteredAndSortedSources.findIndex(s => s.id === endId);
    
    if (startIndex !== -1 && endIndex !== -1) {
      const [from, to] = startIndex < endIndex ? [startIndex, endIndex] : [endIndex, startIndex];
      const rangeIds = filteredAndSortedSources.slice(from, to + 1).map(s => s.id);
      setSelectedSources(prev => new Set([...prev, ...rangeIds]));
    }
  }, [filteredAndSortedSources]);

  // Filtering and sorting
  const updateFilters = useCallback((newFilters: Partial<SourceFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const updateSorting = useCallback((newSorting: Partial<SourceSorting>) => {
    setSorting(prev => ({ ...prev, ...newSorting }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters(defaultFilters);
  }, []);

  // View management
  const toggleBulkActions = useCallback(() => {
    setShowBulkActions(prev => !prev);
  }, []);

  // Source operations
  const handleSourceClick = useCallback((source: Source, setSelectedCitation?: (citation: Citation | null) => void) => {
    if (setSelectedCitation) {
      const citation: Citation = {
        citation_id: parseInt(source.id) || 0,
        source_id: source.id,
        source_title: source.title || 'Untitled',
        source_type: source.source_type || 'document',
        chunk_lines_from: 0,
        chunk_lines_to: 0,
        chunk_index: 0,
        excerpt: source.content || ''
      };
      setSelectedCitation(citation);
    }
  }, []);

  const handleSourceDelete = useCallback(async (sourceId: string, onSourceDelete: (id: string) => Promise<void>) => {
    setProcessingIds(prev => new Set([...prev, sourceId]));
    try {
      await onSourceDelete(sourceId);
      success.sourceDeleted();
      if (notebookId) {
        queryClient.invalidateQueries({ queryKey: ['sources', notebookId] });
      }
    } catch (error) {
      toast({ 
        title: 'Error', 
        description: 'Failed to delete source',
        variant: 'destructive' 
      });
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(sourceId);
        return newSet;
      });
    }
  }, [success, error, queryClient, notebookId]);

  const handleSourceRename = useCallback(async (source: Source, onSourceRename: (source: Source) => Promise<void>) => {
    try {
      await onSourceRename(source);
      success.sourceRenamed();
      if (notebookId) {
        queryClient.invalidateQueries({ queryKey: ['sources', notebookId] });
      }
    } catch (error) {
      toast({ 
        title: 'Error', 
        description: 'Failed to rename source',
        variant: 'destructive' 
      });
    }
  }, [success, error, queryClient, notebookId]);

  // Bulk operations
  const handleBulkDelete = useCallback(async (sourceIds: string[], onBulkDelete: (ids: string[]) => Promise<void>) => {
    setProcessingIds(new Set(sourceIds));
    try {
      await onBulkDelete(sourceIds);
      success.bulkOperation(sourceIds.length, 'source', 'Deleted');
      setSelectedSources(new Set());
      setShowBulkActions(false);
      if (notebookId) {
        queryClient.invalidateQueries({ queryKey: ['sources', notebookId] });
      }
    } catch (error) {
      toast({ 
        title: 'Error', 
        description: 'Failed to delete sources',
        variant: 'destructive' 
      });
    } finally {
      setProcessingIds(new Set());
    }
  }, [success, error, queryClient, notebookId]);

  const handleBulkExport = useCallback(async (sourceIds: string[], onBulkExport: (ids: string[]) => Promise<void>) => {
    setProcessingIds(new Set(sourceIds));
    try {
      await onBulkExport(sourceIds);
      toast({ title: `Exported ${sourceIds.length} source(s)` });
      setSelectedSources(new Set());
      setShowBulkActions(false);
    } catch (error) {
      toast({ 
        title: 'Error', 
        description: 'Failed to export sources',
        variant: 'destructive' 
      });
    } finally {
      setProcessingIds(new Set());
    }
  }, [toast]);

  const handleBulkArchive = useCallback(async (sourceIds: string[], onBulkArchive: (ids: string[]) => Promise<void>) => {
    setProcessingIds(new Set(sourceIds));
    try {
      await onBulkArchive(sourceIds);
      toast({ title: `Archived ${sourceIds.length} source(s)` });
      setSelectedSources(new Set());
      setShowBulkActions(false);
      if (notebookId) {
        queryClient.invalidateQueries({ queryKey: ['sources', notebookId] });
      }
    } catch (error) {
      toast({ 
        title: 'Error', 
        description: 'Failed to archive sources',
        variant: 'destructive' 
      });
    } finally {
      setProcessingIds(new Set());
    }
  }, [success, error, queryClient, notebookId]);

  const state: SourceManagerState = {
    selectedSources,
    filters,
    sorting,
    viewMode,
    processingIds,
    showBulkActions,
  };

  const actions: SourceManagerActions = {
    toggleSourceSelection,
    selectAllSources,
    deselectAllSources,
    selectRange,
    updateFilters,
    updateSorting,
    clearFilters,
    setViewMode,
    toggleBulkActions,
    handleSourceClick,
    handleSourceDelete,
    handleSourceRename,
    handleBulkDelete,
    handleBulkExport,
    handleBulkArchive,
  };

  return {
    state,
    actions,
    filteredAndSortedSources,
    sourceTypes,
    allTags,
    hasSelectedSources: selectedSources.size > 0,
    selectedSourcesCount: selectedSources.size,
  };
};