{"version": 3, "sources": ["../../../../node_modules/@tauri-apps/api/core.js", "../../../../node_modules/@tauri-apps/api/external/tslib/tslib.es6.js"], "sourcesContent": ["import { __classPrivateFieldGet, __classPrivateFieldSet } from './external/tslib/tslib.es6.js';\n\n// Copyright 2019-2024 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\nvar _Channel_onmessage, _Channel_nextMessageIndex, _Channel_pendingMessages, _Channel_messageEndIndex, _Resource_rid;\n/**\n * Invoke your custom commands.\n *\n * This package is also accessible with `window.__TAURI__.core` when [`app.withGlobalTauri`](https://v2.tauri.app/reference/config/#withglobaltauri) in `tauri.conf.json` is set to `true`.\n * @module\n */\n/**\n * A key to be used to implement a special function\n * on your types that define how your type should be serialized\n * when passing across the IPC.\n * @example\n * Given a type in Rust that looks like this\n * ```rs\n * #[derive(serde::Serialize, serde::Deserialize)\n * enum UserId {\n *   String(String),\n *   Number(u32),\n * }\n * ```\n * `UserId::String(\"id\")` would be serialized into `{ String: \"id\" }`\n * and so we need to pass the same structure back to Rust\n * ```ts\n * import { SERIALIZE_TO_IPC_FN } from \"@tauri-apps/api/core\"\n *\n * class UserIdString {\n *   id\n *   constructor(id) {\n *     this.id = id\n *   }\n *\n *   [SERIALIZE_TO_IPC_FN]() {\n *     return { String: this.id }\n *   }\n * }\n *\n * class UserIdNumber {\n *   id\n *   constructor(id) {\n *     this.id = id\n *   }\n *\n *   [SERIALIZE_TO_IPC_FN]() {\n *     return { Number: this.id }\n *   }\n * }\n *\n * type UserId = UserIdString | UserIdNumber\n * ```\n *\n */\n// if this value changes, make sure to update it in:\n// 1. ipc.js\n// 2. process-ipc-message-fn.js\nconst SERIALIZE_TO_IPC_FN = '__TAURI_TO_IPC_KEY__';\n/**\n * Stores the callback in a known location, and returns an identifier that can be passed to the backend.\n * The backend uses the identifier to `eval()` the callback.\n *\n * @return An unique identifier associated with the callback function.\n *\n * @since 1.0.0\n */\nfunction transformCallback(\n// TODO: Make this not optional in v3\ncallback, once = false) {\n    return window.__TAURI_INTERNALS__.transformCallback(callback, once);\n}\nclass Channel {\n    constructor(onmessage) {\n        _Channel_onmessage.set(this, void 0);\n        // the index is used as a mechanism to preserve message order\n        _Channel_nextMessageIndex.set(this, 0);\n        _Channel_pendingMessages.set(this, []);\n        _Channel_messageEndIndex.set(this, void 0);\n        __classPrivateFieldSet(this, _Channel_onmessage, onmessage || (() => { }), \"f\");\n        this.id = transformCallback((rawMessage) => {\n            const index = rawMessage.index;\n            if ('end' in rawMessage) {\n                if (index == __classPrivateFieldGet(this, _Channel_nextMessageIndex, \"f\")) {\n                    this.cleanupCallback();\n                }\n                else {\n                    __classPrivateFieldSet(this, _Channel_messageEndIndex, index, \"f\");\n                }\n                return;\n            }\n            const message = rawMessage.message;\n            // Process the message if we're at the right order\n            if (index == __classPrivateFieldGet(this, _Channel_nextMessageIndex, \"f\")) {\n                __classPrivateFieldGet(this, _Channel_onmessage, \"f\").call(this, message);\n                __classPrivateFieldSet(this, _Channel_nextMessageIndex, __classPrivateFieldGet(this, _Channel_nextMessageIndex, \"f\") + 1, \"f\");\n                // process pending messages\n                while (__classPrivateFieldGet(this, _Channel_nextMessageIndex, \"f\") in __classPrivateFieldGet(this, _Channel_pendingMessages, \"f\")) {\n                    const message = __classPrivateFieldGet(this, _Channel_pendingMessages, \"f\")[__classPrivateFieldGet(this, _Channel_nextMessageIndex, \"f\")];\n                    __classPrivateFieldGet(this, _Channel_onmessage, \"f\").call(this, message);\n                    // eslint-disable-next-line @typescript-eslint/no-array-delete\n                    delete __classPrivateFieldGet(this, _Channel_pendingMessages, \"f\")[__classPrivateFieldGet(this, _Channel_nextMessageIndex, \"f\")];\n                    __classPrivateFieldSet(this, _Channel_nextMessageIndex, __classPrivateFieldGet(this, _Channel_nextMessageIndex, \"f\") + 1, \"f\");\n                }\n                if (__classPrivateFieldGet(this, _Channel_nextMessageIndex, \"f\") === __classPrivateFieldGet(this, _Channel_messageEndIndex, \"f\")) {\n                    this.cleanupCallback();\n                }\n            }\n            // Queue the message if we're not\n            else {\n                // eslint-disable-next-line security/detect-object-injection\n                __classPrivateFieldGet(this, _Channel_pendingMessages, \"f\")[index] = message;\n            }\n        });\n    }\n    cleanupCallback() {\n        window.__TAURI_INTERNALS__.unregisterCallback(this.id);\n    }\n    set onmessage(handler) {\n        __classPrivateFieldSet(this, _Channel_onmessage, handler, \"f\");\n    }\n    get onmessage() {\n        return __classPrivateFieldGet(this, _Channel_onmessage, \"f\");\n    }\n    [(_Channel_onmessage = new WeakMap(), _Channel_nextMessageIndex = new WeakMap(), _Channel_pendingMessages = new WeakMap(), _Channel_messageEndIndex = new WeakMap(), SERIALIZE_TO_IPC_FN)]() {\n        return `__CHANNEL__:${this.id}`;\n    }\n    toJSON() {\n        // eslint-disable-next-line security/detect-object-injection\n        return this[SERIALIZE_TO_IPC_FN]();\n    }\n}\nclass PluginListener {\n    constructor(plugin, event, channelId) {\n        this.plugin = plugin;\n        this.event = event;\n        this.channelId = channelId;\n    }\n    async unregister() {\n        return invoke(`plugin:${this.plugin}|remove_listener`, {\n            event: this.event,\n            channelId: this.channelId\n        });\n    }\n}\n/**\n * Adds a listener to a plugin event.\n *\n * @returns The listener object to stop listening to the events.\n *\n * @since 2.0.0\n */\nasync function addPluginListener(plugin, event, cb) {\n    const handler = new Channel(cb);\n    return invoke(`plugin:${plugin}|registerListener`, { event, handler }).then(() => new PluginListener(plugin, event, handler.id));\n}\n/**\n * Get permission state for a plugin.\n *\n * This should be used by plugin authors to wrap their actual implementation.\n */\nasync function checkPermissions(plugin) {\n    return invoke(`plugin:${plugin}|check_permissions`);\n}\n/**\n * Request permissions.\n *\n * This should be used by plugin authors to wrap their actual implementation.\n */\nasync function requestPermissions(plugin) {\n    return invoke(`plugin:${plugin}|request_permissions`);\n}\n/**\n * Sends a message to the backend.\n * @example\n * ```typescript\n * import { invoke } from '@tauri-apps/api/core';\n * await invoke('login', { user: 'tauri', password: 'poiwe3h4r5ip3yrhtew9ty' });\n * ```\n *\n * @param cmd The command name.\n * @param args The optional arguments to pass to the command.\n * @param options The request options.\n * @return A promise resolving or rejecting to the backend response.\n *\n * @since 1.0.0\n */\nasync function invoke(cmd, args = {}, options) {\n    return window.__TAURI_INTERNALS__.invoke(cmd, args, options);\n}\n/**\n * Convert a device file path to an URL that can be loaded by the webview.\n * Note that `asset:` and `http://asset.localhost` must be added to [`app.security.csp`](https://v2.tauri.app/reference/config/#csp-1) in `tauri.conf.json`.\n * Example CSP value: `\"csp\": \"default-src 'self' ipc: http://ipc.localhost; img-src 'self' asset: http://asset.localhost\"` to use the asset protocol on image sources.\n *\n * Additionally, `\"enable\" : \"true\"` must be added to [`app.security.assetProtocol`](https://v2.tauri.app/reference/config/#assetprotocolconfig)\n * in `tauri.conf.json` and its access scope must be defined on the `scope` array on the same `assetProtocol` object.\n *\n * @param  filePath The file path.\n * @param  protocol The protocol to use. Defaults to `asset`. You only need to set this when using a custom protocol.\n * @example\n * ```typescript\n * import { appDataDir, join } from '@tauri-apps/api/path';\n * import { convertFileSrc } from '@tauri-apps/api/core';\n * const appDataDirPath = await appDataDir();\n * const filePath = await join(appDataDirPath, 'assets/video.mp4');\n * const assetUrl = convertFileSrc(filePath);\n *\n * const video = document.getElementById('my-video');\n * const source = document.createElement('source');\n * source.type = 'video/mp4';\n * source.src = assetUrl;\n * video.appendChild(source);\n * video.load();\n * ```\n *\n * @return the URL that can be used as source on the webview.\n *\n * @since 1.0.0\n */\nfunction convertFileSrc(filePath, protocol = 'asset') {\n    return window.__TAURI_INTERNALS__.convertFileSrc(filePath, protocol);\n}\n/**\n * A rust-backed resource stored through `tauri::Manager::resources_table` API.\n *\n * The resource lives in the main process and does not exist\n * in the Javascript world, and thus will not be cleaned up automatiacally\n * except on application exit. If you want to clean it up early, call {@linkcode Resource.close}\n *\n * @example\n * ```typescript\n * import { Resource, invoke } from '@tauri-apps/api/core';\n * export class DatabaseHandle extends Resource {\n *   static async open(path: string): Promise<DatabaseHandle> {\n *     const rid: number = await invoke('open_db', { path });\n *     return new DatabaseHandle(rid);\n *   }\n *\n *   async execute(sql: string): Promise<void> {\n *     await invoke('execute_sql', { rid: this.rid, sql });\n *   }\n * }\n * ```\n */\nclass Resource {\n    get rid() {\n        return __classPrivateFieldGet(this, _Resource_rid, \"f\");\n    }\n    constructor(rid) {\n        _Resource_rid.set(this, void 0);\n        __classPrivateFieldSet(this, _Resource_rid, rid, \"f\");\n    }\n    /**\n     * Destroys and cleans up this resource from memory.\n     * **You should not call any method on this object anymore and should drop any reference to it.**\n     */\n    async close() {\n        return invoke('plugin:resources|close', {\n            rid: this.rid\n        });\n    }\n}\n_Resource_rid = new WeakMap();\nfunction isTauri() {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access\n    return !!(globalThis || window).isTauri;\n}\n\nexport { Channel, PluginListener, Resource, SERIALIZE_TO_IPC_FN, addPluginListener, checkPermissions, convertFileSrc, invoke, isTauri, requestPermissions, transformCallback };\n", "/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\r\n\r\n\r\nfunction __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nfunction __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\nexport { __classPrivateFieldGet, __classPrivateFieldSet };\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACiBA,SAAS,uBAAuB,UAAU,OAAO,MAAM,GAAG;AACtD,MAAI,SAAS,OAAO,CAAC,EAAG,OAAM,IAAI,UAAU,+CAA+C;AAC3F,MAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,QAAQ,EAAG,OAAM,IAAI,UAAU,0EAA0E;AACjL,SAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,KAAK,QAAQ,IAAI,IAAI,EAAE,QAAQ,MAAM,IAAI,QAAQ;AAChG;AAEA,SAAS,uBAAuB,UAAU,OAAO,OAAO,MAAM,GAAG;AAC7D,MAAI,SAAS,IAAK,OAAM,IAAI,UAAU,gCAAgC;AACtE,MAAI,SAAS,OAAO,CAAC,EAAG,OAAM,IAAI,UAAU,+CAA+C;AAC3F,MAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,QAAQ,EAAG,OAAM,IAAI,UAAU,yEAAyE;AAChL,SAAQ,SAAS,MAAM,EAAE,KAAK,UAAU,KAAK,IAAI,IAAI,EAAE,QAAQ,QAAQ,MAAM,IAAI,UAAU,KAAK,GAAI;AACxG;;;ADvBA,IAAI;AAAJ,IAAwB;AAAxB,IAAmD;AAAnD,IAA6E;AAA7E,IAAuG;AAsDvG,IAAM,sBAAsB;AAS5B,SAAS,kBAET,UAAU,OAAO,OAAO;AACpB,SAAO,OAAO,oBAAoB,kBAAkB,UAAU,IAAI;AACtE;AACA,IAAM,UAAN,MAAc;AAAA,EACV,YAAY,WAAW;AACnB,uBAAmB,IAAI,MAAM,MAAM;AAEnC,8BAA0B,IAAI,MAAM,CAAC;AACrC,6BAAyB,IAAI,MAAM,CAAC,CAAC;AACrC,6BAAyB,IAAI,MAAM,MAAM;AACzC,2BAAuB,MAAM,oBAAoB,cAAc,MAAM;AAAA,IAAE,IAAI,GAAG;AAC9E,SAAK,KAAK,kBAAkB,CAAC,eAAe;AACxC,YAAM,QAAQ,WAAW;AACzB,UAAI,SAAS,YAAY;AACrB,YAAI,SAAS,uBAAuB,MAAM,2BAA2B,GAAG,GAAG;AACvE,eAAK,gBAAgB;AAAA,QACzB,OACK;AACD,iCAAuB,MAAM,0BAA0B,OAAO,GAAG;AAAA,QACrE;AACA;AAAA,MACJ;AACA,YAAM,UAAU,WAAW;AAE3B,UAAI,SAAS,uBAAuB,MAAM,2BAA2B,GAAG,GAAG;AACvE,+BAAuB,MAAM,oBAAoB,GAAG,EAAE,KAAK,MAAM,OAAO;AACxE,+BAAuB,MAAM,2BAA2B,uBAAuB,MAAM,2BAA2B,GAAG,IAAI,GAAG,GAAG;AAE7H,eAAO,uBAAuB,MAAM,2BAA2B,GAAG,KAAK,uBAAuB,MAAM,0BAA0B,GAAG,GAAG;AAChI,gBAAMA,WAAU,uBAAuB,MAAM,0BAA0B,GAAG,EAAE,uBAAuB,MAAM,2BAA2B,GAAG,CAAC;AACxI,iCAAuB,MAAM,oBAAoB,GAAG,EAAE,KAAK,MAAMA,QAAO;AAExE,iBAAO,uBAAuB,MAAM,0BAA0B,GAAG,EAAE,uBAAuB,MAAM,2BAA2B,GAAG,CAAC;AAC/H,iCAAuB,MAAM,2BAA2B,uBAAuB,MAAM,2BAA2B,GAAG,IAAI,GAAG,GAAG;AAAA,QACjI;AACA,YAAI,uBAAuB,MAAM,2BAA2B,GAAG,MAAM,uBAAuB,MAAM,0BAA0B,GAAG,GAAG;AAC9H,eAAK,gBAAgB;AAAA,QACzB;AAAA,MACJ,OAEK;AAED,+BAAuB,MAAM,0BAA0B,GAAG,EAAE,KAAK,IAAI;AAAA,MACzE;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,kBAAkB;AACd,WAAO,oBAAoB,mBAAmB,KAAK,EAAE;AAAA,EACzD;AAAA,EACA,IAAI,UAAU,SAAS;AACnB,2BAAuB,MAAM,oBAAoB,SAAS,GAAG;AAAA,EACjE;AAAA,EACA,IAAI,YAAY;AACZ,WAAO,uBAAuB,MAAM,oBAAoB,GAAG;AAAA,EAC/D;AAAA,EACA,EAAE,qBAAqB,oBAAI,QAAQ,GAAG,4BAA4B,oBAAI,QAAQ,GAAG,2BAA2B,oBAAI,QAAQ,GAAG,2BAA2B,oBAAI,QAAQ,GAAG,oBAAoB,IAAI;AACzL,WAAO,eAAe,KAAK,EAAE;AAAA,EACjC;AAAA,EACA,SAAS;AAEL,WAAO,KAAK,mBAAmB,EAAE;AAAA,EACrC;AACJ;AACA,IAAM,iBAAN,MAAqB;AAAA,EACjB,YAAY,QAAQ,OAAO,WAAW;AAClC,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,MAAM,aAAa;AACf,WAAO,OAAO,UAAU,KAAK,MAAM,oBAAoB;AAAA,MACnD,OAAO,KAAK;AAAA,MACZ,WAAW,KAAK;AAAA,IACpB,CAAC;AAAA,EACL;AACJ;AAQA,eAAe,kBAAkB,QAAQ,OAAO,IAAI;AAChD,QAAM,UAAU,IAAI,QAAQ,EAAE;AAC9B,SAAO,OAAO,UAAU,MAAM,qBAAqB,EAAE,OAAO,QAAQ,CAAC,EAAE,KAAK,MAAM,IAAI,eAAe,QAAQ,OAAO,QAAQ,EAAE,CAAC;AACnI;AAMA,eAAe,iBAAiB,QAAQ;AACpC,SAAO,OAAO,UAAU,MAAM,oBAAoB;AACtD;AAMA,eAAe,mBAAmB,QAAQ;AACtC,SAAO,OAAO,UAAU,MAAM,sBAAsB;AACxD;AAgBA,eAAe,OAAO,KAAK,OAAO,CAAC,GAAG,SAAS;AAC3C,SAAO,OAAO,oBAAoB,OAAO,KAAK,MAAM,OAAO;AAC/D;AA+BA,SAAS,eAAe,UAAU,WAAW,SAAS;AAClD,SAAO,OAAO,oBAAoB,eAAe,UAAU,QAAQ;AACvE;AAuBA,IAAM,WAAN,MAAe;AAAA,EACX,IAAI,MAAM;AACN,WAAO,uBAAuB,MAAM,eAAe,GAAG;AAAA,EAC1D;AAAA,EACA,YAAY,KAAK;AACb,kBAAc,IAAI,MAAM,MAAM;AAC9B,2BAAuB,MAAM,eAAe,KAAK,GAAG;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,QAAQ;AACV,WAAO,OAAO,0BAA0B;AAAA,MACpC,KAAK,KAAK;AAAA,IACd,CAAC;AAAA,EACL;AACJ;AACA,gBAAgB,oBAAI,QAAQ;AAC5B,SAAS,UAAU;AAEf,SAAO,CAAC,EAAE,cAAc,QAAQ;AACpC;", "names": ["message"]}