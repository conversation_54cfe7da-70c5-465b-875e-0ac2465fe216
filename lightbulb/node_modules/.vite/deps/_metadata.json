{"hash": "e140b2e0", "configHash": "f368bef9", "lockfileHash": "ac52f7c7", "browserHash": "a04b7ae5", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "99664575", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "281df077", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "8b62571c", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "bd75957f", "needsInterop": true}, "@tauri-apps/api/core": {"src": "../../../../node_modules/@tauri-apps/api/core.js", "file": "@tauri-apps_api_core.js", "fileHash": "089c598a", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "867126a6", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "cd35e0af", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "32db4b7d", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "e59f8642", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "2d6a794a", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "3076f5d0", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "9bd3ce46", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "dc151f0f", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "3f6c5538", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "d046072c", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "d832340b", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "afde7dd0", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "99ed8197", "needsInterop": false}, "@tauri-apps/api": {"src": "../../../../node_modules/@tauri-apps/api/index.js", "file": "@tauri-apps_api.js", "fileHash": "64c18e4c", "needsInterop": false}}, "chunks": {"chunk-XBCCX5NN": {"file": "chunk-XBCCX5NN.js"}, "chunk-INYV3Q6E": {"file": "chunk-INYV3Q6E.js"}, "chunk-M5M7ZFKO": {"file": "chunk-M5M7ZFKO.js"}, "chunk-J3U7JU3I": {"file": "chunk-J3U7JU3I.js"}, "chunk-PSIWZYBS": {"file": "chunk-PSIWZYBS.js"}, "chunk-5JCUXF34": {"file": "chunk-5JCUXF34.js"}, "chunk-QNBT3DDS": {"file": "chunk-QNBT3DDS.js"}, "chunk-IFAS7IS7": {"file": "chunk-IFAS7IS7.js"}, "chunk-TLMPZXCM": {"file": "chunk-TLMPZXCM.js"}, "chunk-RXUXJAPQ": {"file": "chunk-RXUXJAPQ.js"}, "chunk-6PXSGDAH": {"file": "chunk-6PXSGDAH.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-NXESFFTV": {"file": "chunk-NXESFFTV.js"}, "chunk-DRWLMN53": {"file": "chunk-DRWLMN53.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}