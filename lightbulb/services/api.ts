// API client for connecting to <PERSON><PERSON> backend
import { invoke } from '@tauri-apps/api';

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

interface AuthResponse {
  token: string;
  refresh_token: string;
  user: UserResponse;
}

interface UserResponse {
  id: string;
  email: string;
  username: string;
  created_at: string;
}

interface Thought {
  id: string;
  user_id: string;
  title: string;
  content: string;
  thought_type: string;
  tags?: string[];
  metadata?: any;
  created_at: string;
  updated_at: string;
}

interface ChatConversation {
  id: string;
  user_id: string;
  title?: string;
  created_at: string;
  updated_at: string;
}

interface ChatMessage {
  id: string;
  user_id: string;
  conversation_id: string;
  role: string;
  content: string;
  sources?: string[];
  created_at: string;
}

interface Tag {
  id: string;
  user_id: string;
  name: string;
  color?: string;
  usage_count: number;
  created_at: string;
}

interface Project {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  settings?: any;
  created_at: string;
  updated_at: string;
}

interface Page {
  id: string;
  user_id: string;
  project_id?: string;
  title: string;
  content: string;
  page_type: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

interface Source {
  id: string;
  user_id: string;
  name: string;
  source_type: string;
  file_path?: string;
  url?: string;
  content?: string;
  processed_content?: string;
  chunks?: any;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

interface ProcessedSource {
  source_id: string;
  user_id: string;
  content_type: string;
  processed_content: string;
  chunks: any[];
  metadata: any;
  processing_time_ms: number;
  error?: string;
}

class ApiClient {
  private token?: string;

  constructor() {
    // Load token from localStorage if available
    const stored = localStorage.getItem('auth_token');
    if (stored) {
      this.token = stored;
    }
  }

  private saveToken(token: string) {
    this.token = token;
    localStorage.setItem('auth_token', token);
  }

  private clearToken() {
    this.token = undefined;
    localStorage.removeItem('auth_token');
  }

  private async invokeCommand<T>(command: string, args: any = {}): Promise<T> {
    // Add token to args if available and not auth commands
    if (this.token && !command.startsWith('lightbulb_register') && !command.startsWith('lightbulb_login')) {
      args.token = this.token;
    }

    try {
      const response = await invoke<ApiResponse<T>>(command, args);
      
      if (!response.success) {
        throw new Error(response.error || 'Command failed');
      }
      
      return response.data as T;
    } catch (error) {
      console.error(`Command ${command} failed:`, error);
      throw error;
    }
  }

  // Authentication endpoints
  async register(email: string, username: string, password: string): Promise<AuthResponse> {
    const response = await this.invokeCommand<AuthResponse>('lightbulb_register', {
      email,
      username,
      password,
    });
    
    if (response.token) {
      this.saveToken(response.token);
    }
    
    return response;
  }

  async login(email: string, password: string): Promise<AuthResponse> {
    const response = await this.invokeCommand<AuthResponse>('lightbulb_login', {
      email,
      password,
    });
    
    if (response.token) {
      this.saveToken(response.token);
    }
    
    return response;
  }

  async verifyToken(): Promise<UserResponse> {
    if (!this.token) {
      throw new Error('No token available');
    }
    
    return this.invokeCommand<UserResponse>('lightbulb_verify_token', {
      token: this.token,
    });
  }

  async logout() {
    this.clearToken();
  }

  // Thoughts endpoints
  async getThoughts(): Promise<Thought[]> {
    return this.invokeCommand<Thought[]>('lightbulb_get_thoughts', {});
  }

  async createThought(data: {
    title: string;
    content: string;
    thought_type?: string;
    tags?: string[];
    metadata?: any;
  }): Promise<Thought> {
    return this.invokeCommand<Thought>('lightbulb_create_thought', {
      title: data.title,
      content: data.content,
      thoughtType: data.thought_type,
      tags: data.tags,
      metadata: data.metadata,
    });
  }

  async updateThought(id: string, data: Partial<{
    title: string;
    content: string;
    tags: string[];
    metadata: any;
  }>): Promise<void> {
    return this.invokeCommand<void>('lightbulb_update_thought', {
      thoughtId: id,
      title: data.title,
      content: data.content,
      tags: data.tags,
      metadata: data.metadata,
    });
  }

  async deleteThought(id: string): Promise<void> {
    return this.invokeCommand<void>('lightbulb_delete_thought', {
      thoughtId: id,
    });
  }

  async searchThoughts(query: string): Promise<Thought[]> {
    return this.invokeCommand<Thought[]>('lightbulb_search_thoughts', {
      query,
    });
  }

  // Chat endpoints
  async createConversation(title?: string): Promise<ChatConversation> {
    return this.invokeCommand<ChatConversation>('lightbulb_create_conversation', {
      title,
    });
  }

  async sendMessage(conversationId: string, content: string): Promise<ChatMessage> {
    return this.invokeCommand<ChatMessage>('lightbulb_send_message', {
      conversationId,
      content,
    });
  }

  async getConversationMessages(conversationId: string): Promise<ChatMessage[]> {
    return this.invokeCommand<ChatMessage[]>('lightbulb_get_conversation_messages', {
      conversationId,
    });
  }

  // Tags endpoints
  async getTags(): Promise<Tag[]> {
    return this.invokeCommand<Tag[]>('lightbulb_get_tags', {});
  }

  async createTag(name: string, color?: string): Promise<Tag> {
    return this.invokeCommand<Tag>('lightbulb_create_tag', {
      name,
      color,
    });
  }

  // Projects endpoints
  async getProjects(): Promise<Project[]> {
    return this.invokeCommand<Project[]>('lightbulb_get_projects', {});
  }

  async createProject(data: {
    name: string;
    description?: string;
    settings?: any;
  }): Promise<Project> {
    return this.invokeCommand<Project>('lightbulb_create_project', {
      name: data.name,
      description: data.description,
      settings: data.settings,
    });
  }

  // Pages endpoints
  async getProjectPages(projectId: string): Promise<Page[]> {
    return this.invokeCommand<Page[]>('lightbulb_get_project_pages', {
      projectId,
    });
  }

  async createPage(data: {
    title: string;
    content: string;
    project_id?: string;
    page_type?: string;
    metadata?: any;
  }): Promise<Page> {
    return this.invokeCommand<Page>('lightbulb_create_page', {
      title: data.title,
      content: data.content,
      projectId: data.project_id,
      pageType: data.page_type,
      metadata: data.metadata,
    });
  }

  // Search endpoint - uses the thoughts search for now
  async search(query: string): Promise<Thought[]> {
    return this.searchThoughts(query);
  }

  // Source endpoints
  async uploadSource(data: {
    name: string;
    source_type: string;
    file_path?: string;
    url?: string;
    content?: string;
  }): Promise<Source> {
    return this.invokeCommand<Source>('lightbulb_upload_source', {
      name: data.name,
      sourceType: data.source_type,
      filePath: data.file_path,
      url: data.url,
      content: data.content,
    });
  }

  async getSources(): Promise<Source[]> {
    return this.invokeCommand<Source[]>('lightbulb_get_sources', {});
  }

  async deleteSource(sourceId: string): Promise<void> {
    return this.invokeCommand<void>('lightbulb_delete_source', {
      sourceId,
    });
  }

  async processSource(sourceId: string): Promise<ProcessedSource> {
    return this.invokeCommand<ProcessedSource>('lightbulb_process_source', {
      sourceId,
    });
  }

  async searchSources(query: string): Promise<Source[]> {
    return this.invokeCommand<Source[]>('lightbulb_search_sources', {
      query,
    });
  }

  // AI endpoints - these would need to be implemented in the Tauri backend
  async aiComplete(prompt: string, context?: any): Promise<any> {
    // TODO: Implement AI completion in Tauri backend
    console.warn('AI completion not yet implemented in Tauri backend');
    return { text: 'AI completion not yet implemented' };
  }

  // WebSocket replacement - Tauri has built-in event system
  connectEvents() {
    // TODO: Use Tauri's event system for real-time updates
    console.log('Using Tauri event system for real-time updates');
  }
}

export const apiClient = new ApiClient();
export default apiClient;