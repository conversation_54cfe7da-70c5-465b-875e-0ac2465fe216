import React, { useEffect, useMemo, useState } from 'react';
import { cn } from '@/lib/utils';
import { iconMapping } from '@/lib/icon-mapping';
import { useResponsiveDesign, useIsMobile, useIsTouchDevice } from '@/hooks/useResponsiveDesign';
import { useTouchGestures } from '@/hooks/useTouchGestures';
import { useLightbulbState } from '@/hooks/useLightbulbState';
import { 
  MessageSquare, 
  Upload, 
  LayoutDashboard, 
  Brain, 
  FileText, 
  Folder, 
  Wrench, 
  Search, 
  Tag, 
  Grid, 
  Clock, 
  Bug, 
  Smartphone, 
  HelpCircle, 
  GraduationCap, 
  MessageCircle, 
  MoreHorizontal,
  ChevronRight,
  Plus,
  X,
  Send,
  Save,
  Check,
  Menu
} from 'lucide-react';

// Import UI components
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

// Import new section components
import { MainNavigation } from '@/components/sections/MainNavigation';
import { MobileHeader } from '@/components/sections/MobileHeader';
import { ContentRenderer } from '@/components/sections/ContentRenderer';
import MultiSourceChat from '@/components/MultiSourceChat';
import EnhancedContentProcessor from '@/components/EnhancedContentProcessor';
import VisualWorkspace from '@/components/VisualWorkspace';
import Pages from '@/components/Pages';
import { Projects } from '@/components/Projects';
import { AIHumanizer } from '@/components/AIHumanizer';
import { AdvancedSearch } from '@/components/AdvancedSearch';
import AutoTaggingSystem from '@/components/AutoTaggingSystem';
import { ChromeExtensionIntegration } from '@/components/ChromeExtensionIntegration';
import ContentProcessorTest from '@/components/ContentProcessorTest';
import MobileTester from '@/components/MobileTester';

// Import overlay components
import { OnboardingFlow } from '@/components/onboarding/OnboardingFlow';
import { InteractiveTutorial } from '@/components/onboarding/InteractiveTutorial';
import { HelpSystem } from '@/components/help/HelpSystem';
import { FeedbackWidget } from '@/components/feedback/FeedbackWidget';
import { AIProviderStatus } from '@/components/ai/AIProviderStatus';
import { AuthForm } from './components/AuthForm';
import { Toast, ToastType } from './components/Toast';

// Import services
import { onboardingService } from '@/services/onboardingService';
import { feedbackService } from '@/services/feedbackService';
import { performanceService } from '@/services/performanceService';
import deploymentService from '@/services/deploymentService';

// Import services
import apiClient from './services/api';
import { autoTaggingService, TagSuggestion } from './services/autoTaggingService';
import { categorizationService, Category, CategorySuggestion } from './services/categorizationService';
import { pagesService } from './services/pagesService';
import { projectService } from './services/projectService';

// Define TypeScript interfaces
interface KnowledgeItem {
  id: string;
  title: string;
  content: string;
  tags: string[];
  created_at: number;
  updated_at: number;
  source_type?: 'manual' | 'document' | 'url' | 'note';
  source_url?: string;
  file_type?: string;
}

interface DocumentSource {
  id: string;
  name: string;
  type: 'pdf' | 'txt' | 'docx' | 'url' | 'note';
  content: string;
  url?: string;
  uploaded_at: number;
}

interface ChatMessage {
  id: string;
  content: string;
  is_user: boolean;
  timestamp: number;
  context_ids?: string[];
}

const LightbulbComponent = () => {
  // Authentication state
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [showAuthForm, setShowAuthForm] = useState(false);
  
  // Toast state
  const [toast, setToast] = useState<{ message: string; type: ToastType } | null>(null);
  
  // Use centralized state management
  const { state, actions } = useLightbulbState();
  
  // Destructure state
  const {
    activeSection,
    userCount,
    searchQuery,
    contentItems,
    tags,
    inputValue,
    messages,
    savedItems,
    sources,
    categories,
    pages,
    projects,
    searchResults,
    loading,
    showQuickCapture,
    showAdvancedSearch,
    showMobileSidebar,
    mobileKeyboardOpen,
    showMobileCapture,
    showOnboarding,
    showTutorial,
    tutorialType,
    showHelp,
    showFeedback,
    helpQuery
  } = state;
  
  // Destructure actions
  const {
    setActiveSection,
    setUserCount,
    setSearchQuery,
    setContentItems,
    setTags,
    setInputValue,
    setMessages,
    setSavedItems,
    setSources,
    setCategories,
    setPages,
    setProjects,
    setSearchResults,
    setLoading,
    toggleQuickCapture,
    toggleAdvancedSearch,
    toggleMobileSidebar,
    setMobileKeyboardOpen,
    toggleMobileCapture,
    toggleOnboarding,
    toggleTutorial,
    setTutorialType,
    toggleHelp,
    toggleFeedback,
    setHelpQuery
  } = actions;
  
  // Responsive design hooks
  const isMobile = useIsMobile();
  const isTouchDevice = useIsTouchDevice();
  const responsiveState = useResponsiveDesign();
  const { screenSize } = responsiveState;
  
  // Remove duplicate state declarations - now using centralized state from useLightbulbState hook

  const sidebarItems = [
    { id: 'chat', label: 'Multi-Source Chat', icon: MessageSquare },
    { id: 'processor', label: 'Content Processor', icon: Upload },
    { id: 'visual', label: 'Visual Workspace', icon: LayoutDashboard },
    { id: 'thoughts', label: 'Thoughts', icon: Brain },
    { id: 'pages', label: 'Pages', icon: FileText },
    { id: 'projects', label: 'Projects', icon: Folder },
    { id: 'humanizer', label: 'AI Humanizer', icon: Wrench },
    { id: 'search', label: 'Advanced Search', icon: Search },
    { id: 'tags', label: 'Auto-Tagging', icon: Tag },
    { id: 'extension', label: 'Chrome Extension', icon: Grid },
    { id: 'history', label: 'Chat History', icon: Clock },
    { id: 'test', label: 'Processor Test', icon: Bug },
    { id: 'mobile-test', label: 'Mobile Tester', icon: Smartphone },
  ];

  const helpItems = [
    { id: 'help', label: 'Help & Docs', icon: HelpCircle, action: () => handleShowHelp() },
    { id: 'tutorials', label: 'Tutorials', icon: GraduationCap, action: () => handleStartTutorial('basic') },
    { id: 'feedback', label: 'Send Feedback', icon: MessageCircle, action: () => handleShowFeedback() },
  ];

  // Fetch data on component mount
  useEffect(() => {
    // Check authentication status on mount
    checkAuthStatus();
  }, []);
  
  useEffect(() => {
    // Load data only when authenticated
    if (isAuthenticated) {
      fetchUserCount();
      fetchSavedContent();
      fetchSources();
      loadCategories();
      loadPages();
      loadProjects();
      
      // Initialize new services
      initializeIntegrationServices();
    }
  }, [isAuthenticated]);
  
  const checkAuthStatus = async () => {
    try {
      const user = await apiClient.verifyToken();
      if (user) {
        setCurrentUser(user);
        setIsAuthenticated(true);
      } else {
        setShowAuthForm(true);
      }
    } catch (error) {
      // Not authenticated, show auth form
      setShowAuthForm(true);
    }
  };
  
  const handleAuthSuccess = (user: any) => {
    setCurrentUser(user);
    setIsAuthenticated(true);
    setShowAuthForm(false);
  };
  
  const handleLogout = () => {
    apiClient.logout();
    setIsAuthenticated(false);
    setCurrentUser(null);
    setShowAuthForm(true);
    // Clear local state
    setSavedItems([]);
    setContentItems([]);
    setSources([]);
    setProjects([]);
  };

  const initializeIntegrationServices = () => {
    // Performance service is initialized automatically in its constructor
    // Just access it to ensure it's created
    performanceService.generateReport();
    
    // Check if user should see onboarding
    if (onboardingService.shouldShowOnboarding()) {
      if (!showOnboarding) toggleOnboarding();
    }
    
    // Track feedback event instead of non-existent methods
    feedbackService.trackFeedbackEvent('submitted', 'general');
  };

  const fetchUserCount = async () => {
    try {
      // Get user info from auth token verification
      const user = await apiClient.verifyToken();
      // For now, just set to 1 if user exists
      setUserCount(user ? 1 : 0);
    } catch (error) {
      // User not authenticated
      setUserCount(0);
    }
  };

  const fetchSavedContent = async () => {
    try {
      // Get thoughts from API
      const thoughts = await apiClient.getThoughts();
      
      // Transform thoughts to KnowledgeItem format
      const content: KnowledgeItem[] = thoughts.map(thought => ({
        id: thought.id,
        title: thought.title,
        content: thought.content,
        tags: thought.tags || [],
        created_at: new Date(thought.created_at).getTime() / 1000,
        updated_at: new Date(thought.updated_at).getTime() / 1000,
        source_type: 'manual',
        metadata: thought.metadata
      }));
      
      setSavedItems(content);
      setContentItems(content);
      
      // Extract unique tags
      const allTags = new Set<string>();
      content.forEach(item => {
        item.tags?.forEach(tag => allTags.add(tag));
      });
      setTags(Array.from(allTags));
    } catch (error) {
      console.error('Failed to fetch thoughts:', error);
    }
  };

  const fetchSources = async () => {
    try {
      const sourcesData = await apiClient.getSources();
      setSources(sourcesData as any);
    } catch (error) {
      console.error('Failed to fetch sources:', error);
    }
  };

  const loadCategories = async () => {
    try {
      await categorizationService.loadUserPatterns();
      const allCategories = categorizationService.getCategories();
      setCategories(allCategories);
    } catch (error) {
    }
  };

  const loadPages = async () => {
    try {
      // For now, pages can be empty - will implement later
      setPages([]);
    } catch (error) {
      console.error('Failed to load pages:', error);
    }
  };

  const loadProjects = async () => {
    try {
      const projectsData = await apiClient.getProjects();
      setProjects(projectsData as any);
    } catch (error) {
      console.error('Failed to load projects:', error);
    }
  };

  const handleSaveContent = async () => {
    if (inputValue.trim()) {
      setLoading(true);
      try {
        // Generate auto-tags
        const tagResult = await autoTaggingService.analyzeContent(inputValue);
        const autoTags = tagResult.suggestedTags.map((suggestion: TagSuggestion) => suggestion.tag);
        
        const newItem: KnowledgeItem = {
          id: `item_${Date.now()}`,
          title: inputValue.substring(0, 50) + (inputValue.length > 50 ? '...' : ''),
          content: inputValue,
          tags: autoTags,
          created_at: Date.now() / 1000,
          updated_at: Date.now() / 1000,
          source_type: 'manual'
        };
        
        const savedItem = await apiClient.createThought({
          title: newItem.title,
          content: inputValue,
          thought_type: 'general',
          tags: autoTags,
          metadata: {}
        });
        
        setSavedItems([savedItem, ...savedItems]);
        setContentItems([savedItem, ...contentItems]);
        setInputValue('');
        
        // Update tags
        const newTags = new Set(tags);
        autoTags.forEach(tag => newTags.add(tag));
        setTags(Array.from(newTags));
        
        // Show success toast
        setToast({ message: 'Thought saved successfully!', type: 'success' });
        
        fetchUserCount();
      } catch (error) {
        console.error('Failed to save content:', error);
        setToast({ message: 'Failed to save thought. Please try again.', type: 'error' });
      } finally {
        setLoading(false);
      }
    }
  };

  const filteredContent = contentItems.filter(item => {
    if (!searchQuery) return true;
    const query = searchQuery.toLowerCase();
    return item.title.toLowerCase().includes(query) || 
           item.content.toLowerCase().includes(query) ||
           item.tags.some(tag => tag.toLowerCase().includes(query));
  });

  // New integration handlers
  const handleOnboardingComplete = () => {
    onboardingService.completeOnboarding();
    if (showOnboarding) toggleOnboarding();
    feedbackService.trackEvent('onboarding', 'completed', 'main_flow');
  };

  const handleTutorialComplete = () => {
    onboardingService.completeTutorial(tutorialType);
    if (showTutorial) toggleTutorial();
    feedbackService.trackEvent('tutorial', 'completed', tutorialType);
  };

  const handleStartTutorial = (type: typeof tutorialType) => {
    setTutorialType(type);
    if (!showTutorial) toggleTutorial();
    onboardingService.startTutorial(type, 5); // Assuming 5 steps per tutorial
    feedbackService.trackEvent('tutorial', 'started', type);
  };

  const handleShowHelp = (query = '') => {
    setHelpQuery(query);
    if (!showHelp) toggleHelp();
    feedbackService.trackEvent('help', 'opened', query ? 'search' : 'browse');
  };

  const handleShowFeedback = (type?: 'bug' | 'feature' | 'improvement' | 'general') => {
    if (!showFeedback) toggleFeedback();
    feedbackService.trackEvent('feedback', 'opened', type || 'general');
  };

  const renderContent = () => {
    switch (activeSection) {
      case 'chat':
        return <MultiSourceChat />;
      
      case 'processor':
        return <EnhancedContentProcessor />;
      
      case 'visual':
        return <VisualWorkspace items={savedItems} pages={pages} />;
      
      case 'pages':
        return <Pages />;
      
      case 'projects':
        return <Projects savedItems={savedItems} pages={pages} />;
      
      case 'humanizer':
        return <AIHumanizer />;
      
      case 'search':
        return (
          <AdvancedSearch
            isOpen={true}
            onClose={() => setActiveSection('thoughts')}
            items={savedItems}
            pages={pages}
            onResultsChange={setSearchResults}
          />
        );
      
      case 'tags':
        return <AutoTaggingSystem />;
      
      case 'extension':
        return <ChromeExtensionIntegration />;
      
      case 'history':
        return (
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">Chat History</h2>
            <div className="space-y-4">
              {messages.map(message => (
                <Card key={message.id}>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <p className="text-sm">{message.content}</p>
                      <Badge variant="outline" className="text-xs">
                        {new Date(message.timestamp * 1000).toLocaleTimeString()}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        );
      
      case 'test':
        return <ContentProcessorTest />;
      
      case 'mobile-test':
        return <MobileTester />;
      
      default:
        return (
          <div className={cn(
            "overflow-y-auto",
            isMobile ? "p-4" : "p-6"
          )}>
            <div className={cn(
              "mx-auto",
              isMobile ? "max-w-none" : "max-w-4xl"
            )}>
              <div className="mb-6">
                <h2 className={cn(
                  "font-semibold mb-4",
                  isMobile ? "heading-2" : "text-xl"
                )}>
                  Captured Thoughts
                </h2>
                
                {/* Input Area */}
                <Card className={cn(
                  "mb-4",
                  isMobile && "mobile-card"
                )}>
                  <CardContent className={cn(
                    isMobile ? "p-3" : "p-4"
                  )}>
                    <Textarea
                      value={inputValue}
                      onChange={(e) => setInputValue(e.target.value)}
                      placeholder="Capture your thoughts here..."
                      className={cn(
                        "resize-none min-h-[100px]",
                        isMobile && "mobile-form-input"
                      )}
                    />
                    <div className={cn(
                      "flex items-center mt-3",
                      isMobile ? "flex-col gap-3" : "justify-between"
                    )}>
                      <Button
                        variant="outline"
                        onClick={() => isMobile ? setShowMobileCapture(true) : setShowQuickCapture(true)}
                        className={cn(
                          isMobile && "mobile-button w-full"
                        )}
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        {isMobile ? 'Mobile Capture' : 'Quick Capture'}
                      </Button>
                      <Button 
                        onClick={handleSaveContent}
                        disabled={loading || !inputValue.trim()}
                        className={cn(
                          "bg-primary hover:bg-primary/90 text-primary-foreground",
                          isMobile && "mobile-button w-full"
                        )}
                      >
                        {loading ? 'Saving...' : 'Save Thought'}
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Content Items Grid */}
                <div className={cn(
                  "grid gap-4",
                  isMobile ? "grid-cols-1" : "grid-cols-1"
                )}>
                  {filteredContent.map((item) => (
                    <Card 
                      key={item.id} 
                      className={cn(
                        "transition-shadow",
                        isMobile ? "mobile-card touch-hover" : "hover:shadow-md"
                      )}
                    >
                      <CardHeader className={cn(
                        "pb-3",
                        isMobile && "p-3 pb-2"
                      )}>
                        <div className="flex items-start justify-between">
                          <CardTitle className={cn(
                            "font-medium",
                            isMobile ? "text-sm" : "text-base"
                          )}>
                            {item.title}
                          </CardTitle>
                          <Badge variant="outline" className="text-xs">
                            {isMobile 
                              ? new Date(item.created_at * 1000).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
                              : new Date(item.created_at * 1000).toLocaleDateString()
                            }
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className={cn(
                        isMobile && "p-3 pt-0"
                      )}>
                        <p className="text-sm text-muted-foreground mb-3 line-clamp-3">
                          {item.content}
                        </p>
                        <div className="flex items-center justify-between">
                          <div className="flex gap-2 flex-wrap">
                            {item.tags.slice(0, isMobile ? 2 : 4).map((tag, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                #{tag}
                              </Badge>
                            ))}
                            {item.tags.length > (isMobile ? 2 : 4) && (
                              <Badge variant="secondary" className="text-xs">
                                +{item.tags.length - (isMobile ? 2 : 4)}
                              </Badge>
                            )}
                          </div>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            className={cn(
                              isMobile && "touch-target"
                            )}
                          >
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className={cn(
      "h-screen bg-background text-foreground flex flex-col",
      isMobile && "mobile-content",
      responsiveState.isKeyboardOpen && "keyboard-adjust keyboard-open"
    )}>
      {/* Mobile Header */}
      {isMobile ? (
        <header className="bg-background border-b border-border px-4 py-3 safe-area-top">
          <div className="flex items-center justify-between">
            <button
              onClick={() => setShowMobileSidebar(true)}
              className="touch-target flex items-center justify-center"
            >
              <Menu className="h-5 w-5" />
            </button>
            <h1 className="heading-2 truncate mx-4">
              Lightbulb
            </h1>
            <div className="flex items-center gap-2">
              {isAuthenticated && currentUser && (
                <span className="text-xs text-muted-foreground">
                  {currentUser.username}
                </span>
              )}
              <Badge variant="secondary" className="text-xs">
                {userCount > 999 ? `${Math.floor(userCount / 1000)}k` : userCount}
              </Badge>
            </div>
          </div>
        </header>
      ) : (
        /* Desktop Header */
        <header className="bg-background border-b border-border px-6 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold">
              Capture Your Random Thoughts, And Never Lose Them
            </h1>
            <div className="flex items-center gap-4">
              {isAuthenticated && currentUser && (
                <span className="text-sm text-muted-foreground">
                  {currentUser.username}
                </span>
              )}
              <Badge variant="secondary">
                {userCount.toLocaleString()} users
              </Badge>
              {isAuthenticated && (
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={handleLogout}
                >
                  Logout
                </Button>
              )}
            </div>
          </div>
        </header>
      )}

      <div className={cn(
        "flex flex-1 overflow-hidden",
        isMobile && "mobile-stack"
      )}>
        {/* Mobile Sidebar Overlay */}
        {isMobile && showMobileSidebar && (
          <div 
            className="fixed inset-0 bg-black/50 z-40"
            onClick={() => setShowMobileSidebar(false)}
          >
            <aside className="w-80 max-w-[80vw] bg-card h-full flex flex-col safe-area-left">
              <div className="p-4 border-b border-border">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                      <Brain className="h-5 w-5 text-primary-foreground" />
                    </div>
                    <span className="font-semibold text-lg">Lightbulb</span>
                  </div>
                  <button
                    onClick={() => setShowMobileSidebar(false)}
                    className="touch-target"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>
              </div>
              
              <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
                {sidebarItems.map((item) => (
                  <button
                    key={item.id}
                    onClick={() => {
                      setActiveSection(item.id);
                      setShowMobileSidebar(false);
                    }}
                    className={cn(
                      "w-full flex items-center gap-3 px-4 py-3 rounded-lg text-sm transition-colors touch-friendly",
                      activeSection === item.id
                        ? "bg-accent text-accent-foreground"
                        : "text-muted-foreground hover:bg-accent/50 hover:text-foreground"
                    )}
                  >
                    <item.icon className="h-5 w-5" />
                    {item.label}
                  </button>
                ))}
              </nav>
              
              <div className="p-4 border-t border-border safe-area-bottom">
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Users className="h-3 w-3" />
                  <span>{userCount.toLocaleString()} users</span>
                </div>
              </div>
            </aside>
          </div>
        )}

        {/* Desktop Sidebar */}
        {!isMobile && (
          <aside className="w-64 bg-card border-r border-border flex flex-col">
            <div className="p-4">
              <div className="flex items-center gap-2 mb-6">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                  <iconMapping.Memory className="h-5 w-5 text-primary-foreground" />
                </div>
                <span className="font-semibold text-lg">Lightbulb</span>
              </div>
              
              <nav className="space-y-1">
                {sidebarItems.map((item) => (
                  <button
                    key={item.id}
                    onClick={() => setActiveSection(item.id)}
                    className={cn(
                      "w-full flex items-center gap-3 px-3 py-2 rounded-lg text-sm transition-colors",
                      activeSection === item.id
                        ? "bg-accent text-accent-foreground"
                        : "text-muted-foreground hover:bg-accent/50 hover:text-foreground"
                    )}
                  >
                    <item.icon className="h-4 w-4" />
                    {item.label}
                  </button>
                ))}
                
                {/* Help & Support Section */}
                <div className="pt-4 mt-4 border-t border-border">
                  <p className="text-xs font-medium text-muted-foreground mb-2 px-3">Help & Support</p>
                  {helpItems.map((item) => (
                    <button
                      key={item.id}
                      onClick={item.action}
                      className="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-sm transition-colors text-muted-foreground hover:bg-accent/50 hover:text-foreground"
                    >
                      <item.icon className="h-4 w-4" />
                      {item.label}
                    </button>
                  ))}
                </div>
              </nav>
            </div>
            
            <div className="mt-auto p-4 border-t border-border space-y-3">
              {/* AI Provider Status */}
              <AIProviderStatus compact />
              
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <iconMapping.Group className="h-3 w-3" />
                <span>{userCount.toLocaleString()} users</span>
              </div>
            </div>
          </aside>
        )}

        {/* Main Content Area */}
        <main className={cn(
          "flex-1 flex flex-col bg-background",
          isMobile && "mobile-full-width"
        )}>
          {/* Top Bar with Tags/Filters */}
          <div className={cn(
            "border-b border-border",
            isMobile ? "p-3" : "p-4"
          )}>
            <div className={cn(
              "flex items-center gap-3",
              isMobile ? "flex-col space-y-3" : "flex-wrap"
            )}>
              <div className={cn(
                "flex items-center gap-2 bg-muted rounded-lg px-3 py-1.5",
                isMobile && "w-full"
              )}>
                <Search className="h-4 w-4 text-muted-foreground" />
                <Input
                  type="text"
                  placeholder={isMobile ? "Search..." : "Filter pages, projects, tags"}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className={cn(
                    "bg-transparent border-none text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-0 p-0",
                    isMobile && "mobile-form-input"
                  )}
                />
              </div>
              
              <div className={cn(
                "flex gap-2",
                isMobile ? "flex-wrap w-full" : "flex-wrap"
              )}>
                {tags.slice(0, isMobile ? 4 : 8).map((tag) => (
                  <Badge 
                    key={tag} 
                    variant="outline" 
                    className={cn(
                      "hover:bg-accent cursor-pointer",
                      isMobile && "touch-friendly text-xs"
                    )}
                  >
                    #{tag}
                  </Badge>
                ))}
                {tags.length > (isMobile ? 4 : 8) && (
                  <Button variant="ghost" size="sm" className={isMobile ? "touch-target" : ""}>
                    <Plus className="h-3 w-3 mr-1" />
                    {tags.length - (isMobile ? 4 : 8)} more
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Content Grid Area */}
          <div className={cn(
            "flex-1 flex",
            isMobile && "flex-col"
          )}>
            {/* Main Content */}
            <div className="flex-1">
              {renderContent()}
            </div>

            {/* Right Sidebar - Hidden on mobile */}
            {!isMobile && (
              <aside className="w-80 border-l border-border p-4 overflow-y-auto">
                <div className="space-y-6">
                  {/* Stats Card */}
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm font-medium text-muted-foreground">
                        Quick Stats
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Total Thoughts</span>
                        <span className="font-medium">{savedItems.length}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Pages</span>
                        <span className="font-medium">{pages.length}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Projects</span>
                        <span className="font-medium">{projects.length}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Tags Used</span>
                        <span className="font-medium">{tags.length}</span>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Recent Activity */}
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm font-medium text-muted-foreground">
                        Recent Activity
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      {savedItems.slice(0, 3).map((item) => (
                        <div key={item.id} className="flex items-start gap-2 text-sm">
                          <div className="w-1 h-1 bg-primary rounded-full mt-1.5" />
                          <div className="flex-1">
                            <p className="line-clamp-1">{item.title}</p>
                            <p className="text-xs text-muted-foreground">
                              {new Date(item.created_at * 1000).toRelativeTime?.() || new Date(item.created_at * 1000).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>

                  {/* Quick Actions */}
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm font-medium text-muted-foreground">
                        Quick Actions
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <Button 
                        variant="outline" 
                        className="w-full justify-start"
                        onClick={() => setShowQuickCapture(true)}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Quick Capture
                      </Button>
                      <Button 
                        variant="outline" 
                        className="w-full justify-start"
                        onClick={() => setActiveSection('pages')}
                      >
                        <FileText className="h-4 w-4 mr-2" />
                        Create Page
                      </Button>
                      <Button 
                        variant="outline" 
                        className="w-full justify-start"
                        onClick={() => setActiveSection('projects')}
                      >
                        <Folder className="h-4 w-4 mr-2" />
                        New Project
                      </Button>
                      <Button 
                        variant="outline" 
                        className="w-full justify-start"
                        onClick={() => setShowAdvancedSearch(true)}
                      >
                        <Search className="h-4 w-4 mr-2" />
                        Advanced Search
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              </aside>
            )}
          </div>
        </main>
      </div>

      {/* Mobile Bottom Navigation */}
      {isMobile && (
        <nav className="mobile-nav safe-area-bottom">
          <div className="flex">
            {[
              { id: 'thoughts', icon: Brain, label: 'Thoughts' },
              { id: 'chat', icon: MessageSquare, label: 'Chat' },
              { id: 'visual', icon: LayoutDashboard, label: 'Visual' },
              { id: 'search', icon: Search, label: 'Search' },
              { id: 'more', icon: MoreHorizontal, label: 'More' }
            ].map((item) => (
              <button
                key={item.id}
                onClick={() => {
                  if (item.id === 'more') {
                    setShowMobileSidebar(true);
                  } else {
                    setActiveSection(item.id);
                  }
                }}
                className={cn(
                  "mobile-nav-item",
                  activeSection === item.id && "active"
                )}
              >
                <item.icon className="h-5 w-5" />
                <span className="text-xs mt-1">{item.label}</span>
              </button>
            ))}
          </div>
        </nav>
      )}

      {/* Quick Capture Modal */}
      {showQuickCapture && !isMobile && (
        <QuickCapture
          isOpen={showQuickCapture}
          onClose={() => setShowQuickCapture(false)}
          onSave={async (content: string) => {
            setInputValue(content);
            await handleSaveContent();
            setShowQuickCapture(false);
          }}
        />
      )}

      {/* Mobile Capture Modal */}
      {showMobileCapture && isMobile && (
        <MobileCapture
          isOpen={showMobileCapture}
          onClose={() => setShowMobileCapture(false)}
          onSave={async (content: string, media) => {
            setInputValue(content);
            // Handle media if provided
            if (media) {
              // You would integrate this with your content saving logic
            }
            await handleSaveContent();
            setShowMobileCapture(false);
          }}
        />
      )}

      {/* Advanced Search Modal */}
      {showAdvancedSearch && activeSection !== 'search' && (
        <AdvancedSearch
          isOpen={showAdvancedSearch}
          onClose={() => setShowAdvancedSearch(false)}
          items={savedItems}
          pages={pages}
          onResultsChange={setSearchResults}
        />
      )}

      {/* New Integration Components */}
      
      {/* Onboarding Flow */}
      <OnboardingFlow
        isOpen={showOnboarding}
        onComplete={handleOnboardingComplete}
      />

      {/* Interactive Tutorial */}
      {showTutorial && (
        <InteractiveTutorial
          isVisible={showTutorial}
          onClose={() => { if (showTutorial) toggleTutorial(); }}
          onComplete={handleTutorialComplete}
          steps={[
            {
              id: 'welcome',
              title: 'Welcome to Lightbulb',
              description: 'Let\'s take a quick tour of the features',
              action: 'wait' as const,
              timeout: 3000
            }
          ]}
          title="Getting Started Tutorial"
          description="Learn how to use Lightbulb effectively"
        />
      )}

      {/* Help System */}
      <HelpSystem
        isOpen={showHelp}
        onClose={() => { if (showHelp) toggleHelp(); }}
        initialQuery={helpQuery}
      />

      {/* Feedback Widget */}
      <FeedbackWidget
        isOpen={showFeedback}
        onClose={() => { if (showFeedback) toggleFeedback(); }}
      />
      
      {/* Auth Form Modal */}
      {showAuthForm && (
        <AuthForm
          onSuccess={handleAuthSuccess}
          onClose={() => setShowAuthForm(false)}
        />
      )}
      
      {/* Toast Notification */}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </div>
  );
};

// Add toRelativeTime extension for Date
declare global {
  interface Date {
    toRelativeTime(): string;
  }
}

if (!Date.prototype.toRelativeTime) {
  Date.prototype.toRelativeTime = function() {
    const seconds = Math.floor((Date.now() - this.getTime()) / 1000);
    
    let interval = Math.floor(seconds / 31536000);
    if (interval > 1) return interval + ' years ago';
    if (interval === 1) return '1 year ago';
    
    interval = Math.floor(seconds / 2592000);
    if (interval > 1) return interval + ' months ago';
    if (interval === 1) return '1 month ago';
    
    interval = Math.floor(seconds / 86400);
    if (interval > 1) return interval + ' days ago';
    if (interval === 1) return '1 day ago';
    
    interval = Math.floor(seconds / 3600);
    if (interval > 1) return interval + ' hours ago';
    if (interval === 1) return '1 hour ago';
    
    interval = Math.floor(seconds / 60);
    if (interval > 1) return interval + ' minutes ago';
    if (interval === 1) return '1 minute ago';
    
    return 'just now';
  };
}

export default LightbulbComponent;