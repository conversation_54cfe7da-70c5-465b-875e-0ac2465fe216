"""
Document processing service using unstructured library.
Provides document parsing, chunking, and metadata extraction for various formats.
"""

import logging
import os
import tempfile
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from enum import Enum

from pydantic import BaseModel, Field, ConfigDict
from unstructured.partition.auto import partition
from unstructured.partition.pdf import partition_pdf
from unstructured.partition.docx import partition_docx
from unstructured.partition.pptx import partition_pptx
from unstructured.partition.html import partition_html
from unstructured.partition.md import partition_md
from unstructured.partition.csv import partition_csv
from unstructured.partition.xlsx import partition_xlsx
from unstructured.partition.image import partition_image
from unstructured.partition.epub import partition_epub
from unstructured.partition.email import partition_email
from unstructured.partition.text import partition_text
from unstructured.partition.json import partition_json
from unstructured.chunking.basic import chunk_elements
from unstructured.chunking.title import chunk_by_title
from unstructured.staging.base import elements_to_json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ContentType(str, Enum):
    """Supported content types for processing."""
    PDF = "pdf"
    DOCX = "docx"
    PPTX = "pptx"
    HTML = "html"
    MARKDOWN = "md"
    CSV = "csv"
    XLSX = "xlsx"
    IMAGE = "image"
    EPUB = "epub"
    EMAIL = "email"
    TEXT = "text"
    JSON = "json"
    AUTO = "auto"


class DocumentMetadata(BaseModel):
    """Metadata extracted from documents."""
    model_config = ConfigDict(extra="allow")
    
    title: Optional[str] = None
    author: Optional[str] = None
    subject: Optional[str] = None
    keywords: Optional[List[str]] = Field(default_factory=list)
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    language: Optional[str] = None
    word_count: Optional[int] = None
    page_count: Optional[int] = None
    file_size: Optional[int] = None
    mime_type: Optional[str] = None
    encoding: Optional[str] = None
    custom: Dict[str, Any] = Field(default_factory=dict)


class DocumentChunk(BaseModel):
    """Individual chunk of processed document."""
    id: str
    content: str
    chunk_index: int
    start_char: int
    end_char: int
    element_type: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    embeddings: Optional[List[float]] = None


class ProcessingOptions(BaseModel):
    """Options for document processing."""
    content_type: ContentType = ContentType.AUTO
    chunk_size: int = 500
    chunk_overlap: int = 50
    include_metadata: bool = True
    extract_images: bool = False
    extract_tables: bool = True
    ocr_enabled: bool = False
    language: str = "en"
    encoding: Optional[str] = None
    max_file_size_mb: int = 100


class DocumentSource(BaseModel):
    """Input document for processing."""
    source_id: str
    source_type: str  # file, url, text
    file_path: Optional[str] = None
    url: Optional[str] = None
    raw_content: Optional[str] = None
    file_name: Optional[str] = None
    mime_type: Optional[str] = None
    user_id: str
    tags: List[str] = Field(default_factory=list)


class ProcessedDocument(BaseModel):
    """Processed document with chunks and metadata."""
    source_id: str
    user_id: str
    content_type: ContentType
    raw_content: Optional[str] = None
    processed_content: str
    chunks: List[DocumentChunk]
    metadata: DocumentMetadata
    processing_time_ms: int
    error: Optional[str] = None
    tags: List[str] = Field(default_factory=list)


class DocumentProcessor:
    """Main document processing class using unstructured library."""
    
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp(prefix="lightbulb_")
        logger.info(f"Initialized DocumentProcessor with temp dir: {self.temp_dir}")
    
    def process_document(
        self,
        source: DocumentSource,
        options: Optional[ProcessingOptions] = None
    ) -> ProcessedDocument:
        """
        Process a document from various sources.
        
        Args:
            source: Document source information
            options: Processing options
            
        Returns:
            ProcessedDocument with chunks and metadata
        """
        start_time = datetime.now()
        options = options or ProcessingOptions()
        
        try:
            # Determine content type
            content_type = self._detect_content_type(source, options.content_type)
            
            # Parse document based on type
            elements = self._partition_document(source, content_type, options)
            
            # Extract metadata
            metadata = self._extract_metadata(elements, source)
            
            # Create chunks
            chunks = self._create_chunks(elements, options)
            
            # Build processed document
            processed_content = "\n\n".join([chunk.content for chunk in chunks])
            
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            return ProcessedDocument(
                source_id=source.source_id,
                user_id=source.user_id,
                content_type=content_type,
                raw_content=source.raw_content,
                processed_content=processed_content,
                chunks=chunks,
                metadata=metadata,
                processing_time_ms=processing_time,
                tags=source.tags
            )
            
        except Exception as e:
            logger.error(f"Error processing document: {e}")
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            return ProcessedDocument(
                source_id=source.source_id,
                user_id=source.user_id,
                content_type=ContentType.AUTO,
                processed_content="",
                chunks=[],
                metadata=DocumentMetadata(),
                processing_time_ms=processing_time,
                error=str(e),
                tags=source.tags
            )
    
    def _detect_content_type(
        self,
        source: DocumentSource,
        requested_type: ContentType
    ) -> ContentType:
        """Detect content type from source."""
        if requested_type != ContentType.AUTO:
            return requested_type
        
        if source.file_path:
            ext = Path(source.file_path).suffix.lower()
            type_map = {
                ".pdf": ContentType.PDF,
                ".docx": ContentType.DOCX,
                ".pptx": ContentType.PPTX,
                ".html": ContentType.HTML,
                ".htm": ContentType.HTML,
                ".md": ContentType.MARKDOWN,
                ".csv": ContentType.CSV,
                ".xlsx": ContentType.XLSX,
                ".xls": ContentType.XLSX,
                ".png": ContentType.IMAGE,
                ".jpg": ContentType.IMAGE,
                ".jpeg": ContentType.IMAGE,
                ".gif": ContentType.IMAGE,
                ".epub": ContentType.EPUB,
                ".eml": ContentType.EMAIL,
                ".msg": ContentType.EMAIL,
                ".txt": ContentType.TEXT,
                ".json": ContentType.JSON,
            }
            return type_map.get(ext, ContentType.TEXT)
        
        return ContentType.TEXT
    
    def _partition_document(
        self,
        source: DocumentSource,
        content_type: ContentType,
        options: ProcessingOptions
    ) -> List[Any]:
        """Partition document into elements based on type."""
        
        # Map content types to partition functions
        partition_map = {
            ContentType.PDF: partition_pdf,
            ContentType.DOCX: partition_docx,
            ContentType.PPTX: partition_pptx,
            ContentType.HTML: partition_html,
            ContentType.MARKDOWN: partition_md,
            ContentType.CSV: partition_csv,
            ContentType.XLSX: partition_xlsx,
            ContentType.IMAGE: partition_image,
            ContentType.EPUB: partition_epub,
            ContentType.EMAIL: partition_email,
            ContentType.TEXT: partition_text,
            ContentType.JSON: partition_json,
        }
        
        partition_func = partition_map.get(content_type, partition)
        
        # Prepare kwargs based on source type
        kwargs = {}
        if source.file_path:
            kwargs["filename"] = source.file_path
        elif source.raw_content:
            # Save to temp file for processing
            temp_path = Path(self.temp_dir) / f"{source.source_id}.txt"
            temp_path.write_text(source.raw_content, encoding="utf-8")
            kwargs["filename"] = str(temp_path)
        elif source.url:
            kwargs["url"] = source.url
        
        # Add processing options
        if options.ocr_enabled and content_type in [ContentType.PDF, ContentType.IMAGE]:
            kwargs["strategy"] = "hi_res"
            kwargs["ocr_languages"] = [options.language]
        
        if options.extract_tables:
            kwargs["infer_table_structure"] = True
        
        if options.encoding:
            kwargs["encoding"] = options.encoding
        
        # Partition the document
        elements = partition_func(**kwargs)
        
        return elements
    
    def _extract_metadata(
        self,
        elements: List[Any],
        source: DocumentSource
    ) -> DocumentMetadata:
        """Extract metadata from document elements."""
        metadata = DocumentMetadata()
        
        # Extract from elements
        if elements:
            # Get metadata from first element if available
            first_elem = elements[0]
            if hasattr(first_elem, "metadata"):
                elem_meta = first_elem.metadata
                if hasattr(elem_meta, "to_dict"):
                    meta_dict = elem_meta.to_dict()
                    metadata.title = meta_dict.get("filename")
                    metadata.language = meta_dict.get("languages", ["en"])[0] if meta_dict.get("languages") else "en"
                    metadata.page_count = meta_dict.get("page_number")
        
        # Add source information
        if source.file_name:
            metadata.title = metadata.title or source.file_name
        
        if source.file_path and os.path.exists(source.file_path):
            stat = os.stat(source.file_path)
            metadata.file_size = stat.st_size
            metadata.created_at = datetime.fromtimestamp(stat.st_ctime)
            metadata.modified_at = datetime.fromtimestamp(stat.st_mtime)
        
        # Count words
        total_text = " ".join([str(elem) for elem in elements])
        metadata.word_count = len(total_text.split())
        
        return metadata
    
    def _create_chunks(
        self,
        elements: List[Any],
        options: ProcessingOptions
    ) -> List[DocumentChunk]:
        """Create chunks from document elements."""
        chunks = []
        
        # Use unstructured's chunking
        if options.chunk_size > 0:
            chunked_elements = chunk_by_title(
                elements,
                max_characters=options.chunk_size,
                overlap=options.chunk_overlap,
                multipage_sections=True
            )
        else:
            chunked_elements = elements
        
        # Convert to our chunk format
        current_pos = 0
        for idx, elem in enumerate(chunked_elements):
            text = str(elem)
            chunk = DocumentChunk(
                id=f"chunk_{idx}",
                content=text,
                chunk_index=idx,
                start_char=current_pos,
                end_char=current_pos + len(text),
                element_type=elem.__class__.__name__ if hasattr(elem, "__class__") else None,
                metadata={
                    "page_number": getattr(elem.metadata, "page_number", None) if hasattr(elem, "metadata") else None,
                    "section": getattr(elem.metadata, "section", None) if hasattr(elem, "metadata") else None,
                }
            )
            chunks.append(chunk)
            current_pos += len(text) + 2  # Account for paragraph separation
        
        return chunks
    
    def cleanup(self):
        """Clean up temporary files."""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            logger.info(f"Cleaned up temp dir: {self.temp_dir}")


# FastAPI integration (optional)
def create_processing_api():
    """Create FastAPI app for document processing service."""
    from fastapi import FastAPI, File, UploadFile, HTTPException
    from fastapi.responses import JSONResponse
    import aiofiles
    
    app = FastAPI(title="Lightbulb Document Processor")
    processor = DocumentProcessor()
    
    @app.post("/process")
    async def process_document(
        file: UploadFile = File(...),
        user_id: str = "default",
        options: Optional[ProcessingOptions] = None
    ):
        """Process uploaded document."""
        try:
            # Save uploaded file temporarily
            temp_path = Path(processor.temp_dir) / file.filename
            async with aiofiles.open(temp_path, "wb") as f:
                content = await file.read()
                await f.write(content)
            
            # Create source
            source = DocumentSource(
                source_id=f"upload_{datetime.now().timestamp()}",
                source_type="file",
                file_path=str(temp_path),
                file_name=file.filename,
                mime_type=file.content_type,
                user_id=user_id
            )
            
            # Process document
            result = processor.process_document(source, options)
            
            # Clean up temp file
            os.unlink(temp_path)
            
            return JSONResponse(content=result.model_dump(exclude_none=True))
            
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.on_event("shutdown")
    async def shutdown():
        processor.cleanup()
    
    return app


if __name__ == "__main__":
    # Example usage
    processor = DocumentProcessor()
    
    # Test with a text document
    source = DocumentSource(
        source_id="test_001",
        source_type="text",
        raw_content="This is a test document. It contains multiple sentences. We will process it into chunks.",
        user_id="test_user"
    )
    
    result = processor.process_document(source)
    print(f"Processed document with {len(result.chunks)} chunks")
    print(f"Metadata: {result.metadata}")
    
    processor.cleanup()