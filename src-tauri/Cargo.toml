[package]
name = "sanity"
version = "0.1.0"
description = "GUI app and Toolkit for Claude Code"
authors = ["mufeedvh", "123vviekr"]
license = "AGPL-3.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[[bin]]
name = "sanity"
path = "src/main.rs"

[lib]
name = "sanity_lib"
crate-type = ["lib", "cdylib", "staticlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = [ "macos-private-api", "protocol-asset", "tray-icon", "image-png"] }
tauri-plugin-shell = "2"
tauri-plugin-dialog = "2"
tauri-plugin-fs = "2"
tauri-plugin-process = "2"
tauri-plugin-updater = "2"
tauri-plugin-notification = "2"
tauri-plugin-clipboard-manager = "2"
tauri-plugin-global-shortcut = "2"
tauri-plugin-http = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
tokio = { version = "1", features = ["full"] }
rusqlite = { version = "0.32", features = ["bundled"] }
dirs = "5"
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1"
log = "0.4"
env_logger = "0.11"
regex = "1"
glob = "0.3"
base64 = "0.22"
libc = "0.2"
reqwest = { version = "0.12", features = ["json", "native-tls-vendored"] }
sqlx = { version = "0.8", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"] }
futures = "0.3"
async-trait = "0.1"
tempfile = "3"
jsonwebtoken = "9"
argon2 = "0.5"
rand_core = { version = "0.6", features = ["std"] }
which = "7"
sha2 = "0.10"
zstd = "0.13"
uuid = { version = "1.6", features = ["v4", "serde"] }
walkdir = "2"
serde_yaml = "0.9"
pyo3 = { version = "0.20", features = ["auto-initialize", "abi3-py39"] }
pyo3-asyncio = { version = "0.20", features = ["tokio-runtime"] }


[target.'cfg(target_os = "macos")'.dependencies]
tauri = { version = "2", features = ["macos-private-api"] }
window-vibrancy = "0.5"
cocoa = "0.26"
objc = "0.2"

[features]
# This feature is used for production builds or when a dev server is not specified, DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]

[profile.release]
strip = true
opt-level = "z"
lto = true
codegen-units = 1
