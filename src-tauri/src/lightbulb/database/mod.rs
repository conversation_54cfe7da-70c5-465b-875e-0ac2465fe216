use rusqlite::{Connection, Result, params, OptionalExtension};
use chrono::{DateTime, Utc};
use crate::lightbulb::models::*;

pub fn init_database(conn: &Connection) -> Result<()> {
    // Create users table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS users (
            id TEXT PRIMARY KEY,
            email TEXT UNIQUE NOT NULL,
            username TEXT NOT NULL,
            password_hash TEXT NOT NULL,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL
        )",
        [],
    )?;

    // Create thoughts table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS thoughts (
            id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            title TEXT NOT NULL,
            content TEXT NOT NULL,
            thought_type TEXT DEFAULT 'general',
            tags TEXT,
            metadata TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )",
        [],
    )?;

    // Create chat_conversations table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS chat_conversations (
            id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            title TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )",
        [],
    )?;

    // Create chat_messages table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS chat_messages (
            id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            conversation_id TEXT NOT NULL,
            role TEXT NOT NULL,
            content TEXT NOT NULL,
            sources TEXT,
            created_at TEXT NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (conversation_id) REFERENCES chat_conversations(id) ON DELETE CASCADE
        )",
        [],
    )?;

    // Create tags table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS tags (
            id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            name TEXT NOT NULL,
            color TEXT,
            usage_count INTEGER DEFAULT 0,
            created_at TEXT NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            UNIQUE(user_id, name)
        )",
        [],
    )?;

    // Create projects table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS projects (
            id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            name TEXT NOT NULL,
            description TEXT,
            settings TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )",
        [],
    )?;

    // Create pages table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS pages (
            id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            project_id TEXT,
            title TEXT NOT NULL,
            content TEXT NOT NULL,
            page_type TEXT DEFAULT 'document',
            metadata TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL
        )",
        [],
    )?;

    // Create thought_tags junction table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS thought_tags (
            thought_id TEXT NOT NULL,
            tag_id TEXT NOT NULL,
            PRIMARY KEY (thought_id, tag_id),
            FOREIGN KEY (thought_id) REFERENCES thoughts(id) ON DELETE CASCADE,
            FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
        )",
        [],
    )?;

    // Create sources table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS sources (
            id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            name TEXT NOT NULL,
            source_type TEXT NOT NULL,
            file_path TEXT,
            url TEXT,
            content TEXT,
            processed_content TEXT,
            chunks TEXT,
            metadata TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )",
        [],
    )?;

    // Create thought_sources junction table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS thought_sources (
            thought_id TEXT NOT NULL,
            source_id TEXT NOT NULL,
            PRIMARY KEY (thought_id, source_id),
            FOREIGN KEY (thought_id) REFERENCES thoughts(id) ON DELETE CASCADE,
            FOREIGN KEY (source_id) REFERENCES sources(id) ON DELETE CASCADE
        )",
        [],
    )?;

    // Create indexes for better performance
    conn.execute("CREATE INDEX IF NOT EXISTS idx_thoughts_user_id ON thoughts(user_id)", [])?;
    conn.execute("CREATE INDEX IF NOT EXISTS idx_chat_messages_conversation_id ON chat_messages(conversation_id)", [])?;
    conn.execute("CREATE INDEX IF NOT EXISTS idx_pages_project_id ON pages(project_id)", [])?;
    conn.execute("CREATE INDEX IF NOT EXISTS idx_tags_user_id ON tags(user_id)", [])?;
    conn.execute("CREATE INDEX IF NOT EXISTS idx_sources_user_id ON sources(user_id)", [])?;
    conn.execute("CREATE INDEX IF NOT EXISTS idx_thought_sources_thought_id ON thought_sources(thought_id)", [])?;
    conn.execute("CREATE INDEX IF NOT EXISTS idx_thought_sources_source_id ON thought_sources(source_id)", [])?;

    Ok(())
}

// User operations
pub fn create_user(conn: &Connection, user: &User) -> Result<()> {
    conn.execute(
        "INSERT INTO users (id, email, username, password_hash, created_at, updated_at)
         VALUES (?1, ?2, ?3, ?4, ?5, ?6)",
        params![
            user.id,
            user.email,
            user.username,
            user.password_hash,
            user.created_at.to_rfc3339(),
            user.updated_at.to_rfc3339()
        ],
    )?;
    Ok(())
}

pub fn get_user_by_email(conn: &Connection, email: &str) -> Result<Option<User>> {
    let mut stmt = conn.prepare(
        "SELECT id, email, username, password_hash, created_at, updated_at 
         FROM users WHERE email = ?1"
    )?;
    
    let user = stmt.query_row(params![email], |row| {
        Ok(User {
            id: row.get(0)?,
            email: row.get(1)?,
            username: row.get(2)?,
            password_hash: row.get(3)?,
            created_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(4)?)
                .unwrap()
                .with_timezone(&Utc),
            updated_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(5)?)
                .unwrap()
                .with_timezone(&Utc),
        })
    }).optional()?;
    
    Ok(user)
}

pub fn get_user_by_id(conn: &Connection, user_id: &str) -> Result<Option<User>> {
    let mut stmt = conn.prepare(
        "SELECT id, email, username, password_hash, created_at, updated_at 
         FROM users WHERE id = ?1"
    )?;
    
    let user = stmt.query_row(params![user_id], |row| {
        Ok(User {
            id: row.get(0)?,
            email: row.get(1)?,
            username: row.get(2)?,
            password_hash: row.get(3)?,
            created_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(4)?)
                .unwrap()
                .with_timezone(&Utc),
            updated_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(5)?)
                .unwrap()
                .with_timezone(&Utc),
        })
    }).optional()?;
    
    Ok(user)
}

// Thought operations
pub fn create_thought(conn: &Connection, thought: &Thought) -> Result<()> {
    let tags_json = thought.tags.as_ref().map(|t| serde_json::to_string(t).unwrap());
    let metadata_json = thought.metadata.as_ref().map(|m| serde_json::to_string(m).unwrap());
    
    conn.execute(
        "INSERT INTO thoughts (id, user_id, title, content, thought_type, tags, metadata, created_at, updated_at)
         VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9)",
        params![
            thought.id,
            thought.user_id,
            thought.title,
            thought.content,
            thought.thought_type,
            tags_json,
            metadata_json,
            thought.created_at.to_rfc3339(),
            thought.updated_at.to_rfc3339()
        ],
    )?;
    Ok(())
}

pub fn get_user_thoughts(conn: &Connection, user_id: &str) -> Result<Vec<Thought>> {
    let mut stmt = conn.prepare(
        "SELECT id, user_id, title, content, thought_type, tags, metadata, created_at, updated_at 
         FROM thoughts WHERE user_id = ?1 ORDER BY created_at DESC"
    )?;
    
    let thoughts = stmt.query_map(params![user_id], |row| {
        let tags_json: Option<String> = row.get(5)?;
        let metadata_json: Option<String> = row.get(6)?;
        
        Ok(Thought {
            id: row.get(0)?,
            user_id: row.get(1)?,
            title: row.get(2)?,
            content: row.get(3)?,
            thought_type: row.get(4)?,
            tags: tags_json.and_then(|t| serde_json::from_str(&t).ok()),
            metadata: metadata_json.and_then(|m| serde_json::from_str(&m).ok()),
            created_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(7)?)
                .unwrap()
                .with_timezone(&Utc),
            updated_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(8)?)
                .unwrap()
                .with_timezone(&Utc),
        })
    })?
    .collect::<Result<Vec<_>>>()?;
    
    Ok(thoughts)
}

pub fn update_thought(conn: &Connection, thought_id: &str, update: &UpdateThoughtDto) -> Result<()> {
    let mut updates = Vec::new();
    let mut params: Vec<Box<dyn rusqlite::ToSql>> = Vec::new();
    
    if let Some(title) = &update.title {
        updates.push("title = ?");
        params.push(Box::new(title.clone()));
    }
    
    if let Some(content) = &update.content {
        updates.push("content = ?");
        params.push(Box::new(content.clone()));
    }
    
    if let Some(tags) = &update.tags {
        updates.push("tags = ?");
        params.push(Box::new(serde_json::to_string(tags).unwrap()));
    }
    
    if let Some(metadata) = &update.metadata {
        updates.push("metadata = ?");
        params.push(Box::new(serde_json::to_string(metadata).unwrap()));
    }
    
    if updates.is_empty() {
        return Ok(());
    }
    
    updates.push("updated_at = ?");
    params.push(Box::new(Utc::now().to_rfc3339()));
    
    let sql = format!(
        "UPDATE thoughts SET {} WHERE id = ?",
        updates.join(", ")
    );
    params.push(Box::new(thought_id.to_string()));
    
    conn.execute(&sql, rusqlite::params_from_iter(params.iter()))?;
    Ok(())
}

pub fn delete_thought(conn: &Connection, thought_id: &str) -> Result<()> {
    conn.execute("DELETE FROM thoughts WHERE id = ?1", params![thought_id])?;
    Ok(())
}

// Chat operations
pub fn create_conversation(conn: &Connection, conversation: &ChatConversation) -> Result<()> {
    conn.execute(
        "INSERT INTO chat_conversations (id, user_id, title, created_at, updated_at)
         VALUES (?1, ?2, ?3, ?4, ?5)",
        params![
            conversation.id,
            conversation.user_id,
            conversation.title,
            conversation.created_at.to_rfc3339(),
            conversation.updated_at.to_rfc3339()
        ],
    )?;
    Ok(())
}

pub fn create_message(conn: &Connection, message: &ChatMessage) -> Result<()> {
    let sources_json = message.sources.as_ref().map(|s| serde_json::to_string(s).unwrap());
    
    conn.execute(
        "INSERT INTO chat_messages (id, user_id, conversation_id, role, content, sources, created_at)
         VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7)",
        params![
            message.id,
            message.user_id,
            message.conversation_id,
            message.role,
            message.content,
            sources_json,
            message.created_at.to_rfc3339()
        ],
    )?;
    Ok(())
}

pub fn get_conversation_messages(conn: &Connection, conversation_id: &str) -> Result<Vec<ChatMessage>> {
    let mut stmt = conn.prepare(
        "SELECT id, user_id, conversation_id, role, content, sources, created_at 
         FROM chat_messages WHERE conversation_id = ?1 ORDER BY created_at ASC"
    )?;
    
    let messages = stmt.query_map(params![conversation_id], |row| {
        let sources_json: Option<String> = row.get(5)?;
        
        Ok(ChatMessage {
            id: row.get(0)?,
            user_id: row.get(1)?,
            conversation_id: row.get(2)?,
            role: row.get(3)?,
            content: row.get(4)?,
            sources: sources_json.and_then(|s| serde_json::from_str(&s).ok()),
            created_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(6)?)
                .unwrap()
                .with_timezone(&Utc),
        })
    })?
    .collect::<Result<Vec<_>>>()?;
    
    Ok(messages)
}

// Tag operations
pub fn create_tag(conn: &Connection, tag: &Tag) -> Result<()> {
    conn.execute(
        "INSERT INTO tags (id, user_id, name, color, usage_count, created_at)
         VALUES (?1, ?2, ?3, ?4, ?5, ?6)",
        params![
            tag.id,
            tag.user_id,
            tag.name,
            tag.color,
            tag.usage_count,
            tag.created_at.to_rfc3339()
        ],
    )?;
    Ok(())
}

pub fn get_user_tags(conn: &Connection, user_id: &str) -> Result<Vec<Tag>> {
    let mut stmt = conn.prepare(
        "SELECT id, user_id, name, color, usage_count, created_at 
         FROM tags WHERE user_id = ?1 ORDER BY usage_count DESC"
    )?;
    
    let tags = stmt.query_map(params![user_id], |row| {
        Ok(Tag {
            id: row.get(0)?,
            user_id: row.get(1)?,
            name: row.get(2)?,
            color: row.get(3)?,
            usage_count: row.get(4)?,
            created_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(5)?)
                .unwrap()
                .with_timezone(&Utc),
        })
    })?
    .collect::<Result<Vec<_>>>()?;
    
    Ok(tags)
}

// Project operations
pub fn create_project(conn: &Connection, project: &Project) -> Result<()> {
    let settings_json = project.settings.as_ref().map(|s| serde_json::to_string(s).unwrap());
    
    conn.execute(
        "INSERT INTO projects (id, user_id, name, description, settings, created_at, updated_at)
         VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7)",
        params![
            project.id,
            project.user_id,
            project.name,
            project.description,
            settings_json,
            project.created_at.to_rfc3339(),
            project.updated_at.to_rfc3339()
        ],
    )?;
    Ok(())
}

pub fn get_user_projects(conn: &Connection, user_id: &str) -> Result<Vec<Project>> {
    let mut stmt = conn.prepare(
        "SELECT id, user_id, name, description, settings, created_at, updated_at 
         FROM projects WHERE user_id = ?1 ORDER BY updated_at DESC"
    )?;
    
    let projects = stmt.query_map(params![user_id], |row| {
        let settings_json: Option<String> = row.get(4)?;
        
        Ok(Project {
            id: row.get(0)?,
            user_id: row.get(1)?,
            name: row.get(2)?,
            description: row.get(3)?,
            settings: settings_json.and_then(|s| serde_json::from_str(&s).ok()),
            created_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(5)?)
                .unwrap()
                .with_timezone(&Utc),
            updated_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(6)?)
                .unwrap()
                .with_timezone(&Utc),
        })
    })?
    .collect::<Result<Vec<_>>>()?;
    
    Ok(projects)
}

// Page operations
pub fn create_page(conn: &Connection, page: &Page) -> Result<()> {
    let metadata_json = page.metadata.as_ref().map(|m| serde_json::to_string(m).unwrap());
    
    conn.execute(
        "INSERT INTO pages (id, user_id, project_id, title, content, page_type, metadata, created_at, updated_at)
         VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9)",
        params![
            page.id,
            page.user_id,
            page.project_id,
            page.title,
            page.content,
            page.page_type,
            metadata_json,
            page.created_at.to_rfc3339(),
            page.updated_at.to_rfc3339()
        ],
    )?;
    Ok(())
}

pub fn get_project_pages(conn: &Connection, project_id: &str) -> Result<Vec<Page>> {
    let mut stmt = conn.prepare(
        "SELECT id, user_id, project_id, title, content, page_type, metadata, created_at, updated_at 
         FROM pages WHERE project_id = ?1 ORDER BY created_at DESC"
    )?;
    
    let pages = stmt.query_map(params![project_id], |row| {
        let metadata_json: Option<String> = row.get(6)?;
        
        Ok(Page {
            id: row.get(0)?,
            user_id: row.get(1)?,
            project_id: row.get(2)?,
            title: row.get(3)?,
            content: row.get(4)?,
            page_type: row.get(5)?,
            metadata: metadata_json.and_then(|m| serde_json::from_str(&m).ok()),
            created_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(7)?)
                .unwrap()
                .with_timezone(&Utc),
            updated_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(8)?)
                .unwrap()
                .with_timezone(&Utc),
        })
    })?
    .collect::<Result<Vec<_>>>()?;
    
    Ok(pages)
}

// Source operations
pub fn create_source(conn: &Connection, source: &Source) -> Result<()> {
    conn.execute(
        "INSERT INTO sources (id, user_id, name, source_type, file_path, url, content, processed_content, chunks, metadata, created_at, updated_at)
         VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12)",
        params![
            source.id,
            source.user_id,
            source.name,
            source.source_type,
            source.file_path,
            source.url,
            source.content,
            source.processed_content,
            source.chunks.as_ref().map(|c| c.to_string()),
            source.metadata.as_ref().map(|m| m.to_string()),
            source.created_at.to_rfc3339(),
            source.updated_at.to_rfc3339()
        ],
    )?;
    Ok(())
}

pub fn get_sources_by_user(conn: &Connection, user_id: &str) -> Result<Vec<Source>> {
    let mut stmt = conn.prepare(
        "SELECT id, user_id, name, source_type, file_path, url, content, processed_content, chunks, metadata, created_at, updated_at
         FROM sources WHERE user_id = ?1 ORDER BY created_at DESC"
    )?;

    let sources = stmt.query_map(params![user_id], |row| {
        Ok(Source {
            id: row.get(0)?,
            user_id: row.get(1)?,
            name: row.get(2)?,
            source_type: row.get(3)?,
            file_path: row.get(4)?,
            url: row.get(5)?,
            content: row.get(6)?,
            processed_content: row.get(7)?,
            chunks: row.get::<_, Option<String>>(8)?
                .and_then(|s| serde_json::from_str(&s).ok()),
            metadata: row.get::<_, Option<String>>(9)?
                .and_then(|s| serde_json::from_str(&s).ok()),
            created_at: DateTime::parse_from_rfc3339(&row.get::<_, String>(10)?)
                .unwrap()
                .with_timezone(&Utc),
            updated_at: DateTime::parse_from_rfc3339(&row.get::<_, String>(11)?)
                .unwrap()
                .with_timezone(&Utc),
        })
    })?.collect::<Result<Vec<_>>>()?;

    Ok(sources)
}

pub fn get_source_by_id(conn: &Connection, source_id: &str, user_id: &str) -> Result<Option<Source>> {
    let mut stmt = conn.prepare(
        "SELECT id, user_id, name, source_type, file_path, url, content, processed_content, chunks, metadata, created_at, updated_at
         FROM sources WHERE id = ?1 AND user_id = ?2"
    )?;

    let source = stmt.query_row(params![source_id, user_id], |row| {
        Ok(Source {
            id: row.get(0)?,
            user_id: row.get(1)?,
            name: row.get(2)?,
            source_type: row.get(3)?,
            file_path: row.get(4)?,
            url: row.get(5)?,
            content: row.get(6)?,
            processed_content: row.get(7)?,
            chunks: row.get::<_, Option<String>>(8)?
                .and_then(|s| serde_json::from_str(&s).ok()),
            metadata: row.get::<_, Option<String>>(9)?
                .and_then(|s| serde_json::from_str(&s).ok()),
            created_at: DateTime::parse_from_rfc3339(&row.get::<_, String>(10)?)
                .unwrap()
                .with_timezone(&Utc),
            updated_at: DateTime::parse_from_rfc3339(&row.get::<_, String>(11)?)
                .unwrap()
                .with_timezone(&Utc),
        })
    }).optional()?;

    Ok(source)
}

pub fn update_source_processing(
    conn: &Connection,
    source_id: &str,
    processed_content: &str,
    chunks: &serde_json::Value,
    metadata: &serde_json::Value,
) -> Result<()> {
    conn.execute(
        "UPDATE sources SET processed_content = ?1, chunks = ?2, metadata = ?3, updated_at = ?4
         WHERE id = ?5",
        params![
            processed_content,
            chunks.to_string(),
            metadata.to_string(),
            Utc::now().to_rfc3339(),
            source_id
        ],
    )?;
    Ok(())
}

pub fn delete_source(conn: &Connection, source_id: &str, user_id: &str) -> Result<()> {
    conn.execute(
        "DELETE FROM sources WHERE id = ?1 AND user_id = ?2",
        params![source_id, user_id],
    )?;
    Ok(())
}

// Thought-Source relation operations
pub fn link_thought_to_source(conn: &Connection, thought_id: &str, source_id: &str) -> Result<()> {
    conn.execute(
        "INSERT OR IGNORE INTO thought_sources (thought_id, source_id) VALUES (?1, ?2)",
        params![thought_id, source_id],
    )?;
    Ok(())
}

pub fn get_sources_for_thought(conn: &Connection, thought_id: &str) -> Result<Vec<Source>> {
    let mut stmt = conn.prepare(
        "SELECT s.id, s.user_id, s.name, s.source_type, s.file_path, s.url, s.content, 
         s.processed_content, s.chunks, s.metadata, s.created_at, s.updated_at
         FROM sources s
         INNER JOIN thought_sources ts ON s.id = ts.source_id
         WHERE ts.thought_id = ?1"
    )?;

    let sources = stmt.query_map(params![thought_id], |row| {
        Ok(Source {
            id: row.get(0)?,
            user_id: row.get(1)?,
            name: row.get(2)?,
            source_type: row.get(3)?,
            file_path: row.get(4)?,
            url: row.get(5)?,
            content: row.get(6)?,
            processed_content: row.get(7)?,
            chunks: row.get::<_, Option<String>>(8)?
                .and_then(|s| serde_json::from_str(&s).ok()),
            metadata: row.get::<_, Option<String>>(9)?
                .and_then(|s| serde_json::from_str(&s).ok()),
            created_at: DateTime::parse_from_rfc3339(&row.get::<_, String>(10)?)
                .unwrap()
                .with_timezone(&Utc),
            updated_at: DateTime::parse_from_rfc3339(&row.get::<_, String>(11)?)
                .unwrap()
                .with_timezone(&Utc),
        })
    })?.collect::<Result<Vec<_>>>()?;

    Ok(sources)
}