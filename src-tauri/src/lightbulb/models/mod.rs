use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;

// User models
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct User {
    pub id: String,
    pub email: String,
    pub username: String,
    #[serde(skip_serializing)]
    pub password_hash: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateUserDto {
    pub email: String,
    pub username: String,
    pub password: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LoginDto {
    pub email: String,
    pub password: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AuthResponse {
    pub token: String,
    pub refresh_token: String,
    pub user: UserResponse,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserResponse {
    pub id: String,
    pub email: String,
    pub username: String,
    pub created_at: DateTime<Utc>,
}

// Thought models
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Thought {
    pub id: String,
    pub user_id: String,
    pub title: String,
    pub content: String,
    pub thought_type: String,
    pub tags: Option<Vec<String>>,
    pub metadata: Option<serde_json::Value>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateThoughtDto {
    pub title: String,
    pub content: String,
    pub thought_type: Option<String>,
    pub tags: Option<Vec<String>>,
    pub metadata: Option<serde_json::Value>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateThoughtDto {
    pub title: Option<String>,
    pub content: Option<String>,
    pub tags: Option<Vec<String>>,
    pub metadata: Option<serde_json::Value>,
}

// Chat models
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ChatConversation {
    pub id: String,
    pub user_id: String,
    pub title: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ChatMessage {
    pub id: String,
    pub user_id: String,
    pub conversation_id: String,
    pub role: String, // "user" or "assistant"
    pub content: String,
    pub sources: Option<Vec<String>>,
    pub created_at: DateTime<Utc>,
}

// Tag models
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Tag {
    pub id: String,
    pub user_id: String,
    pub name: String,
    pub color: Option<String>,
    pub usage_count: i32,
    pub created_at: DateTime<Utc>,
}

// Project models
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Project {
    pub id: String,
    pub user_id: String,
    pub name: String,
    pub description: Option<String>,
    pub settings: Option<serde_json::Value>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateProjectDto {
    pub name: String,
    pub description: Option<String>,
    pub settings: Option<serde_json::Value>,
}

// Page models
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Page {
    pub id: String,
    pub user_id: String,
    pub project_id: Option<String>,
    pub title: String,
    pub content: String,
    pub page_type: String,
    pub metadata: Option<serde_json::Value>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreatePageDto {
    pub title: String,
    pub content: String,
    pub project_id: Option<String>,
    pub page_type: Option<String>,
    pub metadata: Option<serde_json::Value>,
}

// Source/Document models
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Source {
    pub id: String,
    pub user_id: String,
    pub name: String,
    pub source_type: String, // file, url, text
    pub file_path: Option<String>,
    pub url: Option<String>,
    pub content: Option<String>,
    pub processed_content: Option<String>,
    pub chunks: Option<serde_json::Value>,
    pub metadata: Option<serde_json::Value>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateSourceDto {
    pub name: String,
    pub source_type: String,
    pub file_path: Option<String>,
    pub url: Option<String>,
    pub content: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProcessedSource {
    pub source_id: String,
    pub user_id: String,
    pub content_type: String,
    pub processed_content: String,
    pub chunks: Vec<serde_json::Value>,
    pub metadata: serde_json::Value,
    pub processing_time_ms: i32,
    pub error: Option<String>,
}

// Relation models
#[derive(Debug, Serialize, Deserialize)]
pub struct ThoughtSource {
    pub thought_id: String,
    pub source_id: String,
}