use tauri::State;
use uuid::Uuid;
use chrono::Utc;
use std::path::PathBuf;
use std::fs;
use crate::lightbulb::{
    LightbulbState,
    models::{Source, CreateSourceDto, ProcessedSource},
    database::{create_source, get_sources_by_user, get_source_by_id, delete_source, update_source_processing},
    services::auth::verify_jwt,
    python_bridge::{PythonDocumentProcessor, DocumentSource, ProcessingOptions, init_python},
};

#[derive(serde::Serialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
}

#[tauri::command]
pub async fn lightbulb_upload_source(
    state: State<'_, LightbulbState>,
    token: String,
    name: String,
    source_type: String,
    file_path: Option<String>,
    url: Option<String>,
    content: Option<String>,
) -> Result<ApiResponse<Source>, String> {
    // Verify JWT token
    let user_id = match verify_jwt(&token, &state.jwt_secret) {
        Ok(claims) => claims.sub,
        Err(e) => {
            return Ok(ApiResponse {
                success: false,
                data: None,
                error: Some(format!("Authentication failed: {}", e)),
            });
        }
    };

    let source_id = Uuid::new_v4().to_string();
    let now = Utc::now();

    let source = Source {
        id: source_id.clone(),
        user_id: user_id.clone(),
        name,
        source_type,
        file_path: file_path.clone(),
        url: url.clone(),
        content: content.clone(),
        processed_content: None,
        chunks: None,
        metadata: None,
        created_at: now,
        updated_at: now,
    };

    // Save source to database
    let db = state.db.clone();
    let conn = db.lock().await;
    
    if let Err(e) = create_source(&conn, &source) {
        return Ok(ApiResponse {
            success: false,
            data: None,
            error: Some(format!("Failed to create source: {}", e)),
        });
    }

    // Process the document asynchronously if it's a file
    if file_path.is_some() || content.is_some() {
        let source_clone = source.clone();
        let db_clone = db.clone();
        tokio::spawn(async move {
            if let Err(e) = process_source_document(source_clone, db_clone).await {
                log::error!("Failed to process source document: {}", e);
            }
        });
    }

    Ok(ApiResponse {
        success: true,
        data: Some(source),
        error: None,
    })
}

#[tauri::command]
pub async fn lightbulb_get_sources(
    state: State<'_, LightbulbState>,
    token: String,
) -> Result<ApiResponse<Vec<Source>>, String> {
    // Verify JWT token
    let user_id = match verify_jwt(&token, &state.jwt_secret) {
        Ok(claims) => claims.sub,
        Err(e) => {
            return Ok(ApiResponse {
                success: false,
                data: None,
                error: Some(format!("Authentication failed: {}", e)),
            });
        }
    };

    let db = state.db.clone();
    let conn = db.lock().await;
    
    match get_sources_by_user(&conn, &user_id) {
        Ok(sources) => Ok(ApiResponse {
            success: true,
            data: Some(sources),
            error: None,
        }),
        Err(e) => Ok(ApiResponse {
            success: false,
            data: None,
            error: Some(format!("Failed to get sources: {}", e)),
        }),
    }
}

#[tauri::command]
pub async fn lightbulb_delete_source(
    state: State<'_, LightbulbState>,
    token: String,
    source_id: String,
) -> Result<ApiResponse<()>, String> {
    // Verify JWT token
    let user_id = match verify_jwt(&token, &state.jwt_secret) {
        Ok(claims) => claims.sub,
        Err(e) => {
            return Ok(ApiResponse {
                success: false,
                data: None,
                error: Some(format!("Authentication failed: {}", e)),
            });
        }
    };

    let db = state.db.clone();
    let conn = db.lock().await;
    
    // Get source to check if it has a file path
    if let Ok(Some(source)) = get_source_by_id(&conn, &source_id, &user_id) {
        // Delete file if it exists
        if let Some(file_path) = source.file_path {
            if PathBuf::from(&file_path).exists() {
                let _ = fs::remove_file(&file_path);
            }
        }
    }
    
    match delete_source(&conn, &source_id, &user_id) {
        Ok(_) => Ok(ApiResponse {
            success: true,
            data: Some(()),
            error: None,
        }),
        Err(e) => Ok(ApiResponse {
            success: false,
            data: None,
            error: Some(format!("Failed to delete source: {}", e)),
        }),
    }
}

#[tauri::command]
pub async fn lightbulb_process_source(
    state: State<'_, LightbulbState>,
    token: String,
    source_id: String,
) -> Result<ApiResponse<ProcessedSource>, String> {
    // Verify JWT token
    let user_id = match verify_jwt(&token, &state.jwt_secret) {
        Ok(claims) => claims.sub,
        Err(e) => {
            return Ok(ApiResponse {
                success: false,
                data: None,
                error: Some(format!("Authentication failed: {}", e)),
            });
        }
    };

    let db = state.db.clone();
    
    // Get source from database
    let source = {
        let conn = db.lock().await;
        match get_source_by_id(&conn, &source_id, &user_id) {
            Ok(Some(s)) => s,
            Ok(None) => {
                return Ok(ApiResponse {
                    success: false,
                    data: None,
                    error: Some("Source not found".to_string()),
                });
            }
            Err(e) => {
                return Ok(ApiResponse {
                    success: false,
                    data: None,
                    error: Some(format!("Failed to get source: {}", e)),
                });
            }
        }
    };

    // Process the document
    match process_source_document(source, db).await {
        Ok(processed) => Ok(ApiResponse {
            success: true,
            data: Some(processed),
            error: None,
        }),
        Err(e) => Ok(ApiResponse {
            success: false,
            data: None,
            error: Some(format!("Failed to process source: {}", e)),
        }),
    }
}

async fn process_source_document(
    source: Source,
    db: std::sync::Arc<tokio::sync::Mutex<rusqlite::Connection>>,
) -> Result<ProcessedSource, anyhow::Error> {
    // Initialize Python if not already done
    init_python()?;
    
    // Create Python processor
    let processor = PythonDocumentProcessor::new()?;
    
    // Prepare document source
    let doc_source = DocumentSource {
        source_id: source.id.clone(),
        source_type: source.source_type.clone(),
        file_path: source.file_path.clone(),
        url: source.url.clone(),
        raw_content: source.content.clone(),
        file_name: Some(source.name.clone()),
        mime_type: None,
        user_id: source.user_id.clone(),
        tags: vec![],
    };
    
    // Process document with Python
    let processed_doc = processor.process_document(doc_source, None).await?;
    
    // Convert chunks and metadata to JSON
    let chunks_json = serde_json::to_value(&processed_doc.chunks)?;
    let metadata_json = serde_json::to_value(&processed_doc.metadata)?;
    
    // Update source in database with processed content
    let conn = db.lock().await;
    update_source_processing(
        &conn,
        &source.id,
        &processed_doc.processed_content,
        &chunks_json,
        &metadata_json,
    )?;
    
    // Clean up Python processor
    processor.cleanup()?;
    
    Ok(ProcessedSource {
        source_id: source.id,
        user_id: source.user_id,
        content_type: processed_doc.content_type,
        processed_content: processed_doc.processed_content,
        chunks: processed_doc.chunks.into_iter().map(|c| serde_json::to_value(c).unwrap()).collect(),
        metadata: metadata_json,
        processing_time_ms: processed_doc.processing_time_ms,
        error: processed_doc.error,
    })
}

#[tauri::command]
pub async fn lightbulb_search_sources(
    state: State<'_, LightbulbState>,
    token: String,
    query: String,
) -> Result<ApiResponse<Vec<Source>>, String> {
    // Verify JWT token
    let user_id = match verify_jwt(&token, &state.jwt_secret) {
        Ok(claims) => claims.sub,
        Err(e) => {
            return Ok(ApiResponse {
                success: false,
                data: None,
                error: Some(format!("Authentication failed: {}", e)),
            });
        }
    };

    let db = state.db.clone();
    let conn = db.lock().await;
    
    // Get all sources for user
    let sources = match get_sources_by_user(&conn, &user_id) {
        Ok(s) => s,
        Err(e) => {
            return Ok(ApiResponse {
                success: false,
                data: None,
                error: Some(format!("Failed to get sources: {}", e)),
            });
        }
    };
    
    // Filter sources based on query
    let query_lower = query.to_lowercase();
    let filtered: Vec<Source> = sources
        .into_iter()
        .filter(|s| {
            s.name.to_lowercase().contains(&query_lower) ||
            s.content.as_ref().map_or(false, |c| c.to_lowercase().contains(&query_lower)) ||
            s.processed_content.as_ref().map_or(false, |c| c.to_lowercase().contains(&query_lower))
        })
        .collect();
    
    Ok(ApiResponse {
        success: true,
        data: Some(filtered),
        error: None,
    })
}