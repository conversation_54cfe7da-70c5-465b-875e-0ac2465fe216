use pyo3::prelude::*;
use pyo3::types::{PyDict, PyList};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use anyhow::Result;
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentMetadata {
    pub title: Option<String>,
    pub author: Option<String>,
    pub subject: Option<String>,
    pub keywords: Vec<String>,
    pub created_at: Option<DateTime<Utc>>,
    pub modified_at: Option<DateTime<Utc>>,
    pub language: Option<String>,
    pub word_count: Option<i32>,
    pub page_count: Option<i32>,
    pub file_size: Option<i64>,
    pub mime_type: Option<String>,
    pub encoding: Option<String>,
    pub custom: serde_json::Value,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentChunk {
    pub id: String,
    pub content: String,
    pub chunk_index: i32,
    pub start_char: i32,
    pub end_char: i32,
    pub element_type: Option<String>,
    pub metadata: serde_json::Value,
    pub embeddings: Option<Vec<f32>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessedDocument {
    pub source_id: String,
    pub user_id: String,
    pub content_type: String,
    pub raw_content: Option<String>,
    pub processed_content: String,
    pub chunks: Vec<DocumentChunk>,
    pub metadata: DocumentMetadata,
    pub processing_time_ms: i32,
    pub error: Option<String>,
    pub tags: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentSource {
    pub source_id: String,
    pub source_type: String,
    pub file_path: Option<String>,
    pub url: Option<String>,
    pub raw_content: Option<String>,
    pub file_name: Option<String>,
    pub mime_type: Option<String>,
    pub user_id: String,
    pub tags: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessingOptions {
    pub content_type: String,
    pub chunk_size: i32,
    pub chunk_overlap: i32,
    pub include_metadata: bool,
    pub extract_images: bool,
    pub extract_tables: bool,
    pub ocr_enabled: bool,
    pub language: String,
    pub encoding: Option<String>,
    pub max_file_size_mb: i32,
}

impl Default for ProcessingOptions {
    fn default() -> Self {
        Self {
            content_type: "auto".to_string(),
            chunk_size: 500,
            chunk_overlap: 50,
            include_metadata: true,
            extract_images: false,
            extract_tables: true,
            ocr_enabled: false,
            language: "en".to_string(),
            encoding: None,
            max_file_size_mb: 100,
        }
    }
}

pub struct PythonDocumentProcessor {
    py_module: Py<PyModule>,
}

impl PythonDocumentProcessor {
    pub fn new() -> Result<Self> {
        Python::with_gil(|py| {
            // Add the Python directory to sys.path
            let sys = py.import("sys")?;
            let path: &PyList = sys.getattr("path")?.extract()?;
            let python_dir = PathBuf::from(env!("CARGO_MANIFEST_DIR")).join("python");
            path.append(python_dir.to_str().unwrap())?;

            // Import the document processor module
            let module = py.import("document_processor")?;
            
            Ok(Self {
                py_module: module.into(),
            })
        })
    }

    pub async fn process_document(
        &self,
        source: DocumentSource,
        options: Option<ProcessingOptions>,
    ) -> Result<ProcessedDocument> {
        let options = options.unwrap_or_default();
        
        Python::with_gil(|py| {
            let module = self.py_module.as_ref(py);
            
            // Create processor instance
            let processor_class = module.getattr("DocumentProcessor")?;
            let processor = processor_class.call0()?;
            
            // Create DocumentSource object
            let source_class = module.getattr("DocumentSource")?;
            let source_dict = PyDict::new(py);
            source_dict.set_item("source_id", &source.source_id)?;
            source_dict.set_item("source_type", &source.source_type)?;
            source_dict.set_item("file_path", &source.file_path)?;
            source_dict.set_item("url", &source.url)?;
            source_dict.set_item("raw_content", &source.raw_content)?;
            source_dict.set_item("file_name", &source.file_name)?;
            source_dict.set_item("mime_type", &source.mime_type)?;
            source_dict.set_item("user_id", &source.user_id)?;
            source_dict.set_item("tags", &source.tags)?;
            let py_source = source_class.call((), Some(source_dict))?;
            
            // Create ProcessingOptions object
            let options_class = module.getattr("ProcessingOptions")?;
            let options_dict = PyDict::new(py);
            options_dict.set_item("content_type", &options.content_type)?;
            options_dict.set_item("chunk_size", options.chunk_size)?;
            options_dict.set_item("chunk_overlap", options.chunk_overlap)?;
            options_dict.set_item("include_metadata", options.include_metadata)?;
            options_dict.set_item("extract_images", options.extract_images)?;
            options_dict.set_item("extract_tables", options.extract_tables)?;
            options_dict.set_item("ocr_enabled", options.ocr_enabled)?;
            options_dict.set_item("language", &options.language)?;
            options_dict.set_item("encoding", &options.encoding)?;
            options_dict.set_item("max_file_size_mb", options.max_file_size_mb)?;
            let py_options = options_class.call((), Some(options_dict))?;
            
            // Process document
            let result = processor.call_method1("process_document", (py_source, py_options))?;
            
            // Convert result to Rust struct
            let result_dict = result.call_method0("model_dump")?;
            let json_str: String = py.import("json")?
                .call_method1("dumps", (result_dict,))?
                .extract()?;
            
            let processed_doc: ProcessedDocument = serde_json::from_str(&json_str)?;
            
            Ok(processed_doc)
        })
    }
    
    pub fn cleanup(&self) -> Result<()> {
        Python::with_gil(|py| {
            let module = self.py_module.as_ref(py);
            if let Ok(processor_class) = module.getattr("DocumentProcessor") {
                if let Ok(processor) = processor_class.call0() {
                    let _ = processor.call_method0("cleanup");
                }
            }
            Ok(())
        })
    }
}

// Initialize Python interpreter on module load
pub fn init_python() -> Result<()> {
    pyo3::prepare_freethreaded_python();
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_process_text_document() {
        init_python().unwrap();
        
        let processor = PythonDocumentProcessor::new().unwrap();
        
        let source = DocumentSource {
            source_id: "test_001".to_string(),
            source_type: "text".to_string(),
            file_path: None,
            url: None,
            raw_content: Some("This is a test document.".to_string()),
            file_name: None,
            mime_type: None,
            user_id: "test_user".to_string(),
            tags: vec![],
        };
        
        let result = processor.process_document(source, None).await.unwrap();
        
        assert!(!result.chunks.is_empty());
        assert_eq!(result.error, None);
        
        processor.cleanup().unwrap();
    }
}