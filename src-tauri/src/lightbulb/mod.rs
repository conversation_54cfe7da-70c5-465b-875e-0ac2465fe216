pub mod models;
pub mod commands;
pub mod services;
pub mod database;
pub mod utils;
pub mod python_bridge;

use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::Mutex;

// Lightbulb application state
#[derive(Clone)]
pub struct LightbulbState {
    pub db: Arc<Mutex<rusqlite::Connection>>,
    pub jwt_secret: String,
}

impl LightbulbState {
    pub fn new(db: Arc<Mutex<rusqlite::Connection>>) -> Self {
        Self {
            db,
            jwt_secret: std::env::var("JWT_SECRET")
                .unwrap_or_else(|_| "your-secret-key-change-in-production".to_string()),
        }
    }
}

// Common response types
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
        }
    }

    pub fn error(message: String) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(message),
        }
    }
}