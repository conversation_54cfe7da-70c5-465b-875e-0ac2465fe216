use serde::{Deserialize, Serialize};
use sqlx::sqlite::{SqlitePool, SqlitePoolOptions, SqliteRow};
use sqlx::{Column, FromRow, Row, TypeInfo};
use std::collections::HashMap;
use std::path::PathBuf;
use tauri::State;
use tokio::sync::RwLock;

// Database configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub database_url: String,
    pub max_connections: u32,
    pub min_connections: u32,
    pub connection_timeout: u64,
    pub idle_timeout: u64,
    pub enable_wal_mode: bool,
    pub enable_foreign_keys: bool,
    pub auto_vacuum: bool,
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            database_url: "sqlite://app.db".to_string(),
            max_connections: 10,
            min_connections: 1,
            connection_timeout: 30,
            idle_timeout: 600,
            enable_wal_mode: true,
            enable_foreign_keys: true,
            auto_vacuum: true,
        }
    }
}

// Migration information
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Migration {
    pub version: i64,
    pub name: String,
    pub sql: String,
    pub applied_at: Option<chrono::DateTime<chrono::Utc>>,
}

// Query result wrapper
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryResult {
    pub rows_affected: u64,
    pub last_insert_id: Option<i64>,
    pub execution_time_ms: u64,
}

// Database statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseStats {
    pub total_connections: u32,
    pub active_connections: u32,
    pub idle_connections: u32,
    pub database_size_bytes: u64,
    pub table_count: u32,
    pub total_queries: u64,
    pub average_query_time_ms: f64,
}

// Table schema information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TableSchema {
    pub name: String,
    pub columns: Vec<ColumnInfo>,
    pub indexes: Vec<IndexInfo>,
    pub row_count: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ColumnInfo {
    pub name: String,
    pub data_type: String,
    pub nullable: bool,
    pub default_value: Option<String>,
    pub primary_key: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IndexInfo {
    pub name: String,
    pub columns: Vec<String>,
    pub unique: bool,
}

// Backup configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BackupConfig {
    pub backup_path: PathBuf,
    pub auto_backup_enabled: bool,
    pub backup_interval_hours: u64,
    pub max_backup_files: u32,
    pub compress_backups: bool,
}

impl Default for BackupConfig {
    fn default() -> Self {
        Self {
            backup_path: PathBuf::from("./backups"),
            auto_backup_enabled: true,
            backup_interval_hours: 24,
            max_backup_files: 7,
            compress_backups: true,
        }
    }
}

// Main database manager
#[derive(Debug, Default)]
pub struct DatabaseManager {
    pool: Option<SqlitePool>,
    config: DatabaseConfig,
    backup_config: BackupConfig,
    query_count: u64,
    total_query_time_ms: u64,
    migrations: Vec<Migration>,
}

impl DatabaseManager {
    pub fn new(config: DatabaseConfig, backup_config: BackupConfig) -> Self {
        Self {
            pool: None,
            config,
            backup_config,
            query_count: 0,
            total_query_time_ms: 0,
            migrations: Self::default_migrations(),
        }
    }

    pub async fn initialize(&mut self) -> Result<(), String> {
        // Create database directory if it doesn't exist
        if let Ok(path) = std::path::Path::new(&self.config.database_url)
            .strip_prefix("sqlite://")
        {
            if let Some(parent) = path.parent()
             {
                 tokio::fs::create_dir_all(parent)
                     .await
                     .map_err(|e| format!("Failed to create database directory: {}", e))?;
             }
         }

        // Create connection pool
        let pool = SqlitePoolOptions::new()
            .max_connections(self.config.max_connections)
            .min_connections(self.config.min_connections)
            .acquire_timeout(std::time::Duration::from_secs(self.config.connection_timeout))
            .idle_timeout(std::time::Duration::from_secs(self.config.idle_timeout))
            .connect(&self.config.database_url)
            .await
            .map_err(|e| format!("Failed to create database pool: {}", e))?;

        // Configure SQLite settings
        if self.config.enable_wal_mode {
            sqlx::query("PRAGMA journal_mode = WAL")
                .execute(&pool)
                .await
                .map_err(|e| format!("Failed to enable WAL mode: {}", e))?;
        }

        if self.config.enable_foreign_keys {
            sqlx::query("PRAGMA foreign_keys = ON")
                .execute(&pool)
                .await
                .map_err(|e| format!("Failed to enable foreign keys: {}", e))?;
        }

        if self.config.auto_vacuum {
            sqlx::query("PRAGMA auto_vacuum = INCREMENTAL")
                .execute(&pool)
                .await
                .map_err(|e| format!("Failed to enable auto vacuum: {}", e))?;
        }

        self.pool = Some(pool);
        self.run_migrations().await?;
        Ok(())
    }

    pub async fn execute_query(
        &mut self,
        query: &str,
        params: Option<HashMap<String, serde_json::Value>>,
    ) -> Result<QueryResult, String> {
        let pool = self.pool.as_ref().ok_or("Database not initialized")?;
        let start_time = std::time::Instant::now();

        let mut query_builder = sqlx::query(query);
        
        if let Some(params) = params {
            for (_key, value) in params {
                match value {
                    serde_json::Value::String(s) => {
                        query_builder = query_builder.bind(s);
                    }
                    serde_json::Value::Number(n) => {
                        if let Some(i) = n.as_i64() {
                            query_builder = query_builder.bind(i);
                        } else if let Some(f) = n.as_f64() {
                            query_builder = query_builder.bind(f);
                        }
                    }
                    serde_json::Value::Bool(b) => {
                        query_builder = query_builder.bind(b);
                    }
                    serde_json::Value::Null => {
                        query_builder = query_builder.bind(None::<String>);
                    }
                    _ => {
                        query_builder = query_builder.bind(value.to_string());
                    }
                }
            }
        }

        let result = query_builder
            .execute(pool)
            .await
            .map_err(|e| format!("Query execution failed: {}", e))?;

        let execution_time = start_time.elapsed().as_millis() as u64;
        self.query_count += 1;
        self.total_query_time_ms += execution_time;

        Ok(QueryResult {
            rows_affected: result.rows_affected(),
            last_insert_id: result.last_insert_rowid().into(),
            execution_time_ms: execution_time,
        })
    }

    pub async fn fetch_rows(
        &mut self,
        query: &str,
        params: Option<HashMap<String, serde_json::Value>>,
    ) -> Result<Vec<HashMap<String, serde_json::Value>>, String> {
        let pool = self.pool.as_ref().ok_or("Database not initialized")?;
        let start_time = std::time::Instant::now();

        let mut query_builder = sqlx::query(query);
        
        if let Some(params) = params {
            for (_key, value) in params {
                match value {
                    serde_json::Value::String(s) => {
                        query_builder = query_builder.bind(s);
                    }
                    serde_json::Value::Number(n) => {
                        if let Some(i) = n.as_i64() {
                            query_builder = query_builder.bind(i);
                        } else if let Some(f) = n.as_f64() {
                            query_builder = query_builder.bind(f);
                        }
                    }
                    serde_json::Value::Bool(b) => {
                        query_builder = query_builder.bind(b);
                    }
                    serde_json::Value::Null => {
                        query_builder = query_builder.bind(None::<String>);
                    }
                    _ => {
                        query_builder = query_builder.bind(value.to_string());
                    }
                }
            }
        }

        let rows = query_builder
            .fetch_all(pool)
            .await
            .map_err(|e| format!("Query fetch failed: {}", e))?;

        let execution_time = start_time.elapsed().as_millis() as u64;
        self.query_count += 1;
        self.total_query_time_ms += execution_time;

        let mut result = Vec::new();
        for row in rows {
            let mut row_map = HashMap::new();
            for (i, column) in row.columns().iter().enumerate() {
                let column_name = column.name().to_string();
                let value: serde_json::Value = match column.type_info().name() {
                    "TEXT" => {
                        if let Ok(s) = row.try_get::<String, _>(i) {
                            serde_json::Value::String(s)
                        } else {
                            serde_json::Value::Null
                        }
                    }
                    "INTEGER" => {
                        if let Ok(i) = row.try_get::<i64, _>(i) {
                            serde_json::Value::Number(serde_json::Number::from(i))
                        } else {
                            serde_json::Value::Null
                        }
                    }
                    "REAL" => {
                        if let Ok(f) = row.try_get::<f64, _>(i) {
                            serde_json::Value::Number(
                                serde_json::Number::from_f64(f).unwrap_or(serde_json::Number::from(0))
                            )
                        } else {
                            serde_json::Value::Null
                        }
                    }
                    "BOOLEAN" => {
                        if let Ok(b) = row.try_get::<bool, _>(i) {
                            serde_json::Value::Bool(b)
                        } else {
                            serde_json::Value::Null
                        }
                    }
                    _ => serde_json::Value::Null,
                };
                row_map.insert(column_name, value);
            }
            result.push(row_map);
        }

        Ok(result)
    }

    pub async fn get_database_stats(&self) -> Result<DatabaseStats, String> {
        let pool = self.pool.as_ref().ok_or("Database not initialized")?;

        // Get database size
        let size_result = sqlx::query("PRAGMA page_count")
            .fetch_one(pool)
            .await
            .map_err(|e| format!("Failed to get page count: {}", e))?;
        let page_count: i64 = size_result.get(0);

        let page_size_result = sqlx::query("PRAGMA page_size")
            .fetch_one(pool)
            .await
            .map_err(|e| format!("Failed to get page size: {}", e))?;
        let page_size: i64 = page_size_result.get(0);

        let database_size_bytes = (page_count * page_size) as u64;

        // Get table count
        let table_count_result = sqlx::query(
            "SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
        )
        .fetch_one(pool)
        .await
        .map_err(|e| format!("Failed to get table count: {}", e))?;
        let table_count: i64 = table_count_result.get(0);

        let average_query_time = if self.query_count > 0 {
            self.total_query_time_ms as f64 / self.query_count as f64
        } else {
            0.0
        };

        Ok(DatabaseStats {
            total_connections: self.config.max_connections,
            active_connections: pool.size(),
            idle_connections: pool.size() - pool.num_idle() as u32,
            database_size_bytes,
            table_count: table_count as u32,
            total_queries: self.query_count,
            average_query_time_ms: average_query_time,
        })
    }

    pub async fn get_table_schemas(&self) -> Result<Vec<TableSchema>, String> {
        let pool = self.pool.as_ref().ok_or("Database not initialized")?;

        // Get all table names
        let tables = sqlx::query(
            "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
        )
        .fetch_all(pool)
        .await
        .map_err(|e| format!("Failed to get table names: {}", e))?;

        let mut schemas = Vec::new();
        for table_row in tables {
            let table_name: String = table_row.get(0);

            // Get column information
            let columns_query = format!("PRAGMA table_info({})", table_name);
            let column_rows = sqlx::query(&columns_query)
                .fetch_all(pool)
                .await
                .map_err(|e| format!("Failed to get column info for {}: {}", table_name, e))?;

            let mut columns = Vec::new();
            for col_row in column_rows {
                columns.push(ColumnInfo {
                    name: col_row.get::<String, _>(1),
                    data_type: col_row.get::<String, _>(2),
                    nullable: col_row.get::<i64, _>(3) == 0,
                    default_value: col_row.get::<Option<String>, _>(4),
                    primary_key: col_row.get::<i64, _>(5) == 1,
                });
            }

            // Get index information
            let indexes_query = format!("PRAGMA index_list({})", table_name);
            let index_rows = sqlx::query(&indexes_query)
                .fetch_all(pool)
                .await
                .map_err(|e| format!("Failed to get index info for {}: {}", table_name, e))?;

            let mut indexes = Vec::new();
            for idx_row in index_rows {
                let index_name: String = idx_row.get(1);
                let unique: bool = idx_row.get::<i64, _>(2) == 1;

                let index_info_query = format!("PRAGMA index_info({})", index_name);
                let index_info_rows = sqlx::query(&index_info_query)
                    .fetch_all(pool)
                    .await
                    .map_err(|e| format!("Failed to get index column info: {}", e))?;

                let mut index_columns = Vec::new();
                for info_row in index_info_rows {
                    index_columns.push(info_row.get::<String, _>(2));
                }

                indexes.push(IndexInfo {
                    name: index_name,
                    columns: index_columns,
                    unique,
                });
            }

            // Get row count
            let count_query = format!("SELECT COUNT(*) FROM {}", table_name);
            let count_result = sqlx::query(&count_query)
                .fetch_one(pool)
                .await
                .map_err(|e| format!("Failed to get row count for {}: {}", table_name, e))?;
            let row_count: i64 = count_result.get(0);

            schemas.push(TableSchema {
                name: table_name,
                columns,
                indexes,
                row_count: row_count as u64,
            });
        }

        Ok(schemas)
    }

    pub async fn create_backup(&self, backup_name: Option<String>) -> Result<PathBuf, String> {
        let pool = self.pool.as_ref().ok_or("Database not initialized")?;
        
        // Create backup directory
        tokio::fs::create_dir_all(&self.backup_config.backup_path)
            .await
            .map_err(|e| format!("Failed to create backup directory: {}", e))?;

        let backup_name = backup_name.unwrap_or_else(|| {
            chrono::Utc::now().format("%Y%m%d_%H%M%S").to_string()
        });

        let backup_file = self.backup_config.backup_path.join(format!("{}.db", backup_name));

        // Use SQLite VACUUM INTO for backup
        let backup_query = format!("VACUUM INTO '{}'", backup_file.display());
        sqlx::query(&backup_query)
            .execute(pool)
            .await
            .map_err(|e| format!("Backup failed: {}", e))?;

        // Compress if enabled
        if self.backup_config.compress_backups {
            // Implementation for compression would go here
            // For now, we'll just return the uncompressed path
        }

        // Clean up old backups
        self.cleanup_old_backups().await?;

        Ok(backup_file)
    }

    async fn cleanup_old_backups(&self) -> Result<(), String> {
        let mut entries = tokio::fs::read_dir(&self.backup_config.backup_path)
            .await
            .map_err(|e| format!("Failed to read backup directory: {}", e))?;

        let mut backup_files = Vec::new();
        while let Some(entry) = entries.next_entry().await.map_err(|e| format!("Failed to read directory entry: {}", e))? {
            if let Some(ext) = entry.path().extension() {
                if ext == "db" {
                    if let Ok(metadata) = entry.metadata().await {
                        backup_files.push((entry.path(), metadata.modified().unwrap_or(std::time::SystemTime::UNIX_EPOCH)));
                    }
                }
            }
        }

        // Sort by modification time (newest first)
        backup_files.sort_by(|a, b| b.1.cmp(&a.1));

        // Remove excess backups
        if backup_files.len() > self.backup_config.max_backup_files as usize {
            for (path, _) in backup_files.iter().skip(self.backup_config.max_backup_files as usize) {
                tokio::fs::remove_file(path)
                    .await
                    .map_err(|e| format!("Failed to remove old backup: {}", e))?;
            }
        }

        Ok(())
    }

    async fn run_migrations(&mut self) -> Result<(), String> {
        let pool = self.pool.as_ref().ok_or("Database not initialized")?;

        // Create migrations table if it doesn't exist
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS _migrations (
                version INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            "#
        )
        .execute(pool)
        .await
        .map_err(|e| format!("Failed to create migrations table: {}", e))?;

        // Get applied migrations
        let applied_migrations = sqlx::query("SELECT version FROM _migrations ORDER BY version")
            .fetch_all(pool)
            .await
            .map_err(|e| format!("Failed to get applied migrations: {}", e))?;

        let applied_versions: Vec<i64> = applied_migrations
            .iter()
            .map(|row| row.get::<i64, _>(0))
            .collect();

        // Run pending migrations
        for migration in &self.migrations {
            if !applied_versions.contains(&migration.version) {
                println!("Running migration {}: {}", migration.version, migration.name);
                
                sqlx::query(&migration.sql)
                    .execute(pool)
                    .await
                    .map_err(|e| format!("Migration {} failed: {}", migration.version, e))?;

                sqlx::query("INSERT INTO _migrations (version, name) VALUES (?, ?)")
                    .bind(migration.version)
                    .bind(&migration.name)
                    .execute(pool)
                    .await
                    .map_err(|e| format!("Failed to record migration: {}", e))?;
            }
        }

        Ok(())
    }

    fn default_migrations() -> Vec<Migration> {
        vec![
            Migration {
                version: 1,
                name: "Create users table".to_string(),
                sql: r#"
                    CREATE TABLE IF NOT EXISTS users (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        username TEXT UNIQUE NOT NULL,
                        email TEXT UNIQUE NOT NULL,
                        password_hash TEXT NOT NULL,
                        role TEXT NOT NULL DEFAULT 'user',
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                "#.to_string(),
                applied_at: None,
            },
            Migration {
                version: 2,
                name: "Create sessions table".to_string(),
                sql: r#"
                    CREATE TABLE IF NOT EXISTS sessions (
                        id TEXT PRIMARY KEY,
                        user_id INTEGER NOT NULL,
                        expires_at DATETIME NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
                    )
                "#.to_string(),
                applied_at: None,
            },
            Migration {
                version: 3,
                name: "Create app_state table".to_string(),
                sql: r#"
                    CREATE TABLE IF NOT EXISTS app_state (
                        key TEXT PRIMARY KEY,
                        value TEXT NOT NULL,
                        expires_at DATETIME,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                "#.to_string(),
                applied_at: None,
            },
            Migration {
                version: 4,
                name: "Create cache table".to_string(),
                sql: r#"
                    CREATE TABLE IF NOT EXISTS cache (
                        key TEXT PRIMARY KEY,
                        value TEXT NOT NULL,
                        expires_at DATETIME,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                "#.to_string(),
                applied_at: None,
            },
        ]
    }
}

// Global database manager state
pub type DatabaseState = RwLock<DatabaseManager>;



// Tauri commands
#[tauri::command]
pub async fn init_database(
    config: Option<DatabaseConfig>,
    backup_config: Option<BackupConfig>,
    state: State<'_, DatabaseState>,
) -> Result<(), String> {
    let mut manager = state.write().await;
    if let Some(config) = config {
        manager.config = config;
    }
    if let Some(backup_config) = backup_config {
        manager.backup_config = backup_config;
    }
    manager.initialize().await
}

#[tauri::command]
pub async fn execute_database_query(
    query: String,
    params: Option<HashMap<String, serde_json::Value>>,
    state: State<'_, DatabaseState>,
) -> Result<QueryResult, String> {
    let mut manager = state.write().await;
    manager.execute_query(&query, params).await
}

#[tauri::command]
pub async fn fetch_database_rows(
    query: String,
    params: Option<HashMap<String, serde_json::Value>>,
    state: State<'_, DatabaseState>,
) -> Result<Vec<HashMap<String, serde_json::Value>>, String> {
    let mut manager = state.write().await;
    manager.fetch_rows(&query, params).await
}

#[tauri::command]
pub async fn get_database_statistics(
    state: State<'_, DatabaseState>,
) -> Result<DatabaseStats, String> {
    let manager = state.read().await;
    manager.get_database_stats().await
}

#[tauri::command]
pub async fn get_database_schema(
    state: State<'_, DatabaseState>,
) -> Result<Vec<TableSchema>, String> {
    let manager = state.read().await;
    manager.get_table_schemas().await
}

#[tauri::command]
pub async fn create_database_backup(
    backup_name: Option<String>,
    state: State<'_, DatabaseState>,
) -> Result<String, String> {
    let manager = state.read().await;
    let backup_path = manager.create_backup(backup_name).await?;
    Ok(backup_path.to_string_lossy().to_string())
}

#[tauri::command]
pub async fn optimize_database(
    state: State<'_, DatabaseState>,
) -> Result<(), String> {
    let manager = state.read().await;
    let pool = manager.pool.as_ref().ok_or("Database not initialized")?;
    
    // Run VACUUM to optimize database
    sqlx::query("VACUUM")
        .execute(pool)
        .await
        .map_err(|e| format!("Database optimization failed: {}", e))?;
    
    // Analyze tables for query optimization
    sqlx::query("ANALYZE")
        .execute(pool)
        .await
        .map_err(|e| format!("Database analysis failed: {}", e))?;
    
    Ok(())
}

#[tauri::command]
pub async fn check_database_integrity(
    state: State<'_, DatabaseState>,
) -> Result<Vec<String>, String> {
    let manager = state.read().await;
    let pool = manager.pool.as_ref().ok_or("Database not initialized")?;
    
    let integrity_results = sqlx::query("PRAGMA integrity_check")
        .fetch_all(pool)
        .await
        .map_err(|e| format!("Integrity check failed: {}", e))?;
    
    let results: Vec<String> = integrity_results
        .iter()
        .map(|row| row.get::<String, _>(0))
        .collect();
    
    Ok(results)
}