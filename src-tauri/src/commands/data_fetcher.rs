use anyhow::{anyhow, Result};
use chrono::{DateTime, Duration, Utc};
use reqwest::{Client, Method, Response};
use rusqlite::{params, Connection, Row};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, State};
use tokio::sync::RwLock;
use uuid::Uuid;

/// Cache entry for storing fetched data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheEntry {
    pub id: String,
    pub key: String,
    pub data: serde_json::Value,
    pub created_at: DateTime<Utc>,
    pub expires_at: Option<DateTime<Utc>>,
    pub etag: Option<String>,
    pub last_modified: Option<String>,
    pub content_type: Option<String>,
    pub size_bytes: u64,
    pub hit_count: u32,
    pub metadata: HashMap<String, String>,
}

/// Data fetch request configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FetchRequest {
    pub url: String,
    pub method: String,
    pub headers: Option<HashMap<String, String>>,
    pub body: Option<serde_json::Value>,
    pub timeout_seconds: Option<u64>,
    pub cache_key: Option<String>,
    pub cache_ttl_seconds: Option<u64>,
    pub offline_fallback: Option<bool>,
    pub retry_config: Option<RetryConfig>,
    pub transform: Option<DataTransform>,
}

/// Retry configuration for failed requests
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetryConfig {
    pub max_attempts: u32,
    pub initial_delay_ms: u64,
    pub max_delay_ms: u64,
    pub backoff_multiplier: f64,
    pub retry_on_status: Vec<u16>,
}

impl Default for RetryConfig {
    fn default() -> Self {
        Self {
            max_attempts: 3,
            initial_delay_ms: 1000,
            max_delay_ms: 30000,
            backoff_multiplier: 2.0,
            retry_on_status: vec![408, 429, 500, 502, 503, 504],
        }
    }
}

/// Data transformation configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataTransform {
    pub json_path: Option<String>,
    pub filter_expression: Option<String>,
    pub sort_by: Option<String>,
    pub limit: Option<u32>,
    pub map_fields: Option<HashMap<String, String>>,
}

/// Fetch response with metadata
#[derive(Debug, Serialize, Deserialize)]
pub struct FetchResponse {
    pub data: serde_json::Value,
    pub cached: bool,
    pub cache_hit: bool,
    pub status_code: Option<u16>,
    pub headers: HashMap<String, String>,
    pub fetch_time_ms: u64,
    pub size_bytes: u64,
    pub etag: Option<String>,
    pub last_modified: Option<String>,
    pub expires_at: Option<DateTime<Utc>>,
}

/// Offline queue entry
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OfflineQueueEntry {
    pub id: String,
    pub request: FetchRequest,
    pub created_at: DateTime<Utc>,
    pub retry_count: u32,
    pub last_attempt: Option<DateTime<Utc>>,
    pub priority: u32,
    pub callback_id: Option<String>,
}

/// Cache statistics
#[derive(Debug, Serialize, Deserialize)]
pub struct CacheStats {
    pub total_entries: u32,
    pub total_size_bytes: u64,
    pub hit_rate: f64,
    pub expired_entries: u32,
    pub oldest_entry: Option<DateTime<Utc>>,
    pub newest_entry: Option<DateTime<Utc>>,
    pub most_accessed_key: Option<String>,
}

/// Data fetcher with caching and offline support
pub struct DataFetcher {
    client: Client,
    db: Arc<Mutex<Connection>>,
    memory_cache: Arc<RwLock<HashMap<String, CacheEntry>>>,
    offline_queue: Arc<RwLock<Vec<OfflineQueueEntry>>>,
    stats: Arc<RwLock<HashMap<String, u64>>>,
}

impl DataFetcher {
    pub fn new(db_path: &str) -> Result<Self> {
        let client = Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .user_agent("Tauri-DataFetcher/1.0")
            .build()?;
        
        let conn = Connection::open(db_path)?;
        
        // Create cache table
        conn.execute(
            r#"
            CREATE TABLE IF NOT EXISTS data_cache (
                id TEXT PRIMARY KEY,
                key TEXT UNIQUE NOT NULL,
                data TEXT NOT NULL,
                created_at TEXT NOT NULL,
                expires_at TEXT,
                etag TEXT,
                last_modified TEXT,
                content_type TEXT,
                size_bytes INTEGER NOT NULL,
                hit_count INTEGER NOT NULL DEFAULT 0,
                metadata TEXT
            )
            "#,
            [],
        )?;
        
        // Create offline queue table
        conn.execute(
            r#"
            CREATE TABLE IF NOT EXISTS offline_queue (
                id TEXT PRIMARY KEY,
                request TEXT NOT NULL,
                created_at TEXT NOT NULL,
                retry_count INTEGER NOT NULL DEFAULT 0,
                last_attempt TEXT,
                priority INTEGER NOT NULL DEFAULT 0,
                callback_id TEXT
            )
            "#,
            [],
        )?;
        
        // Create indexes
        conn.execute("CREATE INDEX IF NOT EXISTS idx_cache_key ON data_cache(key)", [])?;
        conn.execute("CREATE INDEX IF NOT EXISTS idx_cache_expires_at ON data_cache(expires_at)", [])?;
        conn.execute("CREATE INDEX IF NOT EXISTS idx_queue_priority ON offline_queue(priority DESC, created_at ASC)", [])?;
        
        let fetcher = Self {
            client,
            db: Arc::new(Mutex::new(conn)),
            memory_cache: Arc::new(RwLock::new(HashMap::new())),
            offline_queue: Arc::new(RwLock::new(Vec::new())),
            stats: Arc::new(RwLock::new(HashMap::new())),
        };
        
        // Load cache from database
        fetcher.load_cache_from_db()?;
        
        // Load offline queue from database
        fetcher.load_offline_queue_from_db()?;
        
        Ok(fetcher)
    }
    
    /// Load cache entries from database into memory
    fn load_cache_from_db(&self) -> Result<()> {
        let conn = self.db.lock().map_err(|e| anyhow!("Database lock error: {}", e))?;
        
        let mut stmt = conn.prepare(
            "SELECT * FROM data_cache WHERE expires_at IS NULL OR expires_at > ?1"
        )?;
        
        let now = Utc::now().to_rfc3339();
        let entries = stmt.query_map(params![now], |row| {
            self.row_to_cache_entry(row).map_err(|_| rusqlite::Error::InvalidColumnType(0, "cache_entry".to_string(), rusqlite::types::Type::Null))
        })?;
        
        let mut cache = self.memory_cache.blocking_write();
        
        for entry_result in entries {
            let entry = entry_result.map_err(|e| anyhow!("Database error: {}", e))?;
            cache.insert(entry.key.clone(), entry);
        }
        
        Ok(())
    }
    
    /// Load offline queue from database
    fn load_offline_queue_from_db(&self) -> Result<()> {
        let conn = self.db.lock().map_err(|e| anyhow!("Database lock error: {}", e))?;
        
        let mut stmt = conn.prepare(
            "SELECT * FROM offline_queue ORDER BY priority DESC, created_at ASC"
        )?;
        
        let entries = stmt.query_map([], |row| {
            let request_str: String = row.get("request")?;
            let request: FetchRequest = serde_json::from_str(&request_str)
                .map_err(|_e| rusqlite::Error::InvalidColumnType(0, "request".to_string(), rusqlite::types::Type::Text))?;
            
            let last_attempt_str: Option<String> = row.get("last_attempt")?;
            let last_attempt = last_attempt_str.and_then(|s| DateTime::parse_from_rfc3339(&s).ok())
                .map(|dt| dt.with_timezone(&Utc));
            
            Ok(OfflineQueueEntry {
                id: row.get("id")?,
                request,
                created_at: DateTime::parse_from_rfc3339(&row.get::<_, String>("created_at")?)
                    .map_err(|_| rusqlite::Error::InvalidColumnType(0, "created_at".to_string(), rusqlite::types::Type::Text))?
                    .with_timezone(&Utc),
                retry_count: row.get("retry_count")?,
                last_attempt,
                priority: row.get("priority")?,
                callback_id: row.get("callback_id")?,
            })
        })?;
        
        let mut queue = self.offline_queue.blocking_write();
        
        for entry_result in entries {
            let entry = entry_result?;
            queue.push(entry);
        }
        
        Ok(())
    }
    
    /// Convert database row to cache entry
    fn row_to_cache_entry(&self, row: &Row) -> Result<CacheEntry> {
        let metadata_str: Option<String> = row.get("metadata")?;
        let metadata: HashMap<String, String> = if let Some(meta_str) = metadata_str {
            serde_json::from_str(&meta_str).unwrap_or_default()
        } else {
            HashMap::new()
        };
        
        let expires_at_str: Option<String> = row.get("expires_at")?;
        let expires_at = expires_at_str.and_then(|s| DateTime::parse_from_rfc3339(&s).ok())
            .map(|dt| dt.with_timezone(&Utc));
        
        let data_str: String = row.get("data")?;
        let data: serde_json::Value = serde_json::from_str(&data_str)
            .map_err(|e| anyhow!("Failed to parse cached data: {}", e))?;
        
        Ok(CacheEntry {
            id: row.get("id")?,
            key: row.get("key")?,
            data,
            created_at: DateTime::parse_from_rfc3339(&row.get::<_, String>("created_at")?)
                .map_err(|e| anyhow!("Invalid created_at format: {}", e))?
                .with_timezone(&Utc),
            expires_at,
            etag: row.get("etag")?,
            last_modified: row.get("last_modified")?,
            content_type: row.get("content_type")?,
            size_bytes: row.get("size_bytes")?,
            hit_count: row.get("hit_count")?,
            metadata,
        })
    }
    
    /// Fetch data with caching and offline support
    pub async fn fetch_data(&self, request: FetchRequest) -> Result<FetchResponse> {
        let start_time = std::time::Instant::now();
        let cache_key = request.cache_key.clone().unwrap_or_else(|| {
            format!("{}:{}", request.method, request.url)
        });
        
        // Check cache first
        if let Some(cached_entry) = self.get_from_cache(&cache_key).await? {
            // Update hit count
            self.increment_hit_count(&cache_key).await?;
            
            return Ok(FetchResponse {
                data: cached_entry.data,
                cached: true,
                cache_hit: true,
                status_code: None,
                headers: HashMap::new(),
                fetch_time_ms: start_time.elapsed().as_millis() as u64,
                size_bytes: cached_entry.size_bytes,
                etag: cached_entry.etag,
                last_modified: cached_entry.last_modified,
                expires_at: cached_entry.expires_at,
            });
        }
        
        // Try to fetch from network
        match self.fetch_from_network(&request).await {
            Ok(response) => {
                let fetch_time = start_time.elapsed().as_millis() as u64;
                
                // Cache the response if TTL is specified
                if let Some(ttl_seconds) = request.cache_ttl_seconds {
                    let expires_at = Utc::now() + Duration::seconds(ttl_seconds as i64);
                    
                    let cache_entry = CacheEntry {
                        id: Uuid::new_v4().to_string(),
                        key: cache_key,
                        data: response.data.clone(),
                        created_at: Utc::now(),
                        expires_at: Some(expires_at),
                        etag: response.etag.clone(),
                        last_modified: response.last_modified.clone(),
                        content_type: response.headers.get("content-type").cloned(),
                        size_bytes: response.size_bytes,
                        hit_count: 0,
                        metadata: HashMap::new(),
                    };
                    
                    self.store_in_cache(cache_entry).await?;
                }
                
                Ok(FetchResponse {
                    fetch_time_ms: fetch_time,
                    cached: false,
                    cache_hit: false,
                    ..response
                })
            }
            Err(e) => {
                // If offline fallback is enabled, try to get from cache even if expired
                if request.offline_fallback.unwrap_or(false) {
                    if let Some(cached_entry) = self.get_from_cache_ignore_expiry(&cache_key).await? {
                        return Ok(FetchResponse {
                            data: cached_entry.data,
                            cached: true,
                            cache_hit: true,
                            status_code: None,
                            headers: HashMap::new(),
                            fetch_time_ms: start_time.elapsed().as_millis() as u64,
                            size_bytes: cached_entry.size_bytes,
                            etag: cached_entry.etag,
                            last_modified: cached_entry.last_modified,
                            expires_at: cached_entry.expires_at,
                        });
                    }
                }
                
                // Add to offline queue for retry later
                self.add_to_offline_queue(request).await?;
                
                Err(e)
            }
        }
    }
    
    /// Fetch data from network with retry logic
    async fn fetch_from_network(&self, request: &FetchRequest) -> Result<FetchResponse> {
        let retry_config = request.retry_config.clone().unwrap_or_default();
        let mut last_error = None;
        
        for attempt in 0..retry_config.max_attempts {
            if attempt > 0 {
                let delay = std::cmp::min(
                    retry_config.initial_delay_ms * (retry_config.backoff_multiplier.powi(attempt as i32 - 1) as u64),
                    retry_config.max_delay_ms,
                );
                tokio::time::sleep(std::time::Duration::from_millis(delay)).await;
            }
            
            match self.execute_request(request).await {
                Ok(response) => return Ok(response),
                Err(e) => {
                    last_error = Some(e);
                    // Check if we should retry based on status code or error type
                    // For now, we retry on all errors
                }
            }
        }
        
        Err(last_error.unwrap_or_else(|| anyhow!("All retry attempts failed")))
    }
    
    /// Execute HTTP request
    async fn execute_request(&self, request: &FetchRequest) -> Result<FetchResponse> {
        let method = Method::from_bytes(request.method.as_bytes())
            .map_err(|_| anyhow!("Invalid HTTP method: {}", request.method))?;
        
        let mut req_builder = self.client.request(method, &request.url);
        
        // Add headers
        if let Some(headers) = &request.headers {
            for (key, value) in headers {
                req_builder = req_builder.header(key, value);
            }
        }
        
        // Add body for POST/PUT requests
        if let Some(body) = &request.body {
            req_builder = req_builder.json(body);
        }
        
        // Set timeout
        if let Some(timeout_seconds) = request.timeout_seconds {
            req_builder = req_builder.timeout(std::time::Duration::from_secs(timeout_seconds));
        }
        
        let response = req_builder.send().await?;
        let status_code = response.status().as_u16();
        
        // Extract headers
        let mut headers = HashMap::new();
        for (key, value) in response.headers() {
            if let Ok(value_str) = value.to_str() {
                headers.insert(key.to_string(), value_str.to_string());
            }
        }
        
        let etag = headers.get("etag").cloned();
        let last_modified = headers.get("last-modified").cloned();
        
        // Get response body
        let body_bytes = response.bytes().await?;
        let size_bytes = body_bytes.len() as u64;
        
        // Try to parse as JSON, fallback to string
        let data = if let Ok(json_value) = serde_json::from_slice::<serde_json::Value>(&body_bytes) {
            json_value
        } else {
            serde_json::Value::String(
                String::from_utf8(body_bytes.to_vec())
                    .unwrap_or_else(|_| base64::encode(&body_bytes))
            )
        };
        
        // Apply data transformation if specified
        let transformed_data = if let Some(transform) = &request.transform {
            self.apply_data_transform(data, transform)?
        } else {
            data
        };
        
        Ok(FetchResponse {
            data: transformed_data,
            cached: false,
            cache_hit: false,
            status_code: Some(status_code),
            headers,
            fetch_time_ms: 0, // Will be set by caller
            size_bytes,
            etag,
            last_modified,
            expires_at: None,
        })
    }
    
    /// Apply data transformation
    fn apply_data_transform(&self, data: serde_json::Value, transform: &DataTransform) -> Result<serde_json::Value> {
        let mut result = data;
        
        // Apply JSON path extraction
        if let Some(json_path) = &transform.json_path {
            // Simple JSON path implementation (could be enhanced with a proper library)
            let path_parts: Vec<&str> = json_path.split('.').collect();
            for part in path_parts {
                if part.is_empty() {
                    continue;
                }
                
                if let Some(obj) = result.as_object() {
                    result = obj.get(part).cloned().unwrap_or(serde_json::Value::Null);
                } else if let Some(arr) = result.as_array() {
                    if let Ok(index) = part.parse::<usize>() {
                        result = arr.get(index).cloned().unwrap_or(serde_json::Value::Null);
                    }
                }
            }
        }
        
        // Apply field mapping
        if let Some(field_map) = &transform.map_fields {
            if let Some(obj) = result.as_object_mut() {
                let mut new_obj = serde_json::Map::new();
                for (old_key, new_key) in field_map {
                    if let Some(value) = obj.remove(old_key) {
                        new_obj.insert(new_key.clone(), value);
                    }
                }
                // Add remaining fields
                for (key, value) in obj.iter() {
                    new_obj.insert(key.clone(), value.clone());
                }
                result = serde_json::Value::Object(new_obj);
            }
        }
        
        // Apply limit to arrays
        if let Some(limit) = transform.limit {
            if let Some(arr) = result.as_array_mut() {
                arr.truncate(limit as usize);
            }
        }
        
        Ok(result)
    }
    
    /// Get entry from cache
    async fn get_from_cache(&self, key: &str) -> Result<Option<CacheEntry>> {
        let cache = self.memory_cache.read().await;
        
        if let Some(entry) = cache.get(key) {
            // Check if entry has expired
            if let Some(expires_at) = entry.expires_at {
                if Utc::now() > expires_at {
                    return Ok(None);
                }
            }
            Ok(Some(entry.clone()))
        } else {
            Ok(None)
        }
    }
    
    /// Get entry from cache ignoring expiry
    async fn get_from_cache_ignore_expiry(&self, key: &str) -> Result<Option<CacheEntry>> {
        let cache = self.memory_cache.read().await;
        Ok(cache.get(key).cloned())
    }
    
    /// Store entry in cache
    async fn store_in_cache(&self, entry: CacheEntry) -> Result<()> {
        // Store in memory cache
        {
            let mut cache = self.memory_cache.write().await;
            cache.insert(entry.key.clone(), entry.clone());
        }
        
        // Store in database
        let conn = self.db.lock().map_err(|e| anyhow!("Database lock error: {}", e))?;
        
        let data_str = serde_json::to_string(&entry.data)?;
        let metadata_str = serde_json::to_string(&entry.metadata)?;
        let expires_at_str = entry.expires_at.map(|dt| dt.to_rfc3339());
        
        conn.execute(
            r#"
            INSERT OR REPLACE INTO data_cache (
                id, key, data, created_at, expires_at, etag, last_modified,
                content_type, size_bytes, hit_count, metadata
            ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11)
            "#,
            params![
                entry.id,
                entry.key,
                data_str,
                entry.created_at.to_rfc3339(),
                expires_at_str,
                entry.etag,
                entry.last_modified,
                entry.content_type,
                entry.size_bytes,
                entry.hit_count,
                metadata_str
            ],
        )?;
        
        Ok(())
    }
    
    /// Increment hit count for cache entry
    async fn increment_hit_count(&self, key: &str) -> Result<()> {
        // Update memory cache
        {
            let mut cache = self.memory_cache.write().await;
            if let Some(entry) = cache.get_mut(key) {
                entry.hit_count += 1;
            }
        }
        
        // Update database
        let conn = self.db.lock().map_err(|e| anyhow!("Database lock error: {}", e))?;
        conn.execute(
            "UPDATE data_cache SET hit_count = hit_count + 1 WHERE key = ?1",
            params![key],
        )?;
        
        Ok(())
    }
    
    /// Add request to offline queue
    async fn add_to_offline_queue(&self, request: FetchRequest) -> Result<()> {
        let entry = OfflineQueueEntry {
            id: Uuid::new_v4().to_string(),
            request: request.clone(),
            created_at: Utc::now(),
            retry_count: 0,
            last_attempt: None,
            priority: 0,
            callback_id: None,
        };
        
        // Add to memory queue
        {
            let mut queue = self.offline_queue.write().await;
            queue.push(entry.clone());
        }
        
        // Add to database
        let conn = self.db.lock().map_err(|e| anyhow!("Database lock error: {}", e))?;
        let request_str = serde_json::to_string(&request)?;
        
        conn.execute(
            r#"
            INSERT INTO offline_queue (
                id, request, created_at, retry_count, priority
            ) VALUES (?1, ?2, ?3, ?4, ?5)
            "#,
            params![
                entry.id,
                request_str,
                entry.created_at.to_rfc3339(),
                entry.retry_count,
                entry.priority
            ],
        )?;
        
        Ok(())
    }
    
    /// Process offline queue
    pub async fn process_offline_queue(&self) -> Result<u32> {
        let mut processed_count = 0;
        let mut queue = self.offline_queue.write().await;
        let mut to_remove = Vec::new();
        
        for (index, entry) in queue.iter_mut().enumerate() {
            match self.fetch_from_network(&entry.request).await {
                Ok(_) => {
                    // Success - remove from queue
                    to_remove.push(index);
                    processed_count += 1;
                }
                Err(_) => {
                    // Failed - increment retry count
                    entry.retry_count += 1;
                    entry.last_attempt = Some(Utc::now());
                    
                    // Remove if max retries exceeded
                    if entry.retry_count >= 5 {
                        to_remove.push(index);
                    }
                }
            }
        }
        
        // Remove processed entries (in reverse order to maintain indices)
        for &index in to_remove.iter().rev() {
            queue.remove(index);
        }
        
        // Update database
        if !to_remove.is_empty() {
            let conn = self.db.lock().map_err(|e| anyhow!("Database lock error: {}", e))?;
            for &index in &to_remove {
                if let Some(entry) = queue.get(index) {
                    conn.execute(
                        "DELETE FROM offline_queue WHERE id = ?1",
                        params![entry.id],
                    )?;
                }
            }
        }
        
        Ok(processed_count)
    }
    
    /// Clear expired cache entries
    pub async fn clear_expired_cache(&self) -> Result<u32> {
        let now = Utc::now();
        let mut removed_count = 0;
        
        // Clear from memory cache
        {
            let mut cache = self.memory_cache.write().await;
            cache.retain(|_, entry| {
                if let Some(expires_at) = entry.expires_at {
                    if now > expires_at {
                        removed_count += 1;
                        return false;
                    }
                }
                true
            });
        }
        
        // Clear from database
        let conn = self.db.lock().map_err(|e| anyhow!("Database lock error: {}", e))?;
        conn.execute(
            "DELETE FROM data_cache WHERE expires_at IS NOT NULL AND expires_at < ?1",
            params![now.to_rfc3339()],
        )?;
        
        Ok(removed_count)
    }
    
    /// Get cache statistics
    pub async fn get_cache_stats(&self) -> Result<CacheStats> {
        let cache = self.memory_cache.read().await;
        let conn = self.db.lock().map_err(|e| anyhow!("Database lock error: {}", e))?;
        
        let total_entries = cache.len() as u32;
        let total_size_bytes: u64 = cache.values().map(|e| e.size_bytes).sum();
        
        let total_hits: u32 = cache.values().map(|e| e.hit_count).sum();
        let hit_rate = if total_entries > 0 {
            total_hits as f64 / total_entries as f64
        } else {
            0.0
        };
        
        let now = Utc::now();
        let expired_entries = cache.values()
            .filter(|entry| {
                if let Some(expires_at) = entry.expires_at {
                    now > expires_at
                } else {
                    false
                }
            })
            .count() as u32;
        
        let oldest_entry = cache.values()
            .map(|e| e.created_at)
            .min();
        
        let newest_entry = cache.values()
            .map(|e| e.created_at)
            .max();
        
        let most_accessed_key = cache.values()
            .max_by_key(|e| e.hit_count)
            .map(|e| e.key.clone());
        
        Ok(CacheStats {
            total_entries,
            total_size_bytes,
            hit_rate,
            expired_entries,
            oldest_entry,
            newest_entry,
            most_accessed_key,
        })
    }
}

/// Global data fetcher state
pub struct DataFetcherState(pub Mutex<Option<DataFetcher>>);

impl Default for DataFetcherState {
    fn default() -> Self {
        Self(Mutex::new(None))
    }
}

/// Initialize data fetcher
#[tauri::command]
pub async fn init_data_fetcher(
    app: AppHandle,
    state: State<'_, DataFetcherState>,
) -> Result<(), String> {
    let app_dir = app.path().app_data_dir()
        .map_err(|e| format!("Failed to get app data directory: {}", e))?;
    
    std::fs::create_dir_all(&app_dir)
        .map_err(|e| format!("Failed to create app directory: {}", e))?;
    
    let db_path = app_dir.join("data_cache.db");
    
    let data_fetcher = DataFetcher::new(db_path.to_str().unwrap())
        .map_err(|e| format!("Failed to initialize data fetcher: {}", e))?;
    
    let mut fetcher_state = state.0.lock()
        .map_err(|e| format!("Failed to lock data fetcher state: {}", e))?;
    *fetcher_state = Some(data_fetcher);
    
    Ok(())
}

/// Fetch data with caching and offline support
#[tauri::command]
pub async fn fetch_data(
    request: FetchRequest,
    state: State<'_, DataFetcherState>,
) -> Result<FetchResponse, String> {
    let fetcher = {
        let fetcher_state = state.0.lock()
            .map_err(|e| format!("Failed to lock data fetcher state: {}", e))?;
        
        if let Some(fetcher) = &*fetcher_state {
            // Clone the fetcher to avoid holding the lock across await
            Some(DataFetcher {
                client: fetcher.client.clone(),
                db: fetcher.db.clone(),
                memory_cache: fetcher.memory_cache.clone(),
                offline_queue: fetcher.offline_queue.clone(),
                stats: fetcher.stats.clone(),
            })
        } else {
            None
        }
    }; // Lock is dropped here
    
    if let Some(fetcher) = fetcher {
        fetcher.fetch_data(request).await
            .map_err(|e| e.to_string())
    } else {
        Err("Data fetcher not initialized".to_string())
    }
}

/// Process offline queue
#[tauri::command]
pub async fn process_offline_queue(
    state: State<'_, DataFetcherState>,
) -> Result<u32, String> {
    let fetcher = {
        let fetcher_state = state.0.lock()
            .map_err(|e| format!("Failed to lock data fetcher state: {}", e))?;
        
        if let Some(fetcher) = &*fetcher_state {
            // Clone the fetcher to avoid holding the lock across await
            Some(DataFetcher {
                client: fetcher.client.clone(),
                db: fetcher.db.clone(),
                memory_cache: fetcher.memory_cache.clone(),
                offline_queue: fetcher.offline_queue.clone(),
                stats: fetcher.stats.clone(),
            })
        } else {
            None
        }
    }; // Lock is dropped here
    
    if let Some(fetcher) = fetcher {
        fetcher.process_offline_queue().await
            .map_err(|e| e.to_string())
    } else {
        Err("Data fetcher not initialized".to_string())
    }
}

/// Clear expired cache entries
#[tauri::command]
pub async fn clear_expired_cache(
    state: State<'_, DataFetcherState>,
) -> Result<u32, String> {
    let fetcher = {
        let fetcher_state = state.0.lock()
            .map_err(|e| format!("Failed to lock data fetcher state: {}", e))?;
        
        if let Some(fetcher) = &*fetcher_state {
            // Clone the fetcher to avoid holding the lock across await
            // Since DataFetcher uses Arc internally, this is efficient
            Some(DataFetcher {
                client: fetcher.client.clone(),
                db: fetcher.db.clone(),
                memory_cache: fetcher.memory_cache.clone(),
                offline_queue: fetcher.offline_queue.clone(),
                stats: fetcher.stats.clone(),
            })
        } else {
            None
        }
    }; // Lock is dropped here
    
    if let Some(fetcher) = fetcher {
        fetcher.clear_expired_cache().await
            .map_err(|e| e.to_string())
    } else {
        Err("Data fetcher not initialized".to_string())
    }
}

/// Get cache statistics
#[tauri::command]
pub async fn get_cache_stats(
    state: State<'_, DataFetcherState>,
) -> Result<CacheStats, String> {
    let fetcher = {
        let fetcher_state = state.0.lock()
            .map_err(|e| format!("Failed to lock data fetcher state: {}", e))?;
        
        if let Some(fetcher) = &*fetcher_state {
            // Clone the fetcher to avoid holding the lock across await
            Some(DataFetcher {
                client: fetcher.client.clone(),
                db: fetcher.db.clone(),
                memory_cache: fetcher.memory_cache.clone(),
                offline_queue: fetcher.offline_queue.clone(),
                stats: fetcher.stats.clone(),
            })
        } else {
            None
        }
    }; // Lock is dropped here
    
    if let Some(fetcher) = fetcher {
        fetcher.get_cache_stats().await
            .map_err(|e| e.to_string())
    } else {
        Err("Data fetcher not initialized".to_string())
    }
}