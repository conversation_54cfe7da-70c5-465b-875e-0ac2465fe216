use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fmt;
use tauri::State;
use tokio::sync::RwLock;

// Error severity levels
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum ErrorSeverity {
    Low,
    Medium,
    High,
    Critical,
}

// Error categories for better organization
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum ErrorCategory {
    Network,
    Database,
    Authentication,
    Validation,
    FileSystem,
    Configuration,
    External,
    Internal,
}

// Structured error information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorInfo {
    pub id: String,
    pub code: String,
    pub message: String,
    pub details: Option<String>,
    pub category: ErrorCategory,
    pub severity: ErrorSeverity,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub context: HashMap<String, String>,
    pub stack_trace: Option<String>,
    pub user_message: Option<String>,
    pub recovery_suggestions: Vec<String>,
}

// Error recovery action
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct RecoveryAction {
    pub action_type: String,
    pub description: String,
    pub auto_executable: bool,
    pub parameters: HashMap<String, String>,
}

// Error statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorStats {
    pub total_errors: u64,
    pub errors_by_category: HashMap<ErrorCategory, u64>,
    pub errors_by_severity: HashMap<ErrorSeverity, u64>,
    pub recent_errors: Vec<ErrorInfo>,
    pub most_common_errors: Vec<(String, u64)>,
}

// Error handler configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorHandlerConfig {
    pub max_error_history: usize,
    pub auto_recovery_enabled: bool,
    pub notification_threshold: ErrorSeverity,
    pub log_to_file: bool,
    pub log_file_path: Option<String>,
}

impl Default for ErrorHandlerConfig {
    fn default() -> Self {
        Self {
            max_error_history: 1000,
            auto_recovery_enabled: true,
            notification_threshold: ErrorSeverity::Medium,
            log_to_file: true,
            log_file_path: None,
        }
    }
}

// Main error handler
#[derive(Debug, Default)]
pub struct ErrorHandler {
    config: ErrorHandlerConfig,
    error_history: Vec<ErrorInfo>,
    recovery_actions: HashMap<String, RecoveryAction>,
    error_counts: HashMap<String, u64>,
}

impl ErrorHandler {
    pub fn new(config: ErrorHandlerConfig) -> Self {
        Self {
            config,
            error_history: Vec::new(),
            recovery_actions: HashMap::new(),
            error_counts: HashMap::new(),
        }
    }

    pub fn log_error(&mut self, mut error: ErrorInfo) -> Result<(), String> {
        // Generate unique ID if not provided
        if error.id.is_empty() {
            error.id = uuid::Uuid::new_v4().to_string();
        }

        // Update error counts
        *self.error_counts.entry(error.code.clone()).or_insert(0) += 1;

        // Add to history
        self.error_history.push(error.clone());

        // Maintain history size limit
        if self.error_history.len() > self.config.max_error_history {
            self.error_history.remove(0);
        }

        // Log to file if configured
        if self.config.log_to_file {
            self.write_to_log_file(&error)?;
        }

        // Check for auto-recovery
        if self.config.auto_recovery_enabled {
            self.attempt_recovery(&error)?;
        }

        Ok(())
    }

    pub fn get_error_by_id(&self, id: &str) -> Option<&ErrorInfo> {
        self.error_history.iter().find(|e| e.id == id)
    }

    pub fn get_errors_by_category(&self, category: &ErrorCategory) -> Vec<&ErrorInfo> {
        self.error_history
            .iter()
            .filter(|e| &e.category == category)
            .collect()
    }

    pub fn get_errors_by_severity(&self, severity: &ErrorSeverity) -> Vec<&ErrorInfo> {
        self.error_history
            .iter()
            .filter(|e| &e.severity == severity)
            .collect()
    }

    pub fn get_recent_errors(&self, limit: usize) -> Vec<&ErrorInfo> {
        let start = if self.error_history.len() > limit {
            self.error_history.len() - limit
        } else {
            0
        };
        self.error_history[start..].iter().collect()
    }

    pub fn get_error_stats(&self) -> ErrorStats {
        let mut errors_by_category = HashMap::new();
        let mut errors_by_severity = HashMap::new();

        for error in &self.error_history {
            *errors_by_category.entry(error.category.clone()).or_insert(0) += 1;
            *errors_by_severity.entry(error.severity.clone()).or_insert(0) += 1;
        }

        let most_common_errors: Vec<(String, u64)> = {
            let mut counts: Vec<_> = self.error_counts.iter().collect();
            counts.sort_by(|a, b| b.1.cmp(a.1));
            counts.into_iter().take(10).map(|(k, v)| (k.clone(), *v)).collect()
        };

        ErrorStats {
            total_errors: self.error_history.len() as u64,
            errors_by_category,
            errors_by_severity,
            recent_errors: self.get_recent_errors(10).into_iter().cloned().collect(),
            most_common_errors,
        }
    }

    pub fn clear_error_history(&mut self) {
        self.error_history.clear();
        self.error_counts.clear();
    }

    pub fn register_recovery_action(&mut self, error_code: String, action: RecoveryAction) {
        self.recovery_actions.insert(error_code, action);
    }

    pub fn update_config(&mut self, config: ErrorHandlerConfig) {
        self.config = config;
    }

    fn write_to_log_file(&self, error: &ErrorInfo) -> Result<(), String> {
        if let Some(log_path) = &self.config.log_file_path {
            let log_entry = format!(
                "[{}] {} - {} - {}: {}\n",
                error.timestamp.format("%Y-%m-%d %H:%M:%S UTC"),
                error.severity as u8,
                error.category as u8,
                error.code,
                error.message
            );
            
            std::fs::OpenOptions::new()
                .create(true)
                .append(true)
                .open(log_path)
                .and_then(|mut file| {
                    use std::io::Write;
                    file.write_all(log_entry.as_bytes())
                })
                .map_err(|e| format!("Failed to write to log file: {}", e))?;
        }
        Ok(())
    }

    fn attempt_recovery(&self, error: &ErrorInfo) -> Result<(), String> {
        if let Some(action) = self.recovery_actions.get(&error.code) {
            if action.auto_executable {
                // Here you would implement the actual recovery logic
                // For now, we'll just log that recovery was attempted
                println!("Attempting recovery for error {}: {}", error.code, action.description);
            }
        }
        Ok(())
    }
}

// Global error handler state
pub type ErrorHandlerState = RwLock<ErrorHandler>;



// Helper function to create error info
pub fn create_error(
    code: &str,
    message: &str,
    category: ErrorCategory,
    severity: ErrorSeverity,
) -> ErrorInfo {
    ErrorInfo {
        id: String::new(), // Will be generated when logged
        code: code.to_string(),
        message: message.to_string(),
        details: None,
        category,
        severity,
        timestamp: chrono::Utc::now(),
        context: HashMap::new(),
        stack_trace: None,
        user_message: None,
        recovery_suggestions: Vec::new(),
    }
}

// Tauri commands
#[tauri::command]
pub async fn init_error_handler(
    config: Option<ErrorHandlerConfig>,
    state: State<'_, ErrorHandlerState>,
) -> Result<(), String> {
    let config = config.unwrap_or_default();
    let mut handler = state.write().await;
    handler.update_config(config);
    Ok(())
}

#[tauri::command]
pub async fn log_error_info(
    error: ErrorInfo,
    state: State<'_, ErrorHandlerState>,
) -> Result<String, String> {
    let mut handler = state.write().await;
    let error_id = if error.id.is_empty() {
        uuid::Uuid::new_v4().to_string()
    } else {
        error.id.clone()
    };
    
    let mut error_with_id = error;
    error_with_id.id = error_id.clone();
    
    handler.log_error(error_with_id)?;
    Ok(error_id)
}

#[tauri::command]
pub async fn get_error_by_id(
    id: String,
    state: State<'_, ErrorHandlerState>,
) -> Result<Option<ErrorInfo>, String> {
    let handler = state.read().await;
    Ok(handler.get_error_by_id(&id).cloned())
}

#[tauri::command]
pub async fn get_errors_by_category(
    category: ErrorCategory,
    state: State<'_, ErrorHandlerState>,
) -> Result<Vec<ErrorInfo>, String> {
    let handler = state.read().await;
    Ok(handler.get_errors_by_category(&category).into_iter().cloned().collect())
}

#[tauri::command]
pub async fn get_errors_by_severity(
    severity: ErrorSeverity,
    state: State<'_, ErrorHandlerState>,
) -> Result<Vec<ErrorInfo>, String> {
    let handler = state.read().await;
    Ok(handler.get_errors_by_severity(&severity).into_iter().cloned().collect())
}

#[tauri::command]
pub async fn get_recent_errors(
    limit: Option<usize>,
    state: State<'_, ErrorHandlerState>,
) -> Result<Vec<ErrorInfo>, String> {
    let handler = state.read().await;
    let limit = limit.unwrap_or(10);
    Ok(handler.get_recent_errors(limit).into_iter().cloned().collect())
}

#[tauri::command]
pub async fn get_error_statistics(
    state: State<'_, ErrorHandlerState>,
) -> Result<ErrorStats, String> {
    let handler = state.read().await;
    Ok(handler.get_error_stats())
}

#[tauri::command]
pub async fn clear_error_history(
    state: State<'_, ErrorHandlerState>,
) -> Result<(), String> {
    let mut handler = state.write().await;
    handler.clear_error_history();
    Ok(())
}

#[tauri::command]
pub async fn register_recovery_action(
    error_code: String,
    action: RecoveryAction,
    state: State<'_, ErrorHandlerState>,
) -> Result<(), String> {
    let mut handler = state.write().await;
    handler.register_recovery_action(error_code, action);
    Ok(())
}

#[tauri::command]
pub async fn update_error_handler_config(
    config: ErrorHandlerConfig,
    state: State<'_, ErrorHandlerState>,
) -> Result<(), String> {
    let mut handler = state.write().await;
    handler.update_config(config);
    Ok(())
}

// Custom error types for different modules
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AppError {
    Network { message: String, status_code: Option<u16> },
    Database { message: String, query: Option<String> },
    Authentication { message: String, user_id: Option<String> },
    Validation { message: String, field: Option<String> },
    FileSystem { message: String, path: Option<String> },
    Configuration { message: String, key: Option<String> },
    External { message: String, service: Option<String> },
    Internal { message: String, module: Option<String> },
}

impl fmt::Display for AppError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            AppError::Network { message, status_code } => {
                write!(f, "Network Error: {}", message)?;
                if let Some(code) = status_code {
                    write!(f, " (Status: {})", code)?;
                }
                Ok(())
            }
            AppError::Database { message, query } => {
                write!(f, "Database Error: {}", message)?;
                if let Some(q) = query {
                    write!(f, " (Query: {})", q)?;
                }
                Ok(())
            }
            AppError::Authentication { message, user_id } => {
                write!(f, "Authentication Error: {}", message)?;
                if let Some(id) = user_id {
                    write!(f, " (User: {})", id)?;
                }
                Ok(())
            }
            AppError::Validation { message, field } => {
                write!(f, "Validation Error: {}", message)?;
                if let Some(f_name) = field {
                    write!(f, " (Field: {})", f_name)?;
                }
                Ok(())
            }
            AppError::FileSystem { message, path } => {
                write!(f, "File System Error: {}", message)?;
                if let Some(p) = path {
                    write!(f, " (Path: {})", p)?;
                }
                Ok(())
            }
            AppError::Configuration { message, key } => {
                write!(f, "Configuration Error: {}", message)?;
                if let Some(k) = key {
                    write!(f, " (Key: {})", k)?;
                }
                Ok(())
            }
            AppError::External { message, service } => {
                write!(f, "External Service Error: {}", message)?;
                if let Some(s) = service {
                    write!(f, " (Service: {})", s)?;
                }
                Ok(())
            }
            AppError::Internal { message, module } => {
                write!(f, "Internal Error: {}", message)?;
                if let Some(m) = module {
                    write!(f, " (Module: {})", m)?;
                }
                Ok(())
            }
        }
    }
}

impl std::error::Error for AppError {}

// Helper macro for quick error creation
#[macro_export]
macro_rules! app_error {
    (network, $msg:expr) => {
        AppError::Network {
            message: $msg.to_string(),
            status_code: None,
        }
    };
    (network, $msg:expr, $code:expr) => {
        AppError::Network {
            message: $msg.to_string(),
            status_code: Some($code),
        }
    };
    (database, $msg:expr) => {
        AppError::Database {
            message: $msg.to_string(),
            query: None,
        }
    };
    (database, $msg:expr, $query:expr) => {
        AppError::Database {
            message: $msg.to_string(),
            query: Some($query.to_string()),
        }
    };
    (auth, $msg:expr) => {
        AppError::Authentication {
            message: $msg.to_string(),
            user_id: None,
        }
    };
    (auth, $msg:expr, $user:expr) => {
        AppError::Authentication {
            message: $msg.to_string(),
            user_id: Some($user.to_string()),
        }
    };
    (validation, $msg:expr) => {
        AppError::Validation {
            message: $msg.to_string(),
            field: None,
        }
    };
    (validation, $msg:expr, $field:expr) => {
        AppError::Validation {
            message: $msg.to_string(),
            field: Some($field.to_string()),
        }
    };
    (filesystem, $msg:expr) => {
        AppError::FileSystem {
            message: $msg.to_string(),
            path: None,
        }
    };
    (filesystem, $msg:expr, $path:expr) => {
        AppError::FileSystem {
            message: $msg.to_string(),
            path: Some($path.to_string()),
        }
    };
    (config, $msg:expr) => {
        AppError::Configuration {
            message: $msg.to_string(),
            key: None,
        }
    };
    (config, $msg:expr, $key:expr) => {
        AppError::Configuration {
            message: $msg.to_string(),
            key: Some($key.to_string()),
        }
    };
    (external, $msg:expr) => {
        AppError::External {
            message: $msg.to_string(),
            service: None,
        }
    };
    (external, $msg:expr, $service:expr) => {
        AppError::External {
            message: $msg.to_string(),
            service: Some($service.to_string()),
        }
    };
    (internal, $msg:expr) => {
        AppError::Internal {
            message: $msg.to_string(),
            module: None,
        }
    };
    (internal, $msg:expr, $module:expr) => {
        AppError::Internal {
            message: $msg.to_string(),
            module: Some($module.to_string()),
        }
    };
}