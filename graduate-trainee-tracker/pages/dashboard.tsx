import * as React from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  Users,
  GraduationCap,
  ClipboardCheck,
  TrendingUp,
  Calendar,
  Award,
  Target,
  BookOpen,
  BarChart3,
  ArrowRight,
  Clock
} from "lucide-react"
import { GraduateTraineeTrackerState } from "../types"

interface DashboardPageProps {
  state: GraduateTraineeTrackerState
}

export function DashboardPage({ state }: DashboardPageProps) {
  const activeTrainees = state.trainees.filter(t => t.status === "ACTIVE").length
  const completedPrograms = state.programs.filter(p => p.status === "COMPLETED").length
  const pendingReviews = state.reviews.filter(r => r.status === "PENDING").length
  const upcomingDeadlines = state.trainees.filter(t => 
    t.nextReviewDate && new Date(t.nextReviewDate) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
  ).length

  const stats = [
    {
      title: "Active Trainees",
      value: activeTrainees,
      icon: Users,
      description: "Currently in programs",
      trend: "+12% from last month",
      color: "text-blue-500"
    },
    {
      title: "Programs Running",
      value: state.programs.filter(p => p.status === "ACTIVE").length,
      icon: GraduationCap,
      description: "Active programs",
      trend: "2 starting next week",
      color: "text-green-500"
    },
    {
      title: "Pending Reviews",
      value: pendingReviews,
      icon: ClipboardCheck,
      description: "Awaiting review",
      trend: "5 due this week",
      color: "text-orange-500"
    },
    {
      title: "Completion Rate",
      value: "87%",
      icon: TrendingUp,
      description: "Program success rate",
      trend: "+3% improvement",
      color: "text-purple-500"
    }
  ]

  const recentActivities = [
    { type: "review", message: "John Smith completed quarterly review", time: "2 hours ago" },
    { type: "enrollment", message: "Sarah Johnson enrolled in Data Analytics Program", time: "5 hours ago" },
    { type: "completion", message: "Mike Wilson completed Python Fundamentals", time: "1 day ago" },
    { type: "approval", message: "Program extension approved for Emma Davis", time: "2 days ago" },
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground">
          Welcome back! Here's an overview of your graduate trainee programs.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                {stat.description}
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                {stat.trend}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
        {/* Program Progress */}
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Program Progress</CardTitle>
            <CardDescription>
              Track the progress of active training programs
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {state.programs.slice(0, 4).map((program) => (
                <div key={program.id} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <BookOpen className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">{program.name}</span>
                      <Badge variant="outline" className="ml-2">
                        {program.trainees?.length || 0} trainees
                      </Badge>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {Math.floor(Math.random() * 100)}%
                    </span>
                  </div>
                  <Progress value={Math.floor(Math.random() * 100)} className="h-2" />
                </div>
              ))}
            </div>
            <Button variant="ghost" className="w-full mt-4" size="sm">
              View All Programs
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Latest updates from your programs
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity, index) => (
                <div key={index} className="flex items-start gap-3">
                  <div className="rounded-full bg-muted p-2">
                    {activity.type === "review" && <ClipboardCheck className="h-3 w-3" />}
                    {activity.type === "enrollment" && <Users className="h-3 w-3" />}
                    {activity.type === "completion" && <Award className="h-3 w-3" />}
                    {activity.type === "approval" && <Target className="h-3 w-3" />}
                  </div>
                  <div className="flex-1 space-y-1">
                    <p className="text-sm">{activity.message}</p>
                    <p className="text-xs text-muted-foreground">
                      <Clock className="inline h-3 w-3 mr-1" />
                      {activity.time}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Upcoming Deadlines and Performance */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Upcoming Deadlines */}
        <Card>
          <CardHeader>
            <CardTitle>Upcoming Deadlines</CardTitle>
            <CardDescription>
              Reviews and assessments due this week
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {state.trainees.slice(0, 5).map((trainee) => (
                <div key={trainee.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">
                        {trainee.firstName} {trainee.lastName}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Quarterly Review
                      </p>
                    </div>
                  </div>
                  <Badge variant={Math.random() > 0.5 ? "destructive" : "secondary"}>
                    {Math.floor(Math.random() * 7) + 1} days
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Performance Metrics */}
        <Card>
          <CardHeader>
            <CardTitle>Performance Overview</CardTitle>
            <CardDescription>
              Key metrics for this quarter
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="completion" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="completion">Completion</TabsTrigger>
                <TabsTrigger value="satisfaction">Satisfaction</TabsTrigger>
                <TabsTrigger value="retention">Retention</TabsTrigger>
              </TabsList>
              <TabsContent value="completion" className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Overall Rate</span>
                  <span className="text-2xl font-bold">87%</span>
                </div>
                <Progress value={87} />
                <p className="text-xs text-muted-foreground">
                  13% improvement from last quarter
                </p>
              </TabsContent>
              <TabsContent value="satisfaction" className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Average Score</span>
                  <span className="text-2xl font-bold">4.6/5</span>
                </div>
                <Progress value={92} />
                <p className="text-xs text-muted-foreground">
                  Based on 156 reviews
                </p>
              </TabsContent>
              <TabsContent value="retention" className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Retention Rate</span>
                  <span className="text-2xl font-bold">94%</span>
                </div>
                <Progress value={94} />
                <p className="text-xs text-muted-foreground">
                  Above industry average
                </p>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}