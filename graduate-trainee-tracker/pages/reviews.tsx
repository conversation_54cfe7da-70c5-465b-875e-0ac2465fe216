import * as React from "react"
import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Calendar,
  Clock,
  User,
  Star,
  FileText,
  Send,
  Filter,
  Download,
  AlertCircle,
  CheckCircle,
  XCircle,
  MessageSquare,
  TrendingUp,
  Target
} from "lucide-react"
import { GraduateTraineeTrackerState, Review } from "../types"

interface ReviewsPageProps {
  state: GraduateTraineeTrackerState
  setState: React.Dispatch<React.SetStateAction<GraduateTraineeTrackerState>>
}

export function ReviewsPage({ state, setState }: ReviewsPageProps) {
  const [selectedReview, setSelectedReview] = useState<Review | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [filterStatus, setFilterStatus] = useState<string>("all")
  const [filterType, setFilterType] = useState<string>("all")

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "COMPLETED": return CheckCircle
      case "PENDING": return Clock
      case "IN_PROGRESS": return AlertCircle
      case "CANCELLED": return XCircle
      default: return FileText
    }
  }

  const getStatusVariant = (status: string) => {
    switch (status) {
      case "COMPLETED": return "secondary"
      case "PENDING": return "default"
      case "IN_PROGRESS": return "outline"
      case "CANCELLED": return "destructive"
      default: return "outline"
    }
  }

  const filteredReviews = state.reviews.filter(review => {
    const matchesStatus = filterStatus === "all" || review.status === filterStatus
    const matchesType = filterType === "all" || review.type === filterType
    return matchesStatus && matchesType
  })

  const pendingReviews = filteredReviews.filter(r => r.status === "PENDING")
  const inProgressReviews = filteredReviews.filter(r => r.status === "IN_PROGRESS")
  const completedReviews = filteredReviews.filter(r => r.status === "COMPLETED")

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Reviews</h1>
          <p className="text-muted-foreground">
            Manage performance reviews and assessments for trainees
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Send className="mr-2 h-4 w-4" />
                Schedule Review
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[525px]">
              <DialogHeader>
                <DialogTitle>Schedule Performance Review</DialogTitle>
                <DialogDescription>
                  Create a new review for a trainee
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="trainee">Trainee</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a trainee" />
                    </SelectTrigger>
                    <SelectContent>
                      {state.trainees.map(trainee => (
                        <SelectItem key={trainee.id} value={trainee.id}>
                          {trainee.firstName} {trainee.lastName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="type">Review Type</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select review type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="MONTHLY">Monthly Review</SelectItem>
                      <SelectItem value="QUARTERLY">Quarterly Review</SelectItem>
                      <SelectItem value="ANNUAL">Annual Review</SelectItem>
                      <SelectItem value="PROBATION">Probation Review</SelectItem>
                      <SelectItem value="PROJECT">Project Review</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="reviewer">Reviewer</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Assign reviewer" />
                    </SelectTrigger>
                    <SelectContent>
                      {state.mentors.map(mentor => (
                        <SelectItem key={mentor.id} value={mentor.id}>
                          {mentor.firstName} {mentor.lastName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="scheduledDate">Scheduled Date</Label>
                  <Input id="scheduledDate" type="date" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea 
                    id="notes" 
                    placeholder="Add any notes or specific areas to review..."
                    rows={3}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setIsCreateDialogOpen(false)}>
                  Schedule Review
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Reviews</CardTitle>
            <Clock className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingReviews.length}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting completion
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <AlertCircle className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{inProgressReviews.length}</div>
            <p className="text-xs text-muted-foreground">
              Currently being reviewed
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedReviews.length}</div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Rating</CardTitle>
            <Star className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4.2/5</div>
            <p className="text-xs text-muted-foreground">
              Across all reviews
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Review Queue</CardTitle>
          <CardDescription>
            Manage and track all performance reviews
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-4">
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-[180px]">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="PENDING">Pending</SelectItem>
                <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                <SelectItem value="COMPLETED">Completed</SelectItem>
                <SelectItem value="CANCELLED">Cancelled</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="MONTHLY">Monthly</SelectItem>
                <SelectItem value="QUARTERLY">Quarterly</SelectItem>
                <SelectItem value="ANNUAL">Annual</SelectItem>
                <SelectItem value="PROBATION">Probation</SelectItem>
                <SelectItem value="PROJECT">Project</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Tabs defaultValue="pending" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="pending">
                Pending ({pendingReviews.length})
              </TabsTrigger>
              <TabsTrigger value="in-progress">
                In Progress ({inProgressReviews.length})
              </TabsTrigger>
              <TabsTrigger value="completed">
                Completed ({completedReviews.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="pending">
              <ScrollArea className="h-[400px]">
                <div className="space-y-3">
                  {pendingReviews.map(review => {
                    const trainee = state.trainees.find(t => t.id === review.traineeId)
                    const reviewer = state.mentors.find(m => m.id === review.reviewerId)
                    const StatusIcon = getStatusIcon(review.status)
                    
                    return (
                      <Card 
                        key={review.id}
                        className="cursor-pointer hover:shadow-md transition-shadow"
                        onClick={() => setSelectedReview(review)}
                      >
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <Avatar className="h-10 w-10">
                                <AvatarFallback>
                                  {trainee?.firstName[0]}{trainee?.lastName[0]}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <p className="font-medium">
                                  {trainee?.firstName} {trainee?.lastName}
                                </p>
                                <p className="text-sm text-muted-foreground">
                                  {review.type} Review
                                </p>
                              </div>
                            </div>
                            <Badge variant={getStatusVariant(review.status)}>
                              <StatusIcon className="mr-1 h-3 w-3" />
                              {review.status}
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <div className="flex items-center justify-between text-sm">
                            <div className="flex items-center gap-2">
                              <User className="h-3 w-3 text-muted-foreground" />
                              <span>Reviewer: {reviewer?.firstName} {reviewer?.lastName}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Calendar className="h-3 w-3 text-muted-foreground" />
                              <span>{new Date(review.scheduledDate).toLocaleDateString()}</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    )
                  })}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="in-progress">
              <ScrollArea className="h-[400px]">
                <div className="space-y-3">
                  {inProgressReviews.map(review => {
                    const trainee = state.trainees.find(t => t.id === review.traineeId)
                    const reviewer = state.mentors.find(m => m.id === review.reviewerId)
                    
                    return (
                      <Card 
                        key={review.id}
                        className="cursor-pointer hover:shadow-md transition-shadow"
                        onClick={() => setSelectedReview(review)}
                      >
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <Avatar className="h-10 w-10">
                                <AvatarFallback>
                                  {trainee?.firstName[0]}{trainee?.lastName[0]}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <p className="font-medium">
                                  {trainee?.firstName} {trainee?.lastName}
                                </p>
                                <p className="text-sm text-muted-foreground">
                                  {review.type} Review
                                </p>
                              </div>
                            </div>
                            <Badge variant={getStatusVariant(review.status)}>
                              In Progress
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <div className="flex items-center justify-between text-sm">
                            <span>Reviewer: {reviewer?.firstName} {reviewer?.lastName}</span>
                            <Button size="sm" variant="outline">
                              Continue Review
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    )
                  })}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="completed">
              <ScrollArea className="h-[400px]">
                <div className="space-y-3">
                  {completedReviews.map(review => {
                    const trainee = state.trainees.find(t => t.id === review.traineeId)
                    
                    return (
                      <Card 
                        key={review.id}
                        className="cursor-pointer hover:shadow-md transition-shadow"
                        onClick={() => setSelectedReview(review)}
                      >
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <Avatar className="h-10 w-10">
                                <AvatarFallback>
                                  {trainee?.firstName[0]}{trainee?.lastName[0]}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <p className="font-medium">
                                  {trainee?.firstName} {trainee?.lastName}
                                </p>
                                <p className="text-sm text-muted-foreground">
                                  {review.type} Review
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="flex">
                                {[...Array(5)].map((_, i) => (
                                  <Star 
                                    key={i} 
                                    className={`h-3 w-3 ${i < review.rating ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'}`}
                                  />
                                ))}
                              </div>
                              <Badge variant="secondary">
                                Completed
                              </Badge>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <p className="text-sm text-muted-foreground line-clamp-2">
                            {review.comments}
                          </p>
                        </CardContent>
                      </Card>
                    )
                  })}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Review Details Dialog */}
      <Dialog open={!!selectedReview} onOpenChange={() => setSelectedReview(null)}>
        <DialogContent className="sm:max-w-[625px]">
          {selectedReview && (() => {
            const trainee = state.trainees.find(t => t.id === selectedReview.traineeId)
            const reviewer = state.mentors.find(m => m.id === selectedReview.reviewerId)
            
            return (
              <>
                <DialogHeader>
                  <DialogTitle>Performance Review</DialogTitle>
                  <DialogDescription>
                    {trainee?.firstName} {trainee?.lastName} - {selectedReview.type} Review
                  </DialogDescription>
                </DialogHeader>

                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Reviewer</p>
                      <p className="text-sm">{reviewer?.firstName} {reviewer?.lastName}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Date</p>
                      <p className="text-sm">
                        {new Date(selectedReview.scheduledDate).toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Status</p>
                      <Badge variant={getStatusVariant(selectedReview.status)}>
                        {selectedReview.status}
                      </Badge>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Overall Rating</p>
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <Star 
                            key={i} 
                            className={`h-4 w-4 ${i < selectedReview.rating ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'}`}
                          />
                        ))}
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {selectedReview.status === "COMPLETED" ? (
                    <div className="space-y-4">
                      <div>
                        <p className="text-sm font-medium mb-2">Competency Ratings</p>
                        <div className="space-y-2">
                          {state.competencies.slice(0, 5).map(comp => (
                            <div key={comp.id} className="flex items-center justify-between">
                              <span className="text-sm">{comp.name}</span>
                              <div className="flex">
                                {[...Array(5)].map((_, i) => (
                                  <Star 
                                    key={i} 
                                    className={`h-3 w-3 ${i < Math.floor(Math.random() * 5) + 1 ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'}`}
                                  />
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                      <div>
                        <p className="text-sm font-medium mb-2">Review Comments</p>
                        <p className="text-sm text-muted-foreground">
                          {selectedReview.comments}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium mb-2">Recommendations</p>
                        <div className="flex flex-wrap gap-2">
                          <Badge variant="outline">Continue current path</Badge>
                          <Badge variant="outline">Additional training recommended</Badge>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="rating">Overall Performance Rating</Label>
                        <RadioGroup defaultValue="4" className="mt-2">
                          {[5, 4, 3, 2, 1].map(rating => (
                            <div key={rating} className="flex items-center space-x-2">
                              <RadioGroupItem value={rating.toString()} id={`rating-${rating}`} />
                              <Label htmlFor={`rating-${rating}`} className="flex items-center gap-2">
                                <div className="flex">
                                  {[...Array(5)].map((_, i) => (
                                    <Star 
                                      key={i} 
                                      className={`h-3 w-3 ${i < rating ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'}`}
                                    />
                                  ))}
                                </div>
                                <span className="text-sm">
                                  {rating === 5 && "Exceptional"}
                                  {rating === 4 && "Exceeds Expectations"}
                                  {rating === 3 && "Meets Expectations"}
                                  {rating === 2 && "Needs Improvement"}
                                  {rating === 1 && "Unsatisfactory"}
                                </span>
                              </Label>
                            </div>
                          ))}
                        </RadioGroup>
                      </div>
                      <div>
                        <Label htmlFor="comments">Review Comments</Label>
                        <Textarea 
                          id="comments"
                          placeholder="Provide detailed feedback on trainee's performance..."
                          rows={4}
                          className="mt-2"
                        />
                      </div>
                      <div>
                        <Label htmlFor="goals">Goals for Next Period</Label>
                        <Textarea 
                          id="goals"
                          placeholder="Set goals and objectives for the next review period..."
                          rows={3}
                          className="mt-2"
                        />
                      </div>
                    </div>
                  )}
                </div>

                <DialogFooter>
                  {selectedReview.status !== "COMPLETED" ? (
                    <>
                      <Button variant="outline" onClick={() => setSelectedReview(null)}>
                        Save Draft
                      </Button>
                      <Button onClick={() => setSelectedReview(null)}>
                        Submit Review
                      </Button>
                    </>
                  ) : (
                    <Button onClick={() => setSelectedReview(null)}>
                      Close
                    </Button>
                  )}
                </DialogFooter>
              </>
            )
          })()}
        </DialogContent>
      </Dialog>
    </div>
  )
}