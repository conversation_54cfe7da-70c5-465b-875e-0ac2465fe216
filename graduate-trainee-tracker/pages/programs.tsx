import * as React from "react"
import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import {
  GraduationCap,
  Calendar,
  Users,
  Clock,
  Target,
  BookOpen,
  Award,
  Plus,
  Edit,
  Trash2,
  ChevronRight,
  FileText,
  Video,
  BarChart3
} from "lucide-react"
import { GraduateTraineeTrackerState, Program } from "../types"

interface ProgramsPageProps {
  state: GraduateTraineeTrackerState
  setState: React.Dispatch<React.SetStateAction<GraduateTraineeTrackerState>>
}

export function ProgramsPage({ state, setState }: ProgramsPageProps) {
  const [selectedProgram, setSelectedProgram] = useState<Program | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)

  const getProgramStatusVariant = (status: string) => {
    switch (status) {
      case "ACTIVE": return "default"
      case "DRAFT": return "secondary"
      case "COMPLETED": return "outline"
      case "ARCHIVED": return "destructive"
      default: return "outline"
    }
  }

  const getProgramTypeIcon = (type: string) => {
    switch (type) {
      case "TECHNICAL": return BookOpen
      case "LEADERSHIP": return Users
      case "SOFT_SKILLS": return Target
      default: return GraduationCap
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Programs</h1>
          <p className="text-muted-foreground">
            Design and manage graduate trainee development programs
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create Program
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[625px]">
            <DialogHeader>
              <DialogTitle>Create New Program</DialogTitle>
              <DialogDescription>
                Design a new training program for graduate trainees
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">Program Name</Label>
                <Input id="name" placeholder="e.g., Data Analytics Fundamentals" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea 
                  id="description" 
                  placeholder="Describe the program objectives and outcomes..."
                  rows={3}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="type">Program Type</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="TECHNICAL">Technical</SelectItem>
                      <SelectItem value="LEADERSHIP">Leadership</SelectItem>
                      <SelectItem value="SOFT_SKILLS">Soft Skills</SelectItem>
                      <SelectItem value="BUSINESS">Business</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="duration">Duration (weeks)</Label>
                  <Input id="duration" type="number" placeholder="12" />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="startDate">Start Date</Label>
                  <Input id="startDate" type="date" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endDate">End Date</Label>
                  <Input id="endDate" type="date" />
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={() => setIsCreateDialogOpen(false)}>
                Create Program
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Program Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Programs</CardTitle>
            <GraduationCap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{state.programs.length}</div>
            <p className="text-xs text-muted-foreground">
              {state.programs.filter(p => p.status === "ACTIVE").length} active
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Enrolled</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {state.trainees.filter(t => t.status === "ACTIVE").length}
            </div>
            <p className="text-xs text-muted-foreground">
              Across all programs
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Duration</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12 weeks</div>
            <p className="text-xs text-muted-foreground">
              Per program
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">87%</div>
            <p className="text-xs text-muted-foreground">
              Last quarter
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Programs Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {state.programs.map((program) => {
          const Icon = getProgramTypeIcon(program.type)
          const enrolledCount = state.trainees.filter(t => t.programId === program.id).length
          
          return (
            <Card 
              key={program.id} 
              className="cursor-pointer hover:shadow-lg transition-shadow"
              onClick={() => setSelectedProgram(program)}
            >
              <CardHeader>
                <div className="flex items-center justify-between">
                  <Icon className="h-5 w-5 text-muted-foreground" />
                  <Badge variant={getProgramStatusVariant(program.status)}>
                    {program.status}
                  </Badge>
                </div>
                <CardTitle className="mt-2">{program.name}</CardTitle>
                <CardDescription className="line-clamp-2">
                  {program.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Duration</span>
                    <span className="font-medium">{program.duration} weeks</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Enrolled</span>
                    <span className="font-medium">{enrolledCount} trainees</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Start Date</span>
                    <span className="font-medium">
                      {new Date(program.startDate).toLocaleDateString()}
                    </span>
                  </div>
                  <Separator />
                  <div className="flex items-center justify-between">
                    <div className="flex -space-x-2">
                      {state.trainees
                        .filter(t => t.programId === program.id)
                        .slice(0, 3)
                        .map((trainee, index) => (
                          <Avatar key={index} className="h-8 w-8 border-2 border-background">
                            <AvatarFallback className="text-xs">
                              {trainee.firstName[0]}{trainee.lastName[0]}
                            </AvatarFallback>
                          </Avatar>
                        ))}
                      {enrolledCount > 3 && (
                        <div className="flex h-8 w-8 items-center justify-center rounded-full border-2 border-background bg-muted">
                          <span className="text-xs">+{enrolledCount - 3}</span>
                        </div>
                      )}
                    </div>
                    <Button variant="ghost" size="sm">
                      View Details
                      <ChevronRight className="ml-1 h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Program Details Dialog */}
      <Dialog open={!!selectedProgram} onOpenChange={() => setSelectedProgram(null)}>
        <DialogContent className="sm:max-w-[725px]">
          {selectedProgram && (
            <>
              <DialogHeader>
                <div className="flex items-center justify-between">
                  <DialogTitle>{selectedProgram.name}</DialogTitle>
                  <Badge variant={getProgramStatusVariant(selectedProgram.status)}>
                    {selectedProgram.status}
                  </Badge>
                </div>
                <DialogDescription>
                  {selectedProgram.description}
                </DialogDescription>
              </DialogHeader>
              
              <Tabs defaultValue="overview" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="overview">Overview</TabsTrigger>
                  <TabsTrigger value="curriculum">Curriculum</TabsTrigger>
                  <TabsTrigger value="trainees">Trainees</TabsTrigger>
                  <TabsTrigger value="analytics">Analytics</TabsTrigger>
                </TabsList>
                
                <TabsContent value="overview" className="space-y-4">
                  <div className="grid gap-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Type</p>
                        <p className="text-sm">{selectedProgram.type}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Duration</p>
                        <p className="text-sm">{selectedProgram.duration} weeks</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Start Date</p>
                        <p className="text-sm">
                          {new Date(selectedProgram.startDate).toLocaleDateString()}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">End Date</p>
                        <p className="text-sm">
                          {new Date(selectedProgram.endDate).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <Separator />
                    <div>
                      <p className="text-sm font-medium mb-2">Required Competencies</p>
                      <div className="flex flex-wrap gap-2">
                        {selectedProgram.requiredCompetencies?.map((comp, index) => (
                          <Badge key={index} variant="secondary">
                            {comp}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="curriculum">
                  <ScrollArea className="h-[400px]">
                    <div className="space-y-4">
                      {selectedProgram.modules?.map((module, index) => (
                        <Card key={index}>
                          <CardHeader>
                            <div className="flex items-center justify-between">
                              <CardTitle className="text-base">
                                Week {index + 1}: {module.name}
                              </CardTitle>
                              <Badge variant="outline">
                                {module.duration} hours
                              </Badge>
                            </div>
                          </CardHeader>
                          <CardContent>
                            <p className="text-sm text-muted-foreground mb-3">
                              {module.description}
                            </p>
                            <div className="flex gap-4">
                              <div className="flex items-center gap-1">
                                <FileText className="h-3 w-3" />
                                <span className="text-xs">5 lessons</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Video className="h-3 w-3" />
                                <span className="text-xs">3 videos</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Target className="h-3 w-3" />
                                <span className="text-xs">2 assessments</span>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </ScrollArea>
                </TabsContent>
                
                <TabsContent value="trainees">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium">
                        {state.trainees.filter(t => t.programId === selectedProgram.id).length} Enrolled Trainees
                      </p>
                      <Button size="sm">
                        <Plus className="mr-2 h-3 w-3" />
                        Add Trainee
                      </Button>
                    </div>
                    <ScrollArea className="h-[350px]">
                      <div className="space-y-2">
                        {state.trainees
                          .filter(t => t.programId === selectedProgram.id)
                          .map(trainee => (
                            <div key={trainee.id} className="flex items-center justify-between p-3 rounded-lg border">
                              <div className="flex items-center gap-3">
                                <Avatar className="h-8 w-8">
                                  <AvatarFallback className="text-xs">
                                    {trainee.firstName[0]}{trainee.lastName[0]}
                                  </AvatarFallback>
                                </Avatar>
                                <div>
                                  <p className="text-sm font-medium">
                                    {trainee.firstName} {trainee.lastName}
                                  </p>
                                  <p className="text-xs text-muted-foreground">
                                    {trainee.department}
                                  </p>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Progress value={trainee.progress || 0} className="w-20" />
                                <span className="text-sm">{trainee.progress || 0}%</span>
                              </div>
                            </div>
                          ))}
                      </div>
                    </ScrollArea>
                  </div>
                </TabsContent>
                
                <TabsContent value="analytics">
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm">Completion Rate</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">87%</div>
                          <Progress value={87} className="mt-2" />
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm">Avg Score</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">4.2/5</div>
                          <Progress value={84} className="mt-2" />
                        </CardContent>
                      </Card>
                    </div>
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-sm">Progress by Module</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          {selectedProgram.modules?.slice(0, 4).map((module, index) => (
                            <div key={index} className="flex items-center justify-between">
                              <span className="text-sm">{module.name}</span>
                              <div className="flex items-center gap-2">
                                <Progress value={Math.floor(Math.random() * 100)} className="w-24" />
                                <span className="text-xs text-muted-foreground">
                                  {Math.floor(Math.random() * 100)}%
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>
              </Tabs>
              
              <DialogFooter>
                <Button variant="outline" onClick={() => setSelectedProgram(null)}>
                  Close
                </Button>
                <Button>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Program
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}