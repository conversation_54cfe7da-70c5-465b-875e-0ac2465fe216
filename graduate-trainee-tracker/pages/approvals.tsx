import * as React from "react"
import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  FileCheck,
  Users,
  Calendar,
  DollarSign,
  User,
  Briefcase,
  Filter,
  MessageSquare,
  ChevronRight,
  Info
} from "lucide-react"
import { GraduateTraineeTrackerState, WorkflowItem } from "../types"

interface ApprovalsPageProps {
  state: GraduateTraineeTrackerState
  setState: React.Dispatch<React.SetStateAction<GraduateTraineeTrackerState>>
}

export function ApprovalsPage({ state, setState }: ApprovalsPageProps) {
  const [selectedWorkflow, setSelectedWorkflow] = useState<WorkflowItem | null>(null)
  const [filterType, setFilterType] = useState<string>("all")
  const [approvalComment, setApprovalComment] = useState("")

  const filteredWorkflows = state.workflows.filter(workflow => {
    if (filterType === "all") return true
    return workflow.type === filterType
  })

  const pendingApprovals = filteredWorkflows.filter(w => w.status === "PENDING")
  const approvedItems = filteredWorkflows.filter(w => w.status === "APPROVED")
  const rejectedItems = filteredWorkflows.filter(w => w.status === "REJECTED")

  const getWorkflowIcon = (type: string) => {
    switch (type) {
      case "ENROLLMENT": return Users
      case "EXTENSION": return Calendar
      case "BUDGET": return DollarSign
      case "RESOURCE": return Briefcase
      default: return FileCheck
    }
  }

  const getStatusVariant = (status: string) => {
    switch (status) {
      case "APPROVED": return "secondary"
      case "PENDING": return "default"
      case "REJECTED": return "destructive"
      default: return "outline"
    }
  }

  const handleApproval = (workflow: WorkflowItem, approved: boolean) => {
    setState(prev => ({
      ...prev,
      workflows: prev.workflows.map(w =>
        w.id === workflow.id
          ? { ...w, status: approved ? "APPROVED" : "REJECTED" }
          : w
      )
    }))
    setSelectedWorkflow(null)
    setApprovalComment("")
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Approvals</h1>
          <p className="text-muted-foreground">
            Review and approve pending requests for the graduate trainee program
          </p>
        </div>
        <Select value={filterType} onValueChange={setFilterType}>
          <SelectTrigger className="w-[180px]">
            <Filter className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Filter by type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="ENROLLMENT">Enrollment</SelectItem>
            <SelectItem value="EXTENSION">Extension</SelectItem>
            <SelectItem value="BUDGET">Budget</SelectItem>
            <SelectItem value="RESOURCE">Resource</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingApprovals.length}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting review
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{approvedItems.length}</div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rejected</CardTitle>
            <XCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{rejectedItems.length}</div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Time</CardTitle>
            <AlertTriangle className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2.5 days</div>
            <p className="text-xs text-muted-foreground">
              To approve
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Alert for urgent items */}
      {pendingApprovals.some(w => w.priority === "HIGH") && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Urgent Approvals Required</AlertTitle>
          <AlertDescription>
            You have {pendingApprovals.filter(w => w.priority === "HIGH").length} high-priority 
            items requiring immediate attention.
          </AlertDescription>
        </Alert>
      )}

      {/* Approval Tabs */}
      <Card>
        <CardHeader>
          <CardTitle>Approval Queue</CardTitle>
          <CardDescription>
            Review and process approval requests
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="pending" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="pending">
                Pending ({pendingApprovals.length})
              </TabsTrigger>
              <TabsTrigger value="approved">
                Approved ({approvedItems.length})
              </TabsTrigger>
              <TabsTrigger value="rejected">
                Rejected ({rejectedItems.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="pending">
              <ScrollArea className="h-[500px]">
                <div className="space-y-4">
                  {pendingApprovals.map(workflow => {
                    const Icon = getWorkflowIcon(workflow.type)
                    const requester = state.trainees.find(t => t.id === workflow.requesterId) ||
                                    state.mentors.find(m => m.id === workflow.requesterId)
                    
                    return (
                      <Card key={workflow.id}>
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="rounded-full bg-muted p-2">
                                <Icon className="h-4 w-4" />
                              </div>
                              <div>
                                <CardTitle className="text-base">
                                  {workflow.title}
                                </CardTitle>
                                <CardDescription>
                                  Requested by {requester?.firstName} {requester?.lastName}
                                </CardDescription>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {workflow.priority === "HIGH" && (
                                <Badge variant="destructive">Urgent</Badge>
                              )}
                              <Badge variant={getStatusVariant(workflow.status)}>
                                {workflow.status}
                              </Badge>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            <p className="text-sm text-muted-foreground">
                              {workflow.description}
                            </p>
                            <div className="flex items-center justify-between text-sm">
                              <div className="flex items-center gap-4">
                                <div className="flex items-center gap-1">
                                  <Calendar className="h-3 w-3 text-muted-foreground" />
                                  <span>
                                    {new Date(workflow.createdAt).toLocaleDateString()}
                                  </span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <User className="h-3 w-3 text-muted-foreground" />
                                  <span>{workflow.type}</span>
                                </div>
                              </div>
                              <Button 
                                size="sm"
                                onClick={() => setSelectedWorkflow(workflow)}
                              >
                                Review
                                <ChevronRight className="ml-1 h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    )
                  })}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="approved">
              <ScrollArea className="h-[500px]">
                <div className="space-y-4">
                  {approvedItems.map(workflow => {
                    const Icon = getWorkflowIcon(workflow.type)
                    const requester = state.trainees.find(t => t.id === workflow.requesterId) ||
                                    state.mentors.find(m => m.id === workflow.requesterId)
                    const approver = state.mentors.find(m => m.id === workflow.approverId)
                    
                    return (
                      <Card key={workflow.id}>
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <CheckCircle className="h-5 w-5 text-green-500" />
                              <div>
                                <CardTitle className="text-base">
                                  {workflow.title}
                                </CardTitle>
                                <CardDescription>
                                  Approved by {approver?.firstName} {approver?.lastName}
                                </CardDescription>
                              </div>
                            </div>
                            <Badge variant="secondary">
                              Approved
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="flex items-center justify-between text-sm text-muted-foreground">
                            <span>Requested by {requester?.firstName} {requester?.lastName}</span>
                            <span>{new Date(workflow.completedAt || workflow.createdAt).toLocaleDateString()}</span>
                          </div>
                        </CardContent>
                      </Card>
                    )
                  })}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="rejected">
              <ScrollArea className="h-[500px]">
                <div className="space-y-4">
                  {rejectedItems.map(workflow => {
                    const requester = state.trainees.find(t => t.id === workflow.requesterId) ||
                                    state.mentors.find(m => m.id === workflow.requesterId)
                    const approver = state.mentors.find(m => m.id === workflow.approverId)
                    
                    return (
                      <Card key={workflow.id}>
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <XCircle className="h-5 w-5 text-red-500" />
                              <div>
                                <CardTitle className="text-base">
                                  {workflow.title}
                                </CardTitle>
                                <CardDescription>
                                  Rejected by {approver?.firstName} {approver?.lastName}
                                </CardDescription>
                              </div>
                            </div>
                            <Badge variant="destructive">
                              Rejected
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            <div className="flex items-center justify-between text-sm text-muted-foreground">
                              <span>Requested by {requester?.firstName} {requester?.lastName}</span>
                              <span>{new Date(workflow.completedAt || workflow.createdAt).toLocaleDateString()}</span>
                            </div>
                            {workflow.comments && (
                              <div className="rounded-md bg-muted p-2">
                                <p className="text-sm">
                                  <MessageSquare className="inline h-3 w-3 mr-1" />
                                  {workflow.comments}
                                </p>
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    )
                  })}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Approval Dialog */}
      <Dialog open={!!selectedWorkflow} onOpenChange={() => setSelectedWorkflow(null)}>
        <DialogContent className="sm:max-w-[625px]">
          {selectedWorkflow && (() => {
            const requester = state.trainees.find(t => t.id === selectedWorkflow.requesterId) ||
                            state.mentors.find(m => m.id === selectedWorkflow.requesterId)
            const Icon = getWorkflowIcon(selectedWorkflow.type)
            
            return (
              <>
                <DialogHeader>
                  <div className="flex items-center gap-3">
                    <div className="rounded-full bg-muted p-2">
                      <Icon className="h-5 w-5" />
                    </div>
                    <div>
                      <DialogTitle>{selectedWorkflow.title}</DialogTitle>
                      <DialogDescription>
                        Review and approve or reject this request
                      </DialogDescription>
                    </div>
                  </div>
                </DialogHeader>

                <div className="space-y-4">
                  <div className="rounded-lg bg-muted p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Info className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Request Details</span>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Type:</span>
                        <span>{selectedWorkflow.type}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Requester:</span>
                        <span>{requester?.firstName} {requester?.lastName}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Date:</span>
                        <span>{new Date(selectedWorkflow.createdAt).toLocaleDateString()}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Priority:</span>
                        <Badge variant={selectedWorkflow.priority === "HIGH" ? "destructive" : "outline"}>
                          {selectedWorkflow.priority}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm font-medium mb-2">Description</p>
                    <p className="text-sm text-muted-foreground">
                      {selectedWorkflow.description}
                    </p>
                  </div>

                  {selectedWorkflow.type === "BUDGET" && (
                    <div>
                      <p className="text-sm font-medium mb-2">Budget Details</p>
                      <div className="rounded-lg border p-3">
                        <div className="flex justify-between text-sm">
                          <span>Requested Amount:</span>
                          <span className="font-medium">$5,000</span>
                        </div>
                        <div className="flex justify-between text-sm mt-2">
                          <span>Purpose:</span>
                          <span>Training Materials</span>
                        </div>
                      </div>
                    </div>
                  )}

                  {selectedWorkflow.type === "EXTENSION" && (
                    <div>
                      <p className="text-sm font-medium mb-2">Extension Details</p>
                      <div className="rounded-lg border p-3">
                        <div className="flex justify-between text-sm">
                          <span>Current End Date:</span>
                          <span>{new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString()}</span>
                        </div>
                        <div className="flex justify-between text-sm mt-2">
                          <span>Requested Extension:</span>
                          <span>3 months</span>
                        </div>
                      </div>
                    </div>
                  )}

                  <div>
                    <Label htmlFor="comments">Comments (Optional)</Label>
                    <Textarea
                      id="comments"
                      placeholder="Add any comments about this approval decision..."
                      value={approvalComment}
                      onChange={(e) => setApprovalComment(e.target.value)}
                      rows={3}
                      className="mt-2"
                    />
                  </div>
                </div>

                <DialogFooter>
                  <Button 
                    variant="outline" 
                    onClick={() => setSelectedWorkflow(null)}
                  >
                    Cancel
                  </Button>
                  <Button 
                    variant="destructive"
                    onClick={() => handleApproval(selectedWorkflow, false)}
                  >
                    <XCircle className="mr-2 h-4 w-4" />
                    Reject
                  </Button>
                  <Button 
                    onClick={() => handleApproval(selectedWorkflow, true)}
                  >
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Approve
                  </Button>
                </DialogFooter>
              </>
            )
          })()}
        </DialogContent>
      </Dialog>
    </div>
  )
}