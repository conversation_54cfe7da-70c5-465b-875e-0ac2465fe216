// Mock API Integration Tests
import { describe, it, expect, beforeEach } from 'vitest';
import {
  fetchTrainees,
  fetchPrograms,
  fetchReviews,
  fetchWorkflows,
  fetchNotifications,
  fetchCompetencies,
  fetchUsers,
  fetchCompleteState,
  createTrainee,
  updateTrainee,
  createProgram,
  updateProgram,
  createReview,
  updateReview,
  markNotificationAsRead,
  submitApprovalDecision
} from '../mockApi';
import { TraineeStatus, ProgramStatus, ReviewStatus, ReviewType } from '../types';

describe('Mock API Integration', () => {
  describe('Data Fetching', () => {
    it('should fetch trainees successfully', async () => {
      const trainees = await fetchTrainees();
      expect(trainees).toBeDefined();
      expect(Array.isArray(trainees)).toBe(true);
      expect(trainees.length).toBeGreaterThan(0);
      
      const trainee = trainees[0];
      expect(trainee).toHaveProperty('id');
      expect(trainee).toHaveProperty('firstName');
      expect(trainee).toHaveProperty('lastName');
      expect(trainee).toHaveProperty('email');
      expect(trainee).toHaveProperty('status');
      expect(trainee).toHaveProperty('skillsMatrix');
      expect(trainee).toHaveProperty('goals');
    });

    it('should fetch programs successfully', async () => {
      const programs = await fetchPrograms();
      expect(programs).toBeDefined();
      expect(Array.isArray(programs)).toBe(true);
      expect(programs.length).toBeGreaterThan(0);
      
      const program = programs[0];
      expect(program).toHaveProperty('id');
      expect(program).toHaveProperty('title');
      expect(program).toHaveProperty('description');
      expect(program).toHaveProperty('competencies');
      expect(program).toHaveProperty('milestones');
    });

    it('should fetch reviews successfully', async () => {
      const reviews = await fetchReviews();
      expect(reviews).toBeDefined();
      expect(Array.isArray(reviews)).toBe(true);
      expect(reviews.length).toBeGreaterThan(0);
      
      const review = reviews[0];
      expect(review).toHaveProperty('id');
      expect(review).toHaveProperty('traineeId');
      expect(review).toHaveProperty('type');
      expect(review).toHaveProperty('status');
      expect(review).toHaveProperty('overallRating');
    });

    it('should fetch workflows successfully', async () => {
      const workflows = await fetchWorkflows();
      expect(workflows).toBeDefined();
      expect(Array.isArray(workflows)).toBe(true);
      expect(workflows.length).toBeGreaterThan(0);
      
      const workflow = workflows[0];
      expect(workflow).toHaveProperty('id');
      expect(workflow).toHaveProperty('programId');
      expect(workflow).toHaveProperty('stages');
      expect(workflow).toHaveProperty('status');
    });

    it('should fetch notifications successfully', async () => {
      const notifications = await fetchNotifications();
      expect(notifications).toBeDefined();
      expect(Array.isArray(notifications)).toBe(true);
      expect(notifications.length).toBeGreaterThan(0);
      
      const notification = notifications[0];
      expect(notification).toHaveProperty('id');
      expect(notification).toHaveProperty('type');
      expect(notification).toHaveProperty('title');
      expect(notification).toHaveProperty('priority');
    });

    it('should fetch competencies successfully', async () => {
      const competencies = await fetchCompetencies();
      expect(competencies).toBeDefined();
      expect(Array.isArray(competencies)).toBe(true);
      expect(competencies.length).toBeGreaterThan(0);
      
      const competency = competencies[0];
      expect(competency).toHaveProperty('id');
      expect(competency).toHaveProperty('name');
      expect(competency).toHaveProperty('category');
      expect(competency).toHaveProperty('level');
    });

    it('should fetch users successfully', async () => {
      const users = await fetchUsers();
      expect(users).toBeDefined();
      expect(Array.isArray(users)).toBe(true);
      expect(users.length).toBeGreaterThan(0);
      
      const user = users[0];
      expect(user).toHaveProperty('id');
      expect(user).toHaveProperty('firstName');
      expect(user).toHaveProperty('lastName');
      expect(user).toHaveProperty('role');
    });

    it('should fetch complete state successfully', async () => {
      const state = await fetchCompleteState();
      expect(state).toBeDefined();
      expect(state).toHaveProperty('currentUser');
      expect(state).toHaveProperty('trainees');
      expect(state).toHaveProperty('programs');
      expect(state).toHaveProperty('reviews');
      expect(state).toHaveProperty('workflows');
      expect(state).toHaveProperty('notifications');
      expect(state).toHaveProperty('competencies');
      expect(state).toHaveProperty('loading');
      expect(state).toHaveProperty('error');
    });
  });

  describe('Data Creation and Updates', () => {
    it('should create a new trainee', async () => {
      const newTraineeData = {
        employeeId: 'GT2024004',
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        department: 'Engineering',
        startDate: '2024-08-01',
        endDate: '2025-08-01',
        programId: 'prog-001',
        mentorId: 'user-002',
        status: TraineeStatus.PENDING,
        currentStage: 1,
        skillsMatrix: [],
        goals: []
      };

      const createdTrainee = await createTrainee(newTraineeData);
      expect(createdTrainee).toBeDefined();
      expect(createdTrainee.id).toBeDefined();
      expect(createdTrainee.firstName).toBe('Test');
      expect(createdTrainee.lastName).toBe('User');
      expect(createdTrainee.createdAt).toBeDefined();
      expect(createdTrainee.updatedAt).toBeDefined();
    });

    it('should update a trainee', async () => {
      const trainees = await fetchTrainees();
      const traineeToUpdate = trainees[0];
      
      const updatedTrainee = await updateTrainee(traineeToUpdate.id, {
        status: TraineeStatus.COMPLETED,
        currentStage: 3
      });
      
      expect(updatedTrainee).toBeDefined();
      expect(updatedTrainee.id).toBe(traineeToUpdate.id);
      expect(updatedTrainee.status).toBe('completed');
      expect(updatedTrainee.currentStage).toBe(3);
      expect(updatedTrainee.updatedAt).not.toBe(traineeToUpdate.updatedAt);
    });

    it('should create a new program', async () => {
      const newProgramData = {
        title: 'Test Program',
        description: 'A test program for testing',
        duration: 6,
        objectives: ['Test objective 1', 'Test objective 2'],
        competencies: [],
        milestones: [],
        resources: [],
        status: ProgramStatus.DRAFT,
        approvalWorkflow: [],
        createdBy: 'user-001'
      };

      const createdProgram = await createProgram(newProgramData);
      expect(createdProgram).toBeDefined();
      expect(createdProgram.id).toBeDefined();
      expect(createdProgram.title).toBe('Test Program');
      expect(createdProgram.createdAt).toBeDefined();
      expect(createdProgram.updatedAt).toBeDefined();
    });

    it('should create a new review', async () => {
      const newReviewData = {
        traineeId: 'trainee-001',
        reviewerId: 'user-002',
        type: ReviewType.QUARTERLY,
        quarter: 2,
        year: 2024,
        reviewPeriod: {
          quarter: 2,
          year: 2024,
          startDate: '2024-09-01',
          endDate: '2024-11-30'
        },
        scheduledDate: '2024-12-15',
        dueDate: '2024-12-30',
        status: ReviewStatus.SCHEDULED,
        overallRating: 0,
        feedback: [],
        actionItems: [],
        nextReviewDate: '2025-03-15'
      };

      const createdReview = await createReview(newReviewData);
      expect(createdReview).toBeDefined();
      expect(createdReview.id).toBeDefined();
      expect(createdReview.quarter).toBe(2);
      expect(createdReview.createdAt).toBeDefined();
      expect(createdReview.updatedAt).toBeDefined();
    });
  });

  describe('Notification Management', () => {
    it('should mark notification as read', async () => {
      const notifications = await fetchNotifications();
      const notificationToMark = notifications.find(n => !n.isRead);
      
      if (notificationToMark) {
        await markNotificationAsRead(notificationToMark.id);
        
        const updatedNotifications = await fetchNotifications();
        const updatedNotification = updatedNotifications.find(n => n.id === notificationToMark.id);
        
        expect(updatedNotification).toBeDefined();
        expect(updatedNotification?.isRead).toBe(true);
        expect(updatedNotification?.read).toBe(true);
      }
    });
  });

  describe('Approval Workflow', () => {
    it('should submit approval decision', async () => {
      const workflows = await fetchWorkflows();
      const workflow = workflows[0];
      const stage = workflow.stages[0];
      
      await submitApprovalDecision(workflow.id, stage.id, 'approve', 'Approved for next stage');
      
      const updatedWorkflows = await fetchWorkflows();
      const updatedWorkflow = updatedWorkflows.find(w => w.id === workflow.id);
      const updatedStage = updatedWorkflow?.stages.find(s => s.id === stage.id);
      
      expect(updatedStage).toBeDefined();
      expect(updatedStage?.status).toBe('approved');
      expect(updatedStage?.comments).toBe('Approved for next stage');
      expect(updatedStage?.approvedAt).toBeDefined();
    });
  });

  describe('User-specific Notifications', () => {
    it('should fetch notifications for specific user', async () => {
      const userId = 'user-001';
      const notifications = await fetchNotifications(userId);
      
      expect(notifications).toBeDefined();
      expect(Array.isArray(notifications)).toBe(true);
      
      // All notifications should be for the specified user
      notifications.forEach(notification => {
        expect(notification.recipientId).toBe(userId);
      });
    });
  });
});