import React, { ReactElement } from 'react';
import { render, RenderOptions, RenderResult } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { vi } from 'vitest';
import { ApiProvider } from '../components/integration';
import { ErrorBoundary } from '../components/integration';

// Mock data generators
export const mockTrainee = {
  id: '1',
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '+**********',
  dateOfBirth: '1995-01-01',
  address: '123 Main St, City, State 12345',
  emergencyContact: {
    name: '<PERSON>',
    relationship: 'Spouse',
    phone: '+**********',
  },
  programId: 'prog-1',
  startDate: '2024-01-01',
  expectedEndDate: '2024-12-31',
  status: 'active' as const,
  progress: 65,
  currentPhase: 'Development',
  mentor: {
    id: 'mentor-1',
    name: '<PERSON>',
    email: '<EMAIL>',
  },
  skills: [
    { id: 'skill-1', name: 'JavaScript', level: 'intermediate', progress: 75 },
    { id: 'skill-2', name: 'React', level: 'beginner', progress: 45 },
  ],
  assessments: [
    {
      id: 'assess-1',
      title: 'Mid-term Review',
      type: 'performance',
      date: '2024-06-15',
      score: 85,
      status: 'completed',
    },
  ],
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-06-01T00:00:00Z',
};

export const mockProgram = {
  id: 'prog-1',
  title: 'Software Development Graduate Program',
  description: 'Comprehensive 12-month program for software development',
  duration: 12,
  maxTrainees: 20,
  currentTrainees: 15,
  startDate: '2024-01-01',
  endDate: '2024-12-31',
  status: 'active' as const,
  phases: [
    {
      id: 'phase-1',
      name: 'Foundation',
      duration: 3,
      description: 'Basic programming concepts',
      order: 1,
    },
    {
      id: 'phase-2',
      name: 'Development',
      duration: 6,
      description: 'Advanced development skills',
      order: 2,
    },
    {
      id: 'phase-3',
      name: 'Specialization',
      duration: 3,
      description: 'Specialized technology focus',
      order: 3,
    },
  ],
  competencies: [
    {
      id: 'comp-1',
      name: 'Programming Fundamentals',
      description: 'Basic programming concepts and practices',
      category: 'Technical',
      weight: 30,
    },
    {
      id: 'comp-2',
      name: 'Problem Solving',
      description: 'Analytical and problem-solving skills',
      category: 'Soft Skills',
      weight: 25,
    },
  ],
  createdAt: '2023-12-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
};

export const mockReview = {
  id: 'review-1',
  traineeId: '1',
  reviewerId: 'reviewer-1',
  type: 'quarterly' as const,
  period: 'Q2 2024',
  scheduledDate: '2024-06-15',
  completedDate: '2024-06-15',
  status: 'completed' as const,
  overallRating: 4.2,
  feedback: 'Excellent progress in technical skills. Needs improvement in communication.',
  competencyRatings: [
    {
      competencyId: 'comp-1',
      rating: 4.5,
      feedback: 'Strong technical foundation',
    },
    {
      competencyId: 'comp-2',
      rating: 3.8,
      feedback: 'Good problem-solving approach',
    },
  ],
  actionItems: [
    {
      id: 'action-1',
      description: 'Improve presentation skills',
      dueDate: '2024-08-15',
      status: 'pending',
    },
  ],
  createdAt: '2024-06-01T00:00:00Z',
  updatedAt: '2024-06-15T00:00:00Z',
};

export const mockAssessment = {
  id: 'assess-1',
  traineeId: '1',
  title: 'Technical Assessment - JavaScript',
  description: 'Comprehensive JavaScript knowledge assessment',
  type: 'technical' as const,
  scheduledDate: '2024-07-01',
  completedDate: '2024-07-01',
  status: 'completed' as const,
  totalScore: 85,
  maxScore: 100,
  passingScore: 70,
  sections: [
    {
      id: 'section-1',
      name: 'Fundamentals',
      score: 90,
      maxScore: 100,
      questions: 20,
    },
    {
      id: 'section-2',
      name: 'Advanced Concepts',
      score: 80,
      maxScore: 100,
      questions: 15,
    },
  ],
  feedback: 'Strong performance overall. Focus on advanced async patterns.',
  createdAt: '2024-06-15T00:00:00Z',
  updatedAt: '2024-07-01T00:00:00Z',
};

export const mockWorkflow = {
  id: 'workflow-1',
  name: 'Trainee Onboarding',
  description: 'Standard onboarding process for new trainees',
  type: 'onboarding' as const,
  status: 'active' as const,
  steps: [
    {
      id: 'step-1',
      name: 'Document Collection',
      description: 'Collect required documents',
      order: 1,
      required: true,
      estimatedDuration: 2,
    },
    {
      id: 'step-2',
      name: 'Initial Assessment',
      description: 'Conduct initial skills assessment',
      order: 2,
      required: true,
      estimatedDuration: 4,
    },
  ],
  triggers: [
    {
      id: 'trigger-1',
      event: 'trainee_created',
      condition: 'status === "pending"',
    },
  ],
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
};

// Test wrapper components
interface AllTheProvidersProps {
  children: React.ReactNode;
}

const AllTheProviders: React.FC<AllTheProvidersProps> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });

  const mockApiConfig = {
    baseUrl: 'http://localhost:3000/api',
    timeout: 5000,
    retryAttempts: 1,
    retryDelay: 100,
    headers: {
      'Content-Type': 'application/json',
    },
  };

  return (
    <ErrorBoundary>
      <BrowserRouter>
        <QueryClientProvider client={queryClient}>
          <ApiProvider config={mockApiConfig}>
            {children}
          </ApiProvider>
        </QueryClientProvider>
      </BrowserRouter>
    </ErrorBoundary>
  );
};

// Custom render function
const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
): RenderResult => render(ui, { wrapper: AllTheProviders, ...options });

// Mock API responses
export const mockApiResponses = {
  trainees: {
    success: true,
    data: [mockTrainee],
    meta: {
      pagination: {
        page: 1,
        limit: 10,
        total: 1,
        totalPages: 1,
      },
    },
  },
  programs: {
    success: true,
    data: [mockProgram],
    meta: {
      pagination: {
        page: 1,
        limit: 10,
        total: 1,
        totalPages: 1,
      },
    },
  },
  reviews: {
    success: true,
    data: [mockReview],
  },
  assessments: {
    success: true,
    data: [mockAssessment],
  },
  workflows: {
    success: true,
    data: [mockWorkflow],
  },
};

// Mock handlers for MSW (Mock Service Worker)
export const mockHandlers = [
  // Add MSW handlers here when needed
];

// Utility functions for testing
export const waitForLoadingToFinish = () => {
  return new Promise((resolve) => {
    setTimeout(resolve, 100);
  });
};

export const createMockIntersectionObserver = () => {
  const mockIntersectionObserver = vi.fn();
  mockIntersectionObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  });
  window.IntersectionObserver = mockIntersectionObserver;
};

export const createMockResizeObserver = () => {
  const mockResizeObserver = vi.fn();
  mockResizeObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  });
  window.ResizeObserver = mockResizeObserver;
};

export const mockLocalStorage = () => {
  const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
  };
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
  });
  return localStorageMock;
};

export const mockSessionStorage = () => {
  const sessionStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
  };
  Object.defineProperty(window, 'sessionStorage', {
    value: sessionStorageMock,
  });
  return sessionStorageMock;
};

// Export everything
export * from '@testing-library/react';
export { customRender as render };
export { AllTheProviders };