import { describe, it, expect, vi } from 'vitest';
import { renderHook } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import { mockTrainee } from '../test-utils';

// Simple mock hook for testing purposes
const useTrainees = (params?: any) => {
  return {
    data: [mockTrainee()],
    isLoading: false,
    error: null,
    refetch: vi.fn(),
  };
};

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) =>
    React.createElement(QueryClientProvider, { client: queryClient }, children);
};

describe('useTrainees (mock)', () => {
  it('returns trainee data', () => {
    const { result } = renderHook(() => useTrainees(), {
      wrapper: createWrapper(),
    });

    expect(result.current.data).toHaveLength(1);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
    expect(typeof result.current.refetch).toBe('function');
  });

  it('accepts parameters', () => {
    const params = { search: 'test', page: 1 };
    const { result } = renderHook(() => useTrainees(params), {
      wrapper: createWrapper(),
    });

    expect(result.current.data).toBeDefined();
  });
});