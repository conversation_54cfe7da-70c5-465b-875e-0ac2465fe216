import React from 'react';
import { describe, it, expect, vi } from 'vitest';
import { screen, fireEvent } from '@testing-library/react';
import { TraineeCard } from '../../components/trainee/TraineeCard';
import { render, mockTrainee } from '../test-utils';

describe('TraineeCard', () => {
  const mockOnEdit = vi.fn();
  const mockOnDelete = vi.fn();
  const mockOnView = vi.fn();

  const defaultProps = {
    trainee: mockTrainee(),
    onEdit: mockOnEdit,
    onDelete: mockOnDelete,
    onView: mockOnView,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders trainee information correctly', () => {
    const trainee = mockTrainee({
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      program: 'Software Engineering',
      status: 'active',
    });

    render(<TraineeCard {...defaultProps} trainee={trainee} />);

    expect(screen.getByText('<PERSON> Do<PERSON>')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Software Engineering')).toBeInTheDocument();
    expect(screen.getByText('Active')).toBeInTheDocument();
  });

  it('calls onView when card is clicked', () => {
    const trainee = mockTrainee();
    render(<TraineeCard {...defaultProps} trainee={trainee} />);

    const card = screen.getByRole('button', { name: /view trainee/i });
    fireEvent.click(card);

    expect(mockOnView).toHaveBeenCalledWith(trainee.id);
  });

  it('calls onEdit when edit button is clicked', () => {
    const trainee = mockTrainee();
    render(<TraineeCard {...defaultProps} trainee={trainee} />);

    const editButton = screen.getByRole('button', { name: /edit/i });
    fireEvent.click(editButton);

    expect(mockOnEdit).toHaveBeenCalledWith(trainee.id);
  });

  it('calls onDelete when delete button is clicked', () => {
    const trainee = mockTrainee();
    render(<TraineeCard {...defaultProps} trainee={trainee} />);

    const deleteButton = screen.getByRole('button', { name: /delete/i });
    fireEvent.click(deleteButton);

    expect(mockOnDelete).toHaveBeenCalledWith(trainee.id);
  });

  it('displays correct status badge color', () => {
    const activeTrainee = mockTrainee({ status: 'active' });
    const { rerender } = render(<TraineeCard {...defaultProps} trainee={activeTrainee} />);

    expect(screen.getByText('Active')).toHaveClass('bg-green-100');

    const inactiveTrainee = mockTrainee({ status: 'inactive' });
    rerender(<TraineeCard {...defaultProps} trainee={inactiveTrainee} />);

    expect(screen.getByText('Inactive')).toHaveClass('bg-red-100');
  });

  it('shows progress information when available', () => {
    const trainee = mockTrainee({
      progress: {
        completedModules: 5,
        totalModules: 10,
        percentage: 50,
      },
    });

    render(<TraineeCard {...defaultProps} trainee={trainee} />);

    expect(screen.getByText('5/10 modules completed')).toBeInTheDocument();
    expect(screen.getByText('50%')).toBeInTheDocument();
  });

  it('handles missing optional data gracefully', () => {
    const minimalTrainee = mockTrainee({
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      program: undefined,
      progress: undefined,
    });

    render(<TraineeCard {...defaultProps} trainee={minimalTrainee} />);

    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.queryByText(/modules completed/)).not.toBeInTheDocument();
  });
});