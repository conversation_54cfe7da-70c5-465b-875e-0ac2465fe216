import { useCallback, useState } from 'react';
import { useToast } from './useToast';

// Export Format Type
type ExportFormat = 'csv' | 'json' | 'txt' | 'xlsx';

// Export Options Interface
interface ExportOptions {
  includeMetadata?: boolean;
  includeAttribution?: boolean;
  includeComments?: boolean;
  format?: ExportFormat;
  filename?: string;
  quality?: number; // For image exports (0-1)
  scale?: number; // For image scaling
}

// Export Result Interface
interface ExportResult {
  success: boolean;
  url?: string;
  blob?: Blob;
  error?: string;
}

// Hook Return Type
interface UseExportReturn {
  exportToFile: (data: any, filename: string, format: ExportFormat) => Promise<ExportResult>;
  copyToClipboard: (content: string, type?: string) => Promise<boolean>;
  exportData: (data: any, options: ExportOptions) => Promise<ExportResult>;
  isExporting: boolean;
  exportFormats: ExportFormat[];
}

/**
 * Hook for data export functionality
 */
export const useExport = (): UseExportReturn => {
  const { success, error } = useToast();
  const [isExporting, setIsExporting] = useState(false);

  // Available export formats
  const exportFormats: ExportFormat[] = ['csv', 'json', 'txt', 'xlsx'];

  /**
   * Convert data to CSV format
   */
  const convertToCSV = (data: any[]): string => {
    if (!data || data.length === 0) return '';
    
    // Get headers from first object
    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header];
          // Escape commas and quotes in values
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        }).join(',')
      )
    ];
    
    return csvContent.join('\n');
  };

  /**
   * Convert data to JSON format
   */
  const convertToJSON = (data: any): string => {
    return JSON.stringify(data, null, 2);
  };

  /**
   * Convert data to plain text format
   */
  const convertToText = (data: any): string => {
    if (typeof data === 'string') return data;
    if (Array.isArray(data)) return data.join('\n');
    if (typeof data === 'object') return JSON.stringify(data, null, 2);
    return String(data);
  };

  /**
   * Export data to a file
   */
  const exportToFile = useCallback(async (
    data: any,
    filename: string,
    format: ExportFormat
  ): Promise<ExportResult> => {
    setIsExporting(true);
    
    try {
      let content: string | Blob = '';
      let mimeType = 'text/plain';
      
      switch (format) {
        case 'csv':
          content = convertToCSV(Array.isArray(data) ? data : [data]);
          mimeType = 'text/csv';
          break;
        case 'json':
          content = convertToJSON(data);
          mimeType = 'application/json';
          break;
        case 'txt':
          content = convertToText(data);
          mimeType = 'text/plain';
          break;
        default:
          content = convertToText(data);
          mimeType = 'text/plain';
      }
      
      // Create blob from content
      const blob = new Blob([content], { type: `${mimeType};charset=utf-8` });
      
      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${filename}.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Clean up URL
      setTimeout(() => URL.revokeObjectURL(url), 100);
      
      success(`Exported ${filename}.${format} successfully`);
      
      return {
        success: true,
        url,
        blob
      };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to export data';
      error(`Export failed: ${errorMessage}`);
      
      return {
        success: false,
        error: errorMessage
      };
    } finally {
      setIsExporting(false);
    }
  }, [success, error]);

  /**
   * Copy content to clipboard
   */
  const copyToClipboard = useCallback(async (content: string, type = 'text'): Promise<boolean> => {
    try {
      await navigator.clipboard.writeText(content);
      success(`Copied ${type} to clipboard successfully`);
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to copy to clipboard';
      error(`Copy failed: ${errorMessage}`);
      return false;
    }
  }, [success, error]);

  /**
   * Export data with options
   */
  const exportData = useCallback(async (
    data: any,
    options: ExportOptions
  ): Promise<ExportResult> => {
    const {
      format = 'json',
      filename = 'export',
      includeMetadata = false,
      includeAttribution = false
    } = options;
    
    // Add metadata if requested
    let exportData = data;
    if (includeMetadata) {
      exportData = {
        data,
        metadata: {
          exportedAt: new Date().toISOString(),
          format,
          ...(includeAttribution && { exportedBy: 'Graduate Trainee Tracker' })
        }
      };
    }
    
    return await exportToFile(exportData, filename, format);
  }, [exportToFile]);

  return {
    exportToFile,
    copyToClipboard,
    exportData,
    isExporting,
    exportFormats
  };
};