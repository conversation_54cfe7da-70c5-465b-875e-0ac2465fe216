import React, { useState } from 'react';
import { useApi } from './useApi';
import { useAuth } from './useAuth';
import { usePermissions } from './usePermissions';
import { useExport } from './useExport';
import { useErrorHandling } from './useErrorHandling';
import { Permission } from '@/services/authService';

/**
 * Test component to verify integration of all new hooks
 */
export const HooksIntegrationTest: React.FC = () => {
  const [testData, setTestData] = useState<any[]>([]);
  const [testResult, setTestResult] = useState<string>('');
  
  // Test useApi hook
  const { get, post, loading: apiLoading } = useApi();
  
  // Test useAuth hook
  const { user, isAuthenticated, login, logout } = useAuth();
  
  // Test usePermissions hook
  const { hasPermission, canRead, isAdmin } = usePermissions();
  
  // Test useExport hook
  const { exportToFile, isExporting } = useExport();
  
  // Test useErrorHandling hook
  const { handleError, handleAsyncError } = useErrorHandling();
  
  // Test API functionality
  const testApi = async () => {
    try {
      setTestResult('Testing API...');
      // This would normally hit a real endpoint
      // For testing purposes, we'll simulate a successful response
      const mockResponse = {
        data: [{ id: 1, name: 'Test Trainee' }],
        success: true
      };
      setTestData(mockResponse.data);
      setTestResult('API test passed');
    } catch (error: any) {
      handleError(error as Error, { fallbackMessage: 'API test failed' });
    }
  };
  
  // Test auth functionality
  const testAuth = async () => {
    try {
      setTestResult('Testing Auth...');
      // In a real app, this would authenticate with actual credentials
      // For testing, we'll just show the current auth state
      if (isAuthenticated && user) {
        setTestResult(`User authenticated: ${user.name}`);
      } else {
        setTestResult('User not authenticated');
      }
    } catch (error: any) {
      handleError(error as Error, { fallbackMessage: 'Auth test failed' });
    }
  };
  
  // Test permissions functionality
  const testPermissions = () => {
    try {
      setTestResult('Testing Permissions...');
      const canView = hasPermission(Permission.VIEW_TRAINEE_PROFILE);
      const canReadReports = canRead('reports');
      const userIsAdmin = isAdmin();
      
      setTestResult(`Permissions test:
        Can view trainees: ${canView}
        Can read reports: ${canReadReports}
        Is admin: ${userIsAdmin}`);
    } catch (error: any) {
      handleError(error as Error, { fallbackMessage: 'Permissions test failed' });
    }
  };
  
  // Test export functionality
  const testExport = async () => {
    try {
      setTestResult('Testing Export...');
      if (testData.length > 0) {
        const result = await exportToFile(testData, 'test-export', 'json');
        if (result.success) {
          setTestResult('Export test passed');
        } else {
          setTestResult(`Export failed: ${result.error}`);
        }
      } else {
        setTestResult('No data to export');
      }
    } catch (error: any) {
      handleError(error as Error, { fallbackMessage: 'Export test failed' });
    }
  };
  
  // Test error handling functionality
  const testErrorHandling = async () => {
    setTestResult('Testing Error Handling...');
    
    // Test async error handling
    await handleAsyncError(
      async () => {
        // Simulate an async operation
        return new Promise((resolve) => {
          setTimeout(() => resolve('Success'), 100);
        });
      },
      {
        successMessage: 'Async operation completed successfully',
        fallbackMessage: 'Async operation failed'
      }
    );
    
    setTestResult('Error handling test completed');
  };
  
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h2>Graduate Trainee Tracker Hooks Integration Test</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <p><strong>Test Result:</strong> {testResult}</p>
        {apiLoading && <p>API Loading...</p>}
        {isExporting && <p>Export in progress...</p>}
      </div>
      
      <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
        <button onClick={testApi} disabled={apiLoading}>
          Test API Hook
        </button>
        
        <button onClick={testAuth}>
          Test Auth Hook
        </button>
        
        <button onClick={testPermissions}>
          Test Permissions Hook
        </button>
        
        <button onClick={testExport} disabled={isExporting || testData.length === 0}>
          Test Export Hook
        </button>
        
        <button onClick={testErrorHandling}>
          Test Error Handling Hook
        </button>
      </div>
      
      <div style={{ marginTop: '20px' }}>
        <h3>Test Data:</h3>
        <pre>{JSON.stringify(testData, null, 2)}</pre>
      </div>
      
      <div style={{ marginTop: '20px' }}>
        <h3>User Info:</h3>
        <pre>{JSON.stringify({ user, isAuthenticated }, null, 2)}</pre>
      </div>
    </div>
  );
};

export default HooksIntegrationTest;