# Graduate Trainee Tracker Hooks Documentation

This document provides detailed information about the custom hooks available in the Graduate Trainee Tracker component.

## Table of Contents

1. [useApi](#useapi)
2. [useAuth](#useauth)
3. [usePermissions](#usepermissions)
4. [useExport](#useexport)
5. [useErrorHandling](#useerrorhandling)

## useApi

A hook for making API requests with built-in error handling, authentication, and retry logic.

### Usage

```javascript
import { useApi } from '@/components/graduate-trainee-tracker/hooks';

const MyComponent = () => {
  const { get, post, loading } = useApi();

  const fetchData = async () => {
    try {
      const response = await get('/api/trainees');
      console.log(response.data);
    } catch (error) {
      console.error('Failed to fetch data:', error);
    }
  };

  return (
    <div>
      {loading ? <p>Loading...</p> : <button onClick={fetchData}>Fetch Data</button>}
    </div>
  );
};
```

### Methods

- `get(endpoint, options)` - Make a GET request
- `post(endpoint, data, options)` - Make a POST request
- `put(endpoint, data, options)` - Make a PUT request
- `patch(endpoint, data, options)` - Make a PATCH request
- `delete(endpoint, options)` - Make a DELETE request
- `request(endpoint, options)` - Make a request with custom method
- `updateConfig(newConfig)` - Update API configuration
- `setAuthToken(token)` - Set authentication token

### Options

- `method` - HTTP method (GET, POST, PUT, PATCH, DELETE)
- `headers` - Custom headers
- `body` - Request body
- `timeout` - Request timeout in milliseconds
- `retryAttempts` - Number of retry attempts
- `skipErrorToast` - Skip error toast notifications
- `skipLoadingState` - Skip loading state updates

## useAuth

A hook for authentication functionality with user management.

### Usage

```javascript
import { useAuth } from '@/components/graduate-trainee-tracker/hooks';

const LoginComponent = () => {
  const { login, logout, user, isAuthenticated } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const handleLogin = async () => {
    try {
      await login(email, password);
    } catch (error) {
      console.error('Login failed:', error);
    }
  };

  if (isAuthenticated) {
    return (
      <div>
        <p>Welcome, {user.name}!</p>
        <button onClick={logout}>Logout</button>
      </div>
    );
  }

  return (
    <div>
      <input
        type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        placeholder="Email"
      />
      <input
        type="password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        placeholder="Password"
      />
      <button onClick={handleLogin}>Login</button>
    </div>
  );
};
```

### Methods

- `login(email, password)` - Authenticate user
- `register(email, password, name)` - Register new user
- `logout()` - Log out current user
- `refreshTokens()` - Refresh authentication tokens
- `changePassword(currentPassword, newPassword)` - Change user password
- `hasPermission(permission)` - Check if user has specific permission
- `hasRole(role)` - Check if user has specific role

## usePermissions

A hook for permission checking and role-based access control.

### Usage

```javascript
import { usePermissions } from '@/components/graduate-trainee-tracker/hooks';

const SecureComponent = () => {
  const { 
    hasPermission, 
    canRead, 
    canWrite, 
    isAdmin 
  } = usePermissions();

  // Check specific permissions
  const canViewTrainees = hasPermission('VIEW_TRAINEE_PROFILE');
  const canEditPrograms = hasPermission('MANAGE_TRAINING_PROGRAMS');

  // Check resource access
  const canReadReports = canRead('reports');
  const canWriteReports = canWrite('reports');

  // Check role
  const userIsAdmin = isAdmin();

  return (
    <div>
      {canViewTrainees && <TraineeList />}
      {canEditPrograms && <ProgramEditor />}
      {userIsAdmin && <AdminPanel />}
    </div>
  );
};
```

### Methods

- `hasPermission(permission)` - Check if user has specific permission
- `hasRole(role)` - Check if user has specific role
- `checkAccess(resource, action, context)` - Check access to resource
- `requireAccess(resource, action, context)` - Require access to resource (throws error if denied)
- `canRead(resource, context)` - Check if user can read resource
- `canWrite(resource, context)` - Check if user can create resource
- `canEdit(resource, context)` - Check if user can edit resource
- `canDelete(resource, context)` - Check if user can delete resource
- `canShare(resource, context)` - Check if user can share resource
- `isAdmin()` - Check if user is admin
- `isUser()` - Check if user is regular user
- `isViewer()` - Check if user is viewer

## useExport

A hook for data export functionality.

### Usage

```javascript
import { useExport } from '@/components/graduate-trainee-tracker/hooks';

const ExportComponent = () => {
  const { exportToFile, copyToClipboard, isExporting } = useExport();
  const [data, setData] = useState([]);

  const handleExportCSV = async () => {
    await exportToFile(data, 'trainee-data', 'csv');
  };

  const handleExportJSON = async () => {
    await exportToFile(data, 'trainee-data', 'json');
  };

  const handleCopy = async () => {
    const jsonData = JSON.stringify(data, null, 2);
    await copyToClipboard(jsonData, 'trainee data');
  };

  return (
    <div>
      <button onClick={handleExportCSV} disabled={isExporting}>
        Export as CSV
      </button>
      <button onClick={handleExportJSON} disabled={isExporting}>
        Export as JSON
      </button>
      <button onClick={handleCopy} disabled={isExporting}>
        Copy to Clipboard
      </button>
    </div>
  );
};
```

### Methods

- `exportToFile(data, filename, format)` - Export data to file
- `copyToClipboard(content, type)` - Copy content to clipboard
- `exportData(data, options)` - Export data with options
- `isExporting` - Boolean indicating if export is in progress
- `exportFormats` - Array of supported export formats

### Supported Formats

- `csv` - Comma-separated values
- `json` - JavaScript Object Notation
- `txt` - Plain text
- `xlsx` - Excel spreadsheet (basic support)

## useErrorHandling

A hook for centralized error handling.

### Usage

```javascript
import { useErrorHandling } from '@/components/graduate-trainee-tracker/hooks';

const DataComponent = () => {
  const { handleError, handleAsyncError } = useErrorHandling();
  const [data, setData] = useState([]);

  const fetchData = async () => {
    await handleAsyncError(
      async () => {
        const response = await fetch('/api/trainees');
        if (!response.ok) throw new Error('Failed to fetch trainees');
        const result = await response.json();
        setData(result);
        return result;
      },
      {
        successMessage: 'Trainees loaded successfully',
        fallbackMessage: 'Unable to load trainee data'
      }
    );
  };

  const handleManualError = () => {
    try {
      // Some operation that might fail
      throw new Error('Something went wrong');
    } catch (error) {
      handleError(error, {
        fallbackMessage: 'An unexpected error occurred'
      });
    }
  };

  return (
    <div>
      <button onClick={fetchData}>Load Data</button>
      <button onClick={handleManualError}>Trigger Error</button>
    </div>
  );
};
```

### Methods

- `handleError(error, options)` - Handle an error with optional notification
- `handleAsyncError(asyncFn, options)` - Handle async operations with error handling
- `ErrorMessages.getErrorMessage(error, fallback)` - Get error message from error object
- `ErrorMessages.getUserFriendlyMessage(error)` - Get user-friendly error message
- `ErrorClassifier.isNetworkError(error)` - Check if error is network-related
- `ErrorClassifier.isPermissionError(error)` - Check if error is permission-related
- `ErrorClassifier.isValidationError(error)` - Check if error is validation-related
- `ErrorClassifier.isFileError(error)` - Check if error is file-related
- `ErrorClassifier.classifyError(error)` - Classify error type

### Options

- `showNotification` - Show error notification (default: true)
- `logError` - Log error to console (default: true)
- `fallbackMessage` - Fallback message if error has no message
- `onError` - Custom error handler callback
- `successMessage` - Success message for async operations
- `onSuccess` - Success callback for async operations