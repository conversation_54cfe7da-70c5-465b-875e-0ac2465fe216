import { useState, useCallback, useEffect, useRef } from 'react';

export interface ModalState {
  isOpen: boolean;
  modalId: string | null;
  data: any;
  size: 'small' | 'medium' | 'large' | 'fullscreen';
  closable: boolean;
  backdrop: boolean;
  keyboard: boolean;
  centered: boolean;
  scrollable: boolean;
  animation: boolean;
}

export interface ModalActions {
  open: (modalId?: string, data?: any, options?: Partial<ModalOptions>) => void;
  close: () => void;
  toggle: (modalId?: string, data?: any, options?: Partial<ModalOptions>) => void;
  setData: (data: any) => void;
  updateOptions: (options: Partial<ModalOptions>) => void;
}

export interface ModalOptions {
  size?: 'small' | 'medium' | 'large' | 'fullscreen';
  closable?: boolean;
  backdrop?: boolean;
  keyboard?: boolean;
  centered?: boolean;
  scrollable?: boolean;
  animation?: boolean;
  onOpen?: () => void;
  onClose?: () => void;
  onBeforeClose?: () => boolean | Promise<boolean>;
  autoFocus?: boolean;
  restoreFocus?: boolean;
  closeOnEscape?: boolean;
  closeOnBackdrop?: boolean;
}

export interface UseModalOptions extends ModalOptions {
  defaultOpen?: boolean;
  defaultModalId?: string | null;
  defaultData?: any;
  persistState?: boolean;
  storageKey?: string;
}

const DEFAULT_OPTIONS: Required<Omit<UseModalOptions, 'onOpen' | 'onClose' | 'onBeforeClose'>> = {
  defaultOpen: false,
  defaultModalId: null,
  defaultData: null,
  size: 'medium',
  closable: true,
  backdrop: true,
  keyboard: true,
  centered: true,
  scrollable: false,
  animation: true,
  autoFocus: true,
  restoreFocus: true,
  closeOnEscape: true,
  closeOnBackdrop: true,
  persistState: false,
  storageKey: 'graduate-tracker-modal'
};

export const useModal = (options: UseModalOptions = {}) => {
  const config = { ...DEFAULT_OPTIONS, ...options };
  const previousActiveElement = useRef<HTMLElement | null>(null);
  const modalRef = useRef<HTMLElement | null>(null);

  // Initialize state
  const getInitialState = (): ModalState => {
    if (config.persistState && typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem(config.storageKey);
        if (stored) {
          const parsedState = JSON.parse(stored);
          return {
            isOpen: parsedState.isOpen ?? config.defaultOpen,
            modalId: parsedState.modalId ?? config.defaultModalId,
            data: parsedState.data ?? config.defaultData,
            size: parsedState.size ?? config.size,
            closable: parsedState.closable ?? config.closable,
            backdrop: parsedState.backdrop ?? config.backdrop,
            keyboard: parsedState.keyboard ?? config.keyboard,
            centered: parsedState.centered ?? config.centered,
            scrollable: parsedState.scrollable ?? config.scrollable,
            animation: parsedState.animation ?? config.animation
          };
        }
      } catch (error) {
        console.warn('Failed to parse modal state from localStorage:', error);
      }
    }

    return {
      isOpen: config.defaultOpen,
      modalId: config.defaultModalId,
      data: config.defaultData,
      size: config.size,
      closable: config.closable,
      backdrop: config.backdrop,
      keyboard: config.keyboard,
      centered: config.centered,
      scrollable: config.scrollable,
      animation: config.animation
    };
  };

  const [state, setState] = useState<ModalState>(getInitialState);

  // Persist state to localStorage
  const persistState = useCallback((newState: ModalState) => {
    if (config.persistState && typeof window !== 'undefined') {
      try {
        localStorage.setItem(config.storageKey, JSON.stringify(newState));
      } catch (error) {
        console.warn('Failed to persist modal state to localStorage:', error);
      }
    }
  }, [config.persistState, config.storageKey]);

  // Update state and persist
  const updateState = useCallback((updates: Partial<ModalState>) => {
    setState(prevState => {
      const newState = { ...prevState, ...updates };
      persistState(newState);
      return newState;
    });
  }, [persistState]);

  // Handle focus management
  const manageFocus = useCallback((isOpening: boolean) => {
    if (typeof window === 'undefined') return;

    if (isOpening) {
      // Store current active element
      previousActiveElement.current = document.activeElement as HTMLElement;
      
      // Focus modal after a brief delay to ensure it's rendered
      if (config.autoFocus) {
        setTimeout(() => {
          const focusableElement = modalRef.current?.querySelector(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
          ) as HTMLElement;
          
          if (focusableElement) {
            focusableElement.focus();
          } else if (modalRef.current) {
            modalRef.current.focus();
          }
        }, 100);
      }
    } else {
      // Restore focus to previous element
      if (config.restoreFocus && previousActiveElement.current) {
        previousActiveElement.current.focus();
        previousActiveElement.current = null;
      }
    }
  }, [config.autoFocus, config.restoreFocus]);

  // Handle body scroll lock
  const manageBodyScroll = useCallback((lock: boolean) => {
    if (typeof window === 'undefined') return;

    if (lock) {
      document.body.style.overflow = 'hidden';
      document.body.style.paddingRight = `${window.innerWidth - document.documentElement.clientWidth}px`;
    } else {
      document.body.style.overflow = '';
      document.body.style.paddingRight = '';
    }
  }, []);

  // Actions
  const actions: ModalActions = {
    open: useCallback(async (modalId?: string, data?: any, modalOptions?: Partial<ModalOptions>) => {
      const newState: Partial<ModalState> = {
        isOpen: true,
        modalId: modalId || state.modalId,
        data: data !== undefined ? data : state.data,
        ...(modalOptions && {
          size: modalOptions.size || state.size,
          closable: modalOptions.closable !== undefined ? modalOptions.closable : state.closable,
          backdrop: modalOptions.backdrop !== undefined ? modalOptions.backdrop : state.backdrop,
          keyboard: modalOptions.keyboard !== undefined ? modalOptions.keyboard : state.keyboard,
          centered: modalOptions.centered !== undefined ? modalOptions.centered : state.centered,
          scrollable: modalOptions.scrollable !== undefined ? modalOptions.scrollable : state.scrollable,
          animation: modalOptions.animation !== undefined ? modalOptions.animation : state.animation
        })
      };

      updateState(newState);
      manageFocus(true);
      manageBodyScroll(true);
      
      if (options.onOpen) {
        options.onOpen();
      }
    }, [state, updateState, manageFocus, manageBodyScroll, options]),

    close: useCallback(async () => {
      // Check if close is allowed
      if (options.onBeforeClose) {
        const canClose = await options.onBeforeClose();
        if (!canClose) return;
      }

      updateState({ isOpen: false });
      manageFocus(false);
      manageBodyScroll(false);
      
      if (options.onClose) {
        options.onClose();
      }
    }, [updateState, manageFocus, manageBodyScroll, options]),

    toggle: useCallback(async (modalId?: string, data?: any, modalOptions?: Partial<ModalOptions>) => {
      if (state.isOpen) {
        // Check if close is allowed
        if (options.onBeforeClose) {
          const canClose = await options.onBeforeClose();
          if (!canClose) return;
        }

        updateState({ isOpen: false });
        manageFocus(false);
        manageBodyScroll(false);
        
        if (options.onClose) {
          options.onClose();
        }
      } else {
        const newState: Partial<ModalState> = {
          isOpen: true,
          modalId: modalId || state.modalId,
          data: data !== undefined ? data : state.data,
          ...(modalOptions && {
            size: modalOptions.size || state.size,
            closable: modalOptions.closable !== undefined ? modalOptions.closable : state.closable,
            backdrop: modalOptions.backdrop !== undefined ? modalOptions.backdrop : state.backdrop,
            keyboard: modalOptions.keyboard !== undefined ? modalOptions.keyboard : state.keyboard,
            centered: modalOptions.centered !== undefined ? modalOptions.centered : state.centered,
            scrollable: modalOptions.scrollable !== undefined ? modalOptions.scrollable : state.scrollable,
            animation: modalOptions.animation !== undefined ? modalOptions.animation : state.animation
          })
        };

        updateState(newState);
        manageFocus(true);
        manageBodyScroll(true);
        
        if (options.onOpen) {
          options.onOpen();
        }
      }
    }, [state, updateState, manageFocus, manageBodyScroll, options]),

    setData: useCallback((data: any) => {
      updateState({ data });
    }, [updateState]),

    updateOptions: useCallback((modalOptions: Partial<ModalOptions>) => {
      const newState: Partial<ModalState> = {};
      
      if (modalOptions.size) newState.size = modalOptions.size;
      if (modalOptions.closable !== undefined) newState.closable = modalOptions.closable;
      if (modalOptions.backdrop !== undefined) newState.backdrop = modalOptions.backdrop;
      if (modalOptions.keyboard !== undefined) newState.keyboard = modalOptions.keyboard;
      if (modalOptions.centered !== undefined) newState.centered = modalOptions.centered;
      if (modalOptions.scrollable !== undefined) newState.scrollable = modalOptions.scrollable;
      if (modalOptions.animation !== undefined) newState.animation = modalOptions.animation;
      
      updateState(newState);
    }, [updateState])
  };

  // Handle keyboard events
  useEffect(() => {
    if (!state.isOpen || typeof window === 'undefined') return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Close on Escape key
      if (event.key === 'Escape' && state.keyboard && config.closeOnEscape) {
        event.preventDefault();
        actions.close();
      }

      // Trap focus within modal
      if (event.key === 'Tab' && modalRef.current) {
        const focusableElements = modalRef.current.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        const firstElement = focusableElements[0] as HTMLElement;
        const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;
        
        if (event.shiftKey) {
          if (document.activeElement === firstElement) {
            event.preventDefault();
            lastElement?.focus();
          }
        } else {
          if (document.activeElement === lastElement) {
            event.preventDefault();
            firstElement?.focus();
          }
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [state.isOpen, state.keyboard, config.closeOnEscape, actions]);

  // Handle backdrop click
  const handleBackdropClick = useCallback((event: React.MouseEvent) => {
    if (event.target === event.currentTarget && state.backdrop && config.closeOnBackdrop) {
      actions.close();
    }
  }, [state.backdrop, config.closeOnBackdrop, actions]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (state.isOpen) {
        manageBodyScroll(false);
      }
    };
  }, [state.isOpen, manageBodyScroll]);

  // Computed properties
  const computed = {
    modalClasses: [
      'modal',
      state.isOpen ? 'modal--open' : 'modal--closed',
      `modal--${state.size}`,
      state.centered ? 'modal--centered' : '',
      state.scrollable ? 'modal--scrollable' : '',
      state.animation ? 'modal--animated' : ''
    ].filter(Boolean).join(' '),
    
    backdropClasses: [
      'modal-backdrop',
      state.isOpen ? 'modal-backdrop--open' : 'modal-backdrop--closed',
      state.animation ? 'modal-backdrop--animated' : ''
    ].filter(Boolean).join(' '),
    
    shouldRender: state.isOpen || state.animation, // Keep rendered during close animation
    
    ariaProps: {
      role: 'dialog',
      'aria-modal': 'true',
      'aria-labelledby': state.modalId ? `${state.modalId}-title` : undefined,
      'aria-describedby': state.modalId ? `${state.modalId}-description` : undefined,
      tabIndex: -1
    }
  };

  return {
    // State
    ...state,
    
    // Actions
    ...actions,
    
    // Computed properties
    ...computed,
    
    // Event handlers
    handleBackdropClick,
    
    // Refs
    modalRef,
    
    // Configuration
    config: {
      closeOnEscape: config.closeOnEscape,
      closeOnBackdrop: config.closeOnBackdrop,
      autoFocus: config.autoFocus,
      restoreFocus: config.restoreFocus
    }
  };
};

export default useModal;