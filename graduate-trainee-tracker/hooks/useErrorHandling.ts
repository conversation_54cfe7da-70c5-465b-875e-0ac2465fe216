import { useCallback } from 'react';
import { useToast } from './useToast';

// Error Handler Options Interface
interface ErrorHandlerOptions {
  showNotification?: boolean;
  logError?: boolean;
  fallbackMessage?: string;
  onError?: (error: Error) => void;
}

// Async Operation Options Interface
interface AsyncOperationOptions extends ErrorHandlerOptions {
  successMessage?: string;
  loadingMessage?: string;
  onSuccess?: (result: any) => void;
}

// Error Classification Types
type ErrorType = 'network' | 'permission' | 'validation' | 'file' | 'unknown';

// Hook Return Type
interface UseErrorHandlingReturn {
  handleError: (error: Error | string, options?: ErrorHandlerOptions) => Error;
  handleAsyncError: (asyncFn: () => Promise<any>, options?: AsyncOperationOptions) => Promise<any>;
  ErrorMessages: {
    getErrorMessage: (error: Error, fallback?: string) => string;
    getUserFriendlyMessage: (error: Error) => string;
  };
  ErrorClassifier: {
    isNetworkError: (error: Error) => boolean;
    isPermissionError: (error: Error) => boolean;
    isValidationError: (error: Error) => boolean;
    isFileError: (error: Error) => boolean;
    classifyError: (error: Error) => ErrorType;
  };
}

/**
 * Hook for centralized error handling
 */
export const useErrorHandling = (): UseErrorHandlingReturn => {
  const { error: showError, success: showSuccess } = useToast();

  /**
   * Error classification utilities
   */
  const ErrorClassifier = {
    isNetworkError: (error: Error): boolean => {
      const message = error.message.toLowerCase();
      return message.includes('fetch') ||
             message.includes('network') ||
             message.includes('connection') ||
             message.includes('timeout');
    },

    isPermissionError: (error: Error): boolean => {
      const message = error.message.toLowerCase();
      return message.includes('permission') ||
             message.includes('unauthorized') ||
             message.includes('forbidden') ||
             message.includes('access denied');
    },

    isValidationError: (error: Error): boolean => {
      const message = error.message.toLowerCase();
      return message.includes('validation') ||
             message.includes('invalid') ||
             message.includes('required');
    },

    isFileError: (error: Error): boolean => {
      const message = error.message.toLowerCase();
      return message.includes('file') ||
             message.includes('upload') ||
             message.includes('download');
    },

    classifyError: (error: Error): ErrorType => {
      if (ErrorClassifier.isNetworkError(error)) return 'network';
      if (ErrorClassifier.isPermissionError(error)) return 'permission';
      if (ErrorClassifier.isValidationError(error)) return 'validation';
      if (ErrorClassifier.isFileError(error)) return 'file';
      return 'unknown';
    }
  };

  /**
   * Error message utilities
   */
  const ErrorMessages = {
    getErrorMessage: (error: Error, fallback = 'An unexpected error occurred'): string => {
      // If it's a known error with a message, use it
      if (error.message) {
        return error.message;
      }

      // Try to extract meaningful error from different error types
      if (error instanceof Error) {
        return error.message || fallback;
      }

      // Handle string errors
      if (typeof error === 'string') {
        return error;
      }

      // Handle object errors (like API responses)
      if (typeof error === 'object' && error !== null) {
        const errorObj = error as any;
        return errorObj.message || errorObj.error || errorObj.detail || fallback;
      }

      return fallback;
    },

    getUserFriendlyMessage: (error: Error): string => {
      const errorType = ErrorClassifier.classifyError(error);

      switch (errorType) {
        case 'network':
          return 'Unable to connect to the server. Please check your internet connection and try again.';
        case 'permission':
          return 'You don\'t have permission to perform this action. Please contact your administrator.';
        case 'validation':
          return 'The provided information is invalid. Please check your input and try again.';
        case 'file':
          return 'There was an issue with the file. Please check the file format and size.';
        default:
          return ErrorMessages.getErrorMessage(error, 'Something went wrong. Please try again.');
      }
    }
  };

  /**
   * Handle an error with optional notification and logging
   */
  const handleError = useCallback((
    error: Error | string,
    options: ErrorHandlerOptions = {}
  ): Error => {
    const {
      showNotification = true,
      logError = true,
      fallbackMessage = 'An error occurred',
      onError,
    } = options;

    // Normalize error to Error object
    const errorObj = typeof error === 'string' ? new Error(error) : error;

    // Log error if requested
    if (logError) {
      console.error('Error handled:', errorObj);
    }

    // Call custom error handler if provided
    onError?.(errorObj);

    // Show notification if requested
    if (showNotification) {
      const userFriendlyMessage = ErrorMessages.getUserFriendlyMessage(errorObj);
      const shouldUseWarning = ErrorClassifier.classifyError(errorObj) === 'validation';

      if (shouldUseWarning) {
        // For validation errors, we could use a warning toast if available
        showError(userFriendlyMessage);
      } else {
        showError(userFriendlyMessage);
      }
    }

    return errorObj;
  }, [showError]);

  /**
   * Handle async operations with error handling
   */
  const handleAsyncError = useCallback(async (
    asyncFn: () => Promise<any>,
    options: AsyncOperationOptions = {}
  ) => {
    const {
      successMessage,
      onSuccess,
      ...errorOptions
    } = options;

    try {
      const result = await asyncFn();
      
      if (successMessage) {
        showSuccess(successMessage);
      }
      
      if (onSuccess) {
        onSuccess(result);
      }
      
      return result;
    } catch (error) {
      handleError(error as Error, errorOptions);
      return null;
    }
  }, [handleError, showSuccess]);

  return {
    handleError,
    handleAsyncError,
    ErrorMessages,
    ErrorClassifier,
  };
};