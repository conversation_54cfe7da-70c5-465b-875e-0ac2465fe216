import { useState, useEffect, useCallback } from 'react';
import { QuarterlyReview, ReviewStatus, ReviewType } from '../types';
import { fetchReviews, createReview, updateReview } from '../mockApi';

export interface ReviewFilters {
  search?: string;
  status?: ReviewStatus;
  traineeId?: string;
  reviewerId?: string;
  type?: ReviewType;
  quarter?: number;
  year?: number;
}

export interface UseReviewsReturn {
  reviews: QuarterlyReview[];
  filteredReviews: QuarterlyReview[];
  loading: boolean;
  error: string | null;
  fetchReviews: () => Promise<void>;
  createReview: (review: Omit<QuarterlyReview, 'id' | 'createdAt' | 'updatedAt'>) => Promise<QuarterlyReview | null>;
  updateReview: (id: string, updates: Partial<QuarterlyReview>) => Promise<QuarterlyReview | null>;
  filters: ReviewFilters;
  setFilters: (filters: ReviewFilters) => void;
  searchReviews: (query: string) => void;
  filterByStatus: (status: ReviewStatus | undefined) => void;
  filterByTrainee: (traineeId: string | undefined) => void;
  getReviewById: (id: string) => QuarterlyReview | undefined;
  getReviewsByTrainee: (traineeId: string) => QuarterlyReview[];
  getOverdueReviews: () => QuarterlyReview[];
  getUpcomingReviews: (days?: number) => QuarterlyReview[];
  getReviewStats: () => {
    total: number;
    completed: number;
    pending: number;
    scheduled: number;
    overdue: number;
  };
}

export const useReviews = (): UseReviewsReturn => {
  const [reviews, setReviews] = useState<QuarterlyReview[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<ReviewFilters>({});

  // Fetch reviews from API
  const fetchReviewsData = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await fetchReviews();
      setReviews(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch reviews';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Create new review
  const createNewReview = useCallback(async (reviewData: Omit<QuarterlyReview, 'id' | 'createdAt' | 'updatedAt'>): Promise<QuarterlyReview | null> => {
    setLoading(true);
    setError(null);
    try {
      const newReview = await createReview(reviewData);
      setReviews(prev => [...prev, newReview]);
      return newReview;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create review';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Update existing review
  const updateExistingReview = useCallback(async (id: string, updates: Partial<QuarterlyReview>): Promise<QuarterlyReview | null> => {
    setLoading(true);
    setError(null);
    try {
      const updatedReview = await updateReview(id, updates);
      setReviews(prev => prev.map(review => 
        review.id === id ? updatedReview : review
      ));
      return updatedReview;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update review';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Filter reviews based on current filters
  const filteredReviews = reviews.filter(review => {
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      // Search in review content, feedback, and action items
      const matchesSearch = 
        review.selfAssessment?.comments?.toLowerCase().includes(searchLower) ||
        review.mentorAssessment?.comments?.toLowerCase().includes(searchLower) ||
        review.feedback.some(f => f.content.toLowerCase().includes(searchLower)) ||
        review.actionItems.some(a => 
          a.title.toLowerCase().includes(searchLower) ||
          a.description.toLowerCase().includes(searchLower)
        );
      if (!matchesSearch) return false;
    }

    if (filters.status && review.status !== filters.status) {
      return false;
    }

    if (filters.traineeId && review.traineeId !== filters.traineeId) {
      return false;
    }

    if (filters.reviewerId && review.reviewerId !== filters.reviewerId) {
      return false;
    }

    if (filters.type && review.type !== filters.type) {
      return false;
    }

    if (filters.quarter && review.quarter !== filters.quarter) {
      return false;
    }

    if (filters.year && review.year !== filters.year) {
      return false;
    }

    return true;
  });

  // Search reviews by query
  const searchReviews = useCallback((query: string) => {
    setFilters(prev => ({ ...prev, search: query }));
  }, []);

  // Filter by status
  const filterByStatus = useCallback((status: ReviewStatus | undefined) => {
    setFilters(prev => ({ ...prev, status }));
  }, []);

  // Filter by trainee
  const filterByTrainee = useCallback((traineeId: string | undefined) => {
    setFilters(prev => ({ ...prev, traineeId }));
  }, []);

  // Get review by ID
  const getReviewById = useCallback((id: string) => {
    return reviews.find(review => review.id === id);
  }, [reviews]);

  // Get reviews by trainee
  const getReviewsByTrainee = useCallback((traineeId: string) => {
    return reviews.filter(review => review.traineeId === traineeId);
  }, [reviews]);

  // Get overdue reviews
  const getOverdueReviews = useCallback(() => {
    const now = new Date();
    return reviews.filter(review => {
      if (review.status === ReviewStatus.COMPLETED) return false;
      const dueDate = new Date(review.dueDate);
      return dueDate < now;
    });
  }, [reviews]);

  // Get upcoming reviews
  const getUpcomingReviews = useCallback((days: number = 7) => {
    const now = new Date();
    const futureDate = new Date(now.getTime() + (days * 24 * 60 * 60 * 1000));
    
    return reviews.filter(review => {
      if (review.status === ReviewStatus.COMPLETED) return false;
      const scheduledDate = new Date(review.scheduledDate);
      return scheduledDate >= now && scheduledDate <= futureDate;
    });
  }, [reviews]);

  // Get review statistics
  const getReviewStats = useCallback(() => {
    const stats = {
      total: reviews.length,
      completed: 0,
      pending: 0,
      scheduled: 0,
      overdue: 0
    };

    const now = new Date();

    reviews.forEach(review => {
      switch (review.status) {
        case ReviewStatus.COMPLETED:
          stats.completed++;
          break;
        case ReviewStatus.IN_PROGRESS:
          stats.pending++;
          break;
        case ReviewStatus.SCHEDULED:
          stats.scheduled++;
          break;
      }

      // Check for overdue
      if (review.status !== ReviewStatus.COMPLETED) {
        const dueDate = new Date(review.dueDate);
        if (dueDate < now) {
          stats.overdue++;
        }
      }
    });

    return stats;
  }, [reviews]);

  // Load reviews on mount
  useEffect(() => {
    fetchReviewsData();
  }, [fetchReviewsData]);

  return {
    reviews,
    filteredReviews,
    loading,
    error,
    fetchReviews: fetchReviewsData,
    createReview: createNewReview,
    updateReview: updateExistingReview,
    filters,
    setFilters,
    searchReviews,
    filterByStatus,
    filterByTrainee,
    getReviewById,
    getReviewsByTrainee,
    getOverdueReviews,
    getUpcomingReviews,
    getReviewStats
  };
};

export default useReviews;