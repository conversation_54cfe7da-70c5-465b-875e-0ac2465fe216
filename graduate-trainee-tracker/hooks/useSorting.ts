import { useState, useCallback, useMemo } from 'react';
import { SortConfig, SortDirection } from '../types';

export interface UseSortingReturn<T = any> {
  // Sorting state
  sortConfig: SortConfig | null;
  
  // Sorting actions
  setSortConfig: (config: SortConfig | null) => void;
  sortBy: (field: keyof T, direction?: SortDirection) => void;
  toggleSort: (field: keyof T) => void;
  clearSort: () => void;
  
  // Computed values
  sortedField: keyof T | null;
  sortDirection: SortDirection | null;
  isSorted: boolean;
  isSortedBy: (field: keyof T) => boolean;
  getSortDirection: (field: keyof T) => SortDirection | null;
  
  // Data sorting
  sortData: (data: T[]) => T[];
  
  // Sort utilities
  getSortIcon: (field: keyof T) => 'asc' | 'desc' | 'none';
  getSortLabel: (field: keyof T) => string;
}

export interface UseSortingOptions<T = any> {
  initialSort?: SortConfig;
  onSortChange?: (config: SortConfig | null) => void;
  customComparators?: Record<string, (a: any, b: any) => number>;
}

// Default comparator functions
const defaultComparators = {
  string: (a: string, b: string) => a.localeCompare(b),
  number: (a: number, b: number) => a - b,
  date: (a: Date | string, b: Date | string) => {
    const dateA = new Date(a);
    const dateB = new Date(b);
    return dateA.getTime() - dateB.getTime();
  },
  boolean: (a: boolean, b: boolean) => Number(a) - Number(b)
};

// Helper function to get nested property value
const getNestedValue = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => current?.[key], obj);
};

// Helper function to determine data type
const getDataType = (value: any): keyof typeof defaultComparators => {
  if (value === null || value === undefined) return 'string';
  if (typeof value === 'string') {
    // Check if it's a date string
    if (!isNaN(Date.parse(value))) return 'date';
    return 'string';
  }
  if (typeof value === 'number') return 'number';
  if (typeof value === 'boolean') return 'boolean';
  if (value instanceof Date) return 'date';
  return 'string';
};

export const useSorting = <T = any>(options: UseSortingOptions<T> = {}): UseSortingReturn<T> => {
  const {
    initialSort = null,
    onSortChange,
    customComparators = {}
  } = options;

  const [sortConfig, setSortConfigState] = useState<SortConfig | null>(initialSort);

  // Sorting actions
  const setSortConfig = useCallback((config: SortConfig | null) => {
    setSortConfigState(config);
    onSortChange?.(config);
  }, [onSortChange]);

  const sortBy = useCallback((field: keyof T, direction: SortDirection = 'asc') => {
    const newConfig: SortConfig = {
      field: field as string,
      direction
    };
    setSortConfig(newConfig);
  }, [setSortConfig]);

  const toggleSort = useCallback((field: keyof T) => {
    if (!sortConfig || sortConfig.field !== field) {
      // Not currently sorted by this field, sort ascending
      sortBy(field, 'asc');
    } else if (sortConfig.direction === 'asc') {
      // Currently sorted ascending, change to descending
      sortBy(field, 'desc');
    } else {
      // Currently sorted descending, clear sort
      clearSort();
    }
  }, [sortConfig, sortBy]);

  const clearSort = useCallback(() => {
    setSortConfig(null);
  }, [setSortConfig]);

  // Computed values
  const sortedField = useMemo(() => {
    return sortConfig?.field as keyof T | null;
  }, [sortConfig]);

  const sortDirection = useMemo(() => {
    return sortConfig?.direction || null;
  }, [sortConfig]);

  const isSorted = useMemo(() => {
    return sortConfig !== null;
  }, [sortConfig]);

  const isSortedBy = useCallback((field: keyof T): boolean => {
    return sortConfig?.field === field;
  }, [sortConfig]);

  const getSortDirection = useCallback((field: keyof T): SortDirection | null => {
    return isSortedBy(field) ? sortConfig!.direction : null;
  }, [isSortedBy, sortConfig]);

  // Data sorting
  const sortData = useCallback((data: T[]): T[] => {
    if (!sortConfig || !data.length) {
      return data;
    }

    const { field, direction } = sortConfig;
    
    return [...data].sort((a, b) => {
      // Get values for comparison
      const aValue = getNestedValue(a, field);
      const bValue = getNestedValue(b, field);
      
      // Handle null/undefined values
      if (aValue === null || aValue === undefined) {
        if (bValue === null || bValue === undefined) return 0;
        return direction === 'asc' ? 1 : -1;
      }
      if (bValue === null || bValue === undefined) {
        return direction === 'asc' ? -1 : 1;
      }
      
      // Use custom comparator if available
      const customComparator = customComparators[field];
      if (customComparator) {
        const result = customComparator(aValue, bValue);
        return direction === 'asc' ? result : -result;
      }
      
      // Use default comparator based on data type
      const dataType = getDataType(aValue);
      const comparator = defaultComparators[dataType];
      const result = comparator(aValue as never, bValue as never);
      
      return direction === 'asc' ? result : -result;
    });
  }, [sortConfig, customComparators]);

  // Sort utilities
  const getSortIcon = useCallback((field: keyof T): 'asc' | 'desc' | 'none' => {
    if (!isSortedBy(field)) return 'none';
    return sortConfig!.direction === 'asc' ? 'asc' : 'desc';
  }, [isSortedBy, sortConfig]);

  const getSortLabel = useCallback((field: keyof T): string => {
    const direction = getSortDirection(field);
    if (!direction) return 'Not sorted';
    
    const fieldName = String(field).replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    return `Sorted by ${fieldName} (${direction === 'asc' ? 'ascending' : 'descending'})`;
  }, [getSortDirection]);

  return {
    sortConfig,
    setSortConfig,
    sortBy,
    toggleSort,
    clearSort,
    sortedField,
    sortDirection,
    isSorted,
    isSortedBy,
    getSortDirection,
    sortData,
    getSortIcon,
    getSortLabel
  };
};

export default useSorting;