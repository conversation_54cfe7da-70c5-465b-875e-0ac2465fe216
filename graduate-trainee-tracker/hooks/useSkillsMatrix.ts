import { useState, useEffect, useCallback } from 'react';
import {
  Trainee,
  Competency,
  SkillAssessment,
  CompetencyLevel,
  CompetencyCategory,
  User
} from '../types';
import { fetchTrainees, fetchCompetencies, updateTrainee } from '../mockApi';

export interface SkillsMatrixData {
  traineeId: string;
  competencyId: string;
  competencyName: string;
  category: CompetencyCategory;
  currentLevel: CompetencyLevel;
  targetLevel: CompetencyLevel;
  progress: number;
  assessmentDate: string;
  assessorId: string;
  evidence: string[];
  developmentPlan: string;
  gapAnalysis: string;
}

export interface SkillsMatrixSummary {
  totalCompetencies: number;
  assessedCompetencies: number;
  averageLevel: number;
  competenciesOnTrack: number;
  competenciesBehind: number;
  categoryBreakdown: Record<CompetencyCategory, {
    total: number;
    averageLevel: number;
    onTrack: number;
    behind: number;
  }>;
}

export interface CompetencyGap {
  competencyId: string;
  competencyName: string;
  currentLevel: CompetencyLevel;
  targetLevel: CompetencyLevel;
  gap: number;
  priority: 'high' | 'medium' | 'low';
  recommendedActions: string[];
}

export interface DevelopmentRecommendation {
  competencyId: string;
  competencyName: string;
  currentLevel: CompetencyLevel;
  targetLevel: CompetencyLevel;
  recommendedResources: string[];
  estimatedTimeframe: string;
  prerequisites: string[];
  successMetrics: string[];
}

export const useSkillsMatrix = () => {
  const [trainees, setTrainees] = useState<Trainee[]>([]);
  const [competencies, setCompetencies] = useState<Competency[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [traineesData, competenciesData] = await Promise.all([
          fetchTrainees(),
          fetchCompetencies()
        ]);
        setTrainees(traineesData);
        setCompetencies(competenciesData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Get skills matrix for a specific trainee
  const getTraineeSkillsMatrix = useCallback((traineeId: string): SkillsMatrixData[] => {
    const trainee = trainees.find(t => t.id === traineeId);
    if (!trainee) return [];

    return trainee.skillsMatrix.map(assessment => {
      const competency = competencies.find(c => c.id === assessment.competencyId);
      const progress = assessment.targetLevel > 0 
        ? (assessment.currentLevel / assessment.targetLevel) * 100 
        : 0;

      return {
        traineeId,
        competencyId: assessment.competencyId,
        competencyName: competency?.name || 'Unknown Competency',
        category: competency?.category || CompetencyCategory.TECHNICAL,
        currentLevel: assessment.currentLevel as CompetencyLevel,
        targetLevel: assessment.targetLevel as CompetencyLevel,
        progress: Math.min(progress, 100),
        assessmentDate: assessment.assessmentDate,
        assessorId: assessment.assessorId,
        evidence: assessment.evidence,
        developmentPlan: assessment.developmentPlan,
        gapAnalysis: assessment.targetLevel > assessment.currentLevel 
          ? `Gap of ${assessment.targetLevel - assessment.currentLevel} levels` 
          : 'Target achieved'
      };
    });
  }, [trainees, competencies]);

  // Get skills matrix summary for a trainee
  const getSkillsMatrixSummary = useCallback((traineeId: string): SkillsMatrixSummary => {
    const skillsMatrix = getTraineeSkillsMatrix(traineeId);
    const totalCompetencies = competencies.length;
    const assessedCompetencies = skillsMatrix.length;
    
    const averageLevel = skillsMatrix.length > 0 
      ? skillsMatrix.reduce((sum, skill) => sum + skill.currentLevel, 0) / skillsMatrix.length 
      : 0;

    const competenciesOnTrack = skillsMatrix.filter(skill => 
      skill.currentLevel >= skill.targetLevel
    ).length;

    const competenciesBehind = skillsMatrix.filter(skill => 
      skill.currentLevel < skill.targetLevel
    ).length;

    // Category breakdown
    const categoryBreakdown = Object.values(CompetencyCategory).reduce((acc, category) => {
      const categorySkills = skillsMatrix.filter(skill => skill.category === category);
      const categoryCompetencies = competencies.filter(comp => comp.category === category);
      
      acc[category] = {
        total: categoryCompetencies.length,
        averageLevel: categorySkills.length > 0 
          ? categorySkills.reduce((sum, skill) => sum + skill.currentLevel, 0) / categorySkills.length 
          : 0,
        onTrack: categorySkills.filter(skill => skill.currentLevel >= skill.targetLevel).length,
        behind: categorySkills.filter(skill => skill.currentLevel < skill.targetLevel).length
      };
      return acc;
    }, {} as Record<CompetencyCategory, any>);

    return {
      totalCompetencies,
      assessedCompetencies,
      averageLevel,
      competenciesOnTrack,
      competenciesBehind,
      categoryBreakdown
    };
  }, [getTraineeSkillsMatrix, competencies]);

  // Identify competency gaps
  const getCompetencyGaps = useCallback((traineeId: string): CompetencyGap[] => {
    const skillsMatrix = getTraineeSkillsMatrix(traineeId);
    
    return skillsMatrix
      .filter(skill => skill.currentLevel < skill.targetLevel)
      .map(skill => {
        const gap = skill.targetLevel - skill.currentLevel;
        const priority: 'high' | 'medium' | 'low' = gap >= 3 ? 'high' : gap >= 2 ? 'medium' : 'low';
        
        const recommendedActions = [
          `Complete ${skill.competencyName} training modules`,
          `Practice with real-world projects`,
          `Seek mentorship in ${skill.competencyName}`,
          `Attend workshops or conferences`
        ];

        return {
          competencyId: skill.competencyId,
          competencyName: skill.competencyName,
          currentLevel: skill.currentLevel,
          targetLevel: skill.targetLevel,
          gap,
          priority,
          recommendedActions
        };
      })
      .sort((a, b) => b.gap - a.gap); // Sort by gap size (largest first)
  }, [getTraineeSkillsMatrix]);

  // Generate development recommendations
  const getDevelopmentRecommendations = useCallback((traineeId: string): DevelopmentRecommendation[] => {
    const gaps = getCompetencyGaps(traineeId);
    
    return gaps.map(gap => {
      const timeframeMap = {
        1: '1-2 months',
        2: '3-4 months',
        3: '6-8 months',
        4: '8-12 months'
      };

      return {
        competencyId: gap.competencyId,
        competencyName: gap.competencyName,
        currentLevel: gap.currentLevel,
        targetLevel: gap.targetLevel,
        recommendedResources: [
          `${gap.competencyName} fundamentals course`,
          `Hands-on ${gap.competencyName} projects`,
          `${gap.competencyName} certification program`,
          `Mentorship sessions`
        ],
        estimatedTimeframe: timeframeMap[gap.gap as keyof typeof timeframeMap] || '12+ months',
        prerequisites: gap.currentLevel === 1 
          ? ['Basic understanding of core concepts'] 
          : [`Level ${gap.currentLevel} proficiency in ${gap.competencyName}`],
        successMetrics: [
          `Demonstrate Level ${gap.targetLevel} skills`,
          'Complete practical assessments',
          'Receive positive mentor feedback',
          'Apply skills in real projects'
        ]
      };
    });
  }, [getCompetencyGaps]);

  // Update skill assessment
  const updateSkillAssessment = useCallback(async (
    traineeId: string,
    competencyId: string,
    assessment: Partial<SkillAssessment>
  ) => {
    try {
      setLoading(true);
      const trainee = trainees.find(t => t.id === traineeId);
      if (!trainee) {
        throw new Error('Trainee not found');
      }

      const updatedSkillsMatrix = trainee.skillsMatrix.map(skill => 
        skill.competencyId === competencyId 
          ? { ...skill, ...assessment, assessmentDate: new Date().toISOString() }
          : skill
      );

      // If competency doesn't exist in skills matrix, add it
      if (!trainee.skillsMatrix.find(skill => skill.competencyId === competencyId)) {
        const newAssessment: SkillAssessment = {
          competencyId,
          currentLevel: assessment.currentLevel || 1,
          targetLevel: assessment.targetLevel || 3,
          assessmentDate: new Date().toISOString(),
          assessorId: assessment.assessorId || '',
          evidence: assessment.evidence || [],
          developmentPlan: assessment.developmentPlan || ''
        };
        updatedSkillsMatrix.push(newAssessment);
      }

      const updatedTrainee = await updateTrainee(traineeId, {
        skillsMatrix: updatedSkillsMatrix
      });

      setTrainees(prev => prev.map(t => t.id === traineeId ? updatedTrainee : t));
      return updatedTrainee;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update skill assessment');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [trainees]);

  // Bulk update skills matrix
  const bulkUpdateSkillsMatrix = useCallback(async (
    traineeId: string,
    assessments: Array<{ competencyId: string; assessment: Partial<SkillAssessment> }>
  ) => {
    try {
      setLoading(true);
      const trainee = trainees.find(t => t.id === traineeId);
      if (!trainee) {
        throw new Error('Trainee not found');
      }

      let updatedSkillsMatrix = [...trainee.skillsMatrix];

      assessments.forEach(({ competencyId, assessment }) => {
        const existingIndex = updatedSkillsMatrix.findIndex(skill => skill.competencyId === competencyId);
        
        if (existingIndex >= 0) {
          updatedSkillsMatrix[existingIndex] = {
            ...updatedSkillsMatrix[existingIndex],
            ...assessment,
            assessmentDate: new Date().toISOString()
          };
        } else {
          const newAssessment: SkillAssessment = {
            competencyId,
            currentLevel: assessment.currentLevel || 1,
            targetLevel: assessment.targetLevel || 3,
            assessmentDate: new Date().toISOString(),
            assessorId: assessment.assessorId || '',
            evidence: assessment.evidence || [],
            developmentPlan: assessment.developmentPlan || ''
          };
          updatedSkillsMatrix.push(newAssessment);
        }
      });

      const updatedTrainee = await updateTrainee(traineeId, {
        skillsMatrix: updatedSkillsMatrix
      });

      setTrainees(prev => prev.map(t => t.id === traineeId ? updatedTrainee : t));
      return updatedTrainee;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to bulk update skills matrix');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [trainees]);

  // Compare skills between trainees
  const compareTraineeSkills = useCallback((traineeIds: string[]) => {
    const comparisons = traineeIds.map(traineeId => {
      const trainee = trainees.find(t => t.id === traineeId);
      const skillsMatrix = getTraineeSkillsMatrix(traineeId);
      const summary = getSkillsMatrixSummary(traineeId);
      
      return {
        traineeId,
        traineeName: trainee ? `${trainee.firstName} ${trainee.lastName}` : 'Unknown',
        skillsMatrix,
        summary
      };
    });

    return {
      trainees: comparisons,
      competencyComparison: competencies.map(competency => {
        const traineeScores = comparisons.map(comparison => {
          const skill = comparison.skillsMatrix.find(s => s.competencyId === competency.id);
          return {
            traineeId: comparison.traineeId,
            traineeName: comparison.traineeName,
            currentLevel: skill?.currentLevel || 0,
            targetLevel: skill?.targetLevel || 0,
            progress: skill?.progress || 0
          };
        });

        return {
          competencyId: competency.id,
          competencyName: competency.name,
          category: competency.category,
          traineeScores,
          averageLevel: traineeScores.reduce((sum, score) => sum + score.currentLevel, 0) / traineeScores.length,
          highestScore: Math.max(...traineeScores.map(score => score.currentLevel)),
          lowestScore: Math.min(...traineeScores.map(score => score.currentLevel))
        };
      })
    };
  }, [trainees, competencies, getTraineeSkillsMatrix, getSkillsMatrixSummary]);

  // Generate skills matrix report
  const generateSkillsMatrixReport = useCallback((traineeId: string) => {
    const trainee = trainees.find(t => t.id === traineeId);
    const skillsMatrix = getTraineeSkillsMatrix(traineeId);
    const summary = getSkillsMatrixSummary(traineeId);
    const gaps = getCompetencyGaps(traineeId);
    const recommendations = getDevelopmentRecommendations(traineeId);

    return {
      trainee: trainee ? {
        id: trainee.id,
        name: `${trainee.firstName} ${trainee.lastName}`,
        department: trainee.department,
        program: trainee.programId,
        startDate: trainee.startDate
      } : null,
      skillsMatrix,
      summary,
      gaps,
      recommendations,
      generatedAt: new Date().toISOString()
    };
  }, [trainees, getTraineeSkillsMatrix, getSkillsMatrixSummary, getCompetencyGaps, getDevelopmentRecommendations]);

  return {
    // Data
    trainees,
    competencies,
    loading,
    error,

    // Skills matrix operations
    getTraineeSkillsMatrix,
    getSkillsMatrixSummary,
    getCompetencyGaps,
    getDevelopmentRecommendations,
    updateSkillAssessment,
    bulkUpdateSkillsMatrix,
    compareTraineeSkills,
    generateSkillsMatrixReport
  };
};

export default useSkillsMatrix;