// Workflow Tracking Hook - Monitors workflow completion and identifies bottlenecks
import { useState, useEffect, useCallback, useMemo } from 'react';
import {
  ApprovalWorkflow,
  Trainee,
  TrainingProgram,
  QuarterlyReview,
  User,
  ApprovalStatus,
  StageStatus
} from '../types';
import {
  WorkflowAuditor,
  WorkflowAuditResult,
  WorkflowCompletionStatus,
  WorkflowIssue,
  auditWorkflows,
  getWorkflowCompletionStatuses
} from '../utils/workflowAudit';

export interface WorkflowMetrics {
  totalWorkflows: number;
  completedWorkflows: number;
  pendingWorkflows: number;
  overdueWorkflows: number;
  averageCompletionTime: number; // in days
  completionRate: number; // percentage
  bottleneckCount: number;
  criticalIssues: number;
}

export interface WorkflowBottleneck {
  workflowId: string;
  traineeId: string;
  traineeName: string;
  programName: string;
  stageName: string;
  stageNumber: number;
  daysPending: number;
  assignedTo?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface WorkflowProgress {
  workflowId: string;
  traineeId: string;
  traineeName: string;
  programName: string;
  currentStage: number;
  totalStages: number;
  completionPercentage: number;
  status: ApprovalStatus;
  estimatedCompletionDate?: string;
  daysInProgress: number;
  nextAction: string;
  blockers: string[];
}

export interface WorkflowTrackingFilters {
  status?: ApprovalStatus[];
  programId?: string;
  traineeId?: string;
  assignedTo?: string;
  overdue?: boolean;
  hasBottlenecks?: boolean;
  completionRange?: {
    min: number;
    max: number;
  };
}

export interface UseWorkflowTrackingReturn {
  // Data
  workflows: ApprovalWorkflow[];
  workflowProgress: WorkflowProgress[];
  workflowMetrics: WorkflowMetrics;
  bottlenecks: WorkflowBottleneck[];
  auditResult: WorkflowAuditResult | null;
  completionStatuses: WorkflowCompletionStatus[];
  
  // Filtering and Search
  filteredWorkflows: ApprovalWorkflow[];
  filters: WorkflowTrackingFilters;
  setFilters: (filters: WorkflowTrackingFilters) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  
  // Actions
  refreshTracking: () => Promise<void>;
  runAudit: () => Promise<WorkflowAuditResult>;
  getWorkflowDetails: (workflowId: string) => WorkflowProgress | null;
  getBottlenecksForWorkflow: (workflowId: string) => WorkflowBottleneck[];
  
  // Status
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

export const useWorkflowTracking = (
  workflows: ApprovalWorkflow[],
  trainees: Trainee[],
  programs: TrainingProgram[],
  reviews: QuarterlyReview[],
  users: User[],
  autoRefresh: boolean = true,
  refreshInterval: number = 30000 // 30 seconds
): UseWorkflowTrackingReturn => {
  // State
  const [auditResult, setAuditResult] = useState<WorkflowAuditResult | null>(null);
  const [completionStatuses, setCompletionStatuses] = useState<WorkflowCompletionStatus[]>([]);
  const [filters, setFilters] = useState<WorkflowTrackingFilters>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Memoized calculations
  const workflowProgress = useMemo((): WorkflowProgress[] => {
    return workflows.map(workflow => {
      const trainee = trainees.find(t => t.id === workflow.traineeId);
      const program = programs.find(p => p.id === workflow.programId);
      const completedStages = workflow.stages.filter(s => s.status === StageStatus.APPROVED).length;
      const completionPercentage = (completedStages / workflow.stages.length) * 100;
      
      const initiatedDate = new Date(workflow.initiatedAt);
      const now = new Date();
      const daysInProgress = Math.ceil((now.getTime() - initiatedDate.getTime()) / (1000 * 60 * 60 * 24));
      
      // Determine next action
      let nextAction = 'No action required';
      const currentStageData = workflow.stages.find(s => s.stageNumber === workflow.currentStage);
      if (currentStageData && currentStageData.status === StageStatus.PENDING) {
        if (currentStageData.approverId) {
          const approver = users.find(u => u.id === currentStageData.approverId);
          nextAction = `Awaiting approval from ${approver ? `${approver.firstName} ${approver.lastName}` : 'assigned approver'}`;
        } else {
          nextAction = 'Assign approver to current stage';
        }
      }
      
      // Identify blockers
      const blockers: string[] = [];
      if (currentStageData && !currentStageData.approverId) {
        blockers.push('No approver assigned');
      }
      if (currentStageData && currentStageData.approverId) {
        const approver = users.find(u => u.id === currentStageData.approverId);
        if (approver && !approver.isActive) {
          blockers.push('Assigned approver is inactive');
        }
      }
      
      return {
        workflowId: workflow.id,
        traineeId: workflow.traineeId,
        traineeName: trainee ? `${trainee.firstName} ${trainee.lastName}` : 'Unknown Trainee',
        programName: program ? program.title : 'Unknown Program',
        currentStage: workflow.currentStage,
        totalStages: workflow.stages.length,
        completionPercentage,
        status: workflow.status,
        daysInProgress,
        nextAction,
        blockers
      };
    });
  }, [workflows, trainees, programs, users]);

  const workflowMetrics = useMemo((): WorkflowMetrics => {
    const totalWorkflows = workflows.length;
    const completedWorkflows = workflows.filter(w => w.status === ApprovalStatus.APPROVED).length;
    const pendingWorkflows = workflows.filter(w => 
      w.status === ApprovalStatus.PENDING || w.status === ApprovalStatus.IN_PROGRESS
    ).length;
    
    // Calculate average completion time for completed workflows
    const completedWithTimes = workflows.filter(w => w.status === ApprovalStatus.APPROVED && w.completedAt);
    const averageCompletionTime = completedWithTimes.length > 0 
      ? completedWithTimes.reduce((sum, w) => {
          const initiated = new Date(w.initiatedAt);
          const completed = new Date(w.completedAt!);
          const days = Math.ceil((completed.getTime() - initiated.getTime()) / (1000 * 60 * 60 * 24));
          return sum + days;
        }, 0) / completedWithTimes.length
      : 0;
    
    const completionRate = totalWorkflows > 0 ? (completedWorkflows / totalWorkflows) * 100 : 0;
    const overdueWorkflows = auditResult ? auditResult.overdueWorkflows : 0;
    const bottleneckCount = auditResult ? auditResult.bottlenecks : 0;
    const criticalIssues = auditResult ? auditResult.summary.criticalIssues : 0;
    
    return {
      totalWorkflows,
      completedWorkflows,
      pendingWorkflows,
      overdueWorkflows,
      averageCompletionTime,
      completionRate,
      bottleneckCount,
      criticalIssues
    };
  }, [workflows, auditResult]);

  const bottlenecks = useMemo((): WorkflowBottleneck[] => {
    if (!auditResult) return [];
    
    return auditResult.issues
      .filter(issue => issue.type === 'bottleneck')
      .map(issue => {
        const workflow = workflows.find(w => w.id === issue.workflowId);
        const trainee = trainees.find(t => t.id === issue.affectedEntity);
        const program = workflow ? programs.find(p => p.id === workflow.programId) : null;
        const stage = workflow?.stages.find(s => s.id === issue.metadata?.stageId);
        
        return {
          workflowId: issue.workflowId,
          traineeId: issue.affectedEntity,
          traineeName: trainee ? `${trainee.firstName} ${trainee.lastName}` : 'Unknown Trainee',
          programName: program ? program.title : 'Unknown Program',
          stageName: stage ? stage.stageName : 'Unknown Stage',
          stageNumber: stage ? stage.stageNumber : 0,
          daysPending: issue.metadata?.daysPending || 0,
          assignedTo: stage?.approverId ? users.find(u => u.id === stage.approverId)?.firstName + ' ' + users.find(u => u.id === stage.approverId)?.lastName : undefined,
          severity: issue.severity
        };
      });
  }, [auditResult, workflows, trainees, programs, users]);

  const filteredWorkflows = useMemo(() => {
    let filtered = workflows;
    
    // Apply status filter
    if (filters.status && filters.status.length > 0) {
      filtered = filtered.filter(w => filters.status!.includes(w.status));
    }
    
    // Apply program filter
    if (filters.programId) {
      filtered = filtered.filter(w => w.programId === filters.programId);
    }
    
    // Apply trainee filter
    if (filters.traineeId) {
      filtered = filtered.filter(w => w.traineeId === filters.traineeId);
    }
    
    // Apply assigned to filter
    if (filters.assignedTo) {
      filtered = filtered.filter(w => 
        w.stages.some(s => s.approverId === filters.assignedTo)
      );
    }
    
    // Apply overdue filter
    if (filters.overdue) {
      const overdueWorkflowIds = auditResult?.issues
        .filter(i => i.type === 'overdue')
        .map(i => i.workflowId) || [];
      filtered = filtered.filter(w => overdueWorkflowIds.includes(w.id));
    }
    
    // Apply bottleneck filter
    if (filters.hasBottlenecks) {
      const bottleneckWorkflowIds = bottlenecks.map(b => b.workflowId);
      filtered = filtered.filter(w => bottleneckWorkflowIds.includes(w.id));
    }
    
    // Apply completion range filter
    if (filters.completionRange) {
      filtered = filtered.filter(w => {
        const progress = workflowProgress.find(p => p.workflowId === w.id);
        if (!progress) return false;
        return progress.completionPercentage >= filters.completionRange!.min &&
               progress.completionPercentage <= filters.completionRange!.max;
      });
    }
    
    // Apply search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(w => {
        const trainee = trainees.find(t => t.id === w.traineeId);
        const program = programs.find(p => p.id === w.programId);
        const traineeName = trainee ? `${trainee.firstName} ${trainee.lastName}`.toLowerCase() : '';
        const programName = program ? program.title.toLowerCase() : '';
        
        return traineeName.includes(query) || 
               programName.includes(query) ||
               w.id.toLowerCase().includes(query);
      });
    }
    
    return filtered;
  }, [workflows, filters, searchQuery, auditResult, bottlenecks, workflowProgress, trainees, programs]);

  // Actions
  const runAudit = useCallback(async (): Promise<WorkflowAuditResult> => {
    setLoading(true);
    setError(null);
    
    try {
      const result = auditWorkflows(workflows, trainees, programs, reviews, users);
      const statuses = getWorkflowCompletionStatuses(workflows, trainees, programs, reviews, users);
      
      setAuditResult(result);
      setCompletionStatuses(statuses);
      setLastUpdated(new Date());
      
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to run workflow audit';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [workflows, trainees, programs, reviews, users]);

  const refreshTracking = useCallback(async () => {
    await runAudit();
  }, [runAudit]);

  const getWorkflowDetails = useCallback((workflowId: string): WorkflowProgress | null => {
    return workflowProgress.find(p => p.workflowId === workflowId) || null;
  }, [workflowProgress]);

  const getBottlenecksForWorkflow = useCallback((workflowId: string): WorkflowBottleneck[] => {
    return bottlenecks.filter(b => b.workflowId === workflowId);
  }, [bottlenecks]);

  // Auto-refresh effect
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(() => {
        runAudit();
      }, refreshInterval);
      
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, runAudit]);

  // Initial audit run
  useEffect(() => {
    if (workflows.length > 0) {
      runAudit();
    }
  }, [workflows.length]); // Only run when workflows are first loaded

  return {
    // Data
    workflows,
    workflowProgress,
    workflowMetrics,
    bottlenecks,
    auditResult,
    completionStatuses,
    
    // Filtering and Search
    filteredWorkflows,
    filters,
    setFilters,
    searchQuery,
    setSearchQuery,
    
    // Actions
    refreshTracking,
    runAudit,
    getWorkflowDetails,
    getBottlenecksForWorkflow,
    
    // Status
    loading,
    error,
    lastUpdated
  };
};

// Helper hook for workflow statistics
export const useWorkflowStatistics = (trackingData: UseWorkflowTrackingReturn) => {
  return useMemo(() => {
    const { workflowMetrics, auditResult, workflowProgress } = trackingData;
    
    return {
      // Performance metrics
      efficiency: {
        completionRate: workflowMetrics.completionRate,
        averageCompletionTime: workflowMetrics.averageCompletionTime,
        onTimeCompletionRate: auditResult ? 
          ((workflowMetrics.completedWorkflows - auditResult.overdueWorkflows) / workflowMetrics.totalWorkflows) * 100 : 0
      },
      
      // Issue distribution
      issues: auditResult ? {
        total: auditResult.issues.length,
        bySeverity: auditResult.summary,
        byType: auditResult.issues.reduce((acc, issue) => {
          acc[issue.type] = (acc[issue.type] || 0) + 1;
          return acc;
        }, {} as Record<string, number>)
      } : null,
      
      // Progress distribution
      progressDistribution: {
        notStarted: workflowProgress.filter(p => p.completionPercentage === 0).length,
        inProgress: workflowProgress.filter(p => p.completionPercentage > 0 && p.completionPercentage < 100).length,
        completed: workflowProgress.filter(p => p.completionPercentage === 100).length
      },
      
      // Bottleneck analysis
      bottleneckAnalysis: {
        totalBottlenecks: trackingData.bottlenecks.length,
        averageDaysPending: trackingData.bottlenecks.length > 0 ?
          trackingData.bottlenecks.reduce((sum, b) => sum + b.daysPending, 0) / trackingData.bottlenecks.length : 0,
        mostCommonStage: trackingData.bottlenecks.length > 0 ?
          trackingData.bottlenecks.reduce((acc, b) => {
            acc[b.stageName] = (acc[b.stageName] || 0) + 1;
            return acc;
          }, {} as Record<string, number>) : {}
      }
    };
  }, [trackingData]);
};