import { useState, useEffect, useCallback } from 'react';
import { Trainee, TraineeStatus } from '../types';
import { 
  fetchTrainees as apiFetchTrainees,
  createTrainee as apiCreateTrainee,
  updateTrainee as apiUpdateTrainee,
  deleteTrainee as apiDeleteTrainee
} from '../mockApi';

interface UseTraineesOptions {
  autoFetch?: boolean;
  filters?: {
    status?: TraineeStatus[];
    department?: string[];
    program?: string[];
    mentor?: string[];
  };
}

interface UseTraineesReturn {
  trainees: Trainee[];
  loading: boolean;
  error: string | null;
  fetchTrainees: () => Promise<void>;
  createTrainee: (trainee: Omit<Trainee, 'id' | 'createdAt' | 'updatedAt' | 'reviews'>) => Promise<Trainee | null>;
  updateTrainee: (id: string, updates: Partial<Trainee>) => Promise<Trainee | null>;
  deleteTrainee: (id: string) => Promise<boolean>;
  getTraineeById: (id: string) => Trainee | undefined;
  getTraineesByStatus: (status: TraineeStatus) => Trainee[];
  getTraineesByProgram: (programId: string) => Trainee[];
  getTraineesByMentor: (mentorId: string) => Trainee[];
  searchTrainees: (query: string) => Trainee[];
  refreshTrainees: () => Promise<void>;
}

export const useTrainees = (options: UseTraineesOptions = {}): UseTraineesReturn => {
  const { autoFetch = true, filters } = options;
  const [trainees, setTrainees] = useState<Trainee[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchTrainees = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await apiFetchTrainees();
      let filteredData = data;

      // Apply filters if provided
      if (filters) {
        if (filters.status && filters.status.length > 0) {
          filteredData = filteredData.filter(trainee => 
            filters.status!.includes(trainee.status)
          );
        }
        if (filters.department && filters.department.length > 0) {
          filteredData = filteredData.filter(trainee => 
            filters.department!.includes(trainee.department)
          );
        }
        if (filters.program && filters.program.length > 0) {
          filteredData = filteredData.filter(trainee => 
            filters.program!.includes(trainee.programId)
          );
        }
        if (filters.mentor && filters.mentor.length > 0) {
          filteredData = filteredData.filter(trainee => 
            filters.mentor!.includes(trainee.mentorId)
          );
        }
      }

      setTrainees(filteredData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch trainees');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  const createTrainee = useCallback(async (traineeData: Omit<Trainee, 'id' | 'createdAt' | 'updatedAt' | 'reviews'>): Promise<Trainee | null> => {
    setLoading(true);
    setError(null);
    try {
      const newTrainee = await apiCreateTrainee(traineeData);
      setTrainees(prev => [...prev, newTrainee]);
      return newTrainee;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create trainee');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateTrainee = useCallback(async (id: string, updates: Partial<Trainee>): Promise<Trainee | null> => {
    setLoading(true);
    setError(null);
    try {
      const updatedTrainee = await apiUpdateTrainee(id, updates);
      setTrainees(prev => 
        prev.map(trainee => 
          trainee.id === id ? updatedTrainee : trainee
        )
      );
      return updatedTrainee;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update trainee');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteTrainee = useCallback(async (id: string): Promise<boolean> => {
    setLoading(true);
    setError(null);
    try {
      await apiDeleteTrainee(id);
      setTrainees(prev => prev.filter(trainee => trainee.id !== id));
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete trainee');
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const getTraineeById = useCallback((id: string): Trainee | undefined => {
    return trainees.find(trainee => trainee.id === id);
  }, [trainees]);

  const getTraineesByStatus = useCallback((status: TraineeStatus): Trainee[] => {
    return trainees.filter(trainee => trainee.status === status);
  }, [trainees]);

  const getTraineesByProgram = useCallback((programId: string): Trainee[] => {
    return trainees.filter(trainee => trainee.programId === programId);
  }, [trainees]);

  const getTraineesByMentor = useCallback((mentorId: string): Trainee[] => {
    return trainees.filter(trainee => trainee.mentorId === mentorId);
  }, [trainees]);

  const searchTrainees = useCallback((query: string): Trainee[] => {
    const lowercaseQuery = query.toLowerCase();
    return trainees.filter(trainee => 
      trainee.firstName.toLowerCase().includes(lowercaseQuery) ||
      trainee.lastName.toLowerCase().includes(lowercaseQuery) ||
      trainee.email.toLowerCase().includes(lowercaseQuery) ||
      trainee.employeeId.toLowerCase().includes(lowercaseQuery) ||
      trainee.department.toLowerCase().includes(lowercaseQuery)
    );
  }, [trainees]);

  const refreshTrainees = useCallback(async () => {
    await fetchTrainees();
  }, [fetchTrainees]);

  useEffect(() => {
    if (autoFetch) {
      fetchTrainees();
    }
  }, [autoFetch, fetchTrainees]);

  return {
    trainees,
    loading,
    error,
    fetchTrainees,
    createTrainee,
    updateTrainee,
    deleteTrainee,
    getTraineeById,
    getTraineesByStatus,
    getTraineesByProgram,
    getTraineesByMentor,
    searchTrainees,
    refreshTrainees
  };
};