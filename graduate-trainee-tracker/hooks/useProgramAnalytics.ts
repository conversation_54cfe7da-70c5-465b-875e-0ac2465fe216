import { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  Training<PERSON><PERSON>ram, 
  Trainee, 
  QuarterlyReview, 
  Competency,
  TraineeStatus,
  ReviewStatus,
  CompetencyLevel,
  ProgramAnalytics,
  PerformanceTrend,
  CompetencyDistribution,
  LevelDistribution
} from '../types';
import { 
  fetchPrograms, 
  fetchTrainees, 
  fetchReviews, 
  fetchCompetencies 
} from '../mockApi';

interface ProgramMetrics {
  totalTrainees: number;
  activeTrainees: number;
  completedTrainees: number;
  terminatedTrainees: number;
  completionRate: number;
  averageRating: number;
  averageDuration: number;
  retentionRate: number;
}

interface CompetencyAnalysis {
  competencyId: string;
  name: string;
  category: string;
  averageLevel: number;
  improvementRate: number;
  assessmentCount: number;
  distribution: LevelDistribution[];
}

interface ReviewAnalytics {
  totalReviews: number;
  completedReviews: number;
  overdueReviews: number;
  averageCompletionTime: number;
  reviewCompletionRate: number;
  quarterlyTrends: PerformanceTrend[];
}

interface UseProgramAnalyticsOptions {
  programId?: string;
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  autoRefresh?: boolean;
  refreshInterval?: number;
}

interface UseProgramAnalyticsReturn {
  program: TrainingProgram | null;
  programMetrics: ProgramMetrics | null;
  competencyAnalysis: CompetencyAnalysis[];
  reviewAnalytics: ReviewAnalytics | null;
  performanceTrends: PerformanceTrend[];
  loading: boolean;
  error: string | null;
  refreshAnalytics: () => Promise<void>;
  generateReport: () => ProgramAnalytics | null;
  comparePrograms: (programIds: string[]) => Promise<ProgramAnalytics[]>;
  getProgramRanking: () => Promise<{ programId: string; score: number; rank: number }[]>;
  getTopPerformers: (limit?: number) => Trainee[];
  getUnderperformers: (threshold?: number) => Trainee[];
}

export const useProgramAnalytics = (options: UseProgramAnalyticsOptions = {}): UseProgramAnalyticsReturn => {
  const { programId, dateRange, autoRefresh = false, refreshInterval = 300000 } = options;

  const [program, setProgram] = useState<TrainingProgram | null>(null);
  const [trainees, setTrainees] = useState<Trainee[]>([]);
  const [reviews, setReviews] = useState<QuarterlyReview[]>([]);
  const [competencies, setCompetencies] = useState<Competency[]>([]);
  const [allPrograms, setAllPrograms] = useState<TrainingProgram[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAnalyticsData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const [programsData, traineesData, reviewsData, competenciesData] = await Promise.all([
        fetchPrograms(),
        fetchTrainees(),
        fetchReviews(),
        fetchCompetencies()
      ]);

      setAllPrograms(programsData);
      setCompetencies(competenciesData);

      if (programId) {
        const currentProgram = programsData.find(p => p.id === programId);
        if (!currentProgram) {
          throw new Error('Program not found');
        }
        setProgram(currentProgram);

        // Filter data for specific program
        const programTrainees = traineesData.filter(t => t.programId === programId);
        const programReviews = reviewsData.filter(r => 
          programTrainees.some(t => t.id === r.traineeId)
        );

        setTrainees(programTrainees);
        setReviews(programReviews);
      } else {
        setTrainees(traineesData);
        setReviews(reviewsData);
      }

      // Apply date range filter if provided
      if (dateRange) {
        const startDate = new Date(dateRange.startDate);
        const endDate = new Date(dateRange.endDate);
        
        setTrainees(prev => prev.filter(t => {
          const traineeStart = new Date(t.startDate);
          return traineeStart >= startDate && traineeStart <= endDate;
        }));
        
        setReviews(prev => prev.filter(r => {
          const reviewDate = new Date(r.scheduledDate);
          return reviewDate >= startDate && reviewDate <= endDate;
        }));
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch analytics data');
    } finally {
      setLoading(false);
    }
  }, [programId, dateRange]);

  const refreshAnalytics = useCallback(() => fetchAnalyticsData(), [fetchAnalyticsData]);

  // Calculate program metrics
  const programMetrics = useMemo((): ProgramMetrics | null => {
    if (!trainees.length) return null;

    const totalTrainees = trainees.length;
    const activeTrainees = trainees.filter(t => t.status === TraineeStatus.ACTIVE).length;
    const completedTrainees = trainees.filter(t => t.status === TraineeStatus.COMPLETED).length;
    const terminatedTrainees = trainees.filter(t => t.status === TraineeStatus.TERMINATED).length;
    
    const completionRate = totalTrainees > 0 ? (completedTrainees / totalTrainees) * 100 : 0;
    const retentionRate = totalTrainees > 0 ? ((activeTrainees + completedTrainees) / totalTrainees) * 100 : 0;

    // Calculate average rating from completed reviews
    const completedReviews = reviews.filter(r => r.status === ReviewStatus.COMPLETED);
    const averageRating = completedReviews.length > 0 
      ? completedReviews.reduce((sum, r) => sum + r.overallRating, 0) / completedReviews.length 
      : 0;

    // Calculate average duration for completed trainees
    const completedWithDuration = trainees.filter(t => 
      t.status === TraineeStatus.COMPLETED && t.endDate
    );
    const averageDuration = completedWithDuration.length > 0
      ? completedWithDuration.reduce((sum, t) => {
          const start = new Date(t.startDate);
          const end = new Date(t.endDate);
          return sum + (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);
        }, 0) / completedWithDuration.length
      : 0;

    return {
      totalTrainees,
      activeTrainees,
      completedTrainees,
      terminatedTrainees,
      completionRate,
      averageRating,
      averageDuration,
      retentionRate
    };
  }, [trainees, reviews]);

  // Analyze competency performance
  const competencyAnalysis = useMemo((): CompetencyAnalysis[] => {
    if (!competencies.length || !trainees.length) return [];

    return competencies.map(competency => {
      const assessments = trainees.flatMap(t => 
        t.skillsMatrix?.filter(sa => sa.competencyId === competency.id) || []
      );

      const averageLevel = assessments.length > 0
        ? assessments.reduce((sum, a) => sum + a.currentLevel, 0) / assessments.length
        : 0;

      // Calculate improvement rate (simplified)
      const improvementRate = assessments.length > 0
        ? assessments.reduce((sum, a) => {
            const improvement = a.currentLevel - CompetencyLevel.BEGINNER;
            return sum + (improvement / (CompetencyLevel.EXPERT - CompetencyLevel.BEGINNER));
          }, 0) / assessments.length * 100
        : 0;

      // Calculate level distribution
      const distribution: LevelDistribution[] = Object.values(CompetencyLevel)
        .filter(level => typeof level === 'number')
        .map(level => {
          const count = assessments.filter(a => a.currentLevel === level).length;
          const percentage = assessments.length > 0 ? (count / assessments.length) * 100 : 0;
          return {
            level: level as CompetencyLevel,
            count,
            percentage
          };
        });

      return {
        competencyId: competency.id,
        name: competency.name,
        category: competency.category,
        averageLevel,
        improvementRate,
        assessmentCount: assessments.length,
        distribution
      };
    });
  }, [competencies, trainees]);

  // Analyze review performance
  const reviewAnalytics = useMemo((): ReviewAnalytics | null => {
    if (!reviews.length) return null;

    const totalReviews = reviews.length;
    const completedReviews = reviews.filter(r => r.status === ReviewStatus.COMPLETED).length;
    const overdueReviews = reviews.filter(r => r.status === ReviewStatus.OVERDUE).length;
    const reviewCompletionRate = totalReviews > 0 ? (completedReviews / totalReviews) * 100 : 0;

    // Calculate average completion time
    const reviewsWithCompletion = reviews.filter(r => r.completedDate && r.scheduledDate);
    const averageCompletionTime = reviewsWithCompletion.length > 0
      ? reviewsWithCompletion.reduce((sum, r) => {
          const scheduled = new Date(r.scheduledDate);
          const completed = new Date(r.completedDate!);
          return sum + (completed.getTime() - scheduled.getTime()) / (1000 * 60 * 60 * 24);
        }, 0) / reviewsWithCompletion.length
      : 0;

    // Generate quarterly trends
    const quarterlyTrends: PerformanceTrend[] = [];
    const reviewsByQuarter = reviews.reduce((acc, review) => {
      const key = `${review.year}-Q${review.quarter}`;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(review);
      return acc;
    }, {} as Record<string, QuarterlyReview[]>);

    Object.entries(reviewsByQuarter).forEach(([period, periodReviews]) => {
      const completed = periodReviews.filter(r => r.status === ReviewStatus.COMPLETED);
      const averageRating = completed.length > 0
        ? completed.reduce((sum, r) => sum + r.overallRating, 0) / completed.length
        : 0;
      const completionRate = periodReviews.length > 0
        ? (completed.length / periodReviews.length) * 100
        : 0;

      quarterlyTrends.push({
        period,
        averageRating,
        completionRate,
        traineeCount: new Set(periodReviews.map(r => r.traineeId)).size
      });
    });

    return {
      totalReviews,
      completedReviews,
      overdueReviews,
      averageCompletionTime,
      reviewCompletionRate,
      quarterlyTrends: quarterlyTrends.sort((a, b) => a.period.localeCompare(b.period))
    };
  }, [reviews]);

  // Generate performance trends
  const performanceTrends = useMemo((): PerformanceTrend[] => {
    return reviewAnalytics?.quarterlyTrends || [];
  }, [reviewAnalytics]);

  const generateReport = useCallback((): ProgramAnalytics | null => {
    if (!programMetrics || !competencyAnalysis.length) return null;

    const competencyDistribution: CompetencyDistribution[] = competencyAnalysis.map(ca => ({
      competencyId: ca.competencyId,
      competencyName: ca.name,
      averageRating: ca.averageLevel,
      distribution: ca.distribution
    }));

    return {
      totalTrainees: programMetrics.totalTrainees,
      activeTrainees: programMetrics.activeTrainees,
      completionRate: programMetrics.completionRate,
      averageRating: programMetrics.averageRating,
      performanceTrends,
      competencyDistribution
    };
  }, [programMetrics, competencyAnalysis, performanceTrends]);

  const comparePrograms = useCallback(async (programIds: string[]): Promise<ProgramAnalytics[]> => {
    const results: ProgramAnalytics[] = [];
    
    for (const id of programIds) {
      // This would typically make separate API calls or use cached data
      // For now, we'll simulate the comparison
      const programTrainees = trainees.filter(t => t.programId === id);
      const programReviews = reviews.filter(r => 
        programTrainees.some(t => t.id === r.traineeId)
      );

      if (programTrainees.length > 0) {
        const completedTrainees = programTrainees.filter(t => t.status === TraineeStatus.COMPLETED).length;
        const completionRate = (completedTrainees / programTrainees.length) * 100;
        
        const completedReviews = programReviews.filter(r => r.status === ReviewStatus.COMPLETED);
        const averageRating = completedReviews.length > 0
          ? completedReviews.reduce((sum, r) => sum + r.overallRating, 0) / completedReviews.length
          : 0;

        results.push({
          totalTrainees: programTrainees.length,
          activeTrainees: programTrainees.filter(t => t.status === TraineeStatus.ACTIVE).length,
          completionRate,
          averageRating,
          performanceTrends: [],
          competencyDistribution: []
        });
      }
    }

    return results;
  }, [trainees, reviews]);

  const getProgramRanking = useCallback(async (): Promise<{ programId: string; score: number; rank: number }[]> => {
    const rankings = allPrograms.map(program => {
      const programTrainees = trainees.filter(t => t.programId === program.id);
      const programReviews = reviews.filter(r => 
        programTrainees.some(t => t.id === r.traineeId)
      );

      if (programTrainees.length === 0) {
        return { programId: program.id, score: 0, rank: 0 };
      }

      const completionRate = programTrainees.filter(t => t.status === TraineeStatus.COMPLETED).length / programTrainees.length;
      const completedReviews = programReviews.filter(r => r.status === ReviewStatus.COMPLETED);
      const averageRating = completedReviews.length > 0
        ? completedReviews.reduce((sum, r) => sum + r.overallRating, 0) / completedReviews.length
        : 0;
      
      // Simple scoring algorithm (can be enhanced)
      const score = (completionRate * 0.4 + (averageRating / 5) * 0.6) * 100;
      
      return { programId: program.id, score, rank: 0 };
    });

    // Sort by score and assign ranks
    rankings.sort((a, b) => b.score - a.score);
    rankings.forEach((ranking, index) => {
      ranking.rank = index + 1;
    });

    return rankings;
  }, [allPrograms, trainees, reviews]);

  const getTopPerformers = useCallback((limit: number = 5): Trainee[] => {
    const traineeScores = trainees.map(trainee => {
      const traineeReviews = reviews.filter(r => r.traineeId === trainee.id && r.status === ReviewStatus.COMPLETED);
      const averageRating = traineeReviews.length > 0
        ? traineeReviews.reduce((sum, r) => sum + r.overallRating, 0) / traineeReviews.length
        : 0;
      
      const competencyAverage = trainee.skillsMatrix?.length > 0
        ? trainee.skillsMatrix.reduce((sum, sa) => sum + sa.currentLevel, 0) / trainee.skillsMatrix.length
        : 0;
      
      const score = (averageRating + competencyAverage) / 2;
      
      return { trainee, score };
    });

    return traineeScores
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .map(ts => ts.trainee);
  }, [trainees, reviews]);

  const getUnderperformers = useCallback((threshold: number = 2.5): Trainee[] => {
    return trainees.filter(trainee => {
      const traineeReviews = reviews.filter(r => r.traineeId === trainee.id && r.status === ReviewStatus.COMPLETED);
      const averageRating = traineeReviews.length > 0
        ? traineeReviews.reduce((sum, r) => sum + r.overallRating, 0) / traineeReviews.length
        : 0;
      
      return averageRating < threshold && traineeReviews.length > 0;
    });
  }, [trainees, reviews]);

  // Auto-refresh effect
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(fetchAnalyticsData, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, fetchAnalyticsData]);

  // Initial data fetch
  useEffect(() => {
    fetchAnalyticsData();
  }, [fetchAnalyticsData]);

  return {
    program,
    programMetrics,
    competencyAnalysis,
    reviewAnalytics,
    performanceTrends,
    loading,
    error,
    refreshAnalytics,
    generateReport,
    comparePrograms,
    getProgramRanking,
    getTopPerformers,
    getUnderperformers
  };
};