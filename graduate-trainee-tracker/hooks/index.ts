// Data fetching hooks
export { useTrainees } from './useTrainees';
export { usePrograms } from './usePrograms';
export { useReviews } from './useReviews';
export { useWorkflows } from './useWorkflows';
export { useNotifications } from './useNotifications';
export { useCompetencies } from './useCompetencies';
export { useApi } from './useApi';

// State management hooks
export { useGraduateTracker } from './useGraduateTracker';
export { useFilters } from './useFilters';
export { usePagination } from './usePagination';
export { useSorting } from './useSorting';
// useAuth removed - authentication not needed
export { usePermissions } from './usePermissions';

// Business logic hooks
export { useTraineeProgress } from './useTraineeProgress';
export { useProgramAnalytics } from './useProgramAnalytics';
export { useReviewScheduling } from './useReviewScheduling';
export { useApprovalProcess } from './useApprovalProcess';
export { useSkillsMatrix } from './useSkillsMatrix';
export { useExport } from './useExport';
export { useErrorHandling } from './useErrorHandling';

// UI/UX hooks
export { default as useLoading } from './useLoading';
export { default as useModal } from './useModal';
export { default as useSidebar } from './useSidebar';
export { default as useToast } from './useToast';