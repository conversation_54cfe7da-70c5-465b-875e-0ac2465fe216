import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  GraduateTraineeTrackerProps,
  GraduateTraineeTrackerState,
  User,
  Trainee,
  TrainingProgram,
  QuarterlyReview,
  ApprovalWorkflow,
  Notification,
  TraineeStatus,
  GoalCategory,
  GoalStatus,
  MilestoneStatus,
  ResourceType,
  SkillAssessment,
  Competency,
  CompetencyCategory,
  CompetencyLevel,
  ReviewType,
  ReviewStatus,
  AssessorType,
  ApprovalStatus,
  StageStatus,
  ApproverType,
  DevelopmentGoal,
  ActionItem,
  ActionItemStatus,
  Priority,
  Feedback,
  FeedbackCategory,
  ProgramStatus,
  NotificationType,
  NotificationPriority,
  UserRole
} from './types';
import { mockApi } from './mockApi';
import { TraineeTracker } from './Trainees/TraineeTracker';
import { ProgramManagement } from './Programs/ProgramManagement';
import { ReviewSystem } from './Reviews/ReviewSystem';
import { ApprovalWorkflow as ApprovalWorkflowComponent } from './Approvals/ApprovalWorkflow';
import { Dashboard } from './Dashboard/Dashboard';
import { NotificationCenter } from './Notifications/NotificationCenter';

// Import UI components
import { PageHeader } from './ui/layout/PageHeader';
import { Sidebar } from './ui/layout/Sidebar';
import { ResponsiveWrapper } from './ui/layout/ResponsiveWrapper';
import { TraineeTable } from './ui/display/TraineeTable';
import { TraineeCard } from './ui/display/TraineeCard';
import { LoadingSpinner } from './ui/feedback/LoadingSpinner';
import { EmptyState } from './ui/feedback/EmptyState';
import { ErrorBoundary } from './ui/feedback/ErrorBoundary';
import { BulkActions } from './ui/actions/BulkActions';
import { SearchBar } from './ui/forms/SearchBar';
import { FilterForm } from './ui/forms/FilterForm';
import { UserManagementModal } from './ui/modals/UserManagementModal';
import { ConfirmationModal } from './ui/modals/ConfirmationModal';
import { ExportModal } from './ui/modals/ExportModal';

type ViewType = 'dashboard' | 'trainees' | 'programs' | 'reviews' | 'approvals' | 'analytics' | 'notifications' | 'people' | 'school' | 'assessment';

// Mock current user
const mockCurrentUser: User = {
  id: 'user-001',
  employeeId: 'EMP001',
  firstName: 'Sarah',
  lastName: 'Johnson',
  email: '<EMAIL>',
  role: UserRole.LD_OFFICER,
  department: 'Learning & Development',
  permissions: [],
  isActive: true,
  createdAt: '2024-01-15T08:00:00Z',
  updatedAt: '2024-08-28T10:00:00Z'
};

const GraduateTraineeTrackerFixed: React.FC<GraduateTraineeTrackerProps> = ({ 
  currentUser = mockCurrentUser, 
  onNavigate 
}) => {
  const [state, setState] = useState<GraduateTraineeTrackerState>({
    currentUser: currentUser,
    trainees: [],
    programs: [],
    mentors: [],
    reviews: [],
    workflows: [],
    notifications: [],
    competencies: [],
    loading: true,
    error: null,
    selectedTrainee: null,
    selectedProgram: null,
    filters: {
      status: [],
      department: [],
      program: [],
      mentor: [],
      dateRange: {
        startDate: '',
        endDate: ''
      }
    },
    pagination: {
      page: 1,
      pageSize: 10,
      total: 0
    }
  });

  const [activeView, setActiveView] = useState<ViewType>('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [selectedTrainees, setSelectedTrainees] = useState<string[]>([]);
  const [showUserModal, setShowUserModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [traineeToDelete, setTraineeToDelete] = useState<Trainee | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<any>({});

  // Load data using mock API
  useEffect(() => {
    const loadData = async () => {
      try {
        setState(prev => ({ ...prev, loading: true }));

        const [trainees, programs, mentors, reviews, workflows, notifications, competencies] = await Promise.all([
          mockApi.fetchTrainees(),
          mockApi.fetchPrograms(),
          mockApi.fetchMentors(),
          mockApi.fetchReviews(),
          mockApi.fetchWorkflows(),
          mockApi.fetchNotifications(),
          mockApi.fetchCompetencies()
        ]);

        setState(prev => ({
          ...prev,
          trainees,
          programs,
          mentors,
          reviews,
          workflows,
          notifications,
          competencies,
          loading: false,
          error: null
        }));
      } catch (error) {
        console.error('Failed to load data:', error);
        setState(prev => ({
          ...prev,
          loading: false,
          error: 'Failed to load data. Please try again.'
        }));
      }
    };

    loadData();
  }, []); // Empty dependency array - only run once on mount

  const handleViewChange = (view: ViewType) => {
    setActiveView(view);
  };

  const handleExportTraineesData = async () => {
    console.log('Exporting trainees data...');
    // Mock export functionality
    const data = state.trainees;
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'trainees-export.json';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const handleExportProgramsData = async () => {
    console.log('Exporting programs data...');
    // Mock export functionality
    const data = state.programs;
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'programs-export.json';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const renderView = () => {
    switch (activeView) {
      case 'dashboard':
        return (
          <Dashboard
            currentUser={state.currentUser!}
            state={state}
            onStateUpdate={setState}
          />
        );
      case 'trainees':
        return (
          <div className="p-6">
            <PageHeader
              title="Trainee Management"
              actions={[
                {
                  label: 'Add Trainee',
                  onClick: () => setShowUserModal(true),
                  variant: 'primary'
                },
                {
                  label: 'Export',
                  onClick: () => setShowExportModal(true),
                  variant: 'secondary'
                }
              ]}
            />

            <div className="mt-6">
              <div className="space-y-4 mb-6">
                <SearchBar
                  onSearch={setSearchTerm}
                  placeholder="Search trainees..."
                />
                <FilterForm
                  onFilter={setFilters}
                />
              </div>

              {selectedTrainees.length > 0 && (
                <BulkActions
                  selectedCount={selectedTrainees.length}
                  actions={[
                    {
                      label: 'Export Selected',
                      value: 'export'
                    },
                    {
                      label: 'Delete Selected',
                      value: 'delete'
                    }
                  ]}
                  onAction={(actionValue) => {
                    switch (actionValue) {
                      case 'export':
                        setShowExportModal(true);
                        break;
                      case 'delete':
                        if (selectedTrainees.length > 0 && state.trainees.length > 0) {
                          const traineeToDelete = state.trainees.find(t => t.id === selectedTrainees[0]);
                          setTraineeToDelete(traineeToDelete || null);
                          setShowDeleteModal(true);
                        }
                        break;
                    }
                  }}
                  onClearSelection={() => setSelectedTrainees([])}
                />
              )}

              <ResponsiveWrapper>
                {(isMobile) => (
                  isMobile ? (
                    <div className="grid grid-cols-1 gap-4 mt-4">
                      {state.trainees.map(trainee => (
                        <TraineeCard
                          key={trainee.id}
                          trainee={trainee}
                          programs={state.programs}
                          mentors={state.mentors}
                          onEdit={() => {
                            setTraineeToDelete(trainee);
                            setShowUserModal(true);
                          }}
                          onDelete={() => {
                            setTraineeToDelete(trainee);
                            setShowDeleteModal(true);
                          }}
                          onView={() => {
                            console.log('View trainee:', trainee);
                          }}
                        />
                      ))}
                    </div>
                  ) : (
                    <TraineeTable
                      trainees={state.trainees}
                      programs={state.programs}
                      mentors={state.mentors}
                      onEdit={(trainee) => {
                        setTraineeToDelete(trainee);
                        setShowUserModal(true);
                      }}
                      onDelete={(trainee) => {
                        setTraineeToDelete(trainee);
                        setShowDeleteModal(true);
                      }}
                      onView={(trainee) => {
                        console.log('View trainee:', trainee);
                      }}
                    />
                  )
                )}
              </ResponsiveWrapper>
            </div>
          </div>
        );
      case 'programs':
        return (
          <ProgramManagement
            programs={state.programs}
            onCreateProgram={async (program) => {
              const newProgram: TrainingProgram = {
                ...program as any,
                id: `prog-${Date.now()}`,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
              };
              setState(prev => ({
                ...prev,
                programs: [...prev.programs, newProgram]
              }));
            }}
            onUpdateProgram={async (id, updates) => {
              setState(prev => ({
                ...prev,
                programs: prev.programs.map(p =>
                  p.id === id ? { ...p, ...updates, updatedAt: new Date().toISOString() } : p
                )
              }));
            }}
            onDeleteProgram={async (id) => {
              setState(prev => ({
                ...prev,
                programs: prev.programs.filter(p => p.id !== id)
              }));
            }}
          />
        );
      case 'reviews':
        return (
          <ReviewSystem
            reviews={state.reviews}
            trainees={state.trainees}
            currentUser={state.currentUser!}
            onSubmitReview={async (review) => {
              setState(prev => ({
                ...prev,
                reviews: [...prev.reviews, review]
              }));
            }}
          />
        );
      case 'approvals':
        return (
          <ApprovalWorkflowComponent
            workflows={state.workflows}
            currentUser={state.currentUser!}
            onApprove={async (workflowId, stageId, comments) => {
              setState(prev => ({
                ...prev,
                workflows: prev.workflows.map(w =>
                  w.id === workflowId
                    ? {
                        ...w,
                        stages: w.stages.map(s =>
                          s.id === stageId
                            ? { ...s, status: StageStatus.APPROVED, approvedAt: new Date().toISOString(), comments }
                            : s
                        ),
                        status: w.stages.every(s => s.status === StageStatus.APPROVED)
                          ? ApprovalStatus.APPROVED
                          : w.status
                      }
                    : w
                )
              }));
            }}
            onReject={async (workflowId, stageId, reason) => {
              setState(prev => ({
                ...prev,
                workflows: prev.workflows.map(w =>
                  w.id === workflowId
                    ? {
                        ...w,
                        stages: w.stages.map(s =>
                          s.id === stageId
                            ? { ...s, status: StageStatus.REJECTED, rejectedAt: new Date().toISOString(), rejectionReason: reason }
                            : s
                        ),
                        status: ApprovalStatus.REJECTED
                      }
                    : w
                )
              }));
            }}
          />
        );
      case 'notifications':
        return (
          <NotificationCenter
            notifications={state.notifications}
            currentUser={state.currentUser!}
            onMarkAsRead={async (notificationId) => {
              setState(prev => ({
                ...prev,
                notifications: prev.notifications.map(n =>
                  n.id === notificationId ? { ...n, isRead: true, read: true } : n
                )
              }));
            }}
          />
        );
      case 'analytics':
        return (
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Analytics Dashboard</h2>
              <button
                onClick={handleExportTraineesData}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Export Trainees Data
              </button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-white p-4 rounded-lg shadow">
                <h3 className="text-lg font-semibold">Total Trainees</h3>
                <p className="text-3xl font-bold text-blue-600">{state.trainees.length}</p>
              </div>
              <div className="bg-white p-4 rounded-lg shadow">
                <h3 className="text-lg font-semibold">Active Programs</h3>
                <p className="text-3xl font-bold text-green-600">
                  {state.programs.filter(p => p.status === ProgramStatus.ACTIVE).length}
                </p>
              </div>
              <div className="bg-white p-4 rounded-lg shadow">
                <h3 className="text-lg font-semibold">Completed Reviews</h3>
                <p className="text-3xl font-bold text-purple-600">
                  {state.reviews.filter(r => r.status === ReviewStatus.COMPLETED).length}
                </p>
              </div>
              <div className="bg-white p-4 rounded-lg shadow">
                <h3 className="text-lg font-semibold">Pending Approvals</h3>
                <p className="text-3xl font-bold text-orange-600">
                  {state.workflows.filter(w => w.status === ApprovalStatus.PENDING).length}
                </p>
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  if (state.loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner isLoading={state.loading} />
      </div>
    );
  }

  if (state.error) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-2">Error</h2>
          <p className="text-gray-600">{state.error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="flex h-screen bg-gray-50">
        <Sidebar
          isOpen={sidebarOpen}
          onToggle={() => setSidebarOpen(!sidebarOpen)}
          activeView={activeView}
          onViewChange={(view) => handleViewChange(view as ViewType)}
          currentUser={state.currentUser!}
        />

        <div className="flex-1 flex flex-col overflow-hidden">
          <main className="flex-1 overflow-y-auto">
            <AnimatePresence mode="wait">
              <motion.div
                key={activeView}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="h-full"
              >
                {renderView()}
              </motion.div>
            </AnimatePresence>
          </main>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default GraduateTraineeTrackerFixed;
export { GraduateTraineeTrackerFixed as GraduateTraineeTracker };