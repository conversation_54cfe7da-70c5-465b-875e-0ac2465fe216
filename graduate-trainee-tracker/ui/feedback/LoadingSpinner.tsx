import React from 'react';
import { Loader2 } from 'lucide-react';
import { LoadingSpinnerProps } from '../types';
import { useApi } from '../../hooks/useApi';

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  isLoading,
  message = 'Loading...',
  size = 'md',
  className = '',
}) => {
  const { loading: apiLoading } = useApi();

  // Priority: external isLoading prop > fallback to internal API loading
  const loading = isLoading !== undefined ? isLoading : apiLoading;

  if (!loading) return null;

  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'w-4 h-4';
      case 'md':
        return 'w-6 h-6';
      case 'lg':
        return 'w-8 h-8';
      default:
        return 'w-6 h-6';
    }
  };

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <Loader2 className={`${getSizeClass()} animate-spin text-blue-500`} />
      {message && <span className="ml-2 text-sm text-gray-600">{message}</span>}
    </div>
  );
};