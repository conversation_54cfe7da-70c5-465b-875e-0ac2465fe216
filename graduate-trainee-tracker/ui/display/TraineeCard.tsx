import React, { useState } from 'react';
import { Edit, Trash2, Eye, Calendar, User, Mail, Building, CheckCircle, Clock } from 'lucide-react';
import { Trainee, TrainingProgram, User as UserType } from '../types';

interface TraineeCardProps {
  trainee: Trainee;
  programs: TrainingProgram[];
  mentors: UserType[];
  onEdit: (trainee: Trainee) => void;
  onDelete: (trainee: Trainee) => void;
  onView: (trainee: Trainee) => void;
}

export const TraineeCard: React.FC<TraineeCardProps> = ({
  trainee,
  programs,
  mentors,
  onEdit,
  onDelete,
  onView,
}) => {
  const [loading, setLoading] = useState(false);

  // Utility functions for lookups
  const getProgramName = (programId: string): string => {
    const program = programs.find(p => p.id === programId);
    return program?.title || `Program ${programId}`;
  };

  const getMentorName = (mentorId: string): string => {
    const mentor = mentors.find(m => m.id === mentorId);
    return mentor ? `${mentor.firstName} ${mentor.lastName}` : 'Not assigned';
  };

  const getFullName = (firstName: string, lastName: string): string => {
    return `${firstName} ${lastName}`;
  };

  const getProgressPercentage = (currentStage: number): number => {
    // Assuming 5 stages total (0-4), convert to percentage
    return Math.round((currentStage / 4) * 100);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'on_hold':
        return 'bg-yellow-100 text-yellow-800';
      case 'terminated':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusString = (status: string): string => {
    switch (status) {
      case 'active': return 'active';
      case 'completed': return 'completed';
      case 'on_hold': return 'on-hold';
      case 'terminated': return 'terminated';
      case 'pending': return 'pending';
      default: return 'unknown';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200 max-w-sm">
      <div className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-lg">
                    {trainee.firstName.charAt(0)}
                  </span>
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{getFullName(trainee.firstName, trainee.lastName)}</h3>
                <p className="text-sm text-gray-600">{trainee.email}</p>
              </div>
            </div>

            <div className="mt-4 space-y-2">
              <div className="flex items-center text-sm text-gray-600">
                <User className="w-4 h-4 mr-2" />
                <span>{trainee.employeeId}</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Building className="w-4 h-4 mr-2" />
                <span>{trainee.department}</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Calendar className="w-4 h-4 mr-2" />
                <span>{trainee.startDate}</span>
              </div>
            </div>

            <div className="mt-4">
              <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(trainee.status)}`}>
                {getStatusString(trainee.status)}
              </span>
            </div>

            <div className="mt-2">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full"
                  style={{ width: `${getProgressPercentage(trainee.currentStage)}%` }}
                ></div>
              </div>
              <span className="text-xs text-gray-600">{getProgressPercentage(trainee.currentStage)}% Complete</span>
            </div>
          </div>

          <div className="ml-4 flex-shrink-0">
            <div className="flex flex-col space-y-2">
              <button
                onClick={() => onView(trainee)}
                className="p-2 text-blue-600 hover:text-blue-800 transition-colors"
                title="View Details"
              >
                <Eye className="w-4 h-4" />
              </button>
              <button
                onClick={() => onEdit(trainee)}
                className="p-2 text-green-600 hover:text-green-800 transition-colors"
                title="Edit Trainee"
              >
                <Edit className="w-4 h-4" />
              </button>
              <button
                onClick={() => onDelete(trainee)}
                className="p-2 text-red-600 hover:text-red-800 transition-colors"
                title="Delete Trainee"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex justify-between items-center">
            <div>
              <p className="text-sm text-gray-600">
                <span className="font-medium">Program:</span> {getProgramName(trainee.programId)}
              </p>
              <p className="text-sm text-gray-600">
                <span className="font-medium">Mentor:</span> {getMentorName(trainee.mentorId)}
              </p>
            </div>
            <div className="text-sm text-gray-500">
              {new Date(trainee.createdAt).toLocaleDateString()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};