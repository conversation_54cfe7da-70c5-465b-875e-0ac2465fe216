import React, { useState } from 'react';
import { Trainee, TrainingProgram, User } from '../types';
import { Eye, Edit, Trash2 } from 'lucide-react';

interface TraineeTableProps {
  trainees: Trainee[];
  programs: TrainingProgram[];
  mentors: User[];
  onEdit: (trainee: Trainee) => void;
  onDelete: (trainee: Trainee) => void;
  onView: (trainee: Trainee) => void;
}

export const TraineeTable: React.FC<TraineeTableProps> = ({
  trainees,
  programs,
  mentors,
  onEdit,
  onDelete,
  onView,
}) => {
  const [loading, setLoading] = useState(false);

  // Utility functions for lookups
  const getProgramName = (programId: string): string => {
    const program = programs.find(p => p.id === programId);
    return program?.title || `Program ${programId}`;
  };

  const getMentorName = (mentorId: string): string => {
    const mentor = mentors.find(m => m.id === mentorId);
    return mentor ? `${mentor.firstName} ${mentor.lastName}` : 'Not assigned';
  };

  const getFullName = (firstName: string, lastName: string): string => {
    return `${firstName} ${lastName}`;
  };

  const getStatusString = (status: string): string => {
    switch (status) {
      case 'active': return 'active';
      case 'completed': return 'completed';
      case 'on_hold': return 'on-hold';
      case 'terminated': return 'terminated';
      case 'pending': return 'pending';
      default: return 'unknown';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Employee ID
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Full Name
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Email
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Department
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Program
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Start Date
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {trainees.map((trainee) => (
            <tr key={trainee.id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {trainee.employeeId}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {getFullName(trainee.firstName, trainee.lastName)}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {trainee.email}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {trainee.department}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {getProgramName(trainee.programId)}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {trainee.startDate}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                  trainee.status === 'active' ? 'bg-green-100 text-green-800' :
                  trainee.status === 'completed' ? 'bg-blue-100 text-blue-800' :
                  trainee.status === 'on_hold' ? 'bg-yellow-100 text-yellow-800' :
                  trainee.status === 'terminated' ? 'bg-red-100 text-red-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {getStatusString(trainee.status)}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => onView(trainee)}
                    className="p-1 text-blue-600 hover:text-blue-800 transition-colors"
                    title="View Trainee"
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => onEdit(trainee)}
                    className="p-1 text-green-600 hover:text-green-800 transition-colors"
                    title="Edit Trainee"
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => onDelete(trainee)}
                    className="p-1 text-red-600 hover:text-red-800 transition-colors"
                    title="Delete Trainee"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};