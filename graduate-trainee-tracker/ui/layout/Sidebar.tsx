import React from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>2, Clipboard<PERSON>ist, <PERSON><PERSON>s, Menu, X, CheckS<PERSON>re, Bell, UserCheck, GraduationCap, Target } from 'lucide-react';

interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
  activeView: string;
  onViewChange: (view: string) => void;
  currentUser: any;
  className?: string;
  children?: React.ReactNode;
}

interface NavItem {
  name: string;
  href: string;
  icon: React.ReactNode;
  view: string;
}

export const Sidebar: React.FC<SidebarProps> = ({
  isOpen,
  onToggle,
  activeView,
  onViewChange,
  currentUser,
  className = '',
  children
}) => {
  const navigation: NavItem[] = [
    { name: 'Dashboard', href: '/dashboard', icon: <Home className="mr-3 h-5 w-5" />, view: 'dashboard' },
    { name: 'Trainees', href: '/trainees', icon: <Users className="mr-3 h-5 w-5" />, view: 'trainees' },
    { name: 'Programs', href: '/programs', icon: <ClipboardList className="mr-3 h-5 w-5" />, view: 'programs' },
    { name: 'Analytics', href: '/analytics', icon: <BarChart2 className="mr-3 h-5 w-5" />, view: 'analytics' },
    { name: 'Reviews', href: '/reviews', icon: <CheckSquare className="mr-3 h-5 w-5" />, view: 'reviews' },
    { name: 'Approvals', href: '/approvals', icon: <UserCheck className="mr-3 h-5 w-5" />, view: 'approvals' },
    { name: 'Notifications', href: '/notifications', icon: <Bell className="mr-3 h-5 w-5" />, view: 'notifications' },
    { name: 'People', href: '/people', icon: <Users className="mr-3 h-5 w-5" />, view: 'people' },
    { name: 'School', href: '/school', icon: <GraduationCap className="mr-3 h-5 w-5" />, view: 'school' },
    { name: 'Assessment', href: '/assessment', icon: <Target className="mr-3 h-5 w-5" />, view: 'assessment' },
    { name: 'Settings', href: '/settings', icon: <Settings className="mr-3 h-5 w-5" />, view: 'settings' },
  ];

  const handleNavClick = (view: string) => {
    onViewChange(view);
    // Close mobile sidebar when navigation item is clicked
    if (window.innerWidth < 768) {
      onToggle();
    }
  };

  return (
    <>
      {/* Mobile sidebar toggle button */}
      <div className="md:hidden fixed top-4 left-4 z-40">
        <button
          onClick={onToggle}
          className="p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
          aria-label="Toggle sidebar"
        >
          <Menu className="h-6 w-6" />
        </button>
      </div>

      {/* Mobile sidebar backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40 md:hidden bg-black bg-opacity-25"
          onClick={onToggle}
        />
      )}

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-50 pt-16 pb-4 flex flex-col w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out md:translate-x-0 md:static md:inset-0 md:flex md:w-64 ${isOpen ? 'translate-x-0' : '-translate-x-full'} ${className}`}
      >
        {/* User Info Section */}
        {currentUser && (
          <div className="px-4 py-4 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-semibold">
                  {currentUser.firstName[0]}{currentUser.lastName[0]}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {currentUser.firstName} {currentUser.lastName}
                </p>
                <p className="text-xs text-gray-500 truncate">{currentUser.department}</p>
              </div>
            </div>
          </div>
        )}

        {/* Children content (for filters, search, etc.) */}
        {children && (
          <div className="px-4 py-4 border-b border-gray-200">
            {children}
          </div>
        )}

        <div className="flex-1 flex flex-col overflow-y-auto">
          <nav className="flex-1 px-2 py-4 bg-white space-y-1">
            {navigation.map((item) => (
              <button
                key={item.name}
                onClick={() => handleNavClick(item.view)}
                className={`w-full text-left group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-150 ${
                  activeView === item.view
                    ? 'bg-blue-100 text-blue-900 border-r-2 border-blue-500'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                {item.icon}
                <span className="truncate">{item.name}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Mobile close button */}
        <div className="md:hidden border-t border-gray-200 p-4">
          <button
            onClick={onToggle}
            className="w-full flex items-center justify-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-50 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
          >
            <X className="mr-2 h-4 w-4" />
            Close Menu
          </button>
        </div>
      </div>
    </>
  );
};