import React, { useState, useEffect } from 'react';

interface RenderProps {
  isMobile: boolean;
}

interface ResponsiveWrapperProps {
  children: React.ReactNode | ((props: RenderProps) => React.ReactNode);
  className?: string;
  containerClassName?: string;
  mobileBreakpoint?: number; // Default: 768px (Tailwind's md breakpoint)
}

// Hook for mobile detection
const useIsMobile = (breakpoint: number = 768): boolean => {
  const [isMobile, setIsMobile] = useState<boolean>(false);

  useEffect(() => {
    // Check if window is available (for SSR safety)
    if (typeof window === 'undefined') {
      setIsMobile(false);
      return;
    }

    // Initial check
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < breakpoint);
    };

    // Set initial value
    checkIsMobile();

    // Add resize listener
    window.addEventListener('resize', checkIsMobile);

    // Cleanup
    return () => {
      window.removeEventListener('resize', checkIsMobile);
    };
  }, [breakpoint]);

  return isMobile;
};

export const ResponsiveWrapper: React.FC<ResponsiveWrapperProps> = ({
  children,
  className = '',
  containerClassName = '',
  mobileBreakpoint = 768,
}) => {
  const isMobile = useIsMobile(mobileBreakpoint);

  // Render children conditionally based on type
  const renderChildren = (): React.ReactNode => {
    if (typeof children === 'function') {
      // Render prop pattern: children is a function that receives isMobile
      return children({ isMobile });
    } else {
      // Regular React.ReactNode pattern: children is JSX elements
      return children;
    }
  };

  return (
    <div className={`min-h-screen bg-gray-50 ${containerClassName}`}>
      <div className={`max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 ${className}`}>
        {renderChildren()}
      </div>
    </div>
  );
};