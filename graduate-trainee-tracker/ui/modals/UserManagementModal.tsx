import React, { useEffect, useState } from 'react';
import { X } from 'lucide-react';
import { ModalProps, Trainee, TraineeFormData } from '../types';
import { TraineeForm } from '../forms/TraineeForm';
import { useAuth } from '../../hooks/useAuth';
import { usePermissions } from '../../hooks/usePermissions';
import { useErrorHandling } from '../../hooks/useErrorHandling';

interface UserManagementModalProps extends ModalProps {
  mode: 'add' | 'edit';
  trainee?: Trainee;
  onSave: (data: TraineeFormData) => Promise<void>;
  isLoading?: boolean;
}

export const UserManagementModal: React.FC<UserManagementModalProps> = ({
  isOpen,
  onClose,
  mode,
  trainee,
  onSave,
  isLoading = false,
}) => {
  const { isAuthenticated } = useAuth();
  const { hasPermission, canWrite, canEdit } = usePermissions();
  const { handleError } = useErrorHandling();
  const [hasAccess, setHasAccess] = useState(false);

  useEffect(() => {
    // Check if user has access to manage trainees
    const checkAccess = async () => {
      try {
        if (!isAuthenticated) {
          setHasAccess(false);
          return;
        }

        // Check if user has permission to manage trainees
        const canManage = await canWrite('trainees') || await canEdit('trainees');
        setHasAccess(canManage);
      } catch (error) {
        handleError(error as Error, {
          fallbackMessage: 'Failed to check access permissions',
          logError: true
        });
        setHasAccess(false);
      }
    };

    checkAccess();
  }, [isAuthenticated, canWrite, canEdit, handleError]);

  if (!isOpen) return null;

  // Show access denied message if user doesn't have access
  if (!hasAccess) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
        <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
          <div className="flex items-center justify-between p-6 border-b">
            <h2 className="text-xl font-semibold text-gray-900">Access Denied</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
              aria-label="Close modal"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          <div className="p-6">
            <p className="text-gray-700">You don't have permission to manage trainees.</p>
            <div className="mt-4 flex justify-end">
              <button
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const handleSave = async (data: TraineeFormData) => {
    try {
      await onSave(data);
      onClose();
    } catch (error) {
      handleError(error as Error, {
        fallbackMessage: `Failed to ${mode === 'add' ? 'add' : 'update'} trainee`,
        logError: true
      });
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            {mode === 'add' ? 'Add New Trainee' : 'Edit Trainee'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            aria-label="Close modal"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        <div className="p-6">
          <TraineeForm
            mode={mode}
            initialData={trainee}
            onSubmit={handleSave}
            onCancel={onClose}
            isLoading={isLoading}
          />
        </div>
      </div>
    </div>
  );
};