import React, { useState } from 'react';
import { Download, X } from 'lucide-react';
import { ModalProps, ExportOptions } from '../types';
import { useExport } from '../../hooks/useExport';
import { useErrorHandling } from '../../hooks/useErrorHandling';

interface ExportModalProps extends ModalProps {
  onExport: (options: ExportOptions) => Promise<void>;
  isLoading?: boolean;
}

export const ExportModal: React.FC<ExportModalProps> = ({
  isOpen,
  onClose,
  onExport,
  isLoading = false,
}) => {
  const { exportData } = useExport();
  const { handleError } = useErrorHandling();
  const [format, setFormat] = useState<'csv' | 'excel' | 'pdf'>('csv');
  const [fields, setFields] = useState<string[]>(['employeeId', 'fullName', 'email', 'department', 'program', 'startDate', 'status']);
  const [includeHeaders, setIncludeHeaders] = useState(true);

  const availableFields = [
    { value: 'employeeId', label: 'Employee ID' },
    { value: 'fullName', label: 'Full Name' },
    { value: 'email', label: 'Email' },
    { value: 'department', label: 'Department' },
    { value: 'program', label: 'Program' },
    { value: 'startDate', label: 'Start Date' },
    { value: 'endDate', label: 'End Date' },
    { value: 'status', label: 'Status' },
    { value: 'progress', label: 'Progress' },
    { value: 'mentor', label: 'Mentor' },
    { value: 'supervisor', label: 'Supervisor' },
  ];

  const handleExport = async () => {
    try {
      const options: ExportOptions = {
        format,
        fields,
        includeHeaders,
      };
      await onExport(options);
      onClose();
    } catch (error) {
      handleError(error as Error, {
        fallbackMessage: 'Failed to export data',
        logError: true
      });
    }
  };

  const handleFieldToggle = (field: string) => {
    setFields(prev =>
      prev.includes(field)
        ? prev.filter(f => f !== field)
        : [...prev, field]
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">Export Trainee Data</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            aria-label="Close modal"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Export Format
            </label>
            <div className="space-y-2">
              {(['csv', 'excel', 'pdf'] as const).map((f) => (
                <label key={f} className="flex items-center">
                  <input
                    type="radio"
                    value={f}
                    checked={format === f}
                    onChange={(e) => setFormat(e.target.value as 'csv' | 'excel' | 'pdf')}
                    className="mr-2"
                  />
                  <span className="text-sm">{f.toUpperCase()}</span>
                </label>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Fields to Export
            </label>
            <div className="grid grid-cols-2 gap-2">
              {availableFields.map((field) => (
                <label key={field.value} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={fields.includes(field.value)}
                    onChange={() => handleFieldToggle(field.value)}
                    className="mr-2"
                  />
                  <span className="text-sm">{field.label}</span>
                </label>
              ))}
            </div>
          </div>

          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={includeHeaders}
                onChange={(e) => setIncludeHeaders(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm">Include column headers</span>
            </label>
          </div>
        </div>

        <div className="flex justify-end space-x-3 p-6 border-t">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleExport}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            disabled={isLoading || fields.length === 0}
          >
            <Download className="w-4 h-4 inline mr-2" />
            {isLoading ? 'Exporting...' : 'Export Data'}
          </button>
        </div>
      </div>
    </div>
  );
};