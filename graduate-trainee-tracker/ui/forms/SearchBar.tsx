import React, { useState, useEffect, useRef } from 'react';
import { Search, X } from 'lucide-react';
import { useApi } from '../../hooks/useApi';
import { useErrorHandling } from '../../hooks/useErrorHandling';

interface SearchBarProps {
  onSearch: (term: string) => void;
  placeholder?: string;
  initialValue?: string;
  className?: string;
}

export const SearchBar: React.FC<SearchBarProps> = ({
  onSearch,
  placeholder = 'Search trainees...',
  initialValue = '',
  className = '',
}) => {
  const { get } = useApi();
  const { handleError } = useErrorHandling();
  const [searchTerm, setSearchTerm] = useState(initialValue);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const handler = setTimeout(() => {
      if (searchTerm) {
        // Call API to filter trainees based on search term
        get(`/api/trainees?search=${encodeURIComponent(searchTerm)}`)
          .then(response => {
            if (response.success) {
              onSearch(searchTerm);
            } else {
              throw new Error(response.message || 'Failed to search trainees');
            }
          })
          .catch(error => {
            handleError(error as Error, {
              fallbackMessage: 'Failed to search trainees',
              logError: true
            });
          });
      } else {
        onSearch(searchTerm);
      }
    }, 300);

    return () => {
      clearTimeout(handler);
    };
  }, [searchTerm, onSearch, get, handleError]);

  const handleClear = () => {
    setSearchTerm('');
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  return (
    <div className={`relative ${className}`}>
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <Search className="h-5 w-5 text-gray-400" />
      </div>
      <input
        ref={inputRef}
        type="text"
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        placeholder={placeholder}
        className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        aria-label={placeholder}
      />
      {searchTerm && (
        <button
          onClick={handleClear}
          className="absolute inset-y-0 right-0 pr-3 flex items-center"
          aria-label="Clear search"
        >
          <X className="h-5 w-5 text-gray-400 hover:text-gray-600" />
        </button>
      )}
    </div>
  );
};