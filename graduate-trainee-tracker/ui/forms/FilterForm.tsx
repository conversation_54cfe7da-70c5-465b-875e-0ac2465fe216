import React, { useState } from 'react';
import { FilterOptions } from '../types';
import { useApi } from '../../hooks/useApi';
import { useErrorHandling } from '../../hooks/useErrorHandling';

interface FilterFormProps {
  onFilter: (filters: FilterOptions) => void;
  initialFilters?: FilterOptions;
  className?: string;
}

export const FilterForm: React.FC<FilterFormProps> = ({
  onFilter,
  initialFilters = {},
  className = '',
}) => {
  const { get } = useApi();
  const { handleError } = useErrorHandling();
  const [filters, setFilters] = useState<FilterOptions>(initialFilters);

  const handleChange = (field: keyof FilterOptions, value: string) => {
    const newFilters = { ...filters, [field]: value };
    setFilters(newFilters);
    
    // Call API to filter trainees based on filters
    const queryParams = new URLSearchParams();
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value) {
        queryParams.append(key, value.toString());
      }
    });
    
    get(`/api/trainees?${queryParams.toString()}`)
      .then(response => {
        if (response.success) {
          onFilter(newFilters);
        } else {
          throw new Error(response.message || 'Failed to filter trainees');
        }
      })
      .catch(error => {
        handleError(error as Error, {
          fallbackMessage: 'Failed to filter trainees',
          logError: true
        });
      });
  };

  const handleReset = () => {
    setFilters({});
    
    // Call API to get all trainees (no filters)
    get('/api/trainees')
      .then(response => {
        if (response.success) {
          onFilter({});
        } else {
          throw new Error(response.message || 'Failed to reset filters');
        }
      })
      .catch(error => {
        handleError(error as Error, {
          fallbackMessage: 'Failed to reset filters',
          logError: true
        });
      });
  };

  return (
    <div className={`bg-white rounded-lg shadow p-4 ${className}`}>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Department
          </label>
          <select
            value={filters.department || ''}
            onChange={(e) => handleChange('department', e.target.value)}
            className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          >
            <option value="">All Departments</option>
            <option value="Engineering">Engineering</option>
            <option value="Marketing">Marketing</option>
            <option value="Sales">Sales</option>
            <option value="HR">HR</option>
            <option value="Finance">Finance</option>
            <option value="Operations">Operations</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Program
          </label>
          <select
            value={filters.program || ''}
            onChange={(e) => handleChange('program', e.target.value)}
            className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          >
            <option value="">All Programs</option>
            <option value="Graduate Trainee">Graduate Trainee</option>
            <option value="Internship">Internship</option>
            <option value="Management Trainee">Management Trainee</option>
            <option value="Leadership Development">Leadership Development</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Status
          </label>
          <select
            value={filters.status || ''}
            onChange={(e) => handleChange('status', e.target.value as any)}
            className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          >
            <option value="">All Statuses</option>
            <option value="active">Active</option>
            <option value="completed">Completed</option>
            <option value="on-hold">On Hold</option>
            <option value="terminated">Terminated</option>
            <option value="pending">Pending</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Start Date From
          </label>
          <input
            type="date"
            value={filters.startDateFrom || ''}
            onChange={(e) => handleChange('startDateFrom', e.target.value)}
            className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Start Date To
          </label>
          <input
            type="date"
            value={filters.startDateTo || ''}
            onChange={(e) => handleChange('startDateTo', e.target.value)}
            className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          />
        </div>

        <div className="flex items-end">
          <button
            type="button"
            onClick={handleReset}
            className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Reset Filters
          </button>
        </div>
      </div>
    </div>
  );
};