import React, { useState, useRef, useEffect } from 'react';
import { MoreVertical } from 'lucide-react';
import { ActionItem } from '../types';
import { useApi } from '../../hooks/useApi';
import { useErrorHandling } from '../../hooks/useErrorHandling';

interface ActionMenuProps {
  actions: ActionItem[];
  className?: string;
}

export const ActionMenu: React.FC<ActionMenuProps> = ({ actions, className = '' }) => {
  const { delete: deleteApi } = useApi();
  const { handleError } = useErrorHandling();
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Close menu when pressing Escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen]);

  const handleActionClick = async (action: ActionItem) => {
    if (!action.disabled) {
      try {
        // If this is a delete action, use the API to delete
        if (action.variant === 'destructive' && action.label === 'Delete') {
          // We would need to pass the item ID to delete
          // This is a placeholder implementation
          await action.onClick();
        } else {
          await action.onClick();
        }
        setIsOpen(false);
      } catch (error) {
        handleError(error as Error, {
          fallbackMessage: 'Failed to perform action',
          logError: true
        });
      }
    }
  };

  return (
    <div className="relative inline-block text-left" ref={menuRef}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className={`inline-flex items-center p-1 rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 ${className}`}
        aria-label="Open action menu"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <MoreVertical className="h-5 w-5" />
      </button>

      {isOpen && (
        <div
          className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10"
          role="menu"
          aria-orientation="vertical"
          aria-labelledby="action-menu-button"
        >
          <div className="py-1" role="none">
            {actions.map((action, index) => (
              <button
                key={index}
                onClick={() => handleActionClick(action)}
                disabled={action.disabled}
                className={`flex items-center w-full text-left px-4 py-2 text-sm ${
                  action.disabled
                    ? 'text-gray-400 cursor-not-allowed'
                    : action.variant === 'destructive'
                    ? 'text-red-600 hover:bg-red-50'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                role="menuitem"
              >
                {action.icon && <span className="mr-2">{action.icon}</span>}
                {action.label}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};