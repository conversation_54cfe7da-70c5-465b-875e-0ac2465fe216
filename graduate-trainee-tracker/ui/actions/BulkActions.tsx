import React, { useState } from 'react';
import { Trash2, Download, Mail, UserPlus } from 'lucide-react';

interface BulkAction {
  label: string;
  value: string;
  icon?: React.ReactNode;
  disabled?: boolean;
}

interface BulkActionsProps {
  selectedCount: number;
  actions?: BulkAction[];
  onAction: (action: string) => void;
  onClearSelection: () => void;
  className?: string;
}

export const BulkActions: React.FC<BulkActionsProps> = ({
  selectedCount,
  actions,
  onAction,
  onClearSelection,
  className = '',
}) => {
  const [selectedAction, setSelectedAction] = useState('');

  const defaultActions: BulkAction[] = [
    { label: 'Export Selected', value: 'export', icon: <Download className="w-4 h-4" /> },
    { label: 'Send Email', value: 'email', icon: <Mail className="w-4 h-4" /> },
    { label: 'Assign Mentor', value: 'assign-mentor', icon: <UserPlus className="w-4 h-4" /> },
    { label: 'Delete Selected', value: 'delete', icon: <Trash2 className="w-4 h-4" />, disabled: false },
  ];

  const availableActions = actions || defaultActions;

  const handleAction = () => {
    if (selectedAction) {
      onAction(selectedAction);
      setSelectedAction('');
    }
  };

  if (selectedCount === 0) return null;

  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg p-4 flex flex-col sm:flex-row items-start sm:items-center justify-between ${className}`}>
      <div className="mb-3 sm:mb-0">
        <p className="text-sm text-blue-800">
          <span className="font-medium">{selectedCount}</span> trainee{selectedCount !== 1 ? 's' : ''} selected
        </p>
      </div>
      
      <div className="flex flex-col sm:flex-row w-full sm:w-auto space-y-2 sm:space-y-0 sm:space-x-3">
        <div className="flex space-x-2">
          <select
            value={selectedAction}
            onChange={(e) => setSelectedAction(e.target.value)}
            className="block w-full sm:w-auto pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          >
            <option value="">Choose an action</option>
            {availableActions.map((action) => (
              <option 
                key={action.value} 
                value={action.value} 
                disabled={action.disabled}
              >
                {action.label}
              </option>
            ))}
          </select>
          
          <button
            onClick={handleAction}
            disabled={!selectedAction}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Apply
          </button>
        </div>
        
        <button
          onClick={onClearSelection}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Clear Selection
        </button>
      </div>
    </div>
  );
};