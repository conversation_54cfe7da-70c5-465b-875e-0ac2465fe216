// Type definitions for Graduate Trainee Tracker UI components
import { Trainee, User, TrainingProgram } from '../../types';

// Re-export main types for backward compatibility
export type { Trainee, User, TrainingProgram } from '../../types';

export type TraineeStatus = 
  | 'active'
  | 'completed'
  | 'on-hold'
  | 'terminated'
  | 'pending';

export interface TraineeFormData {
  employeeId: string;
  fullName: string;
  email: string;
  department: string;
  program: string;
  startDate: string;
  endDate?: string;
  mentor?: string;
  supervisor?: string;
}

export interface FilterOptions {
  department?: string;
  program?: string;
  status?: TraineeStatus;
  startDateFrom?: string;
  startDateTo?: string;
  searchTerm?: string;
}

export interface ExportOptions {
  format: 'csv' | 'excel' | 'pdf';
  fields: string[];
  filters?: FilterOptions;
  includeHeaders: boolean;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm?: () => void;
  title?: string;
  children?: React.ReactNode;
}

export interface TableColumn<T> {
  key: keyof T | string;
  header: string;
  sortable?: boolean;
  width?: string;
  render?: (value: any, item: T) => React.ReactNode;
}

export interface BreadcrumbItem {
  label: string;
  href?: string;
}

export interface ActionItem {
  label: string;
  onClick: () => void;
  icon?: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'tertiary' | 'default' | 'destructive';
  disabled?: boolean;
}

export interface PageHeaderProps {
  title: string;
  subtitle?: string;
  actions?: ActionItem[];
  breadcrumbs?: BreadcrumbItem[];
  className?: string;
}

export interface NotificationConfig {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  action?: ActionItem;
}

export interface LoadingSpinnerProps {
  isLoading: boolean;
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export interface ErrorState {
  error: Error | string;
  onRetry?: () => void;
}

export interface EmptyStateProps {
  title: string;
  description?: string;
  action?: ActionItem;
  icon?: React.ReactNode;
}

export interface ResponsiveProps {
  className?: string;
  children: React.ReactNode;
}

export interface FormFieldProps {
  label: string;
  name: string;
  required?: boolean;
  error?: string;
  helperText?: string;
  children: React.ReactNode;
}

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: string;
  helperText?: string;
}

export interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  options: Array<{ value: string; label: string }>;
  error?: string;
  helperText?: string;
}

export interface DatePickerProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: string;
  helperText?: string;
  minDate?: Date;
  maxDate?: Date;
}