import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Users,
  GraduationCap,
  ClipboardCheck,
  FileCheck,
  Bell,
  BarChart3,
  School,
  UserCheck,
  Menu,
  X,
  Plus,
  Download,
  Search,
  Filter,
  MoreVertical,
  ChevronRight,
  Calendar,
  Clock,
  TrendingUp,
  AlertCircle,
  CheckCircle2,
  XCircle,
  User,
  Mail,
  Building,
  Award
} from 'lucide-react';

// shadcn/ui components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/components/ui/alert';
import { cn } from '@/lib/utils';

// Import types and mock data
import {
  GraduateTraineeTrackerProps,
  GraduateTraineeTrackerState,
  User as UserType,
  Trainee,
  TrainingProgram,
  QuarterlyReview,
  ApprovalWorkflow,
  Notification,
  TraineeStatus,
  ProgramStatus,
  ReviewStatus,
  ApprovalStatus,
  StageStatus,
  UserRole,
  Competency
} from './types';
import { mockApi } from './mockApi';

// Mock current user
const mockCurrentUser: UserType = {
  id: 'user-001',
  employeeId: 'EMP001',
  firstName: 'Sarah',
  lastName: 'Johnson',
  email: '<EMAIL>',
  role: UserRole.LD_OFFICER,
  department: 'Learning & Development',
  permissions: [],
  isActive: true,
  createdAt: '2024-01-15T08:00:00Z',
  updatedAt: '2024-08-28T10:00:00Z'
};

type ViewType = 'dashboard' | 'trainees' | 'programs' | 'reviews' | 'approvals' | 'analytics' | 'notifications';

const GraduateTraineeTrackerShadcn: React.FC<GraduateTraineeTrackerProps> = ({ 
  currentUser = mockCurrentUser, 
  onNavigate 
}) => {
  const [state, setState] = useState<GraduateTraineeTrackerState>({
    currentUser: currentUser,
    trainees: [],
    programs: [],
    mentors: [],
    reviews: [],
    workflows: [],
    notifications: [],
    competencies: [],
    loading: true,
    error: null,
    selectedTrainee: null,
    selectedProgram: null,
    filters: {
      status: [],
      department: [],
      program: [],
      mentor: [],
      dateRange: {
        startDate: '',
        endDate: ''
      }
    },
    pagination: {
      page: 1,
      pageSize: 10,
      total: 0
    }
  });

  const [activeView, setActiveView] = useState<ViewType>('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddTraineeDialog, setShowAddTraineeDialog] = useState(false);

  // Load data using mock API
  useEffect(() => {
    const loadData = async () => {
      try {
        setState(prev => ({ ...prev, loading: true }));

        const [trainees, programs, mentors, reviews, workflows, notifications, competencies] = await Promise.all([
          mockApi.fetchTrainees(),
          mockApi.fetchPrograms(),
          mockApi.fetchMentors(),
          mockApi.fetchReviews(),
          mockApi.fetchWorkflows(),
          mockApi.fetchNotifications(),
          mockApi.fetchCompetencies()
        ]);

        setState(prev => ({
          ...prev,
          trainees,
          programs,
          mentors,
          reviews,
          workflows,
          notifications,
          competencies,
          loading: false,
          error: null
        }));
      } catch (error) {
        console.error('Failed to load data:', error);
        setState(prev => ({
          ...prev,
          loading: false,
          error: 'Failed to load data. Please try again.'
        }));
      }
    };

    loadData();
  }, []);

  // Calculate statistics
  const stats = {
    totalTrainees: state.trainees.length,
    activeTrainees: state.trainees.filter(t => t.status === TraineeStatus.ACTIVE).length,
    completedTrainees: state.trainees.filter(t => t.status === TraineeStatus.COMPLETED).length,
    totalPrograms: state.programs.length,
    activePrograms: state.programs.filter(p => p.status === ProgramStatus.ACTIVE).length,
    pendingReviews: state.reviews.filter(r => r.status === ReviewStatus.SCHEDULED || r.status === ReviewStatus.IN_PROGRESS).length,
    completedReviews: state.reviews.filter(r => r.status === ReviewStatus.COMPLETED).length,
    pendingApprovals: state.workflows.filter(w => w.status === ApprovalStatus.PENDING).length
  };

  const navigationItems = [
    { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
    { id: 'trainees', label: 'Trainees', icon: Users },
    { id: 'programs', label: 'Programs', icon: GraduationCap },
    { id: 'reviews', label: 'Reviews', icon: ClipboardCheck },
    { id: 'approvals', label: 'Approvals', icon: FileCheck },
    { id: 'analytics', label: 'Analytics', icon: TrendingUp },
    { id: 'notifications', label: 'Notifications', icon: Bell },
  ];

  const getStatusBadge = (status: string) => {
    const variants: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
      active: 'default',
      completed: 'secondary',
      pending: 'outline',
      on_hold: 'destructive',
      terminated: 'destructive'
    };
    return <Badge variant={variants[status] || 'default'}>{status.replace('_', ' ')}</Badge>;
  };

  const renderDashboard = () => (
    <div className="space-y-6">
      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Trainees</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalTrainees}</div>
            <p className="text-xs text-muted-foreground">
              {stats.activeTrainees} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Programs</CardTitle>
            <GraduationCap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activePrograms}</div>
            <p className="text-xs text-muted-foreground">
              of {stats.totalPrograms} total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Reviews</CardTitle>
            <ClipboardCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pendingReviews}</div>
            <p className="text-xs text-muted-foreground">
              {stats.completedReviews} completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
            <FileCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pendingApprovals}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting action
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity & Upcoming Reviews */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Latest updates from the training program</CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-[300px]">
              <div className="space-y-4">
                {state.trainees.slice(0, 5).map((trainee) => (
                  <div key={trainee.id} className="flex items-center space-x-4">
                    <Avatar>
                      <AvatarFallback>{trainee.firstName[0]}{trainee.lastName[0]}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1 space-y-1">
                      <p className="text-sm font-medium leading-none">
                        {trainee.firstName} {trainee.lastName}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {trainee.department}
                      </p>
                    </div>
                    {getStatusBadge(trainee.status)}
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Upcoming Reviews</CardTitle>
            <CardDescription>Scheduled quarterly reviews</CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-[300px]">
              <div className="space-y-4">
                {state.reviews
                  .filter(r => r.status === ReviewStatus.SCHEDULED)
                  .slice(0, 5)
                  .map((review) => {
                    const trainee = state.trainees.find(t => t.id === review.traineeId);
                    return (
                      <div key={review.id} className="flex items-center justify-between">
                        <div className="space-y-1">
                          <p className="text-sm font-medium leading-none">
                            {trainee?.firstName} {trainee?.lastName}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            Q{review.quarter} {review.year} Review
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">
                            {new Date(review.dueDate).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    );
                  })}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common tasks and operations</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button variant="outline" size="sm" onClick={() => setShowAddTraineeDialog(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Trainee
            </Button>
            <Button variant="outline" size="sm">
              <GraduationCap className="mr-2 h-4 w-4" />
              Create Program
            </Button>
            <Button variant="outline" size="sm">
              <ClipboardCheck className="mr-2 h-4 w-4" />
              Schedule Review
            </Button>
            <Button variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              Export Data
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderTrainees = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Trainees</h2>
          <p className="text-muted-foreground">
            Manage and track graduate trainee progress
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
          <Button onClick={() => setShowAddTraineeDialog(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Add Trainee
          </Button>
        </div>
      </div>

      {/* Search Bar */}
      <div className="flex items-center space-x-2">
        <Search className="h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search trainees..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="max-w-sm"
        />
      </div>

      {/* Trainees Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Department</TableHead>
                <TableHead>Program</TableHead>
                <TableHead>Mentor</TableHead>
                <TableHead>Progress</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {state.trainees
                .filter(t => 
                  searchTerm === '' || 
                  `${t.firstName} ${t.lastName}`.toLowerCase().includes(searchTerm.toLowerCase())
                )
                .map((trainee) => {
                  const program = state.programs.find(p => p.id === trainee.programId);
                  const mentor = state.mentors.find(m => m.id === trainee.mentorId);
                  const progress = Math.round((trainee.currentStage / 4) * 100);
                  
                  return (
                    <TableRow key={trainee.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <Avatar>
                            <AvatarFallback>
                              {trainee.firstName[0]}{trainee.lastName[0]}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{trainee.firstName} {trainee.lastName}</p>
                            <p className="text-sm text-muted-foreground">{trainee.email}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{trainee.department}</TableCell>
                      <TableCell>{program?.title || 'N/A'}</TableCell>
                      <TableCell>
                        {mentor ? `${mentor.firstName} ${mentor.lastName}` : 'Unassigned'}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Progress value={progress} className="w-[60px]" />
                          <span className="text-sm text-muted-foreground">{progress}%</span>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(trainee.status)}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem>View Profile</DropdownMenuItem>
                            <DropdownMenuItem>Edit Details</DropdownMenuItem>
                            <DropdownMenuItem>Schedule Review</DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-destructive">
                              Remove Trainee
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  );
                })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );

  const renderPrograms = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Training Programs</h2>
          <p className="text-muted-foreground">
            Manage training programs and curricula
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Create Program
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {state.programs.map((program) => {
          const enrolledCount = state.trainees.filter(t => t.programId === program.id).length;
          
          return (
            <Card key={program.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <GraduationCap className="h-8 w-8 text-primary" />
                  {getStatusBadge(program.status)}
                </div>
                <CardTitle className="mt-4">{program.title}</CardTitle>
                <CardDescription>{program.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Duration:</span>
                    <span className="font-medium">{program.duration} months</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Enrolled:</span>
                    <span className="font-medium">{enrolledCount} trainees</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Milestones:</span>
                    <span className="font-medium">{program.milestones.length}</span>
                  </div>
                </div>
                <div className="mt-4 flex space-x-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    View Details
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    Edit
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );

  const renderNotifications = () => (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Notifications</h2>
        <p className="text-muted-foreground">
          Stay updated with important alerts and reminders
        </p>
      </div>

      <Card>
        <CardContent className="p-0">
          <ScrollArea className="h-[600px]">
            <div className="space-y-1 p-4">
              {state.notifications.map((notification) => {
                const icons = {
                  high: <AlertCircle className="h-5 w-5 text-destructive" />,
                  medium: <AlertCircle className="h-5 w-5 text-yellow-500" />,
                  low: <Bell className="h-5 w-5 text-muted-foreground" />,
                  urgent: <AlertCircle className="h-5 w-5 text-destructive" />
                };
                
                return (
                  <div
                    key={notification.id}
                    className={cn(
                      "flex items-start space-x-4 rounded-lg p-4 transition-colors hover:bg-muted/50",
                      !notification.isRead && "bg-muted/30"
                    )}
                  >
                    {icons[notification.priority]}
                    <div className="flex-1 space-y-1">
                      <p className="text-sm font-medium leading-none">
                        {notification.title}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {notification.message}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(notification.createdAt).toLocaleString()}
                      </p>
                    </div>
                    {!notification.isRead && (
                      <Badge variant="default" className="ml-auto">New</Badge>
                    )}
                  </div>
                );
              })}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );

  const renderContent = () => {
    switch (activeView) {
      case 'dashboard':
        return renderDashboard();
      case 'trainees':
        return renderTrainees();
      case 'programs':
        return renderPrograms();
      case 'notifications':
        return renderNotifications();
      default:
        return renderDashboard();
    }
  };

  if (state.loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="space-y-4">
          <Skeleton className="h-12 w-12 rounded-full" />
          <Skeleton className="h-4 w-32" />
        </div>
      </div>
    );
  }

  if (state.error) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Alert className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{state.error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar */}
      <div className={cn(
        "fixed inset-y-0 left-0 z-50 w-64 transform bg-card transition-transform duration-200 ease-in-out lg:relative lg:translate-x-0",
        sidebarOpen ? "translate-x-0" : "-translate-x-full"
      )}>
        <div className="flex h-full flex-col">
          {/* Logo */}
          <div className="flex h-16 items-center justify-between border-b px-6">
            <div className="flex items-center space-x-2">
              <GraduationCap className="h-6 w-6 text-primary" />
              <span className="text-lg font-semibold">GT Tracker</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden"
              onClick={() => setSidebarOpen(false)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Navigation */}
          <ScrollArea className="flex-1 px-3 py-4">
            <div className="space-y-1">
              {navigationItems.map((item) => (
                <Button
                  key={item.id}
                  variant={activeView === item.id ? "secondary" : "ghost"}
                  className="w-full justify-start"
                  onClick={() => {
                    setActiveView(item.id as ViewType);
                    setSidebarOpen(false);
                  }}
                >
                  <item.icon className="mr-2 h-4 w-4" />
                  {item.label}
                </Button>
              ))}
            </div>
          </ScrollArea>

          {/* User Info */}
          <div className="border-t p-4">
            <div className="flex items-center space-x-3">
              <Avatar>
                <AvatarFallback>
                  {currentUser.firstName[0]}{currentUser.lastName[0]}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <p className="text-sm font-medium">
                  {currentUser.firstName} {currentUser.lastName}
                </p>
                <p className="text-xs text-muted-foreground">
                  {currentUser.role.replace('_', ' ')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-1 flex-col">
        {/* Header */}
        <header className="flex h-16 items-center justify-between border-b px-6">
          <Button
            variant="ghost"
            size="sm"
            className="lg:hidden"
            onClick={() => setSidebarOpen(!sidebarOpen)}
          >
            <Menu className="h-5 w-5" />
          </Button>
          
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm">
              <Bell className="h-5 w-5" />
              {state.notifications.filter(n => !n.isRead).length > 0 && (
                <Badge variant="destructive" className="ml-1 h-5 w-5 rounded-full p-0">
                  {state.notifications.filter(n => !n.isRead).length}
                </Badge>
              )}
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback>
                      {currentUser.firstName[0]}{currentUser.lastName[0]}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuItem>Profile</DropdownMenuItem>
                <DropdownMenuItem>Settings</DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>Log out</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </header>

        {/* Page Content */}
        <main className="flex-1 overflow-y-auto p-6">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeView}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
            >
              {renderContent()}
            </motion.div>
          </AnimatePresence>
        </main>
      </div>

      {/* Add Trainee Dialog */}
      <Dialog open={showAddTraineeDialog} onOpenChange={setShowAddTraineeDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Trainee</DialogTitle>
            <DialogDescription>
              Add a new graduate trainee to the program
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="firstName" className="text-right">
                First Name
              </Label>
              <Input id="firstName" className="col-span-3" />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="lastName" className="text-right">
                Last Name
              </Label>
              <Input id="lastName" className="col-span-3" />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="email" className="text-right">
                Email
              </Label>
              <Input id="email" type="email" className="col-span-3" />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="department" className="text-right">
                Department
              </Label>
              <Select>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select department" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="engineering">Engineering</SelectItem>
                  <SelectItem value="design">Design</SelectItem>
                  <SelectItem value="product">Product</SelectItem>
                  <SelectItem value="marketing">Marketing</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddTraineeDialog(false)}>
              Cancel
            </Button>
            <Button onClick={() => setShowAddTraineeDialog(false)}>
              Add Trainee
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default GraduateTraineeTrackerShadcn;
export { GraduateTraineeTrackerShadcn as GraduateTraineeTracker };