import React, { useState, useMemo, useEffect } from 'react';
import { motion } from 'framer-motion';
import { graduateTraineeApi, AnalyticsOverview, ReportRequest, ExportResponse } from '../../../lib/api/graduateTrainee';
import { GraduateTraineeTrackerState, User, TraineeStatus, ProgramStatus } from '../types';

interface ReportsAnalyticsProps {
  currentUser: User;
  state: GraduateTraineeTrackerState;
  onStateUpdate: React.Dispatch<React.SetStateAction<GraduateTraineeTrackerState>>;
}

export const ReportsAnalytics: React.FC<ReportsAnalyticsProps> = ({
  currentUser,
  state,
  onStateUpdate
}) => {
  const [selectedReport, setSelectedReport] = useState<'overview' | 'performance' | 'programs' | 'trends'>('overview');
  const [dateRange, setDateRange] = useState<'1m' | '3m' | '6m' | '1y'>('6m');
  const [exportFormat, setExportFormat] = useState<'pdf' | 'excel' | 'csv'>('pdf');
  const [analyticsData, setAnalyticsData] = useState<AnalyticsOverview | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);

  // Fetch analytics data
  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setIsLoading(true);
        const data = await graduateTraineeApi.getAnalyticsOverview();
        setAnalyticsData(data);
      } catch (error) {
        console.error('Failed to fetch analytics data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAnalytics();
  }, []);

  const analytics = useMemo(() => {
    if (!analyticsData) {
      return {
        totalTrainees: 0,
        activeTrainees: 0,
        completedTrainees: 0,
        averageProgress: 0,
        averageScore: 0,
        completionRate: 0,
        totalPrograms: 0,
        activePrograms: 0,
        totalBudgetAllocated: 0,
        totalBudgetSpent: 0,
        budgetUtilization: 0,
        monthlyTrends: [],
        programUtilization: []
      };
    }
    return analyticsData;
  }, [analyticsData]);

  const handleExport = async () => {
    try {
      setIsExporting(true);
      const request: ReportRequest = {
        reportType: selectedReport as 'overview' | 'performance' | 'programs' | 'trends',
        format: exportFormat as 'csv' | 'pdf' | 'excel',
        dateRange: {
          startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          endDate: new Date().toISOString().split('T')[0]
        }
      };
      
      const response = await graduateTraineeApi.exportReport(request);
      
      if (response.success && response.fileName) {
        alert(`Report exported successfully: ${response.fileName}`);
      } else {
        alert(`Export failed: ${response.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Export failed:', error);
      alert('Export failed. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  const renderOverviewReport = () => (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow p-6"
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">Total Trainees</dt>
                <dd className="text-lg font-medium text-gray-900">{analytics.totalTrainees}</dd>
              </dl>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-lg shadow p-6"
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">Completion Rate</dt>
                <dd className="text-lg font-medium text-gray-900">{analytics.completionRate.toFixed(1)}%</dd>
              </dl>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-lg shadow p-6"
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">Average Score</dt>
                <dd className="text-lg font-medium text-gray-900">{analytics.averageScore.toFixed(1)}</dd>
              </dl>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-lg shadow p-6"
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">Active Programs</dt>
                <dd className="text-lg font-medium text-gray-900">{analytics.activePrograms}</dd>
              </dl>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Monthly Trends Chart */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Monthly Trends</h3>
        <div className="h-64 flex items-end space-x-2">
          {analytics.monthlyTrends.map((month, index) => (
            <div key={month.month} className="flex-1 flex flex-col items-center">
              <div className="w-full bg-gray-200 rounded-t" style={{ height: '200px' }}>
                <div 
                  className="bg-blue-500 rounded-t transition-all duration-500"
                  style={{ 
                    height: `${Math.max(10, (month.newTrainees / Math.max(...analytics.monthlyTrends.map(m => m.newTrainees))) * 180)}px`,
                    width: '100%'
                  }}
                />
              </div>
              <div className="text-xs text-gray-600 mt-2 text-center">
                <div>{month.month}</div>
                <div className="font-medium">{month.newTrainees} new</div>
                <div className="text-xs">{month.completedTrainees} completed</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Program Utilization */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Program Utilization</h3>
        <div className="space-y-4">
          {analytics.programUtilization.map((program, index) => (
            <div key={program.programName} className="flex items-center space-x-4">
              <div className="flex-1">
                <div className="flex justify-between text-sm">
                  <span className="font-medium text-gray-900">{program.programName}</span>
                  <span className="text-gray-500">
                    {program.completedTrainees}/{program.totalTrainees} ({program.completionRate.toFixed(0)}%)
                  </span>
                </div>
                <div className="mt-1 w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${Math.min(100, program.completionRate)}%` }}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Reports & Analytics</h2>
            <p className="text-sm text-gray-600 mt-1">
              Comprehensive insights and reporting for the Graduate Trainee Tracker
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value as any)}
              className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md"
            >
              <option value="1m">Last Month</option>
              <option value="3m">Last 3 Months</option>
              <option value="6m">Last 6 Months</option>
              <option value="1y">Last Year</option>
            </select>
            <select
              value={exportFormat}
              onChange={(e) => setExportFormat(e.target.value as any)}
              className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md"
            >
              <option value="pdf">PDF</option>
              <option value="excel">Excel</option>
              <option value="csv">CSV</option>
            </select>
            <button
              onClick={handleExport}
              disabled={isExporting}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              {isExporting ? 'Exporting...' : 'Export'}
            </button>
          </div>
        </div>
      </div>

      {/* Report Navigation */}
      <div className="mb-6">
        <nav className="flex space-x-8">
          {[
            { key: 'overview', label: 'Overview', icon: '📊' },
            { key: 'performance', label: 'Performance', icon: '📈' },
            { key: 'programs', label: 'Programs', icon: '🎓' },
            { key: 'trends', label: 'Trends', icon: '📉' }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setSelectedReport(tab.key as any)}
              className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                selectedReport === tab.key
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span>{tab.icon}</span>
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Report Content */}
      <motion.div
        key={selectedReport}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {selectedReport === 'overview' && renderOverviewReport()}
        {selectedReport === 'performance' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Analytics</h3>
            <p className="text-gray-600">Detailed performance metrics and analysis coming soon...</p>
          </div>
        )}
        {selectedReport === 'programs' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Program Analytics</h3>
            <p className="text-gray-600">Program effectiveness and utilization reports coming soon...</p>
          </div>
        )}
        {selectedReport === 'trends' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Trend Analysis</h3>
            <p className="text-gray-600">Historical trends and predictive analytics coming soon...</p>
          </div>
        )}
      </motion.div>
    </div>
  );
};