import React, { useState, useMemo } from 'react';
import { FormField, DatePicker, MultiSelect, Badge } from './ui';

// Types
export interface ReportBuilderProps {
  className?: string;
  onGenerate?: (config: ReportConfig) => void;
  onSave?: (config: ReportConfig) => void;
  onLoad?: (reportId: string) => void;
  onExport?: (config: ReportConfig, format: ExportFormat) => void;
  initialConfig?: Partial<ReportConfig>;
  availableTemplates?: ReportTemplate[];
  permissions?: string[];
}

export interface ReportConfig {
  id?: string;
  name: string;
  description?: string;
  type: ReportType;
  category: ReportCategory;
  dataSource: DataSource;
  filters: ReportFilters;
  groupBy: GroupByConfig[];
  sortBy: SortByConfig[];
  columns: ReportColumn[];
  aggregations: AggregationConfig[];
  formatting: FormattingConfig;
  scheduling?: SchedulingConfig;
  sharing?: SharingConfig;
  visualization?: VisualizationConfig;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  isTemplate: boolean;
  tags: string[];
}

export type ReportType = 
  | 'summary' 
  | 'detailed' 
  | 'analytical' 
  | 'dashboard' 
  | 'export' 
  | 'custom';

export type ReportCategory = 
  | 'trainees' 
  | 'programs' 
  | 'assessments' 
  | 'reviews' 
  | 'workflows' 
  | 'analytics' 
  | 'compliance' 
  | 'financial';

export type ExportFormat = 'pdf' | 'excel' | 'csv' | 'json' | 'html';

export interface DataSource {
  primary: string;
  joins?: DataJoin[];
  customQuery?: string;
}

export interface DataJoin {
  table: string;
  type: 'inner' | 'left' | 'right' | 'full';
  on: string;
  alias?: string;
}

export interface ReportFilters {
  dateRange?: {
    start: string;
    end: string;
    preset?: 'today' | 'week' | 'month' | 'quarter' | 'year' | 'custom';
  };
  status?: string[];
  categories?: string[];
  users?: string[];
  programs?: string[];
  departments?: string[];
  locations?: string[];
  customFilters?: CustomFilter[];
}

export interface CustomFilter {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'between' | 'in' | 'not_in';
  value: any;
  dataType: 'string' | 'number' | 'date' | 'boolean' | 'array';
}

export interface GroupByConfig {
  field: string;
  label: string;
  order: number;
  aggregateFunction?: 'count' | 'sum' | 'avg' | 'min' | 'max';
}

export interface SortByConfig {
  field: string;
  direction: 'asc' | 'desc';
  order: number;
}

export interface ReportColumn {
  field: string;
  label: string;
  type: 'string' | 'number' | 'date' | 'boolean' | 'currency' | 'percentage' | 'custom';
  width?: number;
  visible: boolean;
  order: number;
  formatting?: ColumnFormatting;
  aggregation?: 'sum' | 'avg' | 'count' | 'min' | 'max';
}

export interface ColumnFormatting {
  dateFormat?: string;
  numberFormat?: {
    decimals: number;
    thousandsSeparator: boolean;
    prefix?: string;
    suffix?: string;
  };
  conditionalFormatting?: ConditionalFormat[];
}

export interface ConditionalFormat {
  condition: {
    operator: 'equals' | 'greater_than' | 'less_than' | 'between';
    value: any;
  };
  style: {
    color?: string;
    backgroundColor?: string;
    fontWeight?: 'normal' | 'bold';
    icon?: string;
  };
}

export interface AggregationConfig {
  field: string;
  function: 'count' | 'sum' | 'avg' | 'min' | 'max' | 'distinct_count';
  label: string;
  formatting?: ColumnFormatting;
}

export interface FormattingConfig {
  theme: 'default' | 'minimal' | 'corporate' | 'modern';
  colors: {
    primary: string;
    secondary: string;
    accent: string;
  };
  fonts: {
    header: string;
    body: string;
    size: 'small' | 'medium' | 'large';
  };
  layout: {
    orientation: 'portrait' | 'landscape';
    margins: {
      top: number;
      right: number;
      bottom: number;
      left: number;
    };
    showHeader: boolean;
    showFooter: boolean;
    showPageNumbers: boolean;
  };
}

export interface SchedulingConfig {
  enabled: boolean;
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  time: string;
  timezone: string;
  recipients: string[];
  format: ExportFormat;
  nextRun?: string;
}

export interface SharingConfig {
  isPublic: boolean;
  allowedUsers: string[];
  allowedRoles: string[];
  permissions: {
    view: boolean;
    edit: boolean;
    delete: boolean;
    share: boolean;
  };
  expiresAt?: string;
}

export interface VisualizationConfig {
  enabled: boolean;
  charts: ChartConfig[];
  layout: 'grid' | 'tabs' | 'accordion';
}

export interface ChartConfig {
  id: string;
  type: 'bar' | 'line' | 'pie' | 'doughnut' | 'area' | 'scatter';
  title: string;
  dataField: string;
  labelField: string;
  aggregation: 'sum' | 'count' | 'avg';
  colors: string[];
  position: {
    row: number;
    col: number;
    width: number;
    height: number;
  };
}

export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  category: ReportCategory;
  config: Partial<ReportConfig>;
  preview?: string;
  tags: string[];
  isBuiltIn: boolean;
  usageCount: number;
}

// Utility functions
const getDefaultConfig = (): ReportConfig => ({
  name: '',
  type: 'summary',
  category: 'trainees',
  dataSource: {
    primary: 'trainees'
  },
  filters: {},
  groupBy: [],
  sortBy: [],
  columns: [],
  aggregations: [],
  formatting: {
    theme: 'default',
    colors: {
      primary: '#3b82f6',
      secondary: '#6b7280',
      accent: '#10b981'
    },
    fonts: {
      header: 'Arial',
      body: 'Arial',
      size: 'medium'
    },
    layout: {
      orientation: 'portrait',
      margins: { top: 20, right: 20, bottom: 20, left: 20 },
      showHeader: true,
      showFooter: true,
      showPageNumbers: true
    }
  },
  createdBy: 'current-user',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  isTemplate: false,
  tags: []
});

const getAvailableFields = (dataSource: string): { field: string; label: string; type: string }[] => {
  const fieldMaps: Record<string, any[]> = {
    trainees: [
      { field: 'id', label: 'ID', type: 'string' },
      { field: 'name', label: 'Name', type: 'string' },
      { field: 'email', label: 'Email', type: 'string' },
      { field: 'department', label: 'Department', type: 'string' },
      { field: 'program', label: 'Program', type: 'string' },
      { field: 'status', label: 'Status', type: 'string' },
      { field: 'startDate', label: 'Start Date', type: 'date' },
      { field: 'progress', label: 'Progress', type: 'percentage' },
      { field: 'score', label: 'Score', type: 'number' }
    ],
    programs: [
      { field: 'id', label: 'ID', type: 'string' },
      { field: 'name', label: 'Name', type: 'string' },
      { field: 'description', label: 'Description', type: 'string' },
      { field: 'duration', label: 'Duration', type: 'number' },
      { field: 'capacity', label: 'Capacity', type: 'number' },
      { field: 'enrolled', label: 'Enrolled', type: 'number' },
      { field: 'startDate', label: 'Start Date', type: 'date' },
      { field: 'endDate', label: 'End Date', type: 'date' },
      { field: 'status', label: 'Status', type: 'string' }
    ],
    assessments: [
      { field: 'id', label: 'ID', type: 'string' },
      { field: 'title', label: 'Title', type: 'string' },
      { field: 'type', label: 'Type', type: 'string' },
      { field: 'trainee', label: 'Trainee', type: 'string' },
      { field: 'score', label: 'Score', type: 'number' },
      { field: 'maxScore', label: 'Max Score', type: 'number' },
      { field: 'percentage', label: 'Percentage', type: 'percentage' },
      { field: 'completedAt', label: 'Completed At', type: 'date' },
      { field: 'duration', label: 'Duration', type: 'number' }
    ]
  };
  
  return fieldMaps[dataSource] || [];
};

const validateConfig = (config: ReportConfig): string[] => {
  const errors: string[] = [];
  
  if (!config.name.trim()) {
    errors.push('Report name is required');
  }
  
  if (!config.dataSource.primary) {
    errors.push('Data source is required');
  }
  
  if (config.columns.length === 0) {
    errors.push('At least one column must be selected');
  }
  
  return errors;
};

const formatFieldLabel = (field: string): string => {
  return field
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase())
    .trim();
};

// Sub-components
interface ReportBasicInfoProps {
  config: ReportConfig;
  onChange: (updates: Partial<ReportConfig>) => void;
  templates: ReportTemplate[];
  onLoadTemplate: (template: ReportTemplate) => void;
}

const ReportBasicInfo: React.FC<ReportBasicInfoProps> = ({
  config,
  onChange,
  templates,
  onLoadTemplate
}) => {
  return (
    <div className="report-builder__basic-info">
      <h3 className="report-builder__section-title">Basic Information</h3>
      
      <div className="report-builder__form-grid">
        <FormField
          name="name"
          label="Report Name"
          value={config.name}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => onChange({ name: e.target.value })}
          required
          placeholder="Enter report name"
        />
        
        <FormField
          name="description"
          label="Description"
          value={config.description || ''}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => onChange({ description: e.target.value })}
          placeholder="Enter report description"
        />
        
        <FormField
          name="type"
          label="Report Type"
          value={config.type}
          onChange={(e: React.ChangeEvent<HTMLSelectElement>) => onChange({ type: e.target.value as ReportType })}
          options={[
            { value: 'summary', label: 'Summary Report' },
            { value: 'detailed', label: 'Detailed Report' },
            { value: 'analytical', label: 'Analytical Report' },
            { value: 'dashboard', label: 'Dashboard Report' },
            { value: 'export', label: 'Export Report' },
            { value: 'custom', label: 'Custom Report' }
          ]}
        />
        
        <FormField
          name="category"
          label="Category"
          value={config.category}
          onChange={(e: React.ChangeEvent<HTMLSelectElement>) => onChange({ category: e.target.value as ReportCategory })}
          options={[
            { value: 'trainees', label: 'Trainees' },
            { value: 'programs', label: 'Programs' },
            { value: 'assessments', label: 'Assessments' },
            { value: 'reviews', label: 'Reviews' },
            { value: 'workflows', label: 'Workflows' },
            { value: 'analytics', label: 'Analytics' },
            { value: 'compliance', label: 'Compliance' },
            { value: 'financial', label: 'Financial' }
          ]}
        />
      </div>
      
      {templates.length > 0 && (
        <div className="report-builder__templates">
          <h4 className="report-builder__subsection-title">Templates</h4>
          <div className="report-builder__template-grid">
            {templates.map((template) => (
              <div key={template.id} className="report-builder__template-card">
                <div className="report-builder__template-header">
                  <h5 className="report-builder__template-name">{template.name}</h5>
                  <Badge variant="default" size="small">
                    {template.category}
                  </Badge>
                </div>
                
                <p className="report-builder__template-description">
                  {template.description}
                </p>
                
                <div className="report-builder__template-tags">
                  {template.tags.map((tag) => (
                    <Badge key={tag} variant="outline" size="small">
                      {tag}
                    </Badge>
                  ))}
                </div>
                
                <button
                  onClick={() => onLoadTemplate(template)}
                  className="report-builder__template-load"
                >
                  Use Template
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

interface DataSourceConfigProps {
  config: ReportConfig;
  onChange: (updates: Partial<ReportConfig>) => void;
}

const DataSourceConfig: React.FC<DataSourceConfigProps> = ({ config, onChange }) => {
  const availableFields = useMemo(() => {
    return getAvailableFields(config.dataSource.primary);
  }, [config.dataSource.primary]);
  
  const handleColumnToggle = (field: string, enabled: boolean) => {
    const existingColumn = config.columns.find(col => col.field === field);
    
    if (enabled && !existingColumn) {
      const fieldInfo = availableFields.find(f => f.field === field);
      const newColumn: ReportColumn = {
        field,
        label: fieldInfo?.label || formatFieldLabel(field),
        type: fieldInfo?.type as any || 'string',
        visible: true,
        order: config.columns.length
      };
      
      onChange({
        columns: [...config.columns, newColumn]
      });
    } else if (!enabled && existingColumn) {
      onChange({
        columns: config.columns.filter(col => col.field !== field)
      });
    }
  };
  
  return (
    <div className="report-builder__data-source">
      <h3 className="report-builder__section-title">Data Source & Columns</h3>
      
      <FormField
        name="dataSource"
        label="Primary Data Source"
        value={config.dataSource.primary}
        onChange={(e: React.ChangeEvent<HTMLSelectElement>) => onChange({ 
          dataSource: { ...config.dataSource, primary: e.target.value },
          columns: [] // Reset columns when data source changes
        })}
        options={[
          { value: 'trainees', label: 'Trainees' },
          { value: 'programs', label: 'Programs' },
          { value: 'assessments', label: 'Assessments' },
          { value: 'reviews', label: 'Reviews' },
          { value: 'workflows', label: 'Workflows' }
        ]}
      />
      
      <div className="report-builder__columns">
        <h4 className="report-builder__subsection-title">Available Columns</h4>
        <div className="report-builder__column-grid">
          {availableFields.map((field) => {
            const isSelected = config.columns.some(col => col.field === field.field);
            
            return (
              <label key={field.field} className="report-builder__column-option">
                <input
                  type="checkbox"
                  checked={isSelected}
                  onChange={(e) => handleColumnToggle(field.field, e.target.checked)}
                />
                <span className="report-builder__column-label">{field.label}</span>
                <Badge variant="outline" size="small">
                  {field.type}
                </Badge>
              </label>
            );
          })}
        </div>
      </div>
      
      {config.columns.length > 0 && (
        <div className="report-builder__selected-columns">
          <h4 className="report-builder__subsection-title">Selected Columns</h4>
          <div className="report-builder__column-list">
            {config.columns
              .sort((a, b) => a.order - b.order)
              .map((column, index) => (
                <div key={column.field} className="report-builder__column-item">
                  <div className="report-builder__column-info">
                    <span className="report-builder__column-name">{column.label}</span>
                    <Badge variant="outline" size="small">
                      {column.type}
                    </Badge>
                  </div>
                  
                  <div className="report-builder__column-controls">
                    <button
                      onClick={() => {
                        const newColumns = [...config.columns];
                        if (index > 0) {
                          [newColumns[index], newColumns[index - 1]] = [newColumns[index - 1], newColumns[index]];
                          newColumns.forEach((col, i) => col.order = i);
                          onChange({ columns: newColumns });
                        }
                      }}
                      disabled={index === 0}
                      className="report-builder__column-move"
                      title="Move up"
                    >
                      ↑
                    </button>
                    
                    <button
                      onClick={() => {
                        const newColumns = [...config.columns];
                        if (index < newColumns.length - 1) {
                          [newColumns[index], newColumns[index + 1]] = [newColumns[index + 1], newColumns[index]];
                          newColumns.forEach((col, i) => col.order = i);
                          onChange({ columns: newColumns });
                        }
                      }}
                      disabled={index === config.columns.length - 1}
                      className="report-builder__column-move"
                      title="Move down"
                    >
                      ↓
                    </button>
                    
                    <button
                      onClick={() => handleColumnToggle(column.field, false)}
                      className="report-builder__column-remove"
                      title="Remove column"
                    >
                      ×
                    </button>
                  </div>
                </div>
              ))}
          </div>
        </div>
      )}
    </div>
  );
};

interface FiltersConfigProps {
  config: ReportConfig;
  onChange: (updates: Partial<ReportConfig>) => void;
}

const FiltersConfig: React.FC<FiltersConfigProps> = ({ config, onChange }) => {
  const updateFilters = (filterUpdates: Partial<ReportFilters>) => {
    onChange({
      filters: {
        ...config.filters,
        ...filterUpdates
      }
    });
  };
  
  return (
    <div className="report-builder__filters">
      <h3 className="report-builder__section-title">Filters</h3>
      
      <div className="report-builder__form-grid">
        <div className="report-builder__date-range">
          <h4 className="report-builder__subsection-title">Date Range</h4>
          
          <FormField
            name="datePreset"
            label="Preset"
            value={config.filters.dateRange?.preset || 'custom'}
            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
              const preset = e.target.value as 'today' | 'week' | 'month' | 'quarter' | 'year' | 'custom';
              if (preset !== 'custom') {
                const now = new Date();
                let start: Date;
                
                switch (preset) {
                  case 'today':
                    start = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    break;
                  case 'week':
                    start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    break;
                  case 'month':
                    start = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
                    break;
                  case 'quarter':
                    start = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());
                    break;
                  case 'year':
                    start = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
                    break;
                  default:
                    start = now;
                }
                
                updateFilters({
                  dateRange: {
                    preset,
                    start: start.toISOString().split('T')[0],
                    end: now.toISOString().split('T')[0]
                  }
                });
              } else {
                updateFilters({
                  dateRange: {
                    preset: 'custom',
                    start: '',
                    end: ''
                  }
                });
              }
            }}
            options={[
              { value: 'today', label: 'Today' },
              { value: 'week', label: 'Last 7 days' },
              { value: 'month', label: 'Last 30 days' },
              { value: 'quarter', label: 'Last 3 months' },
              { value: 'year', label: 'Last year' },
              { value: 'custom', label: 'Custom range' }
            ]}
          />
          
          {config.filters.dateRange?.preset === 'custom' && (
            <div className="report-builder__date-inputs">
              <DatePicker
                label="Start Date"
                value={config.filters.dateRange?.start ? new Date(config.filters.dateRange.start) : undefined}
                onChange={(date: Date | undefined) => updateFilters({
                  dateRange: {
                    ...config.filters.dateRange,
                    start: date?.toISOString().split('T')[0] || ''
                  }
                })}
              />
              
              <DatePicker
                label="End Date"
                value={config.filters.dateRange?.end ? new Date(config.filters.dateRange.end) : undefined}
                onChange={(date: Date | undefined) => updateFilters({
                  dateRange: {
                    ...config.filters.dateRange,
                    end: date?.toISOString().split('T')[0] || ''
                  }
                })}
                minDate={config.filters.dateRange?.start ? new Date(config.filters.dateRange.start) : undefined}
              />
            </div>
          )}
        </div>
        
        <MultiSelect
          label="Status"
          value={config.filters.status || []}
          onChange={(values) => updateFilters({ status: values as string[] })}
          options={[
            { value: 'active', label: 'Active' },
            { value: 'inactive', label: 'Inactive' },
            { value: 'pending', label: 'Pending' },
            { value: 'completed', label: 'Completed' },
            { value: 'cancelled', label: 'Cancelled' }
          ]}
          placeholder="Select status filters"
        />
        
        <MultiSelect
          label="Categories"
          value={config.filters.categories || []}
          onChange={(values) => updateFilters({ categories: values as string[] })}
          options={[
            { value: 'beginner', label: 'Beginner' },
            { value: 'intermediate', label: 'Intermediate' },
            { value: 'advanced', label: 'Advanced' },
            { value: 'leadership', label: 'Leadership' },
            { value: 'technical', label: 'Technical' }
          ]}
          placeholder="Select category filters"
        />
        
        <MultiSelect
          label="Departments"
          value={config.filters.departments || []}
          onChange={(values) => updateFilters({ departments: values as string[] })}
          options={[
            { value: 'engineering', label: 'Engineering' },
            { value: 'marketing', label: 'Marketing' },
            { value: 'sales', label: 'Sales' },
            { value: 'hr', label: 'Human Resources' },
            { value: 'finance', label: 'Finance' }
          ]}
          placeholder="Select department filters"
        />
      </div>
    </div>
  );
};

interface FormattingConfigProps {
  config: ReportConfig;
  onChange: (updates: Partial<ReportConfig>) => void;
}

const FormattingConfig: React.FC<FormattingConfigProps> = ({ config, onChange }) => {
  const updateFormatting = (formattingUpdates: Partial<FormattingConfig>) => {
    onChange({
      formatting: {
        ...config.formatting,
        ...formattingUpdates
      }
    });
  };
  
  return (
    <div className="report-builder__formatting">
      <h3 className="report-builder__section-title">Formatting & Layout</h3>
      
      <div className="report-builder__form-grid">
        <FormField
          name="theme"
          label="Theme"
          value={config.formatting.theme}
          onChange={(e: React.ChangeEvent<HTMLSelectElement>) => updateFormatting({ theme: e.target.value as 'default' | 'minimal' | 'corporate' | 'modern' })}
          options={[
            { value: 'default', label: 'Default' },
            { value: 'minimal', label: 'Minimal' },
            { value: 'corporate', label: 'Corporate' },
            { value: 'modern', label: 'Modern' }
          ]}
        />
        
        <FormField
          name="orientation"
          label="Orientation"
          value={config.formatting.layout.orientation}
          onChange={(e: React.ChangeEvent<HTMLSelectElement>) => updateFormatting({
            layout: {
              ...config.formatting.layout,
              orientation: e.target.value as 'portrait' | 'landscape'
            }
          })}
          options={[
            { value: 'portrait', label: 'Portrait' },
            { value: 'landscape', label: 'Landscape' }
          ]}
        />
        
        <FormField
          name="fontSize"
          label="Font Size"
          value={config.formatting.fonts.size}
          onChange={(e: React.ChangeEvent<HTMLSelectElement>) => updateFormatting({
            fonts: {
              ...config.formatting.fonts,
              size: e.target.value as 'small' | 'medium' | 'large'
            }
          })}
          options={[
            { value: 'small', label: 'Small' },
            { value: 'medium', label: 'Medium' },
            { value: 'large', label: 'Large' }
          ]}
        />
      </div>
      
      <div className="report-builder__layout-options">
        <h4 className="report-builder__subsection-title">Layout Options</h4>
        
        <div className="report-builder__checkbox-group">
          <label className="report-builder__checkbox">
            <input
              type="checkbox"
              checked={config.formatting.layout.showHeader}
              onChange={(e) => updateFormatting({
                layout: {
                  ...config.formatting.layout,
                  showHeader: e.target.checked
                }
              })}
            />
            <span>Show Header</span>
          </label>
          
          <label className="report-builder__checkbox">
            <input
              type="checkbox"
              checked={config.formatting.layout.showFooter}
              onChange={(e) => updateFormatting({
                layout: {
                  ...config.formatting.layout,
                  showFooter: e.target.checked
                }
              })}
            />
            <span>Show Footer</span>
          </label>
          
          <label className="report-builder__checkbox">
            <input
              type="checkbox"
              checked={config.formatting.layout.showPageNumbers}
              onChange={(e) => updateFormatting({
                layout: {
                  ...config.formatting.layout,
                  showPageNumbers: e.target.checked
                }
              })}
            />
            <span>Show Page Numbers</span>
          </label>
        </div>
      </div>
    </div>
  );
};

interface ReportPreviewProps {
  config: ReportConfig;
}

const ReportPreview: React.FC<ReportPreviewProps> = ({ config }) => {
  const mockData = [
    { id: '1', name: 'John Doe', department: 'Engineering', status: 'Active', progress: 85 },
    { id: '2', name: 'Jane Smith', department: 'Marketing', status: 'Active', progress: 92 },
    { id: '3', name: 'Mike Johnson', department: 'Sales', status: 'Pending', progress: 67 }
  ];
  
  return (
    <div className="report-builder__preview">
      <h3 className="report-builder__section-title">Preview</h3>
      
      <div className="report-builder__preview-container">
        <div className="report-builder__preview-header">
          <h4 className="report-builder__preview-title">{config.name || 'Untitled Report'}</h4>
          {config.description && (
            <p className="report-builder__preview-description">{config.description}</p>
          )}
        </div>
        
        {config.columns.length > 0 ? (
          <div className="report-builder__preview-table">
            <table>
              <thead>
                <tr>
                  {config.columns
                    .filter(col => col.visible)
                    .sort((a, b) => a.order - b.order)
                    .map(column => (
                      <th key={column.field}>{column.label}</th>
                    ))}
                </tr>
              </thead>
              <tbody>
                {mockData.map((row) => (
                  <tr key={row.id}>
                    {config.columns
                      .filter(col => col.visible)
                      .sort((a, b) => a.order - b.order)
                      .map(column => (
                        <td key={column.field}>
                          {row[column.field as keyof typeof row] || '-'}
                        </td>
                      ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="report-builder__preview-empty">
            <p>Select columns to see preview</p>
          </div>
        )}
      </div>
    </div>
  );
};

// Main ReportBuilder Component
export const ReportBuilder: React.FC<ReportBuilderProps> = ({
  className = '',
  onGenerate,
  onSave,
  onLoad,
  onExport,
  initialConfig,
  availableTemplates = [],
  permissions = ['read', 'write']
}) => {
  const [config, setConfig] = useState<ReportConfig>(() => ({
    ...getDefaultConfig(),
    ...initialConfig
  }));
  
  const [activeTab, setActiveTab] = useState<'basic' | 'data' | 'filters' | 'formatting' | 'preview'>('basic');
  const [errors, setErrors] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  
  const handleConfigChange = (updates: Partial<ReportConfig>) => {
    const newConfig = { ...config, ...updates };
    setConfig(newConfig);
    setErrors(validateConfig(newConfig));
  };
  
  const handleLoadTemplate = (template: ReportTemplate) => {
    const newConfig = {
      ...getDefaultConfig(),
      ...template.config,
      id: undefined,
      name: `${template.name} (Copy)`,
      isTemplate: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    setConfig(newConfig);
    setErrors(validateConfig(newConfig));
  };
  
  const handleGenerate = async () => {
    const validationErrors = validateConfig(config);
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }
    
    setIsGenerating(true);
    try {
      await onGenerate?.(config);
    } finally {
      setIsGenerating(false);
    }
  };
  
  const handleSave = () => {
    const validationErrors = validateConfig(config);
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }
    
    onSave?.(config);
  };
  
  const handleExport = (format: ExportFormat) => {
    const validationErrors = validateConfig(config);
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }
    
    onExport?.(config, format);
  };
  
  const canWrite = permissions.includes('write');
  
  return (
    <div className={`report-builder ${className}`}>
      <div className="report-builder__header">
        <h2 className="report-builder__title">Report Builder</h2>
        
        <div className="report-builder__actions">
          {canWrite && (
            <>
              <button
                onClick={handleSave}
                className="report-builder__action report-builder__action--save"
                disabled={errors.length > 0}
              >
                Save Report
              </button>
              
              <button
                onClick={handleGenerate}
                className="report-builder__action report-builder__action--generate"
                disabled={errors.length > 0 || isGenerating}
              >
                {isGenerating ? 'Generating...' : 'Generate Report'}
              </button>
            </>
          )}
          
          <div className="report-builder__export-dropdown">
            <button className="report-builder__action report-builder__action--export">
              Export ▼
            </button>
            <div className="report-builder__export-menu">
              <button onClick={() => handleExport('pdf')}>Export as PDF</button>
              <button onClick={() => handleExport('excel')}>Export as Excel</button>
              <button onClick={() => handleExport('csv')}>Export as CSV</button>
              <button onClick={() => handleExport('json')}>Export as JSON</button>
            </div>
          </div>
        </div>
      </div>
      
      {errors.length > 0 && (
        <div className="report-builder__errors">
          <h4>Please fix the following errors:</h4>
          <ul>
            {errors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </div>
      )}
      
      <div className="report-builder__tabs">
        <button
          onClick={() => setActiveTab('basic')}
          className={`report-builder__tab ${activeTab === 'basic' ? 'report-builder__tab--active' : ''}`}
        >
          Basic Info
        </button>
        <button
          onClick={() => setActiveTab('data')}
          className={`report-builder__tab ${activeTab === 'data' ? 'report-builder__tab--active' : ''}`}
        >
          Data & Columns
        </button>
        <button
          onClick={() => setActiveTab('filters')}
          className={`report-builder__tab ${activeTab === 'filters' ? 'report-builder__tab--active' : ''}`}
        >
          Filters
        </button>
        <button
          onClick={() => setActiveTab('formatting')}
          className={`report-builder__tab ${activeTab === 'formatting' ? 'report-builder__tab--active' : ''}`}
        >
          Formatting
        </button>
        <button
          onClick={() => setActiveTab('preview')}
          className={`report-builder__tab ${activeTab === 'preview' ? 'report-builder__tab--active' : ''}`}
        >
          Preview
        </button>
      </div>
      
      <div className="report-builder__content">
        {activeTab === 'basic' && (
          <ReportBasicInfo
            config={config}
            onChange={handleConfigChange}
            templates={availableTemplates}
            onLoadTemplate={handleLoadTemplate}
          />
        )}
        
        {activeTab === 'data' && (
          <DataSourceConfig
            config={config}
            onChange={handleConfigChange}
          />
        )}
        
        {activeTab === 'filters' && (
          <FiltersConfig
            config={config}
            onChange={handleConfigChange}
          />
        )}
        
        {activeTab === 'formatting' && (
          <FormattingConfig
            config={config}
            onChange={handleConfigChange}
          />
        )}
        
        {activeTab === 'preview' && (
          <ReportPreview config={config} />
        )}
      </div>
    </div>
  );
};

export default ReportBuilder;