import React, { useState, useRef, useCallback, forwardRef } from 'react';
import { FieldError } from 'react-hook-form';

// Types
export interface FileUploadFile {
  id: string;
  file: File;
  name: string;
  size: number;
  type: string;
  url?: string;
  progress?: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
}

export interface FileUploadProps {
  value?: FileUploadFile[];
  onChange?: (files: FileUploadFile[]) => void;
  onUpload?: (files: File[]) => Promise<string[]>; // Returns URLs
  accept?: string;
  multiple?: boolean;
  maxFiles?: number;
  maxSize?: number; // in bytes
  minSize?: number; // in bytes
  disabled?: boolean;
  error?: FieldError | string | boolean;
  className?: string;
  variant?: 'default' | 'dropzone' | 'button';
  size?: 'small' | 'medium' | 'large';
  showPreview?: boolean;
  showProgress?: boolean;
  allowReorder?: boolean;
  allowRemove?: boolean;
  placeholder?: string;
  uploadText?: string;
  browseText?: string;
  dragActiveText?: string;
  maxSizeText?: string;
  fileTypeText?: string;
  removeText?: string;
  retryText?: string;
  validateFile?: (file: File) => string | null; // Return error message or null
  customPreview?: (file: FileUploadFile) => React.ReactNode;
  name?: string;
  id?: string;
  'aria-label'?: string;
  'aria-describedby'?: string;
}

// Utility functions
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const generateFileId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};

const getFileType = (file: File): string => {
  if (file.type.startsWith('image/')) return 'image';
  if (file.type.startsWith('video/')) return 'video';
  if (file.type.startsWith('audio/')) return 'audio';
  if (file.type === 'application/pdf') return 'pdf';
  if (file.type.includes('document') || file.type.includes('word')) return 'document';
  if (file.type.includes('spreadsheet') || file.type.includes('excel')) return 'spreadsheet';
  if (file.type.includes('presentation') || file.type.includes('powerpoint')) return 'presentation';
  return 'file';
};

const isImageFile = (file: File): boolean => {
  return file.type.startsWith('image/');
};

// File Preview Component
interface FilePreviewProps {
  file: FileUploadFile;
  onRemove?: (file: FileUploadFile) => void;
  onRetry?: (file: FileUploadFile) => void;
  showProgress?: boolean;
  allowRemove?: boolean;
  customPreview?: (file: FileUploadFile) => React.ReactNode;
  removeText?: string;
  retryText?: string;
}

const FilePreview: React.FC<FilePreviewProps> = ({
  file,
  onRemove,
  onRetry,
  showProgress = true,
  allowRemove = true,
  customPreview,
  removeText = 'Remove',
  retryText = 'Retry'
}) => {
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  
  React.useEffect(() => {
    if (isImageFile(file.file)) {
      const url = URL.createObjectURL(file.file);
      setImageUrl(url);
      return () => URL.revokeObjectURL(url);
    }
  }, [file.file]);
  
  if (customPreview) {
    return <div className="file-upload__custom-preview">{customPreview(file)}</div>;
  }
  
  const fileType = getFileType(file.file);
  
  const handleRemove = () => {
    onRemove?.(file);
  };
  
  const handleRetry = () => {
    onRetry?.(file);
  };
  
  const getFileIcon = () => {
    switch (fileType) {
      case 'image':
        return imageUrl ? (
          <img src={imageUrl} alt={file.name} className="file-upload__preview-image" />
        ) : (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
          </svg>
        );
      case 'video':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z"/>
          </svg>
        );
      case 'audio':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
          </svg>
        );
      case 'pdf':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M20 2H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-8.5 7.5c0 .83-.67 1.5-1.5 1.5H9v2H7.5V7H10c.83 0 1.5.67 1.5 1.5v1zm5 2c0 .83-.67 1.5-1.5 1.5h-2.5V7H15c.83 0 1.5.67 1.5 1.5v3zm4-3H19v1h1.5V11H19v1h-1.5V7h3v1.5zM9 9.5h1v-1H9v1z"/>
          </svg>
        );
      default:
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M6 2c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8l6-6V8l-6-6H6zm7 7V3.5L18.5 9H13z"/>
          </svg>
        );
    }
  };
  
  const statusClasses = {
    pending: 'file-upload__file--pending',
    uploading: 'file-upload__file--uploading',
    success: 'file-upload__file--success',
    error: 'file-upload__file--error'
  };
  
  return (
    <div className={`file-upload__file ${statusClasses[file.status]}`}>
      <div className="file-upload__file-icon">
        {getFileIcon()}
      </div>
      
      <div className="file-upload__file-info">
        <div className="file-upload__file-name" title={file.name}>
          {file.name}
        </div>
        <div className="file-upload__file-size">
          {formatFileSize(file.size)}
        </div>
        
        {file.error && (
          <div className="file-upload__file-error">
            {file.error}
          </div>
        )}
        
        {showProgress && file.status === 'uploading' && typeof file.progress === 'number' && (
          <div className="file-upload__progress">
            <div className="file-upload__progress-bar">
              <div 
                className="file-upload__progress-fill"
                style={{ width: `${file.progress}%` }}
              />
            </div>
            <span className="file-upload__progress-text">{file.progress}%</span>
          </div>
        )}
      </div>
      
      <div className="file-upload__file-actions">
        {file.status === 'error' && onRetry && (
          <button
            type="button"
            onClick={handleRetry}
            className="file-upload__retry-button"
            title={retryText}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
            </svg>
          </button>
        )}
        
        {allowRemove && (
          <button
            type="button"
            onClick={handleRemove}
            className="file-upload__remove-button"
            title={removeText}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        )}
      </div>
    </div>
  );
};

// Main FileUpload Component
export const FileUpload = forwardRef<HTMLDivElement, FileUploadProps>((
  {
    value = [],
    onChange,
    onUpload,
    accept,
    multiple = false,
    maxFiles,
    maxSize,
    minSize,
    disabled = false,
    error,
    className = '',
    variant = 'default',
    size = 'medium',
    showPreview = true,
    showProgress = true,
    allowReorder = false,
    allowRemove = true,
    placeholder = 'Drop files here or click to browse',
    uploadText = 'Upload Files',
    browseText = 'Browse Files',
    dragActiveText = 'Drop files here...',
    maxSizeText = 'File size exceeds limit',
    fileTypeText = 'File type not supported',
    removeText = 'Remove',
    retryText = 'Retry',
    validateFile,
    customPreview,
    name,
    id,
    'aria-label': ariaLabel,
    'aria-describedby': ariaDescribedBy
  },
  ref
) => {
  const [isDragActive, setIsDragActive] = useState(false);
  const [dragCounter, setDragCounter] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  const hasError = !!error;
  
  const baseClasses = 'file-upload';
  const sizeClasses = {
    small: 'file-upload--small',
    medium: 'file-upload--medium',
    large: 'file-upload--large'
  };
  const variantClasses = {
    default: 'file-upload--default',
    dropzone: 'file-upload--dropzone',
    button: 'file-upload--button'
  };
  
  const containerClasses = [
    baseClasses,
    sizeClasses[size],
    variantClasses[variant],
    hasError && 'file-upload--error',
    disabled && 'file-upload--disabled',
    isDragActive && 'file-upload--drag-active',
    className
  ].filter(Boolean).join(' ');
  
  const validateFileConstraints = useCallback((file: File): string | null => {
    // Custom validation first
    if (validateFile) {
      const customError = validateFile(file);
      if (customError) return customError;
    }
    
    // Size validation
    if (maxSize && file.size > maxSize) {
      return `${maxSizeText} (${formatFileSize(maxSize)})`;
    }
    
    if (minSize && file.size < minSize) {
      return `File size too small (minimum: ${formatFileSize(minSize)})`;
    }
    
    // Type validation
    if (accept) {
      const acceptedTypes = accept.split(',').map(type => type.trim());
      const isAccepted = acceptedTypes.some(acceptedType => {
        if (acceptedType.startsWith('.')) {
          return file.name.toLowerCase().endsWith(acceptedType.toLowerCase());
        }
        if (acceptedType.includes('*')) {
          const baseType = acceptedType.split('/')[0];
          return file.type.startsWith(baseType);
        }
        return file.type === acceptedType;
      });
      
      if (!isAccepted) {
        return fileTypeText;
      }
    }
    
    return null;
  }, [accept, maxSize, minSize, validateFile, maxSizeText, fileTypeText]);
  
  const processFiles = useCallback((files: FileList | File[]) => {
    const fileArray = Array.from(files);
    const newFiles: FileUploadFile[] = [];
    
    // Check file count limit
    const totalFiles = value.length + fileArray.length;
    if (maxFiles && totalFiles > maxFiles) {
      // Take only the allowed number of files
      const allowedCount = maxFiles - value.length;
      fileArray.splice(allowedCount);
    }
    
    fileArray.forEach(file => {
      const error = validateFileConstraints(file);
      
      const fileUpload: FileUploadFile = {
        id: generateFileId(),
        file,
        name: file.name,
        size: file.size,
        type: file.type,
        status: error ? 'error' : 'pending',
        error: error || undefined
      };
      
      newFiles.push(fileUpload);
    });
    
    const updatedFiles = [...value, ...newFiles];
    onChange?.(updatedFiles);
    
    // Auto-upload if onUpload is provided and files are valid
    if (onUpload) {
      const validFiles = newFiles.filter(f => f.status === 'pending');
      if (validFiles.length > 0) {
        handleUpload(validFiles);
      }
    }
  }, [value, onChange, onUpload, maxFiles, validateFileConstraints]);
  
  const handleUpload = useCallback(async (filesToUpload: FileUploadFile[]) => {
    if (!onUpload) return;
    
    // Update status to uploading
    const updatedFiles = value.map(file => {
      if (filesToUpload.some(f => f.id === file.id)) {
        return { ...file, status: 'uploading' as const, progress: 0 };
      }
      return file;
    });
    onChange?.(updatedFiles);
    
    try {
      const files = filesToUpload.map(f => f.file);
      const urls = await onUpload(files);
      
      // Update with success status and URLs
      const finalFiles = value.map(file => {
        const uploadIndex = filesToUpload.findIndex(f => f.id === file.id);
        if (uploadIndex !== -1) {
          return {
            ...file,
            status: 'success' as const,
            url: urls[uploadIndex],
            progress: 100
          };
        }
        return file;
      });
      onChange?.(finalFiles);
    } catch (error) {
      // Update with error status
      const errorFiles = value.map(file => {
        if (filesToUpload.some(f => f.id === file.id)) {
          return {
            ...file,
            status: 'error' as const,
            error: error instanceof Error ? error.message : 'Upload failed'
          };
        }
        return file;
      });
      onChange?.(errorFiles);
    }
  }, [value, onChange, onUpload]);
  
  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      processFiles(files);
    }
    // Reset input value to allow selecting the same file again
    event.target.value = '';
  };
  
  const handleDragEnter = (event: React.DragEvent) => {
    event.preventDefault();
    event.stopPropagation();
    setDragCounter(prev => prev + 1);
    if (event.dataTransfer.items && event.dataTransfer.items.length > 0) {
      setIsDragActive(true);
    }
  };
  
  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    event.stopPropagation();
    setDragCounter(prev => {
      const newCounter = prev - 1;
      if (newCounter === 0) {
        setIsDragActive(false);
      }
      return newCounter;
    });
  };
  
  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    event.stopPropagation();
  };
  
  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragActive(false);
    setDragCounter(0);
    
    if (disabled) return;
    
    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      processFiles(files);
    }
  };
  
  const handleBrowseClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };
  
  const handleRemoveFile = (fileToRemove: FileUploadFile) => {
    const updatedFiles = value.filter(file => file.id !== fileToRemove.id);
    onChange?.(updatedFiles);
  };
  
  const handleRetryFile = (fileToRetry: FileUploadFile) => {
    handleUpload([fileToRetry]);
  };
  
  const renderDropzone = () => {
    return (
      <div
        className="file-upload__dropzone"
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={handleBrowseClick}
        role="button"
        tabIndex={disabled ? -1 : 0}
        aria-label={ariaLabel || placeholder}
        aria-describedby={ariaDescribedBy}
      >
        <div className="file-upload__dropzone-content">
          <div className="file-upload__dropzone-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
            </svg>
          </div>
          
          <div className="file-upload__dropzone-text">
            {isDragActive ? dragActiveText : placeholder}
          </div>
          
          <button
            type="button"
            className="file-upload__browse-button"
            disabled={disabled}
          >
            {browseText}
          </button>
          
          {(maxSize || accept) && (
            <div className="file-upload__constraints">
              {maxSize && (
                <span>Max size: {formatFileSize(maxSize)}</span>
              )}
              {accept && (
                <span>Accepted: {accept}</span>
              )}
            </div>
          )}
        </div>
      </div>
    );
  };
  
  const renderButton = () => {
    return (
      <button
        type="button"
        className="file-upload__button"
        onClick={handleBrowseClick}
        disabled={disabled}
        aria-label={ariaLabel || browseText}
        aria-describedby={ariaDescribedBy}
      >
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
        </svg>
        {browseText}
      </button>
    );
  };
  
  const renderFileList = () => {
    if (!showPreview || value.length === 0) return null;
    
    return (
      <div className="file-upload__file-list">
        {value.map(file => (
          <FilePreview
            key={file.id}
            file={file}
            onRemove={allowRemove ? handleRemoveFile : undefined}
            onRetry={handleRetryFile}
            showProgress={showProgress}
            allowRemove={allowRemove}
            customPreview={customPreview}
            removeText={removeText}
            retryText={retryText}
          />
        ))}
      </div>
    );
  };
  
  return (
    <div ref={ref || containerRef} className={containerClasses}>
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={handleFileInputChange}
        className="file-upload__input"
        name={name}
        id={id}
        disabled={disabled}
        style={{ display: 'none' }}
      />
      
      {variant === 'dropzone' && renderDropzone()}
      {variant === 'button' && renderButton()}
      {variant === 'default' && (
        <>
          {renderDropzone()}
          {renderFileList()}
        </>
      )}
      
      {variant !== 'default' && renderFileList()}
    </div>
  );
});

FileUpload.displayName = 'FileUpload';

export default FileUpload;