import React, { forwardRef, ReactNode } from 'react';
import { FieldError } from 'react-hook-form';

// Types
export interface FormFieldProps {
  label?: string;
  name: string;
  error?: FieldError | string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  children: ReactNode;
  description?: string;
  tooltip?: string;
  inline?: boolean;
  size?: 'small' | 'medium' | 'large';
  variant?: 'default' | 'filled' | 'outlined';
}

export interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  error?: boolean;
  size?: 'small' | 'medium' | 'large';
  variant?: 'default' | 'filled' | 'outlined';
}

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  error?: boolean;
  size?: 'small' | 'medium' | 'large';
  variant?: 'default' | 'filled' | 'outlined';
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
}

export interface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'size'> {
  error?: boolean;
  size?: 'small' | 'medium' | 'large';
  variant?: 'default' | 'filled' | 'outlined';
  placeholder?: string;
  options?: Array<{ value: string | number; label: string; disabled?: boolean }>;
}

// Base Input Component
export const Input = forwardRef<HTMLInputElement, InputProps>((
  { className = '', error, size = 'medium', variant = 'default', ...props },
  ref
) => {
  const baseClasses = 'form-input';
  const sizeClasses = {
    small: 'form-input--small',
    medium: 'form-input--medium',
    large: 'form-input--large'
  };
  const variantClasses = {
    default: 'form-input--default',
    filled: 'form-input--filled',
    outlined: 'form-input--outlined'
  };
  
  const classes = [
    baseClasses,
    sizeClasses[size],
    variantClasses[variant],
    error && 'form-input--error',
    props.disabled && 'form-input--disabled',
    className
  ].filter(Boolean).join(' ');

  return (
    <input
      ref={ref}
      className={classes}
      {...props}
    />
  );
});

Input.displayName = 'Input';

// Textarea Component
export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>((
  { className = '', error, size = 'medium', variant = 'default', resize = 'vertical', ...props },
  ref
) => {
  const baseClasses = 'form-textarea';
  const sizeClasses = {
    small: 'form-textarea--small',
    medium: 'form-textarea--medium',
    large: 'form-textarea--large'
  };
  const variantClasses = {
    default: 'form-textarea--default',
    filled: 'form-textarea--filled',
    outlined: 'form-textarea--outlined'
  };
  const resizeClasses = {
    none: 'form-textarea--resize-none',
    vertical: 'form-textarea--resize-vertical',
    horizontal: 'form-textarea--resize-horizontal',
    both: 'form-textarea--resize-both'
  };
  
  const classes = [
    baseClasses,
    sizeClasses[size],
    variantClasses[variant],
    resizeClasses[resize],
    error && 'form-textarea--error',
    props.disabled && 'form-textarea--disabled',
    className
  ].filter(Boolean).join(' ');

  return (
    <textarea
      ref={ref}
      className={classes}
      {...props}
    />
  );
});

Textarea.displayName = 'Textarea';

// Select Component
export const Select = forwardRef<HTMLSelectElement, SelectProps>((
  { className = '', error, size = 'medium', variant = 'default', placeholder, options = [], children, ...props },
  ref
) => {
  const baseClasses = 'form-select';
  const sizeClasses = {
    small: 'form-select--small',
    medium: 'form-select--medium',
    large: 'form-select--large'
  };
  const variantClasses = {
    default: 'form-select--default',
    filled: 'form-select--filled',
    outlined: 'form-select--outlined'
  };
  
  const classes = [
    baseClasses,
    sizeClasses[size],
    variantClasses[variant],
    error && 'form-select--error',
    props.disabled && 'form-select--disabled',
    className
  ].filter(Boolean).join(' ');

  return (
    <select
      ref={ref}
      className={classes}
      {...props}
    >
      {placeholder && (
        <option value="" disabled>
          {placeholder}
        </option>
      )}
      {options.map((option) => (
        <option
          key={option.value}
          value={option.value}
          disabled={option.disabled}
        >
          {option.label}
        </option>
      ))}
      {children}
    </select>
  );
});

Select.displayName = 'Select';

// Main FormField Component
export const FormField = forwardRef<HTMLDivElement, FormFieldProps>((
  {
    label,
    name,
    error,
    required,
    disabled,
    className = '',
    children,
    description,
    tooltip,
    inline = false,
    size = 'medium',
    variant = 'default'
  },
  ref
) => {
  const errorMessage = typeof error === 'string' ? error : error?.message;
  const hasError = !!errorMessage;

  const baseClasses = 'form-field';
  const sizeClasses = {
    small: 'form-field--small',
    medium: 'form-field--medium',
    large: 'form-field--large'
  };
  const variantClasses = {
    default: 'form-field--default',
    filled: 'form-field--filled',
    outlined: 'form-field--outlined'
  };
  
  const classes = [
    baseClasses,
    sizeClasses[size],
    variantClasses[variant],
    inline && 'form-field--inline',
    hasError && 'form-field--error',
    disabled && 'form-field--disabled',
    className
  ].filter(Boolean).join(' ');

  return (
    <div ref={ref} className={classes}>
      {label && (
        <div className="form-field__label-wrapper">
          <label htmlFor={name} className="form-field__label">
            {label}
            {required && <span className="form-field__required">*</span>}
          </label>
          {tooltip && (
            <div className="form-field__tooltip" title={tooltip}>
              <svg
                className="form-field__tooltip-icon"
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="currentColor"
              >
                <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                <path d="m8.93 6.588-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533L8.93 6.588zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0z"/>
              </svg>
            </div>
          )}
        </div>
      )}
      
      <div className="form-field__input-wrapper">
        {children}
      </div>
      
      {description && (
        <div className="form-field__description">
          {description}
        </div>
      )}
      
      {hasError && (
        <div className="form-field__error">
          <svg
            className="form-field__error-icon"
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="currentColor"
          >
            <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8 4a.905.905 0 0 0-.9.995l.35 3.507a.552.552 0 0 0 1.1 0l.35-3.507A.905.905 0 0 0 8 4zm.002 6a1 1 0 1 0 0 2 1 1 0 0 0 0-2z"/>
          </svg>
          <span className="form-field__error-message">{errorMessage}</span>
        </div>
      )}
    </div>
  );
});

FormField.displayName = 'FormField';

// Create compound component with proper typing
const FormFieldWithSubComponents = FormField as typeof FormField & {
  Input: typeof Input;
  Textarea: typeof Textarea;
  Select: typeof Select;
};

// Compound component exports
FormFieldWithSubComponents.Input = Input;
FormFieldWithSubComponents.Textarea = Textarea;
FormFieldWithSubComponents.Select = Select;

export default FormFieldWithSubComponents;