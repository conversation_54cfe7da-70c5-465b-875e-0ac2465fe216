import React, { forwardRef, useMemo } from 'react';

// Types
export interface ProgressBarProps {
  value: number;
  max?: number;
  min?: number;
  label?: string;
  showValue?: boolean;
  showPercentage?: boolean;
  color?: 'default' | 'primary' | 'success' | 'warning' | 'error' | 'info' | string;
  variant?: 'default' | 'striped' | 'animated' | 'gradient';
  size?: 'small' | 'medium' | 'large';
  orientation?: 'horizontal' | 'vertical';
  className?: string;
  trackClassName?: string;
  fillClassName?: string;
  labelClassName?: string;
  animated?: boolean;
  striped?: boolean;
  indeterminate?: boolean;
  segments?: ProgressSegment[];
  showSegmentLabels?: boolean;
  rounded?: boolean;
  thickness?: number;
  children?: React.ReactNode;
  formatValue?: (value: number, max: number) => string;
  formatPercentage?: (percentage: number) => string;
  id?: string;
  'aria-label'?: string;
  'aria-describedby'?: string;
}

export interface ProgressSegment {
  value: number;
  color?: string;
  label?: string;
  className?: string;
}

// Utility functions
const clamp = (value: number, min: number, max: number): number => {
  return Math.min(Math.max(value, min), max);
};

const calculatePercentage = (value: number, min: number, max: number): number => {
  const clampedValue = clamp(value, min, max);
  const range = max - min;
  return range === 0 ? 0 : ((clampedValue - min) / range) * 100;
};

const defaultFormatValue = (value: number, max: number): string => {
  return `${Math.round(value)}/${Math.round(max)}`;
};

const defaultFormatPercentage = (percentage: number): string => {
  return `${Math.round(percentage)}%`;
};

// Segmented Progress Component
interface SegmentedProgressProps {
  segments: ProgressSegment[];
  max: number;
  min: number;
  orientation: 'horizontal' | 'vertical';
  showSegmentLabels: boolean;
  className?: string;
}

const SegmentedProgress: React.FC<SegmentedProgressProps> = ({
  segments,
  max,
  min,
  orientation,
  showSegmentLabels,
  className = ''
}) => {
  const totalValue = segments.reduce((sum, segment) => sum + segment.value, 0);
  const clampedTotal = clamp(totalValue, min, max);
  
  return (
    <div className={`progress-bar__segments ${className}`}>
      {segments.map((segment, index) => {
        const segmentPercentage = calculatePercentage(segment.value, 0, clampedTotal);
        const segmentClasses = [
          'progress-bar__segment',
          segment.className
        ].filter(Boolean).join(' ');
        
        const segmentStyle: React.CSSProperties = {
          [orientation === 'horizontal' ? 'width' : 'height']: `${segmentPercentage}%`,
          backgroundColor: segment.color
        };
        
        return (
          <div key={index} className="progress-bar__segment-container">
            <div
              className={segmentClasses}
              style={segmentStyle}
              title={segment.label}
            />
            {showSegmentLabels && segment.label && (
              <span className="progress-bar__segment-label">
                {segment.label}
              </span>
            )}
          </div>
        );
      })}
    </div>
  );
};

// Indeterminate Progress Component
const IndeterminateProgress: React.FC<{
  orientation: 'horizontal' | 'vertical';
  className?: string;
}> = ({ orientation, className = '' }) => {
  return (
    <div className={`progress-bar__indeterminate ${className}`}>
      <div className="progress-bar__indeterminate-bar" />
    </div>
  );
};

// Main ProgressBar Component
export const ProgressBar = forwardRef<HTMLDivElement, ProgressBarProps>((
  {
    value,
    max = 100,
    min = 0,
    label,
    showValue = false,
    showPercentage = false,
    color = 'default',
    variant = 'default',
    size = 'medium',
    orientation = 'horizontal',
    className = '',
    trackClassName = '',
    fillClassName = '',
    labelClassName = '',
    animated = false,
    striped = false,
    indeterminate = false,
    segments,
    showSegmentLabels = false,
    rounded = true,
    thickness,
    children,
    formatValue = defaultFormatValue,
    formatPercentage = defaultFormatPercentage,
    id,
    'aria-label': ariaLabel,
    'aria-describedby': ariaDescribedBy
  },
  ref
) => {
  const percentage = useMemo(() => {
    if (indeterminate) return 0;
    return calculatePercentage(value, min, max);
  }, [value, min, max, indeterminate]);
  
  const clampedValue = useMemo(() => {
    return clamp(value, min, max);
  }, [value, min, max]);
  
  const baseClasses = 'progress-bar';
  
  const colorClasses = {
    default: 'progress-bar--default',
    primary: 'progress-bar--primary',
    success: 'progress-bar--success',
    warning: 'progress-bar--warning',
    error: 'progress-bar--error',
    info: 'progress-bar--info'
  };
  
  const variantClasses = {
    default: 'progress-bar--default-variant',
    striped: 'progress-bar--striped',
    animated: 'progress-bar--animated',
    gradient: 'progress-bar--gradient'
  };
  
  const sizeClasses = {
    small: 'progress-bar--small',
    medium: 'progress-bar--medium',
    large: 'progress-bar--large'
  };
  
  const orientationClasses = {
    horizontal: 'progress-bar--horizontal',
    vertical: 'progress-bar--vertical'
  };
  
  const containerClasses = [
    baseClasses,
    colorClasses[color as keyof typeof colorClasses] || 'progress-bar--custom',
    variantClasses[variant],
    sizeClasses[size],
    orientationClasses[orientation],
    animated && 'progress-bar--animated',
    striped && 'progress-bar--striped',
    indeterminate && 'progress-bar--indeterminate',
    rounded && 'progress-bar--rounded',
    className
  ].filter(Boolean).join(' ');
  
  const trackClasses = [
    'progress-bar__track',
    trackClassName
  ].filter(Boolean).join(' ');
  
  const fillClasses = [
    'progress-bar__fill',
    fillClassName
  ].filter(Boolean).join(' ');
  
  const labelClasses = [
    'progress-bar__label',
    labelClassName
  ].filter(Boolean).join(' ');
  
  const containerStyle: React.CSSProperties = {
    ...(thickness && {
      [orientation === 'horizontal' ? 'height' : 'width']: `${thickness}px`
    })
  };
  
  const fillStyle: React.CSSProperties = {
    [orientation === 'horizontal' ? 'width' : 'height']: `${percentage}%`,
    ...(typeof color === 'string' && !colorClasses[color as keyof typeof colorClasses] && {
      backgroundColor: color
    })
  };
  
  const renderLabel = () => {
    if (!label && !showValue && !showPercentage && !children) return null;
    
    return (
      <div className={labelClasses}>
        {label && (
          <span className="progress-bar__label-text">{label}</span>
        )}
        
        {showValue && (
          <span className="progress-bar__value">
            {formatValue(clampedValue, max)}
          </span>
        )}
        
        {showPercentage && (
          <span className="progress-bar__percentage">
            {formatPercentage(percentage)}
          </span>
        )}
        
        {children}
      </div>
    );
  };
  
  const renderProgress = () => {
    if (segments && segments.length > 0) {
      return (
        <SegmentedProgress
          segments={segments}
          max={max}
          min={min}
          orientation={orientation}
          showSegmentLabels={showSegmentLabels}
          className="progress-bar__segmented"
        />
      );
    }
    
    if (indeterminate) {
      return (
        <IndeterminateProgress
          orientation={orientation}
          className="progress-bar__indeterminate-container"
        />
      );
    }
    
    return (
      <div className={trackClasses}>
        <div
          className={fillClasses}
          style={fillStyle}
          role="progressbar"
          aria-valuenow={clampedValue}
          aria-valuemin={min}
          aria-valuemax={max}
          aria-label={ariaLabel}
          aria-describedby={ariaDescribedBy}
        />
      </div>
    );
  };
  
  return (
    <div
      ref={ref}
      className={containerClasses}
      style={containerStyle}
      id={id}
    >
      {orientation === 'horizontal' ? (
        <>
          {renderLabel()}
          {renderProgress()}
        </>
      ) : (
        <>
          {renderProgress()}
          {renderLabel()}
        </>
      )}
    </div>
  );
});

ProgressBar.displayName = 'ProgressBar';

// Circular Progress Component
export interface CircularProgressProps {
  value: number;
  max?: number;
  min?: number;
  size?: number;
  strokeWidth?: number;
  color?: string;
  trackColor?: string;
  showValue?: boolean;
  showPercentage?: boolean;
  label?: string;
  className?: string;
  animated?: boolean;
  indeterminate?: boolean;
  formatValue?: (value: number, max: number) => string;
  formatPercentage?: (percentage: number) => string;
  children?: React.ReactNode;
  id?: string;
  'aria-label'?: string;
  'aria-describedby'?: string;
}

export const CircularProgress = forwardRef<SVGSVGElement, CircularProgressProps>((
  {
    value,
    max = 100,
    min = 0,
    size = 120,
    strokeWidth = 8,
    color = 'currentColor',
    trackColor = 'rgba(0, 0, 0, 0.1)',
    showValue = false,
    showPercentage = false,
    label,
    className = '',
    animated = true,
    indeterminate = false,
    formatValue = defaultFormatValue,
    formatPercentage = defaultFormatPercentage,
    children,
    id,
    'aria-label': ariaLabel,
    'aria-describedby': ariaDescribedBy
  },
  ref
) => {
  const percentage = useMemo(() => {
    if (indeterminate) return 25; // Show partial circle for indeterminate
    return calculatePercentage(value, min, max);
  }, [value, min, max, indeterminate]);
  
  const clampedValue = useMemo(() => {
    return clamp(value, min, max);
  }, [value, min, max]);
  
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;
  
  const containerClasses = [
    'circular-progress',
    animated && 'circular-progress--animated',
    indeterminate && 'circular-progress--indeterminate',
    className
  ].filter(Boolean).join(' ');
  
  return (
    <div className={containerClasses} style={{ width: size, height: size }}>
      <svg
        ref={ref}
        width={size}
        height={size}
        viewBox={`0 0 ${size} ${size}`}
        className="circular-progress__svg"
        id={id}
        role="progressbar"
        aria-valuenow={indeterminate ? undefined : clampedValue}
        aria-valuemin={indeterminate ? undefined : min}
        aria-valuemax={indeterminate ? undefined : max}
        aria-label={ariaLabel}
        aria-describedby={ariaDescribedBy}
      >
        {/* Track */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          fill="none"
          stroke={trackColor}
          strokeWidth={strokeWidth}
          className="circular-progress__track"
        />
        
        {/* Progress */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          fill="none"
          stroke={color}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          className="circular-progress__fill"
          transform={`rotate(-90 ${size / 2} ${size / 2})`}
        />
      </svg>
      
      {/* Content */}
      <div className="circular-progress__content">
        {label && (
          <div className="circular-progress__label">{label}</div>
        )}
        
        {showValue && (
          <div className="circular-progress__value">
            {formatValue(clampedValue, max)}
          </div>
        )}
        
        {showPercentage && (
          <div className="circular-progress__percentage">
            {formatPercentage(percentage)}
          </div>
        )}
        
        {children}
      </div>
    </div>
  );
});

CircularProgress.displayName = 'CircularProgress';

// Create compound component with proper typing
type ProgressBarWithSubComponents = typeof ProgressBar & {
  Circular: typeof CircularProgress;
};

const ProgressBarWithSubComponents = ProgressBar as ProgressBarWithSubComponents;
ProgressBarWithSubComponents.Circular = CircularProgress;

export default ProgressBarWithSubComponents;