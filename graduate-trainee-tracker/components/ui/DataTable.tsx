import React, { useState, useMemo, useCallback, useRef, useEffect } from 'react';
import { usePagination } from '../../hooks/usePagination';
import { useSorting } from '../../hooks/useSorting';
import { useFilters } from '../../hooks/useFilters';

export interface Column<T = any> {
  key: string;
  title: string;
  dataIndex?: keyof T;
  render?: (value: any, record: T, index: number) => React.ReactNode;
  width?: number | string;
  minWidth?: number;
  maxWidth?: number;
  sortable?: boolean;
  filterable?: boolean;
  filterType?: 'text' | 'select' | 'date' | 'number' | 'boolean';
  filterOptions?: Array<{ label: string; value: any }>;
  align?: 'left' | 'center' | 'right';
  fixed?: 'left' | 'right';
  ellipsis?: boolean;
  className?: string;
  headerClassName?: string;
}

export interface DataTableProps<T = any> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  pagination?: {
    enabled?: boolean;
    pageSize?: number;
    showSizeChanger?: boolean;
    showQuickJumper?: boolean;
    showTotal?: boolean;
    position?: 'top' | 'bottom' | 'both';
  };
  sorting?: {
    enabled?: boolean;
    defaultSort?: { key: string; direction: 'asc' | 'desc' };
    multiple?: boolean;
  };
  filtering?: {
    enabled?: boolean;
    searchable?: boolean;
    searchPlaceholder?: string;
  };
  selection?: {
    enabled?: boolean;
    type?: 'checkbox' | 'radio';
    selectedRowKeys?: React.Key[];
    onSelectionChange?: (selectedRowKeys: React.Key[], selectedRows: T[]) => void;
    getCheckboxProps?: (record: T) => { disabled?: boolean };
  };
  rowKey?: keyof T | ((record: T) => React.Key);
  size?: 'small' | 'medium' | 'large';
  bordered?: boolean;
  striped?: boolean;
  hoverable?: boolean;
  sticky?: boolean;
  scroll?: { x?: number | string; y?: number | string };
  expandable?: {
    expandedRowRender?: (record: T, index: number) => React.ReactNode;
    expandRowByClick?: boolean;
    defaultExpandAllRows?: boolean;
    expandedRowKeys?: React.Key[];
    onExpand?: (expanded: boolean, record: T) => void;
  };
  onRow?: (record: T, index: number) => {
    onClick?: (event: React.MouseEvent) => void;
    onDoubleClick?: (event: React.MouseEvent) => void;
    onContextMenu?: (event: React.MouseEvent) => void;
    onMouseEnter?: (event: React.MouseEvent) => void;
    onMouseLeave?: (event: React.MouseEvent) => void;
  };
  emptyText?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

export const DataTable = <T extends Record<string, any>>(
  props: DataTableProps<T>
) => {
  const {
    data = [],
    columns = [],
    loading = false,
    pagination = { enabled: true },
    sorting = { enabled: true },
    filtering = { enabled: true },
    selection,
    rowKey = 'id',
    size = 'medium',
    bordered = false,
    striped = false,
    hoverable = true,
    sticky = false,
    scroll,
    expandable,
    onRow,
    emptyText = 'No data available',
    className = '',
    style
  } = props;

  // Refs
  const tableRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLTableSectionElement>(null);

  // Local state
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>(
    selection?.selectedRowKeys || []
  );
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>(
    expandable?.expandedRowKeys || (expandable?.defaultExpandAllRows ? data.map(getRowKey) : [])
  );
  const [columnWidths, setColumnWidths] = useState<Record<string, number>>({});

  // Hooks
  const paginationHook = usePagination({
    initialPageSize: pagination.pageSize || 10
  });

  const sortingHook = useSorting({
    initialSort: sorting.defaultSort ? {
      field: sorting.defaultSort.key,
      direction: sorting.defaultSort.direction
    } : undefined
  });

  const filteringHook = useFilters();

  // Helper functions
  function getRowKey(record: T, index?: number): React.Key {
    if (typeof rowKey === 'function') {
      return rowKey(record);
    }
    return record[rowKey] ?? index ?? 0;
  }

  // Process data through filters, sorting, and pagination
  const processedData = useMemo(() => {
    let result = [...data];

    // Apply filters
    if (filtering.enabled && filteringHook.hasActiveFilters) {
      result = filteringHook.applyFilters(result as any);
    }

    // Apply sorting
    if (sorting.enabled && sortingHook.sortConfig) {
      result = sortingHook.sortData(result);
    }

    // Update pagination total
    paginationHook.setTotal(result.length);

    // Apply pagination
    if (pagination.enabled) {
      result = paginationHook.paginateData(result);
    }

    return result;
  }, [data, filtering.enabled, filteringHook.hasActiveFilters, filteringHook.applyFilters, sorting.enabled, sortingHook.sortConfig, sortingHook.sortData, pagination.enabled, paginationHook.paginateData, paginationHook.setTotal]);

  // Selection handlers
  const handleSelectAll = useCallback((checked: boolean) => {
    const newSelectedKeys = checked ? processedData.map(getRowKey) : [];
    setSelectedRowKeys(newSelectedKeys);
    selection?.onSelectionChange?.(newSelectedKeys, checked ? processedData : []);
  }, [processedData, selection]);

  const handleSelectRow = useCallback((record: T, checked: boolean) => {
    const key = getRowKey(record);
    const newSelectedKeys = checked
      ? [...selectedRowKeys, key]
      : selectedRowKeys.filter(k => k !== key);
    
    setSelectedRowKeys(newSelectedKeys);
    const selectedRows = data.filter(item => newSelectedKeys.includes(getRowKey(item)));
    selection?.onSelectionChange?.(newSelectedKeys, selectedRows);
  }, [selectedRowKeys, data, selection]);

  // Expansion handlers
  const handleExpand = useCallback((record: T, expanded: boolean) => {
    const key = getRowKey(record);
    const newExpandedKeys = expanded
      ? [...expandedRowKeys, key]
      : expandedRowKeys.filter(k => k !== key);
    
    setExpandedRowKeys(newExpandedKeys);
    expandable?.onExpand?.(expanded, record);
  }, [expandedRowKeys, expandable]);

  // Column sorting handler
  const handleSort = useCallback((columnKey: string) => {
    if (sorting.enabled) {
      sortingHook.toggleSort(columnKey as keyof T);
    }
  }, [sorting.enabled, sortingHook]);

  // Column filtering handler
  const handleFilter = useCallback((columnKey: string, value: any) => {
    if (filtering.enabled) {
      // For now, we'll use a simple approach since the useFilters hook
      // doesn't have a generic setFilter method
      console.log('Filter applied:', columnKey, value);
    }
  }, [filtering.enabled]);

  // Render selection column
  const renderSelectionColumn = () => {
    if (!selection?.enabled) return null;

    const isAllSelected = processedData.length > 0 && 
      processedData.every(record => selectedRowKeys.includes(getRowKey(record)));
    const isIndeterminate = selectedRowKeys.length > 0 && !isAllSelected;

    return (
      <th className="data-table__header-cell data-table__header-cell--selection">
        {selection.type !== 'radio' && (
          <input
            type="checkbox"
            checked={isAllSelected}
            ref={input => {
              if (input) input.indeterminate = isIndeterminate;
            }}
            onChange={(e) => handleSelectAll(e.target.checked)}
            className="data-table__checkbox"
          />
        )}
      </th>
    );
  };

  // Render expansion column
  const renderExpansionColumn = () => {
    if (!expandable?.expandedRowRender) return null;

    return (
      <th className="data-table__header-cell data-table__header-cell--expand">
        {/* Empty header for expand column */}
      </th>
    );
  };

  // Render table header
  const renderHeader = () => (
    <thead ref={headerRef} className="data-table__header">
      <tr className="data-table__header-row">
        {renderSelectionColumn()}
        {renderExpansionColumn()}
        {columns.map((column) => {
          const isSorted = sortingHook.isSortedBy(column.key as keyof T);
          const sortDirection = sortingHook.getSortDirection(column.key as keyof T);

          return (
            <th
              key={column.key}
              className={`
                data-table__header-cell
                ${column.headerClassName || ''}
                ${column.sortable && sorting.enabled ? 'data-table__header-cell--sortable' : ''}
                ${isSorted ? `data-table__header-cell--sorted data-table__header-cell--sorted-${sortDirection}` : ''}
                ${column.align ? `data-table__header-cell--${column.align}` : ''}
              `.trim()}
              style={{
                width: column.width,
                minWidth: column.minWidth,
                maxWidth: column.maxWidth
              }}
              onClick={() => column.sortable && handleSort(column.key)}
            >
              <div className="data-table__header-content">
                <span className="data-table__header-title">{column.title}</span>
                {column.sortable && sorting.enabled && (
                  <span className="data-table__sort-indicator">
                    {!isSorted && '↕'}
                    {sortDirection === 'asc' && '↑'}
                    {sortDirection === 'desc' && '↓'}
                  </span>
                )}
              </div>
              {column.filterable && filtering.enabled && (
                <div className="data-table__filter">
                  {column.filterType === 'select' ? (
                    <select
                      onChange={(e) => handleFilter(column.key, e.target.value)}
                      className="data-table__filter-select"
                    >
                      <option value="">All</option>
                      {column.filterOptions?.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  ) : (
                    <input
                      type={column.filterType || 'text'}
                      placeholder={`Filter ${column.title}`}
                      onChange={(e) => handleFilter(column.key, e.target.value)}
                      className="data-table__filter-input"
                    />
                  )}
                </div>
              )}
            </th>
          );
        })}
      </tr>
    </thead>
  );

  // Render table body
  const renderBody = () => {
    if (loading) {
      return (
        <tbody className="data-table__body">
          <tr className="data-table__loading-row">
            <td 
              colSpan={columns.length + (selection?.enabled ? 1 : 0) + (expandable?.expandedRowRender ? 1 : 0)}
              className="data-table__loading-cell"
            >
              <div className="data-table__loading">
                <div className="data-table__spinner"></div>
                <span>Loading...</span>
              </div>
            </td>
          </tr>
        </tbody>
      );
    }

    if (processedData.length === 0) {
      return (
        <tbody className="data-table__body">
          <tr className="data-table__empty-row">
            <td 
              colSpan={columns.length + (selection?.enabled ? 1 : 0) + (expandable?.expandedRowRender ? 1 : 0)}
              className="data-table__empty-cell"
            >
              <div className="data-table__empty">
                {emptyText}
              </div>
            </td>
          </tr>
        </tbody>
      );
    }

    return (
      <tbody className="data-table__body">
        {processedData.map((record, index) => {
          const key = getRowKey(record, index);
          const isSelected = selectedRowKeys.includes(key);
          const isExpanded = expandedRowKeys.includes(key);
          const rowProps = onRow?.(record, index) || {};

          return (
            <React.Fragment key={key}>
              <tr
                className={`
                  data-table__row
                  ${isSelected ? 'data-table__row--selected' : ''}
                  ${hoverable ? 'data-table__row--hoverable' : ''}
                `.trim()}
                {...rowProps}
                onClick={(e) => {
                  if (expandable?.expandRowByClick) {
                    handleExpand(record, !isExpanded);
                  }
                  rowProps.onClick?.(e);
                }}
              >
                {selection?.enabled && (
                  <td className="data-table__cell data-table__cell--selection">
                    <input
                      type={selection.type || 'checkbox'}
                      checked={isSelected}
                      onChange={(e) => handleSelectRow(record, e.target.checked)}
                      disabled={selection.getCheckboxProps?.(record)?.disabled}
                      className="data-table__checkbox"
                    />
                  </td>
                )}
                {expandable?.expandedRowRender && (
                  <td className="data-table__cell data-table__cell--expand">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleExpand(record, !isExpanded);
                      }}
                      className="data-table__expand-button"
                    >
                      {isExpanded ? '−' : '+'}
                    </button>
                  </td>
                )}
                {columns.map((column) => {
                  const value = column.dataIndex ? record[column.dataIndex] : record;
                  const cellContent = column.render ? column.render(value, record, index) : String(value || '');

                  return (
                    <td
                      key={column.key}
                      className={`
                        data-table__cell
                        ${column.className || ''}
                        ${column.align ? `data-table__cell--${column.align}` : ''}
                        ${column.ellipsis ? 'data-table__cell--ellipsis' : ''}
                      `.trim()}
                      style={{
                        width: column.width,
                        minWidth: column.minWidth,
                        maxWidth: column.maxWidth
                      }}
                    >
                      {cellContent}
                    </td>
                  );
                })}
              </tr>
              {isExpanded && expandable?.expandedRowRender && (
                <tr className="data-table__expanded-row">
                  <td 
                    colSpan={columns.length + (selection?.enabled ? 1 : 0) + 1}
                    className="data-table__expanded-cell"
                  >
                    {expandable.expandedRowRender(record, index)}
                  </td>
                </tr>
              )}
            </React.Fragment>
          );
        })}
      </tbody>
    );
  };

  // Render pagination
  const renderPagination = () => {
    if (!pagination.enabled || paginationHook.totalPages <= 1) return null;

    return (
      <div className="data-table__pagination">
        <div className="data-table__pagination-info">
          {pagination.showTotal && (
            <span className="data-table__pagination-total">
              Total {paginationHook.pagination.total} items
            </span>
          )}
        </div>
        <div className="data-table__pagination-controls">
          <button
            onClick={paginationHook.goToFirstPage}
            disabled={paginationHook.pagination.page === 1}
            className="data-table__pagination-button"
          >
            First
          </button>
          <button
            onClick={paginationHook.previousPage}
            disabled={!paginationHook.hasPreviousPage}
            className="data-table__pagination-button"
          >
            Previous
          </button>
          
          {pagination.showQuickJumper && (
            <div className="data-table__pagination-jumper">
              <span>Go to</span>
              <input
                type="number"
                min={1}
                max={paginationHook.totalPages}
                value={paginationHook.pagination.page}
                onChange={(e) => paginationHook.setPage(Number(e.target.value))}
                className="data-table__pagination-input"
              />
            </div>
          )}
          
          <span className="data-table__pagination-info">
            Page {paginationHook.pagination.page} of {paginationHook.totalPages}
          </span>
          
          <button
            onClick={paginationHook.nextPage}
            disabled={!paginationHook.hasNextPage}
            className="data-table__pagination-button"
          >
            Next
          </button>
          <button
            onClick={paginationHook.goToLastPage}
            disabled={paginationHook.pagination.page === paginationHook.totalPages}
            className="data-table__pagination-button"
          >
            Last
          </button>
        </div>
        
        {pagination.showSizeChanger && (
          <div className="data-table__pagination-size">
            <select
              value={paginationHook.pagination.pageSize}
              onChange={(e) => paginationHook.setPageSize(Number(e.target.value))}
              className="data-table__pagination-select"
            >
              <option value={10}>10 / page</option>
              <option value={20}>20 / page</option>
              <option value={50}>50 / page</option>
              <option value={100}>100 / page</option>
            </select>
          </div>
        )}
      </div>
    );
  };

  // Update selected keys when prop changes
  useEffect(() => {
    if (selection?.selectedRowKeys) {
      setSelectedRowKeys(selection.selectedRowKeys);
    }
  }, [selection?.selectedRowKeys]);

  // Update expanded keys when prop changes
  useEffect(() => {
    if (expandable?.expandedRowKeys) {
      setExpandedRowKeys(expandable.expandedRowKeys);
    }
  }, [expandable?.expandedRowKeys]);

  return (
    <div 
      ref={tableRef}
      className={`
        data-table
        data-table--${size}
        ${bordered ? 'data-table--bordered' : ''}
        ${striped ? 'data-table--striped' : ''}
        ${sticky ? 'data-table--sticky' : ''}
        ${loading ? 'data-table--loading' : ''}
        ${className}
      `.trim()}
      style={style}
    >
      {pagination.position === 'top' || pagination.position === 'both' ? renderPagination() : null}
      
      <div 
        className="data-table__container"
        style={{
          overflowX: scroll?.x ? 'auto' : undefined,
          overflowY: scroll?.y ? 'auto' : undefined,
          maxHeight: scroll?.y
        }}
      >
        <table className="data-table__table">
          {renderHeader()}
          {renderBody()}
        </table>
      </div>
      
      {(!pagination.position || pagination.position === 'bottom' || pagination.position === 'both') ? renderPagination() : null}
    </div>
  );
};

export default DataTable;