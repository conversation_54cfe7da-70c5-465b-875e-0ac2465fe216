import React, { forwardRef, useState, useMemo } from 'react';

// Types
export interface AvatarProps {
  src?: string;
  alt?: string;
  name?: string;
  size?: 'xs' | 'small' | 'medium' | 'large' | 'xl' | '2xl' | number;
  shape?: 'circle' | 'square' | 'rounded';
  variant?: 'default' | 'bordered' | 'shadow' | 'ring';
  color?: string;
  backgroundColor?: string;
  className?: string;
  fallback?: React.ReactNode;
  showFallback?: boolean;
  loading?: 'lazy' | 'eager';
  crossOrigin?: 'anonymous' | 'use-credentials';
  onClick?: () => void;
  onError?: () => void;
  onLoad?: () => void;
  draggable?: boolean;
  status?: 'online' | 'offline' | 'away' | 'busy' | 'idle';
  statusPosition?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  badge?: React.ReactNode;
  badgePosition?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  id?: string;
  'aria-label'?: string;
  'aria-describedby'?: string;
  role?: string;
}

export interface AvatarGroupProps {
  children: React.ReactNode;
  max?: number;
  size?: AvatarProps['size'];
  shape?: AvatarProps['shape'];
  variant?: AvatarProps['variant'];
  className?: string;
  spacing?: 'tight' | 'normal' | 'loose' | number;
  showMore?: boolean;
  moreText?: string;
  onMoreClick?: () => void;
  direction?: 'left' | 'right';
}

// Utility functions
const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

const generateColorFromName = (name: string): string => {
  const colors = [
    '#f87171', '#fb923c', '#fbbf24', '#a3e635', '#34d399',
    '#22d3ee', '#60a5fa', '#a78bfa', '#f472b6', '#fb7185'
  ];
  
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  return colors[Math.abs(hash) % colors.length];
};

const getSizeValue = (size: AvatarProps['size']): number => {
  if (typeof size === 'number') return size;
  
  const sizes = {
    xs: 24,
    small: 32,
    medium: 40,
    large: 48,
    xl: 56,
    '2xl': 64
  };
  
  return sizes[size || 'medium'];
};

const getStatusColor = (status: AvatarProps['status']): string => {
  const colors = {
    online: '#10b981',
    offline: '#6b7280',
    away: '#f59e0b',
    busy: '#ef4444',
    idle: '#8b5cf6'
  };
  return colors[status!];
};

// Status Indicator Component
interface StatusIndicatorProps {
  status: AvatarProps['status'];
  position: AvatarProps['statusPosition'];
  size: number;
}

const StatusIndicator: React.FC<StatusIndicatorProps> = ({ status, position, size }) => {
  if (!status) return null;
  
  const indicatorSize = Math.max(8, size * 0.25);
  const statusColor = getStatusColor(status);
  
  const positionClasses = {
    'top-right': 'avatar__status--top-right',
    'top-left': 'avatar__status--top-left',
    'bottom-right': 'avatar__status--bottom-right',
    'bottom-left': 'avatar__status--bottom-left'
  };
  
  return (
    <div
      className={`avatar__status ${positionClasses[position || 'bottom-right']}`}
      style={{
        width: indicatorSize,
        height: indicatorSize,
        backgroundColor: statusColor
      }}
      title={status}
      aria-label={`Status: ${status}`}
    />
  );
};

// Badge Component
interface BadgeIndicatorProps {
  badge: React.ReactNode;
  position: AvatarProps['badgePosition'];
}

const BadgeIndicator: React.FC<BadgeIndicatorProps> = ({ badge, position }) => {
  if (!badge) return null;
  
  const positionClasses = {
    'top-right': 'avatar__badge--top-right',
    'top-left': 'avatar__badge--top-left',
    'bottom-right': 'avatar__badge--bottom-right',
    'bottom-left': 'avatar__badge--bottom-left'
  };
  
  return (
    <div className={`avatar__badge ${positionClasses[position || 'top-right']}`}>
      {badge}
    </div>
  );
};

// Fallback Component
interface FallbackProps {
  name?: string;
  fallback?: React.ReactNode;
  size: number;
  color?: string;
  backgroundColor?: string;
}

const Fallback: React.FC<FallbackProps> = ({ name, fallback, size, color, backgroundColor }) => {
  const initials = name ? getInitials(name) : '?';
  const bgColor = backgroundColor || (name ? generateColorFromName(name) : '#6b7280');
  const textColor = color || '#ffffff';
  const fontSize = Math.max(12, size * 0.4);
  
  return (
    <div
      className="avatar__fallback"
      style={{
        backgroundColor: bgColor,
        color: textColor,
        fontSize: `${fontSize}px`,
        width: '100%',
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontWeight: 'bold'
      }}
    >
      {fallback || initials}
    </div>
  );
};

// Main Avatar Component
export const Avatar = forwardRef<HTMLDivElement, AvatarProps>((
  {
    src,
    alt,
    name,
    size = 'medium',
    shape = 'circle',
    variant = 'default',
    color,
    backgroundColor,
    className = '',
    fallback,
    showFallback = true,
    loading = 'lazy',
    crossOrigin,
    onClick,
    onError,
    onLoad,
    draggable = false,
    status,
    statusPosition = 'bottom-right',
    badge,
    badgePosition = 'top-right',
    id,
    'aria-label': ariaLabel,
    'aria-describedby': ariaDescribedBy,
    role
  },
  ref
) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  
  const sizeValue = useMemo(() => getSizeValue(size), [size]);
  
  const baseClasses = 'avatar';
  
  const shapeClasses = {
    circle: 'avatar--circle',
    square: 'avatar--square',
    rounded: 'avatar--rounded'
  };
  
  const variantClasses = {
    default: 'avatar--default',
    bordered: 'avatar--bordered',
    shadow: 'avatar--shadow',
    ring: 'avatar--ring'
  };
  
  const sizeClasses = typeof size === 'string' ? {
    xs: 'avatar--xs',
    small: 'avatar--small',
    medium: 'avatar--medium',
    large: 'avatar--large',
    xl: 'avatar--xl',
    '2xl': 'avatar--2xl'
  } : {};
  
  const containerClasses = [
    baseClasses,
    shapeClasses[shape],
    variantClasses[variant],
    typeof size === 'string' ? sizeClasses[size as keyof typeof sizeClasses] : 'avatar--custom',
    onClick && 'avatar--clickable',
    className
  ].filter(Boolean).join(' ');
  
  const containerStyle: React.CSSProperties = {
    width: sizeValue,
    height: sizeValue,
    ...(typeof size === 'number' && {
      minWidth: sizeValue,
      minHeight: sizeValue
    })
  };
  
  const handleImageError = () => {
    setImageError(true);
    onError?.();
  };
  
  const handleImageLoad = () => {
    setImageLoaded(true);
    onLoad?.();
  };
  
  const handleClick = () => {
    if (onClick) {
      onClick();
    }
  };
  
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (onClick && (event.key === 'Enter' || event.key === ' ')) {
      event.preventDefault();
      handleClick();
    }
  };
  
  const shouldShowImage = src && !imageError;
  const shouldShowFallback = showFallback && (!src || imageError);
  
  return (
    <div
      ref={ref}
      className={containerClasses}
      style={containerStyle}
      onClick={onClick ? handleClick : undefined}
      onKeyDown={onClick ? handleKeyDown : undefined}
      tabIndex={onClick ? 0 : undefined}
      role={role || (onClick ? 'button' : 'img')}
      aria-label={ariaLabel || alt || (name ? `Avatar for ${name}` : 'Avatar')}
      aria-describedby={ariaDescribedBy}
      id={id}
    >
      <div className="avatar__content">
        {shouldShowImage && (
          <img
            src={src}
            alt={alt || (name ? `Avatar for ${name}` : 'Avatar')}
            className="avatar__image"
            loading={loading}
            crossOrigin={crossOrigin}
            onError={handleImageError}
            onLoad={handleImageLoad}
            draggable={draggable}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover'
            }}
          />
        )}
        
        {shouldShowFallback && (
          <Fallback
            name={name}
            fallback={fallback}
            size={sizeValue}
            color={color}
            backgroundColor={backgroundColor}
          />
        )}
      </div>
      
      <StatusIndicator
        status={status}
        position={statusPosition}
        size={sizeValue}
      />
      
      <BadgeIndicator
        badge={badge}
        position={badgePosition}
      />
    </div>
  );
});

Avatar.displayName = 'Avatar';

// Avatar Group Component
export const AvatarGroup = forwardRef<HTMLDivElement, AvatarGroupProps>((
  {
    children,
    max = 5,
    size = 'medium',
    shape = 'circle',
    variant = 'default',
    className = '',
    spacing = 'normal',
    showMore = true,
    moreText,
    onMoreClick,
    direction = 'right'
  },
  ref
) => {
  const childrenArray = React.Children.toArray(children);
  const visibleChildren = childrenArray.slice(0, max);
  const hiddenCount = Math.max(0, childrenArray.length - max);
  
  const spacingClasses = {
    tight: 'avatar-group--tight',
    normal: 'avatar-group--normal',
    loose: 'avatar-group--loose'
  };
  
  const directionClasses = {
    left: 'avatar-group--left',
    right: 'avatar-group--right'
  };
  
  const containerClasses = [
    'avatar-group',
    typeof spacing === 'string' ? spacingClasses[spacing] : 'avatar-group--custom',
    directionClasses[direction],
    className
  ].filter(Boolean).join(' ');
  
  const containerStyle: React.CSSProperties = {
    ...(typeof spacing === 'number' && {
      gap: `${spacing}px`
    })
  };
  
  const renderMoreAvatar = () => {
    if (!showMore || hiddenCount <= 0) return null;
    
    const moreContent = moreText || `+${hiddenCount}`;
    
    return (
      <Avatar
        size={size}
        shape={shape}
        variant={variant}
        fallback={moreContent}
        onClick={onMoreClick}
        className="avatar-group__more"
        backgroundColor="#6b7280"
        color="#ffffff"
        aria-label={`${hiddenCount} more avatars`}
      />
    );
  };
  
  return (
    <div
      ref={ref}
      className={containerClasses}
      style={containerStyle}
      role="group"
      aria-label="Avatar group"
    >
      {visibleChildren.map((child, index) => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child as React.ReactElement<AvatarProps>, {
            key: index,
            size: child.props.size || size,
            shape: child.props.shape || shape,
            variant: child.props.variant || variant,
            className: `avatar-group__item ${child.props.className || ''}`.trim()
          });
        }
        return child;
      })}
      
      {renderMoreAvatar()}
    </div>
  );
});

AvatarGroup.displayName = 'AvatarGroup';

// Initials Avatar Component
export interface InitialsAvatarProps extends Omit<AvatarProps, 'src'> {
  name: string;
}

export const InitialsAvatar = forwardRef<HTMLDivElement, InitialsAvatarProps>((
  props,
  ref
) => {
  return <Avatar ref={ref} {...props} src={undefined} showFallback={true} />;
});

InitialsAvatar.displayName = 'InitialsAvatar';

// Icon Avatar Component
export interface IconAvatarProps extends Omit<AvatarProps, 'src' | 'name'> {
  icon: React.ReactNode;
}

export const IconAvatar = forwardRef<HTMLDivElement, IconAvatarProps>((
  { icon, ...props },
  ref
) => {
  return (
    <Avatar
      ref={ref}
      {...props}
      src={undefined}
      name={undefined}
      fallback={icon}
      showFallback={true}
    />
  );
});

IconAvatar.displayName = 'IconAvatar';

// Create compound component with proper typing
type AvatarWithSubComponents = typeof Avatar & {
  Group: typeof AvatarGroup;
  Initials: typeof InitialsAvatar;
  Icon: typeof IconAvatar;
};

const AvatarWithSubComponents = Avatar as AvatarWithSubComponents;
AvatarWithSubComponents.Group = AvatarGroup;
AvatarWithSubComponents.Initials = InitialsAvatar;
AvatarWithSubComponents.Icon = IconAvatar;

export default AvatarWithSubComponents;