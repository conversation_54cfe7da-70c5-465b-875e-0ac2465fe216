import * as React from "react"
import { cn } from "@/lib/utils"
import {
  Users,
  GraduationCap,
  ClipboardCheck,
  FileCheck,
  Bell,
  BarChart3,
  Settings,
  LogOut,
  ChevronLeft,
  Menu
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { She<PERSON>, She<PERSON><PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { ScrollArea } from "@/components/ui/scroll-area"

interface MainLayoutProps {
  children: React.ReactNode
  currentUser: any
  activeView: string
  onViewChange: (view: string) => void
  notifications?: number
}

const sidebarNavItems = [
  {
    id: "dashboard",
    title: "Dashboard",
    icon: BarChart3,
    href: "#dashboard",
  },
  {
    id: "trainees",
    title: "Trainees",
    icon: Users,
    href: "#trainees",
  },
  {
    id: "programs",
    title: "Programs",
    icon: GraduationCap,
    href: "#programs",
  },
  {
    id: "reviews",
    title: "Reviews",
    icon: ClipboardCheck,
    href: "#reviews",
  },
  {
    id: "approvals",
    title: "Approvals",
    icon: FileCheck,
    href: "#approvals",
  },
]

export function MainLayout({
  children,
  currentUser,
  activeView,
  onViewChange,
  notifications = 0
}: MainLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = React.useState(false)

  return (
    <div className="relative flex min-h-screen">
      {/* Desktop Sidebar */}
      <aside
        className={cn(
          "hidden lg:flex lg:flex-col transition-all duration-300 border-r bg-background",
          sidebarCollapsed ? "lg:w-16" : "lg:w-64"
        )}
      >
        <div className="flex h-14 items-center border-b px-4">
          {!sidebarCollapsed && (
            <div className="flex items-center gap-2 font-semibold">
              <GraduationCap className="h-5 w-5" />
              <span>GT Tracker</span>
            </div>
          )}
          <Button
            variant="ghost"
            size="icon"
            className="ml-auto h-8 w-8"
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
          >
            <ChevronLeft className={cn(
              "h-4 w-4 transition-transform",
              sidebarCollapsed && "rotate-180"
            )} />
            <span className="sr-only">Toggle sidebar</span>
          </Button>
        </div>
        
        <ScrollArea className="flex-1 px-3">
          <div className="space-y-1 py-2">
            {sidebarNavItems.map((item) => (
              <Button
                key={item.id}
                variant={activeView === item.id ? "secondary" : "ghost"}
                className={cn(
                  "w-full justify-start",
                  sidebarCollapsed && "justify-center px-2"
                )}
                onClick={() => onViewChange(item.id)}
              >
                <item.icon className={cn(
                  "h-4 w-4",
                  !sidebarCollapsed && "mr-2"
                )} />
                {!sidebarCollapsed && <span>{item.title}</span>}
              </Button>
            ))}
          </div>
        </ScrollArea>

        <Separator />
        
        <div className="p-4">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className={cn(
                  "w-full justify-start",
                  sidebarCollapsed && "justify-center px-2"
                )}
              >
                <Avatar className="h-8 w-8">
                  <AvatarFallback>
                    {currentUser?.firstName?.[0]}{currentUser?.lastName?.[0]}
                  </AvatarFallback>
                </Avatar>
                {!sidebarCollapsed && (
                  <div className="ml-2 flex flex-col items-start">
                    <span className="text-sm font-medium">
                      {currentUser?.firstName} {currentUser?.lastName}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {currentUser?.email}
                    </span>
                  </div>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Settings className="mr-2 h-4 w-4" />
                Settings
              </DropdownMenuItem>
              <DropdownMenuItem>
                <LogOut className="mr-2 h-4 w-4" />
                Log out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </aside>

      {/* Mobile Sidebar */}
      <Sheet>
        <SheetTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="lg:hidden fixed left-4 top-4 z-40"
          >
            <Menu className="h-5 w-5" />
            <span className="sr-only">Toggle navigation menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-64 p-0">
          <div className="flex h-14 items-center border-b px-4">
            <div className="flex items-center gap-2 font-semibold">
              <GraduationCap className="h-5 w-5" />
              <span>GT Tracker</span>
            </div>
          </div>
          
          <ScrollArea className="flex-1 px-3">
            <div className="space-y-1 py-2">
              {sidebarNavItems.map((item) => (
                <Button
                  key={item.id}
                  variant={activeView === item.id ? "secondary" : "ghost"}
                  className="w-full justify-start"
                  onClick={() => onViewChange(item.id)}
                >
                  <item.icon className="mr-2 h-4 w-4" />
                  <span>{item.title}</span>
                </Button>
              ))}
            </div>
          </ScrollArea>
        </SheetContent>
      </Sheet>

      {/* Main Content */}
      <main className="flex-1">
        <header className="sticky top-0 z-30 flex h-14 items-center gap-4 border-b bg-background px-6">
          <div className="ml-auto flex items-center gap-4">
            <Button variant="ghost" size="icon" className="relative">
              <Bell className="h-5 w-5" />
              {notifications > 0 && (
                <Badge 
                  variant="destructive" 
                  className="absolute -right-1 -top-1 h-5 w-5 p-0 flex items-center justify-center"
                >
                  {notifications}
                </Badge>
              )}
              <span className="sr-only">Notifications</span>
            </Button>
          </div>
        </header>
        
        <div className="container py-6">
          {children}
        </div>
      </main>
    </div>
  )
}