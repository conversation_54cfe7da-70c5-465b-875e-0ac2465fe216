import { useState, useCallback, useRef, useEffect } from 'react';
import { z } from 'zod';
import {
  FormState,
  createFormState,
  updateFormField,
  submitForm,
  submitFormSuccess,
  submitFormError,
  validateForm,
  getFieldError,
  hasFieldError
} from './FormValidation';

export interface UseFormOptions<T> {
  initialData?: Partial<T>;
  validationSchema: z.ZodSchema<T>;
  onSubmit?: (data: T) => Promise<void> | void;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  resetOnSubmit?: boolean;
}

export interface UseFormReturn<T> {
  // Form state
  data: Partial<T>;
  errors?: z.ZodError;
  isValid: boolean;
  isDirty: boolean;
  isSubmitting: boolean;
  
  // Field methods
  setValue: (field: keyof T, value: any) => void;
  setValues: (values: Partial<T>) => void;
  getValue: (field: keyof T) => any;
  
  // Validation methods
  validateField: (field: keyof T) => boolean;
  validateForm: () => boolean;
  getFieldError: (field: keyof T) => string | undefined;
  hasFieldError: (field: keyof T) => boolean;
  clearFieldError: (field: keyof T) => void;
  clearErrors: () => void;
  
  // Form methods
  handleSubmit: (e?: React.FormEvent) => Promise<void>;
  reset: (newData?: Partial<T>) => void;
  
  // Field props generators
  getFieldProps: (field: keyof T) => {
    name: string;
    value: any;
    onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
    onBlur: (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
    error?: string;
    hasError: boolean;
  };
  
  getCheckboxProps: (field: keyof T) => {
    name: string;
    checked: boolean;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onBlur: (e: React.FocusEvent<HTMLInputElement>) => void;
    error?: string;
    hasError: boolean;
  };
  
  getSelectProps: (field: keyof T) => {
    name: string;
    value: any;
    onChange: (value: any) => void;
    onBlur: () => void;
    error?: string;
    hasError: boolean;
  };
}

export const useForm = <T extends Record<string, any>>(
  options: UseFormOptions<T>
): UseFormReturn<T> => {
  const {
    initialData = {},
    validationSchema,
    onSubmit,
    validateOnChange = true,
    validateOnBlur = true,
    resetOnSubmit = false
  } = options;
  
  const [state, setState] = useState<FormState<T>>(() => 
    createFormState(initialData)
  );
  
  const touchedFields = useRef<Set<keyof T>>(new Set());
  const isInitialMount = useRef(true);
  
  // Initialize form validation on mount
  useEffect(() => {
    if (isInitialMount.current) {
      const validation = validateForm(validationSchema, state.data);
      setState(prev => ({
        ...prev,
        isValid: validation.success,
        errors: validation.errors
      }));
      isInitialMount.current = false;
    }
  }, [validationSchema]);
  
  const setValue = useCallback((field: keyof T, value: any) => {
    setState(prev => {
      const newState = updateFormField(
        prev,
        field as string,
        value,
        validationSchema
      );
      
      if (validateOnChange || touchedFields.current.has(field)) {
        return newState;
      }
      
      // Don't show validation errors until field is touched
      return {
        ...newState,
        errors: prev.errors
      };
    });
  }, [validationSchema, validateOnChange]);
  
  const setValues = useCallback((values: Partial<T>) => {
    setState(prev => {
      const newData = { ...prev.data, ...values };
      const validation = validateForm(validationSchema, newData);
      
      return {
        ...prev,
        data: newData,
        errors: validation.errors,
        isValid: validation.success,
        isDirty: true
      };
    });
  }, [validationSchema]);
  
  const getValue = useCallback((field: keyof T) => {
    return state.data[field];
  }, [state.data]);
  
  const validateField = useCallback((field: keyof T) => {
    touchedFields.current.add(field);
    
    // Validate the entire form and check if this field has errors
    const validation = validateForm(validationSchema, state.data);
    const fieldError = getFieldError(validation.errors, field as string);
    
    return !fieldError;
  }, [state.data, validationSchema]);
  
  const validateFormMethod = useCallback(() => {
    const validation = validateForm(validationSchema, state.data);
    
    setState(prev => ({
      ...prev,
      errors: validation.errors,
      isValid: validation.success
    }));
    
    return validation.success;
  }, [state.data, validationSchema]);
  
  const getFieldErrorMethod = useCallback((field: keyof T) => {
    return getFieldError(state.errors, field as string);
  }, [state.errors]);
  
  const hasFieldErrorMethod = useCallback((field: keyof T) => {
    return hasFieldError(state.errors, field as string);
  }, [state.errors]);
  
  const clearFieldError = useCallback((field: keyof T) => {
    if (!state.errors) return;
    
    const newErrors = {
      ...state.errors,
      errors: state.errors.errors.filter(error => 
        error.path.join('.') !== field
      )
    };
    
    setState(prev => ({
      ...prev,
      errors: newErrors.errors.length > 0 ? newErrors as z.ZodError : undefined,
      isValid: newErrors.errors.length === 0
    }));
  }, [state.errors]);
  
  const clearErrors = useCallback(() => {
    setState(prev => ({
      ...prev,
      errors: undefined,
      isValid: true
    }));
  }, []);
  
  const handleSubmit = useCallback(async (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }
    
    // Mark all fields as touched
    Object.keys(state.data).forEach(field => {
      touchedFields.current.add(field as keyof T);
    });
    
    const validation = validateForm(validationSchema, state.data);
    
    if (!validation.success) {
      setState(prev => ({
        ...prev,
        errors: validation.errors,
        isValid: false
      }));
      return;
    }
    
    if (onSubmit) {
      setState(prev => submitForm(prev));
      
      try {
        await onSubmit(validation.data!);
        setState(prev => submitFormSuccess(prev));
        
        if (resetOnSubmit) {
          reset();
        }
      } catch (error) {
        const zodError = error instanceof z.ZodError 
          ? error 
          : new z.ZodError([{
              code: 'custom',
              message: error instanceof Error ? error.message : 'Submission failed',
              path: []
            }]);
            
        setState(prev => submitFormError(prev, zodError));
      }
    }
  }, [state.data, validationSchema, onSubmit, resetOnSubmit]);
  
  const reset = useCallback((newData?: Partial<T>) => {
    const resetData = newData || initialData;
    setState(createFormState(resetData));
    touchedFields.current.clear();
  }, [initialData]);
  
  const getFieldProps = useCallback((field: keyof T) => {
    const fieldName = field as string;
    const value = state.data[field] || '';
    const error = getFieldError(state.errors, fieldName);
    const hasError = touchedFields.current.has(field) && !!error;
    
    return {
      name: fieldName,
      value,
      onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        setValue(field, e.target.value);
      },
      onBlur: (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        touchedFields.current.add(field);
        if (validateOnBlur) {
          validateField(field);
        }
      },
      error: hasError ? error : undefined,
      hasError
    };
  }, [state.data, state.errors, setValue, validateField, validateOnBlur]);
  
  const getCheckboxProps = useCallback((field: keyof T) => {
    const fieldName = field as string;
    const checked = Boolean(state.data[field]);
    const error = getFieldError(state.errors, fieldName);
    const hasError = touchedFields.current.has(field) && !!error;
    
    return {
      name: fieldName,
      checked,
      onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
        setValue(field, e.target.checked);
      },
      onBlur: (e: React.FocusEvent<HTMLInputElement>) => {
        touchedFields.current.add(field);
        if (validateOnBlur) {
          validateField(field);
        }
      },
      error: hasError ? error : undefined,
      hasError
    };
  }, [state.data, state.errors, setValue, validateField, validateOnBlur]);
  
  const getSelectProps = useCallback((field: keyof T) => {
    const fieldName = field as string;
    const value = state.data[field];
    const error = getFieldError(state.errors, fieldName);
    const hasError = touchedFields.current.has(field) && !!error;
    
    return {
      name: fieldName,
      value,
      onChange: (value: any) => {
        setValue(field, value);
      },
      onBlur: () => {
        touchedFields.current.add(field);
        if (validateOnBlur) {
          validateField(field);
        }
      },
      error: hasError ? error : undefined,
      hasError
    };
  }, [state.data, state.errors, setValue, validateField, validateOnBlur]);
  
  return {
    // State
    data: state.data,
    errors: state.errors,
    isValid: state.isValid,
    isDirty: state.isDirty,
    isSubmitting: state.isSubmitting,
    
    // Field methods
    setValue,
    setValues,
    getValue,
    
    // Validation methods
    validateField,
    validateForm: validateFormMethod,
    getFieldError: getFieldErrorMethod,
    hasFieldError: hasFieldErrorMethod,
    clearFieldError,
    clearErrors,
    
    // Form methods
    handleSubmit,
    reset,
    
    // Props generators
    getFieldProps,
    getCheckboxProps,
    getSelectProps
  };
};

export default useForm;