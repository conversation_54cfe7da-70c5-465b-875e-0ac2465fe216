// Form Validation Utilities
import { z } from 'zod';

// Base validation schemas
export const emailSchema = z.string().email('Invalid email address');
export const phoneSchema = z.string().regex(/^[\+]?[1-9][\d]{0,15}$/, 'Invalid phone number');
export const urlSchema = z.string().url('Invalid URL');
export const dateSchema = z.date().or(z.string().transform((str) => new Date(str)));

// Common field validations
export const requiredString = (message = 'This field is required') => 
  z.string().min(1, message);

export const optionalString = z.string().optional();

export const requiredNumber = (message = 'This field is required') => 
  z.number({ required_error: message });

export const positiveNumber = (message = 'Must be a positive number') => 
  z.number().positive(message);

export const percentageSchema = z.number().min(0).max(100, 'Must be between 0 and 100');

// Trainee validation schemas
export const traineePersonalSchema = z.object({
  firstName: requiredString('First name is required'),
  lastName: requiredString('Last name is required'),
  email: emailSchema,
  phone: phoneSchema.optional(),
  dateOfBirth: dateSchema.optional(),
  nationalId: requiredString('National ID is required'),
  address: z.object({
    street: optionalString,
    city: optionalString,
    state: optionalString,
    zipCode: optionalString,
    country: optionalString
  }).optional()
});

export const traineeEducationSchema = z.object({
  degree: requiredString('Degree is required'),
  institution: requiredString('Institution is required'),
  graduationYear: z.number().min(1900).max(new Date().getFullYear()),
  gpa: z.number().min(0).max(4).optional(),
  major: optionalString,
  minor: optionalString
});

export const traineeExperienceSchema = z.object({
  company: requiredString('Company is required'),
  position: requiredString('Position is required'),
  startDate: dateSchema,
  endDate: dateSchema.optional(),
  description: optionalString,
  skills: z.array(z.string()).optional()
});

// Program validation schemas
export const programBasicSchema = z.object({
  title: requiredString('Program title is required'),
  description: requiredString('Description is required'),
  category: requiredString('Category is required'),
  level: z.enum(['beginner', 'intermediate', 'advanced']),
  duration: positiveNumber('Duration must be positive'),
  durationUnit: z.enum(['days', 'weeks', 'months']),
  capacity: positiveNumber('Capacity must be positive'),
  startDate: dateSchema,
  endDate: dateSchema,
  status: z.enum(['draft', 'active', 'completed', 'cancelled'])
});

export const programCompetencySchema = z.object({
  id: requiredString(),
  name: requiredString('Competency name is required'),
  description: optionalString,
  weight: percentageSchema,
  requiredLevel: z.number().min(1).max(5)
});

export const programModuleSchema = z.object({
  id: requiredString(),
  title: requiredString('Module title is required'),
  description: optionalString,
  duration: positiveNumber(),
  order: z.number().min(0),
  competencies: z.array(z.string()),
  resources: z.array(z.string()).optional()
});

// Review validation schemas
export const reviewBasicSchema = z.object({
  traineeId: requiredString('Trainee is required'),
  reviewerId: requiredString('Reviewer is required'),
  programId: requiredString('Program is required'),
  reviewType: z.enum(['monthly', 'quarterly', 'final', 'ad-hoc']),
  reviewDate: dateSchema,
  status: z.enum(['draft', 'submitted', 'approved', 'rejected'])
});

export const competencyRatingSchema = z.object({
  competencyId: requiredString(),
  rating: z.number().min(1).max(5),
  comments: optionalString,
  evidence: z.array(z.string()).optional()
});

export const reviewGoalSchema = z.object({
  id: requiredString(),
  description: requiredString('Goal description is required'),
  targetDate: dateSchema,
  status: z.enum(['not-started', 'in-progress', 'completed', 'overdue']),
  progress: percentageSchema
});

// Assessment validation schemas
export const assessmentSchema = z.object({
  title: requiredString('Assessment title is required'),
  description: optionalString,
  type: z.enum(['quiz', 'project', 'presentation', 'practical']),
  programId: requiredString('Program is required'),
  competencies: z.array(z.string()).min(1, 'At least one competency is required'),
  duration: positiveNumber('Duration must be positive'),
  passingScore: percentageSchema,
  maxAttempts: z.number().min(1).optional(),
  instructions: optionalString,
  resources: z.array(z.string()).optional()
});

// Workflow validation schemas
export const workflowStepSchema = z.object({
  id: requiredString(),
  title: requiredString('Step title is required'),
  description: optionalString,
  assigneeId: requiredString('Assignee is required'),
  dueDate: dateSchema.optional(),
  status: z.enum(['pending', 'in-progress', 'completed', 'skipped']),
  order: z.number().min(0),
  dependencies: z.array(z.string()).optional()
});

export const workflowSchema = z.object({
  title: requiredString('Workflow title is required'),
  description: optionalString,
  type: z.enum(['approval', 'review', 'assessment', 'onboarding']),
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  status: z.enum(['draft', 'active', 'completed', 'cancelled']),
  steps: z.array(workflowStepSchema).min(1, 'At least one step is required'),
  metadata: z.record(z.any()).optional()
});

// Notification validation schemas
export const notificationSchema = z.object({
  title: requiredString('Title is required'),
  message: requiredString('Message is required'),
  type: z.enum(['info', 'success', 'warning', 'error']),
  priority: z.enum(['low', 'medium', 'high']),
  recipientIds: z.array(z.string()).min(1, 'At least one recipient is required'),
  scheduledDate: dateSchema.optional(),
  expiryDate: dateSchema.optional(),
  metadata: z.record(z.any()).optional()
});

// Report validation schemas
export const reportFilterSchema = z.object({
  field: requiredString('Field is required'),
  operator: z.enum(['equals', 'not_equals', 'contains', 'starts_with', 'ends_with', 'greater_than', 'less_than', 'between', 'in', 'not_in']),
  value: z.any(),
  values: z.array(z.any()).optional()
});

export const reportColumnSchema = z.object({
  field: requiredString('Field is required'),
  label: requiredString('Label is required'),
  type: z.enum(['string', 'number', 'date', 'boolean']),
  format: optionalString,
  sortable: z.boolean().default(true),
  filterable: z.boolean().default(true)
});

export const reportConfigSchema = z.object({
  title: requiredString('Report title is required'),
  description: optionalString,
  dataSource: requiredString('Data source is required'),
  columns: z.array(reportColumnSchema).min(1, 'At least one column is required'),
  filters: z.array(reportFilterSchema).optional(),
  sorting: z.array(z.object({
    field: requiredString(),
    direction: z.enum(['asc', 'desc'])
  })).optional(),
  groupBy: z.array(z.string()).optional(),
  aggregations: z.array(z.object({
    field: requiredString(),
    function: z.enum(['count', 'sum', 'avg', 'min', 'max']),
    label: optionalString
  })).optional()
});

// Validation helper functions
export const validateForm = <T>(schema: z.ZodSchema<T>, data: unknown): { success: boolean; data?: T; errors?: z.ZodError } => {
  try {
    const result = schema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: error };
    }
    throw error;
  }
};

export const getFieldError = (errors: z.ZodError | undefined, fieldPath: string): string | undefined => {
  if (!errors) return undefined;
  
  const fieldError = errors.errors.find(error => 
    error.path.join('.') === fieldPath
  );
  
  return fieldError?.message;
};

export const hasFieldError = (errors: z.ZodError | undefined, fieldPath: string): boolean => {
  return getFieldError(errors, fieldPath) !== undefined;
};

// Form state management
export interface FormState<T> {
  data: Partial<T>;
  errors?: z.ZodError;
  isValid: boolean;
  isDirty: boolean;
  isSubmitting: boolean;
}

export const createFormState = <T>(initialData: Partial<T> = {}): FormState<T> => ({
  data: initialData,
  errors: undefined,
  isValid: false,
  isDirty: false,
  isSubmitting: false
});

export const updateFormField = <T>(
  state: FormState<T>,
  fieldPath: string,
  value: any,
  schema: z.ZodSchema<T>
): FormState<T> => {
  const newData = {
    ...state.data,
    [fieldPath]: value
  };
  
  const validation = validateForm(schema, newData);
  
  return {
    ...state,
    data: newData,
    errors: validation.errors,
    isValid: validation.success,
    isDirty: true
  };
};

export const submitForm = <T>(state: FormState<T>): FormState<T> => ({
  ...state,
  isSubmitting: true
});

export const submitFormSuccess = <T>(state: FormState<T>): FormState<T> => ({
  ...state,
  isSubmitting: false,
  isDirty: false
});

export const submitFormError = <T>(state: FormState<T>, errors: z.ZodError): FormState<T> => ({
  ...state,
  isSubmitting: false,
  errors
});