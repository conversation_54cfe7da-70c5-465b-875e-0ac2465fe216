import React from 'react';
import { motion } from 'framer-motion';
import { User, Notification } from '../types';

interface HeaderProps {
  currentUser: User;
  notifications: Notification[];
  onLogout: () => void;
  onProfile: () => void;
}

export const Header: React.FC<HeaderProps> = ({
  currentUser,
  notifications,
  onLogout,
  onProfile
}) => {
  const [currentView] = React.useState('dashboard');
  const [showNotifications, setShowNotifications] = React.useState(false);
  const onNotificationToggle = () => setShowNotifications(!showNotifications);
  
  // Calculate notification count from unread notifications
  const notificationCount = notifications.filter(n => !n.isRead).length;
  
  const getViewTitle = (view: string) => {
    const titles: Record<string, string> = {
      dashboard: 'Dashboard',
      trainees: 'Trainee Management',
      programs: 'Program Management',
      reviews: 'Review System',
      approvals: 'Approval Workflows',
      analytics: 'Performance Analytics',
      notifications: 'Notifications'
    };
    return titles[view] || 'Graduate Trainee Tracker';
  };

  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h1 className="text-2xl font-semibold text-gray-900">
            {getViewTitle(currentView)}
          </h1>
        </div>

        <div className="flex items-center space-x-4">
          {/* Notification Bell */}
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onNotificationToggle}
            className={`relative p-2 rounded-full transition-colors ${
              showNotifications
                ? 'bg-blue-100 text-blue-600'
                : 'text-gray-600 hover:bg-gray-100'
            }`}
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
              />
            </svg>
            {notificationCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                {notificationCount > 99 ? '99+' : notificationCount}
              </span>
            )}
          </motion.button>

          {/* User Profile */}
          <div className="flex items-center space-x-3">
            <div className="text-right">
              <p className="text-sm font-medium text-gray-900">
                {currentUser.firstName} {currentUser.lastName}
              </p>
              <p className="text-xs text-gray-500 capitalize">
                {currentUser.role.replace('_', ' ')}
              </p>
            </div>
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-medium">
                {currentUser.firstName.charAt(0)}{currentUser.lastName.charAt(0)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};