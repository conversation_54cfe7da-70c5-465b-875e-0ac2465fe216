import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Settings, 
  BarChart3, 
  CheckCircle, 
  AlertTriangle, 
  RefreshCw,
  Download,
  Upload
} from 'lucide-react';

import { ApprovalWorkflow, Trainee, TrainingProgram, User, QuarterlyReview } from '../types';
import { WorkflowAnalyticsDashboard } from './WorkflowAnalyticsDashboard';
import { WorkflowTracker } from './WorkflowTracker';
import { ApprovalWorkflow as ApprovalWorkflowComponent } from '../Approvals/ApprovalWorkflow';
import { useWorkflowTracking } from '../hooks/useWorkflowTracking';
import { defaultValidationService } from '../services/workflowValidationService';
import { auditWorkflows } from '../utils/workflowAudit';

interface WorkflowManagementSystemProps {
  workflows: ApprovalWorkflow[];
  trainees: Trainee[];
  programs: TrainingProgram[];
  users: User[];
  reviews: QuarterlyReview[];
  onWorkflowUpdate?: (workflow: ApprovalWorkflow) => void;
  onRefresh?: () => void;
}

interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  issues: number;
  lastCheck: Date;
  uptime: string;
}

export const WorkflowManagementSystem: React.FC<WorkflowManagementSystemProps> = ({
  workflows,
  trainees,
  programs,
  users,
  reviews,
  onWorkflowUpdate,
  onRefresh
}) => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [systemHealth, setSystemHealth] = useState<SystemHealth>({
    status: 'healthy',
    issues: 0,
    lastCheck: new Date(),
    uptime: '99.9%'
  });
  const [isRunningDiagnostics, setIsRunningDiagnostics] = useState(false);

  const {
    workflowMetrics,
    bottlenecks,
    auditResult,
    loading,
    error,
    refreshTracking,
    runAudit
  } = useWorkflowTracking(workflows, trainees, programs, reviews, users);

  // System health monitoring
  useEffect(() => {
    const checkSystemHealth = async () => {
      try {
        const issues = bottlenecks.length + (auditResult?.issues.length || 0);
        let status: 'healthy' | 'warning' | 'critical' = 'healthy';
        
        if (issues > 10) {
          status = 'critical';
        } else if (issues > 5) {
          status = 'warning';
        }

        setSystemHealth({
          status,
          issues,
          lastCheck: new Date(),
          uptime: '99.9%' // This would be calculated from actual uptime data
        });
      } catch (error) {
        console.error('Error checking system health:', error);
        setSystemHealth(prev => ({ ...prev, status: 'critical' }));
      }
    };

    checkSystemHealth();
    const interval = setInterval(checkSystemHealth, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [bottlenecks, auditResult]);

  const handleRunDiagnostics = async () => {
    setIsRunningDiagnostics(true);
    try {
      await runAudit();
      await refreshTracking();
      
      // Run comprehensive validation
      const validationPromises = workflows.map(async (workflow) => {
        const trainee = trainees.find(t => t.id === workflow.traineeId);
        const program = programs.find(p => p.id === workflow.programId);
        const traineeReviews = reviews.filter(r => r.traineeId === workflow.traineeId);
        
        return await defaultValidationService.validateWorkflowComprehensive(workflow, {
          trainee,
          program,
          users,
          reviews: traineeReviews,
          allWorkflows: workflows
        });
      });

      await Promise.all(validationPromises);
      
      if (onRefresh) {
        onRefresh();
      }
    } catch (error) {
      console.error('Error running diagnostics:', error);
    } finally {
      setIsRunningDiagnostics(false);
    }
  };

  const handleExportSystemReport = () => {
    const systemReport = {
      timestamp: new Date().toISOString(),
      systemHealth,
      metrics: workflowMetrics,
      bottlenecks,
      auditResult,
      workflows: workflows.length,
      trainees: trainees.length,
      programs: programs.length,
      users: users.length,
      reviews: reviews.length
    };
    
    const blob = new Blob([JSON.stringify(systemReport, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `workflow-system-report-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'critical': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="w-4 h-4" />;
      case 'warning': return <AlertTriangle className="w-4 h-4" />;
      case 'critical': return <AlertTriangle className="w-4 h-4" />;
      default: return <Settings className="w-4 h-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* System Header */}
      <div className="mb-6">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              Graduate Trainee Workflow Management System
            </h1>
            <p className="text-gray-600">
              Comprehensive workflow tracking, validation, and analytics platform
            </p>
          </div>
          
          <div className="flex items-center gap-4">
            {/* System Health Status */}
            <Card className="min-w-[200px]">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">System Health</p>
                    <div className={`flex items-center gap-2 ${getStatusColor(systemHealth.status)}`}>
                      {getStatusIcon(systemHealth.status)}
                      <span className="font-medium capitalize">{systemHealth.status}</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-600">{systemHealth.issues} issues</p>
                    <p className="text-xs text-gray-500">{systemHealth.uptime} uptime</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={handleRunDiagnostics}
                disabled={isRunningDiagnostics}
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${isRunningDiagnostics ? 'animate-spin' : ''}`} />
                Run Diagnostics
              </Button>
              <Button variant="outline" onClick={handleExportSystemReport}>
                <Download className="w-4 h-4 mr-2" />
                Export Report
              </Button>
            </div>
          </div>
        </div>

        {/* System Alerts */}
        {error && (
          <Alert variant="destructive" className="mt-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              System Error: {error}
            </AlertDescription>
          </Alert>
        )}

        {systemHealth.status === 'critical' && (
          <Alert variant="destructive" className="mt-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Critical system issues detected. {systemHealth.issues} issues require immediate attention.
            </AlertDescription>
          </Alert>
        )}

        {systemHealth.status === 'warning' && (
          <Alert className="mt-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              System warnings detected. {systemHealth.issues} issues should be reviewed.
            </AlertDescription>
          </Alert>
        )}
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            Analytics Dashboard
          </TabsTrigger>
          <TabsTrigger value="tracker" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            Workflow Tracker
          </TabsTrigger>
          <TabsTrigger value="approvals" className="flex items-center gap-2">
            <CheckCircle className="w-4 h-4" />
            Approval Workflows
          </TabsTrigger>
          <TabsTrigger value="system" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            System Management
          </TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard">
          <WorkflowAnalyticsDashboard
            workflows={workflows}
            trainees={trainees}
            programs={programs}
            users={users}
            reviews={reviews}
            onRefresh={onRefresh}
          />
        </TabsContent>

        <TabsContent value="tracker">
          <WorkflowTracker
          workflows={workflows}
          trainees={trainees}
          programs={programs}
          users={users}
          reviews={reviews}
          currentUser={currentUser}
          onWorkflowUpdate={onWorkflowUpdate}
        />
        </TabsContent>

        <TabsContent value="approvals">
          {workflows.length > 0 && (
            <ApprovalWorkflowComponent
              workflow={workflows[0]}
              currentUser={currentUser}
              onApprove={(stageId, comments) => {
                console.log('Approving workflow stage:', stageId, comments);
              }}
              onReject={(stageId, reason) => {
                console.log('Rejecting workflow stage:', stageId, reason);
              }}
            />
          )}
        </TabsContent>

        <TabsContent value="system">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* System Metrics */}
            <Card>
              <CardHeader>
                <CardTitle>System Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded">
                    <div className="text-2xl font-bold text-blue-600">{workflows.length}</div>
                    <div className="text-sm text-blue-600">Total Workflows</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded">
                    <div className="text-2xl font-bold text-green-600">{trainees.length}</div>
                    <div className="text-sm text-green-600">Active Trainees</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded">
                    <div className="text-2xl font-bold text-purple-600">{programs.length}</div>
                    <div className="text-sm text-purple-600">Training Programs</div>
                  </div>
                  <div className="text-center p-4 bg-orange-50 rounded">
                    <div className="text-2xl font-bold text-orange-600">{users.length}</div>
                    <div className="text-sm text-orange-600">System Users</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* System Configuration */}
            <Card>
              <CardHeader>
                <CardTitle>System Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Auto-refresh interval</span>
                    <Badge variant="outline">30 seconds</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Validation checks</span>
                    <Badge variant="outline">Enabled</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Audit frequency</span>
                    <Badge variant="outline">Real-time</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Notification alerts</span>
                    <Badge variant="outline">Active</Badge>
                  </div>
                </div>
                
                <div className="pt-4 border-t">
                  <p className="text-sm text-gray-600 mb-2">Last system check:</p>
                  <p className="text-sm font-medium">{systemHealth.lastCheck.toLocaleString()}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default WorkflowManagementSystem;