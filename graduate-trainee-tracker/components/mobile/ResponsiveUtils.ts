import { useEffect, useState } from 'react';

// Breakpoint definitions
export const breakpoints = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

export type Breakpoint = keyof typeof breakpoints;

// Media query utilities
export const mediaQueries = {
  xs: `(min-width: ${breakpoints.xs}px)`,
  sm: `(min-width: ${breakpoints.sm}px)`,
  md: `(min-width: ${breakpoints.md}px)`,
  lg: `(min-width: ${breakpoints.lg}px)`,
  xl: `(min-width: ${breakpoints.xl}px)`,
  '2xl': `(min-width: ${breakpoints['2xl']}px)`,
  mobile: `(max-width: ${breakpoints.md - 1}px)`,
  tablet: `(min-width: ${breakpoints.md}px) and (max-width: ${breakpoints.lg - 1}px)`,
  desktop: `(min-width: ${breakpoints.lg}px)`,
} as const;

// Hook to get current screen size
export const useScreenSize = () => {
  const [screenSize, setScreenSize] = useState<{
    width: number;
    height: number;
    breakpoint: Breakpoint;
  }>(() => {
    if (typeof window !== 'undefined') {
      return {
        width: window.innerWidth,
        height: window.innerHeight,
        breakpoint: getCurrentBreakpoint(window.innerWidth),
      };
    }
    return {
      width: 0,
      height: 0,
      breakpoint: 'xs',
    };
  });

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      setScreenSize({
        width,
        height,
        breakpoint: getCurrentBreakpoint(width),
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return screenSize;
};

// Hook to check if screen matches a breakpoint
export const useBreakpoint = (breakpoint: Breakpoint) => {
  const { breakpoint: currentBreakpoint } = useScreenSize();
  return getBreakpointIndex(currentBreakpoint) >= getBreakpointIndex(breakpoint);
};

// Hook for mobile detection
export const useIsMobile = () => {
  const { width } = useScreenSize();
  return width < breakpoints.md;
};

// Hook for tablet detection
export const useIsTablet = () => {
  const { width } = useScreenSize();
  return width >= breakpoints.md && width < breakpoints.lg;
};

// Hook for desktop detection
export const useIsDesktop = () => {
  const { width } = useScreenSize();
  return width >= breakpoints.lg;
};

// Utility functions
function getCurrentBreakpoint(width: number): Breakpoint {
  if (width >= breakpoints['2xl']) return '2xl';
  if (width >= breakpoints.xl) return 'xl';
  if (width >= breakpoints.lg) return 'lg';
  if (width >= breakpoints.md) return 'md';
  if (width >= breakpoints.sm) return 'sm';
  return 'xs';
}

function getBreakpointIndex(breakpoint: Breakpoint): number {
  const breakpointOrder: Breakpoint[] = ['xs', 'sm', 'md', 'lg', 'xl', '2xl'];
  return breakpointOrder.indexOf(breakpoint);
}

// CSS class utilities for responsive design
export const responsiveClasses = {
  // Display utilities
  hideOnMobile: 'hidden md:block',
  hideOnTablet: 'block md:hidden lg:block',
  hideOnDesktop: 'block lg:hidden',
  showOnMobile: 'block md:hidden',
  showOnTablet: 'hidden md:block lg:hidden',
  showOnDesktop: 'hidden lg:block',
  
  // Grid utilities
  gridMobile: 'grid-cols-1',
  gridTablet: 'md:grid-cols-2',
  gridDesktop: 'lg:grid-cols-3 xl:grid-cols-4',
  
  // Spacing utilities
  paddingMobile: 'p-4',
  paddingTablet: 'md:p-6',
  paddingDesktop: 'lg:p-8',
  
  // Text utilities
  textMobile: 'text-sm',
  textTablet: 'md:text-base',
  textDesktop: 'lg:text-lg',
} as const;

// Responsive value selector
export function getResponsiveValue<T>(
  values: Partial<Record<Breakpoint | 'mobile' | 'tablet' | 'desktop', T>>,
  currentBreakpoint: Breakpoint
): T | undefined {
  // Check for exact breakpoint match
  if (values[currentBreakpoint]) {
    return values[currentBreakpoint];
  }
  
  // Check for device type matches
  const width = breakpoints[currentBreakpoint];
  if (width < breakpoints.md && values.mobile) {
    return values.mobile;
  }
  if (width >= breakpoints.md && width < breakpoints.lg && values.tablet) {
    return values.tablet;
  }
  if (width >= breakpoints.lg && values.desktop) {
    return values.desktop;
  }
  
  // Fallback to closest smaller breakpoint
  const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs'];
  const currentIndex = breakpointOrder.indexOf(currentBreakpoint);
  
  for (let i = currentIndex + 1; i < breakpointOrder.length; i++) {
    const fallbackBreakpoint = breakpointOrder[i];
    if (values[fallbackBreakpoint]) {
      return values[fallbackBreakpoint];
    }
  }
  
  return undefined;
}

// Touch detection
export const useIsTouchDevice = () => {
  const [isTouch, setIsTouch] = useState(false);
  
  useEffect(() => {
    const checkTouch = () => {
      setIsTouch(
        'ontouchstart' in window ||
        navigator.maxTouchPoints > 0 ||
        // @ts-ignore
        navigator.msMaxTouchPoints > 0
      );
    };
    
    checkTouch();
    window.addEventListener('touchstart', checkTouch, { once: true });
    
    return () => {
      window.removeEventListener('touchstart', checkTouch);
    };
  }, []);
  
  return isTouch;
};

// Orientation detection
export const useOrientation = () => {
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>(() => {
    if (typeof window !== 'undefined') {
      return window.innerHeight > window.innerWidth ? 'portrait' : 'landscape';
    }
    return 'portrait';
  });
  
  useEffect(() => {
    const handleOrientationChange = () => {
      setOrientation(
        window.innerHeight > window.innerWidth ? 'portrait' : 'landscape'
      );
    };
    
    window.addEventListener('resize', handleOrientationChange);
    window.addEventListener('orientationchange', handleOrientationChange);
    
    return () => {
      window.removeEventListener('resize', handleOrientationChange);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, []);
  
  return orientation;
};