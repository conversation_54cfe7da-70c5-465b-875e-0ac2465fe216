import React, { useState, useMemo } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  ChevronDown,
  ChevronUp,
  Search,
  Filter,
  MoreVertical,
  Eye,
  Edit,
  Trash2,
  RefreshCw,
} from 'lucide-react';
import { useIsMobile, useIsTouchDevice } from './ResponsiveUtils';

interface MobileDataTableColumn<T = any> {
  key: keyof T;
  label: string;
  sortable?: boolean;
  searchable?: boolean;
  render?: (value: any, row: T) => React.ReactNode;
  className?: string;
  priority?: 'high' | 'medium' | 'low'; // For responsive hiding
}

interface MobileDataTableAction<T = any> {
  label: string;
  icon?: React.ComponentType<{ className?: string }>;
  onClick: (row: T) => void;
  variant?: 'default' | 'destructive' | 'secondary';
  disabled?: (row: T) => boolean;
}

interface MobileDataTableProps<T = any> {
  data: T[];
  columns: MobileDataTableColumn<T>[];
  actions?: MobileDataTableAction<T>[];
  searchable?: boolean;
  sortable?: boolean;
  filterable?: boolean;
  loading?: boolean;
  emptyMessage?: string;
  onRefresh?: () => void;
  className?: string;
  cardView?: boolean; // Toggle between table and card view
  expandable?: boolean; // Allow row expansion for details
  renderExpandedContent?: (row: T) => React.ReactNode;
}

type SortDirection = 'asc' | 'desc' | null;

export const MobileDataTable = <T extends Record<string, any>>({
  data,
  columns,
  actions = [],
  searchable = true,
  sortable = true,
  filterable = false,
  loading = false,
  emptyMessage = 'No data available',
  onRefresh,
  className,
  cardView = true,
  expandable = false,
  renderExpandedContent,
}: MobileDataTableProps<T>) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [sortColumn, setSortColumn] = useState<keyof T | null>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);
  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set());
  const [showFilters, setShowFilters] = useState(false);
  const [selectedAction, setSelectedAction] = useState<{ rowIndex: number; show: boolean }>({ rowIndex: -1, show: false });
  
  const isMobile = useIsMobile();
  const isTouch = useIsTouchDevice();

  // Filter and sort data
  const processedData = useMemo(() => {
    let filtered = data;

    // Apply search filter
    if (searchQuery) {
      const searchableColumns = columns.filter(col => col.searchable !== false);
      filtered = data.filter(row =>
        searchableColumns.some(col => {
          const value = row[col.key];
          return String(value).toLowerCase().includes(searchQuery.toLowerCase());
        })
      );
    }

    // Apply sorting
    if (sortColumn && sortDirection) {
      filtered = [...filtered].sort((a, b) => {
        const aValue = a[sortColumn];
        const bValue = b[sortColumn];
        
        if (aValue === bValue) return 0;
        
        const comparison = aValue < bValue ? -1 : 1;
        return sortDirection === 'asc' ? comparison : -comparison;
      });
    }

    return filtered;
  }, [data, searchQuery, sortColumn, sortDirection, columns]);

  const handleSort = (column: keyof T) => {
    if (!sortable) return;
    
    if (sortColumn === column) {
      setSortDirection(prev => {
        if (prev === 'asc') return 'desc';
        if (prev === 'desc') return null;
        return 'asc';
      });
      if (sortDirection === 'desc') {
        setSortColumn(null);
      }
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  const toggleRowExpansion = (index: number) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedRows(newExpanded);
  };

  const toggleActionMenu = (rowIndex: number) => {
    setSelectedAction(prev => ({
      rowIndex,
      show: prev.rowIndex === rowIndex ? !prev.show : true
    }));
  };

  const renderCellValue = (column: MobileDataTableColumn<T>, row: T) => {
    const value = row[column.key];
    if (column.render) {
      return column.render(value, row);
    }
    return String(value || '');
  };

  const renderCardView = () => (
    <div className="space-y-3">
      {processedData.map((row, index) => {
        const isExpanded = expandedRows.has(index);
        const showActions = selectedAction.rowIndex === index && selectedAction.show;
        
        return (
          <div
            key={index}
            className={cn(
              'bg-white rounded-lg border border-gray-200 shadow-sm',
              'transition-all duration-200',
              isExpanded && 'shadow-md'
            )}
          >
            {/* Card Header */}
            <div className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  {/* Primary columns (high priority) */}
                  {columns
                    .filter(col => col.priority === 'high' || !col.priority)
                    .slice(0, 2)
                    .map(column => (
                      <div key={String(column.key)} className="mb-2 last:mb-0">
                        <div className="text-xs text-gray-500 uppercase tracking-wide">
                          {column.label}
                        </div>
                        <div className="text-sm font-medium text-gray-900 truncate">
                          {renderCellValue(column, row)}
                        </div>
                      </div>
                    ))
                  }
                </div>
                
                {/* Actions */}
                {actions.length > 0 && (
                  <div className="relative ml-4">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => toggleActionMenu(index)}
                      className={cn(
                        'h-8 w-8',
                        isTouch && 'min-h-[44px] min-w-[44px]'
                      )}
                    >
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                    
                    {showActions && (
                      <div className="absolute right-0 top-full mt-1 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10 min-w-[120px]">
                        {actions.map((action, actionIndex) => {
                          const Icon = action.icon;
                          const isDisabled = action.disabled?.(row) || false;
                          
                          return (
                            <button
                              key={actionIndex}
                              onClick={() => {
                                if (!isDisabled) {
                                  action.onClick(row);
                                  setSelectedAction({ rowIndex: -1, show: false });
                                }
                              }}
                              disabled={isDisabled}
                              className={cn(
                                'w-full flex items-center px-3 py-2 text-sm text-left',
                                'hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed',
                                action.variant === 'destructive' && 'text-red-600 hover:bg-red-50',
                                isTouch && 'min-h-[44px]'
                              )}
                            >
                              {Icon && <Icon className="h-4 w-4 mr-2" />}
                              {action.label}
                            </button>
                          );
                        })}
                      </div>
                    )}
                  </div>
                )}
              </div>
              
              {/* Secondary columns (medium priority) */}
              <div className="mt-3 grid grid-cols-2 gap-3">
                {columns
                  .filter(col => col.priority === 'medium')
                  .map(column => (
                    <div key={String(column.key)}>
                      <div className="text-xs text-gray-500">
                        {column.label}
                      </div>
                      <div className="text-sm text-gray-900">
                        {renderCellValue(column, row)}
                      </div>
                    </div>
                  ))
                }
              </div>
              
              {/* Expand button */}
              {(expandable || columns.some(col => col.priority === 'low')) && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => toggleRowExpansion(index)}
                  className={cn(
                    'mt-3 w-full justify-center',
                    isTouch && 'min-h-[44px]'
                  )}
                >
                  {isExpanded ? (
                    <>
                      <ChevronUp className="h-4 w-4 mr-1" />
                      Show Less
                    </>
                  ) : (
                    <>
                      <ChevronDown className="h-4 w-4 mr-1" />
                      Show More
                    </>
                  )}
                </Button>
              )}
            </div>
            
            {/* Expanded Content */}
            {isExpanded && (
              <div className="border-t border-gray-200 p-4 bg-gray-50">
                {/* Low priority columns */}
                {columns
                  .filter(col => col.priority === 'low')
                  .map(column => (
                    <div key={String(column.key)} className="mb-3 last:mb-0">
                      <div className="text-xs text-gray-500 mb-1">
                        {column.label}
                      </div>
                      <div className="text-sm text-gray-900">
                        {renderCellValue(column, row)}
                      </div>
                    </div>
                  ))
                }
                
                {/* Custom expanded content */}
                {renderExpandedContent && (
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    {renderExpandedContent(row)}
                  </div>
                )}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );

  if (!isMobile) {
    return null; // Use regular DataTable on desktop
  }

  return (
    <div className={cn('w-full', className)}>
      {/* Header */}
      <div className="mb-4">
        {/* Search and Actions */}
        <div className="flex items-center space-x-2 mb-3">
          {searchable && (
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={cn(
                  'w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg',
                  'focus:ring-2 focus:ring-blue-500 focus:border-transparent',
                  'placeholder-gray-400 text-sm',
                  isTouch && 'min-h-[44px]'
                )}
              />
            </div>
          )}
          
          {filterable && (
            <Button
              variant="outline"
              size="icon"
              onClick={() => setShowFilters(!showFilters)}
              className={cn(isTouch && 'min-h-[44px] min-w-[44px]')}
            >
              <Filter className="h-4 w-4" />
            </Button>
          )}
          
          {onRefresh && (
            <Button
              variant="outline"
              size="icon"
              onClick={onRefresh}
              disabled={loading}
              className={cn(isTouch && 'min-h-[44px] min-w-[44px]')}
            >
              <RefreshCw className={cn('h-4 w-4', loading && 'animate-spin')} />
            </Button>
          )}
        </div>
        
        {/* Results count */}
        <div className="text-sm text-gray-500">
          {processedData.length} of {data.length} items
          {searchQuery && ` matching "${searchQuery}"`}
        </div>
      </div>

      {/* Content */}
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
          <span className="ml-2 text-gray-500">Loading...</span>
        </div>
      ) : processedData.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-2">
            <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
          </div>
          <p className="text-gray-500">{emptyMessage}</p>
        </div>
      ) : (
        renderCardView()
      )}
      
      {/* Click outside to close action menus */}
      {selectedAction.show && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setSelectedAction({ rowIndex: -1, show: false })}
        />
      )}
    </div>
  );
};

export default MobileDataTable;