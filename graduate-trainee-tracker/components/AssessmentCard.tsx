import React, { useState, useMemo } from 'react';
import { Badge, ProgressBar, Avatar } from './ui';

// Types
export interface AssessmentCardProps {
  assessmentId: string;
  className?: string;
  variant?: 'default' | 'compact' | 'detailed';
  showActions?: boolean;
  onView?: (assessmentId: string) => void;
  onEdit?: (assessmentId: string) => void;
  onDelete?: (assessmentId: string) => void;
  onRetake?: (assessmentId: string) => void;
}

export interface Assessment {
  id: string;
  title: string;
  description: string;
  type: 'technical' | 'behavioral' | 'competency' | 'project' | 'certification';
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  duration: number; // in minutes
  totalQuestions: number;
  passingScore: number;
  maxAttempts: number;
  status: 'draft' | 'active' | 'inactive' | 'archived';
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  tags: string[];
  competencies: string[];
  prerequisites?: string[];
  resources?: AssessmentResource[];
}

export interface AssessmentResult {
  id: string;
  assessmentId: string;
  traineeId: string;
  attempt: number;
  status: 'not_started' | 'in_progress' | 'completed' | 'passed' | 'failed' | 'expired';
  score: number;
  maxScore: number;
  percentage: number;
  startedAt: string;
  completedAt?: string;
  timeSpent: number; // in minutes
  answers: AssessmentAnswer[];
  feedback?: string;
  reviewedBy?: string;
  reviewedAt?: string;
}

export interface AssessmentAnswer {
  questionId: string;
  answer: string | string[];
  isCorrect: boolean;
  points: number;
  maxPoints: number;
  timeSpent: number;
}

export interface AssessmentResource {
  id: string;
  title: string;
  type: 'document' | 'video' | 'link' | 'image';
  url: string;
  description?: string;
}

export interface AssessmentStats {
  totalAttempts: number;
  averageScore: number;
  passRate: number;
  averageTimeSpent: number;
  lastAttempt?: string;
  bestScore?: number;
  attemptsRemaining: number;
}

// Utility functions
const formatDuration = (minutes: number): string => {
  if (minutes < 60) {
    return `${minutes}m`;
  }
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
};

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const getDifficultyColor = (difficulty: string): string => {
  const colors = {
    beginner: 'success',
    intermediate: 'info',
    advanced: 'warning',
    expert: 'error'
  };
  return colors[difficulty as keyof typeof colors] || 'default';
};

const getTypeColor = (type: string): string => {
  const colors = {
    technical: 'info',
    behavioral: 'success',
    competency: 'warning',
    project: 'error',
    certification: 'default'
  };
  return colors[type as keyof typeof colors] || 'default';
};

const getStatusColor = (status: string): string => {
  const colors = {
    not_started: 'default',
    in_progress: 'info',
    completed: 'success',
    passed: 'success',
    failed: 'error',
    expired: 'warning',
    draft: 'default',
    active: 'success',
    inactive: 'warning',
    archived: 'default'
  };
  return colors[status as keyof typeof colors] || 'default';
};

const getScoreColor = (percentage: number): string => {
  if (percentage >= 90) return 'success';
  if (percentage >= 80) return 'info';
  if (percentage >= 70) return 'warning';
  return 'error';
};

const calculatePassRate = (results: AssessmentResult[]): number => {
  if (results.length === 0) return 0;
  const passedResults = results.filter(result => result.status === 'passed');
  return Math.round((passedResults.length / results.length) * 100);
};

const calculateAverageScore = (results: AssessmentResult[]): number => {
  if (results.length === 0) return 0;
  const totalScore = results.reduce((sum, result) => sum + result.percentage, 0);
  return Math.round(totalScore / results.length);
};

// Sub-components
interface AssessmentHeaderProps {
  assessment: Assessment;
  variant: 'default' | 'compact' | 'detailed';
}

const AssessmentHeader: React.FC<AssessmentHeaderProps> = ({ assessment, variant }) => {
  return (
    <div className="assessment-card__header">
      <div className="assessment-card__title-section">
        <h3 className="assessment-card__title">{assessment.title}</h3>
        
        <div className="assessment-card__badges">
          <Badge variant={getTypeColor(assessment.type) as any} size="small">
            {assessment.type}
          </Badge>
          
          <Badge variant={getDifficultyColor(assessment.difficulty) as any} size="small">
            {assessment.difficulty}
          </Badge>
          
          <Badge variant={getStatusColor(assessment.status) as any} size="small">
            {assessment.status}
          </Badge>
        </div>
      </div>
      
      {variant !== 'compact' && (
        <div className="assessment-card__meta">
          <span className="assessment-card__category">{assessment.category}</span>
          <span className="assessment-card__duration">{formatDuration(assessment.duration)}</span>
          <span className="assessment-card__questions">{assessment.totalQuestions} questions</span>
        </div>
      )}
    </div>
  );
};

interface AssessmentStatsProps {
  stats: AssessmentStats;
  passingScore: number;
  variant: 'default' | 'compact' | 'detailed';
}

const AssessmentStatsSection: React.FC<AssessmentStatsProps> = ({ stats, passingScore, variant }) => {
  if (variant === 'compact') return null;
  
  return (
    <div className="assessment-card__stats">
      <div className="assessment-card__stats-grid">
        <div className="assessment-card__stat">
          <span className="assessment-card__stat-label">Attempts</span>
          <span className="assessment-card__stat-value">{stats.totalAttempts}</span>
        </div>
        
        <div className="assessment-card__stat">
          <span className="assessment-card__stat-label">Pass Rate</span>
          <span className="assessment-card__stat-value">{stats.passRate}%</span>
        </div>
        
        <div className="assessment-card__stat">
          <span className="assessment-card__stat-label">Avg Score</span>
          <span className="assessment-card__stat-value">{stats.averageScore}%</span>
        </div>
        
        {stats.bestScore !== undefined && (
          <div className="assessment-card__stat">
            <span className="assessment-card__stat-label">Best Score</span>
            <span className="assessment-card__stat-value">
              <Badge variant={getScoreColor(stats.bestScore) as any} size="small">
                {stats.bestScore}%
              </Badge>
            </span>
          </div>
        )}
      </div>
      
      {variant === 'detailed' && (
        <div className="assessment-card__progress-section">
          <div className="assessment-card__progress-item">
            <span className="assessment-card__progress-label">Pass Rate</span>
            <ProgressBar
              value={stats.passRate}
              max={100}
              color={getScoreColor(stats.passRate)}
              size="small"
              label={`${stats.passRate}%`}
            />
          </div>
          
          <div className="assessment-card__progress-item">
            <span className="assessment-card__progress-label">Average Score</span>
            <ProgressBar
              value={stats.averageScore}
              max={100}
              color={getScoreColor(stats.averageScore)}
              size="small"
              label={`${stats.averageScore}%`}
            />
          </div>
        </div>
      )}
    </div>
  );
};

interface AssessmentResultProps {
  result?: AssessmentResult;
  assessment: Assessment;
  variant: 'default' | 'compact' | 'detailed';
}

const AssessmentResultSection: React.FC<AssessmentResultProps> = ({ result, assessment, variant }) => {
  if (!result) {
    return (
      <div className="assessment-card__result assessment-card__result--not-started">
        <div className="assessment-card__result-status">
          <Badge variant="default" size="small">
            Not Started
          </Badge>
        </div>
        
        {variant !== 'compact' && (
          <div className="assessment-card__result-info">
            <span className="assessment-card__result-text">
              Passing score: {assessment.passingScore}%
            </span>
            <span className="assessment-card__result-text">
              Max attempts: {assessment.maxAttempts}
            </span>
          </div>
        )}
      </div>
    );
  }
  
  const isPassed = result.status === 'passed';
  const isCompleted = ['completed', 'passed', 'failed'].includes(result.status);
  
  return (
    <div className="assessment-card__result">
      <div className="assessment-card__result-header">
        <div className="assessment-card__result-status">
          <Badge variant={getStatusColor(result.status) as any} size="small">
            {result.status.replace('_', ' ')}
          </Badge>
          
          {isCompleted && (
            <Badge variant={getScoreColor(result.percentage) as any} size="small">
              {result.percentage}%
            </Badge>
          )}
        </div>
        
        <div className="assessment-card__result-attempt">
          Attempt {result.attempt} of {assessment.maxAttempts}
        </div>
      </div>
      
      {variant !== 'compact' && isCompleted && (
        <div className="assessment-card__result-details">
          <div className="assessment-card__result-score">
            <span className="assessment-card__result-label">Score:</span>
            <span className="assessment-card__result-value">
              {result.score} / {result.maxScore} ({result.percentage}%)
            </span>
          </div>
          
          <div className="assessment-card__result-time">
            <span className="assessment-card__result-label">Time:</span>
            <span className="assessment-card__result-value">
              {formatDuration(result.timeSpent)}
            </span>
          </div>
          
          {result.completedAt && (
            <div className="assessment-card__result-date">
              <span className="assessment-card__result-label">Completed:</span>
              <span className="assessment-card__result-value">
                {formatDateTime(result.completedAt)}
              </span>
            </div>
          )}
        </div>
      )}
      
      {variant === 'detailed' && isCompleted && (
        <div className="assessment-card__result-progress">
          <ProgressBar
            value={result.percentage}
            max={100}
            color={getScoreColor(result.percentage)}
            size="medium"
            label={`${result.percentage}%`}
          />
          
          <div className="assessment-card__passing-line">
            <span className="assessment-card__passing-text">
              Passing: {assessment.passingScore}%
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

interface AssessmentActionsProps {
  assessment: Assessment;
  result?: AssessmentResult;
  stats: AssessmentStats;
  showActions: boolean;
  onView?: (assessmentId: string) => void;
  onEdit?: (assessmentId: string) => void;
  onDelete?: (assessmentId: string) => void;
  onRetake?: (assessmentId: string) => void;
}

const AssessmentActions: React.FC<AssessmentActionsProps> = ({
  assessment,
  result,
  stats,
  showActions,
  onView,
  onEdit,
  onDelete,
  onRetake
}) => {
  if (!showActions) return null;
  
  const canRetake = result && 
    ['completed', 'failed'].includes(result.status) && 
    stats.attemptsRemaining > 0;
  
  const canStart = !result || result.status === 'not_started';
  
  return (
    <div className="assessment-card__actions">
      {onView && (
        <button
          onClick={() => onView(assessment.id)}
          className="assessment-card__action-btn assessment-card__action-btn--view"
        >
          View Details
        </button>
      )}
      
      {canStart && (
        <button
          onClick={() => onView?.(assessment.id)}
          className="assessment-card__action-btn assessment-card__action-btn--start"
        >
          Start Assessment
        </button>
      )}
      
      {canRetake && onRetake && (
        <button
          onClick={() => onRetake(assessment.id)}
          className="assessment-card__action-btn assessment-card__action-btn--retake"
        >
          Retake ({stats.attemptsRemaining} left)
        </button>
      )}
      
      {result?.status === 'in_progress' && (
        <button
          onClick={() => onView?.(assessment.id)}
          className="assessment-card__action-btn assessment-card__action-btn--continue"
        >
          Continue
        </button>
      )}
      
      {onEdit && (
        <button
          onClick={() => onEdit(assessment.id)}
          className="assessment-card__action-btn assessment-card__action-btn--edit"
        >
          Edit
        </button>
      )}
      
      {onDelete && (
        <button
          onClick={() => onDelete(assessment.id)}
          className="assessment-card__action-btn assessment-card__action-btn--delete"
        >
          Delete
        </button>
      )}
    </div>
  );
};

// Main AssessmentCard Component
export const AssessmentCard: React.FC<AssessmentCardProps> = ({
  assessmentId,
  className = '',
  variant = 'default',
  showActions = true,
  onView,
  onEdit,
  onDelete,
  onRetake
}) => {
  // Mock loading and error states - in real implementation, this would come from a hook
  const loading = false;
  const error = null;
  const assessmentData = null;
  const [showDetails, setShowDetails] = useState(false);
  
  // Mock data - in real implementation, this would come from the hook
  const mockAssessment: Assessment = {
    id: assessmentId,
    title: 'React Fundamentals Assessment',
    description: 'Test your knowledge of React basics including components, props, state, and hooks.',
    type: 'technical',
    category: 'Frontend Development',
    difficulty: 'intermediate',
    duration: 60,
    totalQuestions: 25,
    passingScore: 70,
    maxAttempts: 3,
    status: 'active',
    createdBy: 'instructor-1',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T14:30:00Z',
    tags: ['react', 'javascript', 'frontend'],
    competencies: ['react-basics', 'component-design'],
    prerequisites: ['javascript-fundamentals'],
    resources: [
      {
        id: 'res-1',
        title: 'React Documentation',
        type: 'link',
        url: 'https://reactjs.org/docs',
        description: 'Official React documentation'
      }
    ]
  };
  
  const mockResult: AssessmentResult = {
    id: 'result-1',
    assessmentId,
    traineeId: 'trainee-1',
    attempt: 1,
    status: 'completed',
    score: 18,
    maxScore: 25,
    percentage: 72,
    startedAt: '2024-01-25T09:00:00Z',
    completedAt: '2024-01-25T10:15:00Z',
    timeSpent: 45,
    answers: [],
    feedback: 'Good understanding of React basics. Focus on hooks and state management.'
  };
  
  const mockStats: AssessmentStats = {
    totalAttempts: 1,
    averageScore: 72,
    passRate: 100,
    averageTimeSpent: 45,
    lastAttempt: '2024-01-25T10:15:00Z',
    bestScore: 72,
    attemptsRemaining: 2
  };
  
  // Use mock data for now
  const assessment = assessmentData || mockAssessment;
  const result = mockResult;
  const stats = mockStats;
  
  const containerClasses = [
    'assessment-card',
    `assessment-card--${variant}`,
    result?.status ? `assessment-card--${result.status}` : 'assessment-card--not-started',
    className
  ].filter(Boolean).join(' ');
  
  if (loading) {
    return (
      <div className={`assessment-card assessment-card--loading ${className}`}>
        <div className="assessment-card__skeleton">
          <div className="assessment-card__skeleton-header" />
          <div className="assessment-card__skeleton-content" />
          <div className="assessment-card__skeleton-footer" />
        </div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className={`assessment-card assessment-card--error ${className}`}>
        <div className="assessment-card__error">
          <span className="assessment-card__error-message">
            Failed to load assessment
          </span>
          <button 
            onClick={() => window.location.reload()}
            className="assessment-card__error-retry"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className={containerClasses}>
      <AssessmentHeader assessment={assessment} variant={variant} />
      
      {variant !== 'compact' && assessment.description && (
        <div className="assessment-card__description">
          {assessment.description}
        </div>
      )}
      
      <AssessmentResultSection 
        result={result} 
        assessment={assessment} 
        variant={variant} 
      />
      
      <AssessmentStatsSection 
        stats={stats} 
        passingScore={assessment.passingScore} 
        variant={variant} 
      />
      
      {variant === 'detailed' && assessment.tags.length > 0 && (
        <div className="assessment-card__tags">
          <span className="assessment-card__tags-label">Tags:</span>
          <div className="assessment-card__tags-list">
            {assessment.tags.map((tag: string, index: number) => (
              <Badge key={index} variant="default" size="small">
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      )}
      
      {variant === 'detailed' && assessment.competencies.length > 0 && (
        <div className="assessment-card__competencies">
          <span className="assessment-card__competencies-label">Competencies:</span>
          <div className="assessment-card__competencies-list">
            {assessment.competencies.map((competency: string, index: number) => (
              <Badge key={index} variant="info" size="small">
                {competency}
              </Badge>
            ))}
          </div>
        </div>
      )}
      
      <AssessmentActions
        assessment={assessment}
        result={result}
        stats={stats}
        showActions={showActions}
        onView={onView}
        onEdit={onEdit}
        onDelete={onDelete}
        onRetake={onRetake}
      />
      
      {variant === 'detailed' && showDetails && (
        <div className="assessment-card__details">
          <div className="assessment-card__details-section">
            <h4>Prerequisites</h4>
            {assessment.prerequisites && assessment.prerequisites.length > 0 ? (
              <ul>
                {assessment.prerequisites.map((prereq: string, index: number) => (
                  <li key={index}>{prereq}</li>
                ))}
              </ul>
            ) : (
              <span>None</span>
            )}
          </div>
          
          {assessment.resources && assessment.resources.length > 0 && (
            <div className="assessment-card__details-section">
              <h4>Resources</h4>
              <ul>
                {assessment.resources.map((resource: AssessmentResource) => (
                  <li key={resource.id}>
                    <a href={resource.url} target="_blank" rel="noopener noreferrer">
                      {resource.title}
                    </a>
                    {resource.description && (
                      <span className="assessment-card__resource-desc">
                        - {resource.description}
                      </span>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
      
      {variant === 'detailed' && (
        <button
          onClick={() => setShowDetails(!showDetails)}
          className="assessment-card__toggle-details"
        >
          {showDetails ? 'Hide Details' : 'Show Details'}
        </button>
      )}
    </div>
  );
};

export default AssessmentCard;