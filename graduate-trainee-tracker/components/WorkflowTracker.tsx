// Workflow Tracker Component - Comprehensive workflow monitoring interface
import React, { useState, useMemo } from 'react';
import {
  ApprovalWorkflow,
  Trainee,
  TrainingProgram,
  QuarterlyReview,
  User,
  ApprovalStatus,
  StageStatus
} from '../types';
import {
  useWorkflowTracking,
  useWorkflowStatistics,
  WorkflowTrackingFilters,
  WorkflowProgress,
  WorkflowBottleneck
} from '../hooks/useWorkflowTracking';

export interface WorkflowTrackerProps {
  workflows: ApprovalWorkflow[];
  trainees: Trainee[];
  programs: TrainingProgram[];
  reviews: QuarterlyReview[];
  users: User[];
  currentUser: User;
  onWorkflowSelect?: (workflowId: string) => void;
  onEscalateWorkflow?: (workflowId: string, reason: string) => void;
  onReassignStage?: (workflowId: string, stageId: string, newApproverId: string) => void;
  onWorkflowUpdate?: (workflow: ApprovalWorkflow) => void;
}

type ViewMode = 'overview' | 'progress' | 'bottlenecks' | 'audit';

export const WorkflowTracker: React.FC<WorkflowTrackerProps> = ({
  workflows,
  trainees,
  programs,
  reviews,
  users,
  currentUser,
  onWorkflowSelect,
  onEscalateWorkflow,
  onReassignStage
}) => {
  const [viewMode, setViewMode] = useState<ViewMode>('overview');
  const [selectedWorkflow, setSelectedWorkflow] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);

  // Use workflow tracking hook
  const trackingData = useWorkflowTracking(
    workflows,
    trainees,
    programs,
    reviews,
    users,
    true, // auto-refresh
    30000 // 30 seconds
  );

  const statistics = useWorkflowStatistics(trackingData);

  const {
    workflowProgress,
    workflowMetrics,
    bottlenecks,
    auditResult,
    filteredWorkflows,
    filters,
    setFilters,
    searchQuery,
    setSearchQuery,
    refreshTracking,
    loading,
    error,
    lastUpdated
  } = trackingData;

  // Handle filter changes
  const handleFilterChange = (newFilters: Partial<WorkflowTrackingFilters>) => {
    setFilters({ ...filters, ...newFilters });
  };

  // Handle workflow selection
  const handleWorkflowSelect = (workflowId: string) => {
    setSelectedWorkflow(workflowId);
    onWorkflowSelect?.(workflowId);
  };

  // Get status color
  const getStatusColor = (status: ApprovalStatus): string => {
    switch (status) {
      case ApprovalStatus.APPROVED:
        return 'text-green-600 bg-green-100';
      case ApprovalStatus.REJECTED:
        return 'text-red-600 bg-red-100';
      case ApprovalStatus.IN_PROGRESS:
        return 'text-blue-600 bg-blue-100';
      case ApprovalStatus.PENDING:
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  // Get severity color
  const getSeverityColor = (severity: string): string => {
    switch (severity) {
      case 'critical':
        return 'text-red-800 bg-red-200';
      case 'high':
        return 'text-red-600 bg-red-100';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100';
      case 'low':
        return 'text-green-600 bg-green-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  // Render metrics cards
  const renderMetricsCards = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center">
          <div className="p-2 bg-blue-100 rounded-lg">
            <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </div>
          <div className="ml-4">
            <p className="text-sm font-medium text-gray-600">Total Workflows</p>
            <p className="text-2xl font-semibold text-gray-900">{workflowMetrics.totalWorkflows}</p>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center">
          <div className="p-2 bg-green-100 rounded-lg">
            <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="ml-4">
            <p className="text-sm font-medium text-gray-600">Completion Rate</p>
            <p className="text-2xl font-semibold text-gray-900">{workflowMetrics.completionRate.toFixed(1)}%</p>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center">
          <div className="p-2 bg-red-100 rounded-lg">
            <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="ml-4">
            <p className="text-sm font-medium text-gray-600">Overdue</p>
            <p className="text-2xl font-semibold text-gray-900">{workflowMetrics.overdueWorkflows}</p>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center">
          <div className="p-2 bg-yellow-100 rounded-lg">
            <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="ml-4">
            <p className="text-sm font-medium text-gray-600">Bottlenecks</p>
            <p className="text-2xl font-semibold text-gray-900">{workflowMetrics.bottleneckCount}</p>
          </div>
        </div>
      </div>
    </div>
  );

  // Render workflow progress table
  const renderProgressTable = () => (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">Workflow Progress</h3>
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Trainee
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Program
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Progress
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Next Action
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Days in Progress
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {workflowProgress.map((progress) => {
              const workflow = workflows.find(w => w.id === progress.workflowId);
              if (!workflow) return null;
              
              return (
                <tr key={progress.workflowId} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {progress.traineeName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {progress.programName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-full bg-gray-200 rounded-full h-2 mr-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${progress.completionPercentage}%` }}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-600">
                        {progress.completionPercentage.toFixed(0)}%
                      </span>
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {progress.currentStage}/{progress.totalStages} stages
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(progress.status)}`}>
                      {progress.status.replace('_', ' ')}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {progress.nextAction}
                    {progress.blockers.length > 0 && (
                      <div className="text-xs text-red-600 mt-1">
                        Blockers: {progress.blockers.join(', ')}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {progress.daysInProgress} days
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => handleWorkflowSelect(progress.workflowId)}
                      className="text-blue-600 hover:text-blue-900 mr-3"
                    >
                      View
                    </button>
                    {progress.blockers.length > 0 && (
                      <button
                        onClick={() => onEscalateWorkflow?.(progress.workflowId, 'Workflow has blockers')}
                        className="text-red-600 hover:text-red-900"
                      >
                        Escalate
                      </button>
                    )}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );

  // Render bottlenecks table
  const renderBottlenecksTable = () => (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">Workflow Bottlenecks</h3>
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Trainee
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Program
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Stage
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Assigned To
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Days Pending
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Severity
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {bottlenecks.map((bottleneck, index) => (
              <tr key={`${bottleneck.workflowId}-${index}`} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {bottleneck.traineeName}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {bottleneck.programName}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <div>
                    {bottleneck.stageName}
                    <div className="text-xs text-gray-400">Stage {bottleneck.stageNumber}</div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {bottleneck.assignedTo || 'Unassigned'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {bottleneck.daysPending} days
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getSeverityColor(bottleneck.severity)}`}>
                    {bottleneck.severity}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    onClick={() => handleWorkflowSelect(bottleneck.workflowId)}
                    className="text-blue-600 hover:text-blue-900 mr-3"
                  >
                    View
                  </button>
                  <button
                    onClick={() => onEscalateWorkflow?.(bottleneck.workflowId, `Bottleneck at ${bottleneck.stageName}`)}
                    className="text-red-600 hover:text-red-900"
                  >
                    Escalate
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  // Render audit results
  const renderAuditResults = () => (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">Audit Results</h3>
        {lastUpdated && (
          <p className="text-sm text-gray-500 mt-1">
            Last updated: {lastUpdated.toLocaleString()}
          </p>
        )}
      </div>
      
      {auditResult && (
        <div className="p-6">
          {/* Summary */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{auditResult.summary.criticalIssues}</div>
              <div className="text-sm text-gray-600">Critical Issues</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{auditResult.summary.highPriorityIssues}</div>
              <div className="text-sm text-gray-600">High Priority</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">{auditResult.summary.mediumPriorityIssues}</div>
              <div className="text-sm text-gray-600">Medium Priority</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{auditResult.summary.lowPriorityIssues}</div>
              <div className="text-sm text-gray-600">Low Priority</div>
            </div>
          </div>

          {/* Recommendations */}
          {auditResult.recommendations.length > 0 && (
            <div className="mb-6">
              <h4 className="text-md font-medium text-gray-900 mb-3">Recommendations</h4>
              <ul className="list-disc list-inside space-y-2">
                {auditResult.recommendations.map((recommendation, index) => (
                  <li key={index} className="text-sm text-gray-700">{recommendation}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Issues List */}
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-3">Issues ({auditResult.issues.length})</h4>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {auditResult.issues.map((issue) => (
                <div key={issue.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h5 className="text-sm font-medium text-gray-900">{issue.title}</h5>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getSeverityColor(issue.severity)}`}>
                          {issue.severity}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{issue.description}</p>
                      <p className="text-sm text-blue-600 mt-2">
                        <strong>Suggested Action:</strong> {issue.suggestedAction}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );

  // Render view mode tabs
  const renderTabs = () => (
    <div className="border-b border-gray-200 mb-6">
      <nav className="-mb-px flex space-x-8">
        {[
          { id: 'overview', label: 'Overview', icon: '📊' },
          { id: 'progress', label: 'Progress', icon: '📈' },
          { id: 'bottlenecks', label: 'Bottlenecks', icon: '⚠️' },
          { id: 'audit', label: 'Audit', icon: '🔍' }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setViewMode(tab.id as ViewMode)}
            className={`${
              viewMode === tab.id
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
          >
            <span>{tab.icon}</span>
            <span>{tab.label}</span>
          </button>
        ))}
      </nav>
    </div>
  );

  // Render search and filters
  const renderSearchAndFilters = () => (
    <div className="mb-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div className="flex-1 max-w-lg">
          <input
            type="text"
            placeholder="Search workflows..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            Filters
          </button>
          <button
            onClick={refreshTracking}
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Workflow Tracker</h2>
        {error && (
          <div className="text-red-600 text-sm">
            Error: {error}
          </div>
        )}
      </div>

      {/* Metrics Cards */}
      {renderMetricsCards()}

      {/* Search and Filters */}
      {renderSearchAndFilters()}

      {/* Tabs */}
      {renderTabs()}

      {/* Content based on view mode */}
      {viewMode === 'overview' && (
        <div className="space-y-6">
          {renderProgressTable()}
        </div>
      )}

      {viewMode === 'progress' && (
        <div className="space-y-6">
          {renderProgressTable()}
        </div>
      )}

      {viewMode === 'bottlenecks' && (
        <div className="space-y-6">
          {renderBottlenecksTable()}
        </div>
      )}

      {viewMode === 'audit' && (
        <div className="space-y-6">
          {renderAuditResults()}
        </div>
      )}
    </div>
  );
};

export default WorkflowTracker;