import React, { useState, useMemo } from 'react';
import { Ava<PERSON>, Badge, ProgressBar, StatCard } from './ui';
import { useTrainees, useTraineeProgress } from '../hooks';

// Types
export interface TraineeProfileProps {
  traineeId: string;
  className?: string;
  onEdit?: () => void;
  onDelete?: () => void;
  showActions?: boolean;
  compact?: boolean;
}

export interface TraineeContactInfo {
  email: string;
  phone?: string;
  address?: string;
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
}

export interface TraineeEducation {
  degree: string;
  institution: string;
  graduationYear: number;
  gpa?: number;
  honors?: string[];
}

export interface TraineeExperience {
  company: string;
  position: string;
  startDate: string;
  endDate?: string;
  description: string;
  skills: string[];
}

export interface TraineeSkill {
  name: string;
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  category: string;
  verified: boolean;
  lastAssessed?: string;
}

export interface TraineeGoal {
  id: string;
  title: string;
  description: string;
  targetDate: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'overdue';
  progress: number;
}

// Utility functions
const getStatusColor = (status: string): string => {
  const colors = {
    active: 'success',
    inactive: 'warning',
    completed: 'info',
    suspended: 'error'
  };
  return colors[status as keyof typeof colors] || 'default';
};

const getSkillLevelColor = (level: string): string => {
  const colors = {
    beginner: 'error',
    intermediate: 'warning',
    advanced: 'info',
    expert: 'success'
  };
  return colors[level as keyof typeof colors] || 'default';
};

const getGoalStatusColor = (status: string): string => {
  const colors = {
    not_started: 'default',
    in_progress: 'info',
    completed: 'success',
    overdue: 'error'
  };
  return colors[status as keyof typeof colors] || 'default';
};

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

const calculateAge = (birthDate: string): number => {
  const today = new Date();
  const birth = new Date(birthDate);
  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }
  
  return age;
};

// Sub-components
interface ProfileHeaderProps {
  trainee: any;
  onEdit?: () => void;
  onDelete?: () => void;
  showActions?: boolean;
}

const ProfileHeader: React.FC<ProfileHeaderProps> = ({ trainee, onEdit, onDelete, showActions }) => {
  return (
    <div className="trainee-profile__header">
      <div className="trainee-profile__avatar-section">
        <Avatar
          src={trainee.avatar}
          name={`${trainee.firstName} ${trainee.lastName}`}
          size="xl"
          status={trainee.status === 'active' ? 'online' : 'offline'}
        />
        
        <div className="trainee-profile__basic-info">
          <h2 className="trainee-profile__name">
            {trainee.firstName} {trainee.lastName}
          </h2>
          
          <div className="trainee-profile__title">
            {trainee.position || 'Graduate Trainee'}
          </div>
          
          <div className="trainee-profile__badges">
            <Badge variant={getStatusColor(trainee.status) as any}>
              {trainee.status}
            </Badge>
            
            {trainee.program && (
              <Badge variant="outline">
                {trainee.program.name}
              </Badge>
            )}
            
            {trainee.cohort && (
              <Badge variant="secondary">
                Cohort {trainee.cohort}
              </Badge>
            )}
          </div>
        </div>
      </div>
      
      {showActions && (
        <div className="trainee-profile__actions">
          {onEdit && (
            <button
              onClick={onEdit}
              className="trainee-profile__action-btn trainee-profile__action-btn--edit"
            >
              Edit Profile
            </button>
          )}
          
          {onDelete && (
            <button
              onClick={onDelete}
              className="trainee-profile__action-btn trainee-profile__action-btn--delete"
            >
              Delete
            </button>
          )}
        </div>
      )}
    </div>
  );
};

interface ContactInfoProps {
  contact: TraineeContactInfo;
}

const ContactInfo: React.FC<ContactInfoProps> = ({ contact }) => {
  return (
    <div className="trainee-profile__section">
      <h3 className="trainee-profile__section-title">Contact Information</h3>
      
      <div className="trainee-profile__contact-grid">
        <div className="trainee-profile__contact-item">
          <label>Email</label>
          <span>{contact.email}</span>
        </div>
        
        {contact.phone && (
          <div className="trainee-profile__contact-item">
            <label>Phone</label>
            <span>{contact.phone}</span>
          </div>
        )}
        
        {contact.address && (
          <div className="trainee-profile__contact-item">
            <label>Address</label>
            <span>{contact.address}</span>
          </div>
        )}
        
        {contact.emergencyContact && (
          <div className="trainee-profile__contact-item trainee-profile__contact-item--emergency">
            <label>Emergency Contact</label>
            <div>
              <div>{contact.emergencyContact.name}</div>
              <div>{contact.emergencyContact.phone}</div>
              <div className="trainee-profile__emergency-relationship">
                {contact.emergencyContact.relationship}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

interface EducationProps {
  education: TraineeEducation[];
}

const Education: React.FC<EducationProps> = ({ education }) => {
  return (
    <div className="trainee-profile__section">
      <h3 className="trainee-profile__section-title">Education</h3>
      
      <div className="trainee-profile__education-list">
        {education.map((edu, index) => (
          <div key={index} className="trainee-profile__education-item">
            <div className="trainee-profile__education-header">
              <h4>{edu.degree}</h4>
              <span className="trainee-profile__education-year">{edu.graduationYear}</span>
            </div>
            
            <div className="trainee-profile__education-institution">
              {edu.institution}
            </div>
            
            {edu.gpa && (
              <div className="trainee-profile__education-gpa">
                GPA: {edu.gpa}
              </div>
            )}
            
            {edu.honors && edu.honors.length > 0 && (
              <div className="trainee-profile__education-honors">
                {edu.honors.map((honor, honorIndex) => (
                  <Badge key={honorIndex} variant="success" size="small">
                    {honor}
                  </Badge>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

interface ExperienceProps {
  experience: TraineeExperience[];
}

const Experience: React.FC<ExperienceProps> = ({ experience }) => {
  return (
    <div className="trainee-profile__section">
      <h3 className="trainee-profile__section-title">Experience</h3>
      
      <div className="trainee-profile__experience-list">
        {experience.map((exp, index) => (
          <div key={index} className="trainee-profile__experience-item">
            <div className="trainee-profile__experience-header">
              <h4>{exp.position}</h4>
              <span className="trainee-profile__experience-duration">
                {formatDate(exp.startDate)} - {exp.endDate ? formatDate(exp.endDate) : 'Present'}
              </span>
            </div>
            
            <div className="trainee-profile__experience-company">
              {exp.company}
            </div>
            
            <div className="trainee-profile__experience-description">
              {exp.description}
            </div>
            
            {exp.skills && exp.skills.length > 0 && (
              <div className="trainee-profile__experience-skills">
                {exp.skills.map((skill, skillIndex) => (
                  <Badge key={skillIndex} variant="outline" size="small">
                    {skill}
                  </Badge>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

interface SkillsProps {
  skills: TraineeSkill[];
}

const Skills: React.FC<SkillsProps> = ({ skills }) => {
  const skillsByCategory = useMemo(() => {
    return skills.reduce((acc, skill) => {
      if (!acc[skill.category]) {
        acc[skill.category] = [];
      }
      acc[skill.category].push(skill);
      return acc;
    }, {} as Record<string, TraineeSkill[]>);
  }, [skills]);
  
  return (
    <div className="trainee-profile__section">
      <h3 className="trainee-profile__section-title">Skills</h3>
      
      <div className="trainee-profile__skills-categories">
        {Object.entries(skillsByCategory).map(([category, categorySkills]) => (
          <div key={category} className="trainee-profile__skills-category">
            <h4 className="trainee-profile__skills-category-title">{category}</h4>
            
            <div className="trainee-profile__skills-list">
              {categorySkills.map((skill, index) => (
                <div key={index} className="trainee-profile__skill-item">
                  <div className="trainee-profile__skill-header">
                    <span className="trainee-profile__skill-name">{skill.name}</span>
                    
                    <div className="trainee-profile__skill-badges">
                      <Badge variant={getSkillLevelColor(skill.level) as any} size="small">
                        {skill.level}
                      </Badge>
                      
                      {skill.verified && (
                        <Badge variant="success" size="small">
                          Verified
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  {skill.lastAssessed && (
                    <div className="trainee-profile__skill-assessed">
                      Last assessed: {formatDate(skill.lastAssessed)}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

interface GoalsProps {
  goals: TraineeGoal[];
}

const Goals: React.FC<GoalsProps> = ({ goals }) => {
  return (
    <div className="trainee-profile__section">
      <h3 className="trainee-profile__section-title">Goals</h3>
      
      <div className="trainee-profile__goals-list">
        {goals.map((goal) => (
          <div key={goal.id} className="trainee-profile__goal-item">
            <div className="trainee-profile__goal-header">
              <h4 className="trainee-profile__goal-title">{goal.title}</h4>
              
              <div className="trainee-profile__goal-badges">
                <Badge variant={getGoalStatusColor(goal.status) as any} size="small">
                  {goal.status.replace('_', ' ')}
                </Badge>
                
                <span className="trainee-profile__goal-target">
                  Target: {formatDate(goal.targetDate)}
                </span>
              </div>
            </div>
            
            <div className="trainee-profile__goal-description">
              {goal.description}
            </div>
            
            <div className="trainee-profile__goal-progress">
              <ProgressBar
                value={goal.progress}
                max={100}
                showPercentage
                size="small"
                color={goal.status === 'completed' ? 'success' : 'primary'}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

interface ProgressStatsProps {
  progress: any;
}

const ProgressStats: React.FC<ProgressStatsProps> = ({ progress }) => {
  return (
    <div className="trainee-profile__section">
      <h3 className="trainee-profile__section-title">Progress Overview</h3>
      
      <div className="trainee-profile__stats-grid">
        <StatCard
          title="Overall Progress"
          value={`${progress.overallProgress}%`}
          variant="default"
          size="medium"
        >
          <StatCard.ProgressBar value={progress.overallProgress} max={100} />
        </StatCard>
        
        <StatCard
          title="Completed Modules"
          value={progress.completedModules}
          subtitle={`of ${progress.totalModules}`}
          variant="filled"
          size="medium"
        />
        
        <StatCard
          title="Assessments Passed"
          value={progress.passedAssessments}
          subtitle={`of ${progress.totalAssessments}`}
          variant="outlined"
          size="medium"
        />
        
        <StatCard
          title="Skills Acquired"
          value={progress.skillsAcquired}
          trend={{
            value: progress.skillsTrend,
            direction: progress.skillsTrend > 0 ? 'up' : 'down'
          }}
          variant="gradient"
          size="medium"
        />
      </div>
    </div>
  );
};

// Main TraineeProfile Component
export const TraineeProfile: React.FC<TraineeProfileProps> = ({
  traineeId,
  className = '',
  onEdit,
  onDelete,
  showActions = true,
  compact = false
}) => {
  const [activeTab, setActiveTab] = useState<string>('overview');
  const { data: trainee, loading, error } = useTrainees({ id: traineeId });
  const { progress } = useTraineeProgress({ traineeId });
  
  if (loading) {
    return (
      <div className="trainee-profile trainee-profile--loading">
        <div className="trainee-profile__skeleton">
          Loading trainee profile...
        </div>
      </div>
    );
  }
  
  if (error || !trainee) {
    return (
      <div className="trainee-profile trainee-profile--error">
        <div className="trainee-profile__error-message">
          {error || 'Trainee not found'}
        </div>
      </div>
    );
  }
  
  const containerClasses = [
    'trainee-profile',
    compact && 'trainee-profile--compact',
    className
  ].filter(Boolean).join(' ');
  
  const tabs = [
    { id: 'overview', label: 'Overview' },
    { id: 'contact', label: 'Contact' },
    { id: 'education', label: 'Education' },
    { id: 'experience', label: 'Experience' },
    { id: 'skills', label: 'Skills' },
    { id: 'goals', label: 'Goals' },
    { id: 'progress', label: 'Progress' }
  ];
  
  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="trainee-profile__overview">
            <div className="trainee-profile__overview-grid">
              <div className="trainee-profile__overview-info">
                <div className="trainee-profile__info-item">
                  <label>Employee ID</label>
                  <span>{trainee.employeeId}</span>
                </div>
                
                <div className="trainee-profile__info-item">
                  <label>Start Date</label>
                  <span>{formatDate(trainee.startDate)}</span>
                </div>
                
                <div className="trainee-profile__info-item">
                  <label>Age</label>
                  <span>{calculateAge(trainee.birthDate)} years</span>
                </div>
                
                <div className="trainee-profile__info-item">
                  <label>Department</label>
                  <span>{trainee.department}</span>
                </div>
                
                <div className="trainee-profile__info-item">
                  <label>Supervisor</label>
                  <span>{trainee.supervisor?.name}</span>
                </div>
              </div>
              
              {progress && (
                <div className="trainee-profile__overview-stats">
                  <ProgressStats progress={progress} />
                </div>
              )}
            </div>
          </div>
        );
      
      case 'contact':
        return <ContactInfo contact={trainee.contact} />;
      
      case 'education':
        return <Education education={trainee.education || []} />;
      
      case 'experience':
        return <Experience experience={trainee.experience || []} />;
      
      case 'skills':
        return <Skills skills={trainee.skills || []} />;
      
      case 'goals':
        return <Goals goals={trainee.goals || []} />;
      
      case 'progress':
        return progress ? <ProgressStats progress={progress} /> : null;
      
      default:
        return null;
    }
  };
  
  return (
    <div className={containerClasses}>
      <ProfileHeader
        trainee={trainee}
        onEdit={onEdit}
        onDelete={onDelete}
        showActions={showActions}
      />
      
      {!compact && (
        <>
          <div className="trainee-profile__tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`trainee-profile__tab ${
                  activeTab === tab.id ? 'trainee-profile__tab--active' : ''
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
          
          <div className="trainee-profile__content">
            {renderTabContent()}
          </div>
        </>
      )}
    </div>
  );
};

export default TraineeProfile;