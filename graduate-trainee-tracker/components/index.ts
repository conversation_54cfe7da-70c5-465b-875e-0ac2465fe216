// Core Components
export { Header } from './Header';
export { Sidebar } from './Sidebar';
export { Dashboard } from '../Dashboard/Dashboard';

// Main Feature Components
export { TraineeTracker } from '../Trainees/TraineeTracker';
export { ProgramManagement } from '../Programs/ProgramManagement';
export { ReviewSystem } from '../Reviews/ReviewSystem';
export { ApprovalWorkflow } from '../Approvals/ApprovalWorkflow';
export { NotificationCenter } from '../Notifications/NotificationCenter';
export { ReportsAnalytics } from '../Analytics/ReportsAnalytics';

// Feature Sub-components
export { default as TraineeProfile } from './TraineeProfile';
export { default as ProgramForm } from './ProgramForm';
export { default as ReviewForm } from './ReviewForm';
export { default as AssessmentCard } from './AssessmentCard';
export { default as WorkflowCard } from './WorkflowCard';
export { default as NotificationPanel } from './NotificationPanel';
export { default as DashboardCard } from './DashboardCard';
export { default as ReportBuilder } from './ReportBuilder';

// UI Components
export * from './ui';