import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import { 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  FileText, 
  Activity,
  Filter,
  Download,
  RefreshCw
} from 'lucide-react';

import { ApprovalWorkflow, Trainee, TrainingProgram, User, QuarterlyReview } from '../types';
import { useWorkflowTracking } from '../hooks/useWorkflowTracking';
import { defaultValidationService, ComprehensiveValidationResult } from '../services/workflowValidationService';
import { auditWorkflows, WorkflowAuditResult } from '../utils/workflowAudit';

interface WorkflowAnalyticsDashboardProps {
  workflows: ApprovalWorkflow[];
  trainees: Trainee[];
  programs: TrainingProgram[];
  users: User[];
  reviews: QuarterlyReview[];
  onRefresh?: () => void;
}

interface DashboardMetrics {
  totalWorkflows: number;
  completedWorkflows: number;
  pendingWorkflows: number;
  overdueWorkflows: number;
  averageCompletionTime: number;
  completionRate: number;
  bottleneckCount: number;
  validationIssues: number;
}

interface ChartData {
  name: string;
  value: number;
  color?: string;
}

const COLORS = {
  primary: '#3b82f6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
  info: '#6366f1',
  secondary: '#6b7280'
};

export const WorkflowAnalyticsDashboard: React.FC<WorkflowAnalyticsDashboardProps> = ({
  workflows,
  trainees,
  programs,
  users,
  reviews,
  onRefresh
}) => {
  const [selectedTimeRange, setSelectedTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [validationResults, setValidationResults] = useState<ComprehensiveValidationResult[]>([]);
  const [auditResults, setAuditResults] = useState<WorkflowAuditResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedProgram, setSelectedProgram] = useState<string>('all');

  const {
    workflowMetrics,
    workflowProgress,
    bottlenecks,
    auditResult,
    completionStatuses
  } = useWorkflowTracking(workflows, trainees, programs, reviews, users);

  // Calculate dashboard metrics
  const dashboardMetrics = useMemo((): DashboardMetrics => {
    const filteredWorkflows = selectedProgram === 'all' 
      ? workflows 
      : workflows.filter(w => w.programId === selectedProgram);

    const completed = filteredWorkflows.filter(w => w.status === 'approved').length;
    const pending = filteredWorkflows.filter(w => w.status === 'pending').length;
    const overdue = bottlenecks.filter(b => b.severity === 'high' || b.severity === 'critical').length;
    
    const completionRate = filteredWorkflows.length > 0 ? (completed / filteredWorkflows.length) * 100 : 0;
    
    // Calculate average completion time
    const completedWorkflows = filteredWorkflows.filter(w => w.status === 'approved' && w.completedAt);
    const avgCompletionTime = completedWorkflows.length > 0 
      ? completedWorkflows.reduce((sum, w) => {
          const start = new Date(w.initiatedAt).getTime();
          const end = new Date(w.completedAt!).getTime();
          return sum + (end - start);
        }, 0) / completedWorkflows.length / (1000 * 60 * 60 * 24) // Convert to days
      : 0;

    return {
      totalWorkflows: filteredWorkflows.length,
      completedWorkflows: completed,
      pendingWorkflows: pending,
      overdueWorkflows: overdue,
      averageCompletionTime: Math.round(avgCompletionTime),
      completionRate: Math.round(completionRate),
      bottleneckCount: bottlenecks.length,
      validationIssues: validationResults.filter(r => !r.isValid).length
    };
  }, [workflows, selectedProgram, bottlenecks, validationResults]);

  // Prepare chart data
  const statusChartData: ChartData[] = [
    { name: 'Completed', value: dashboardMetrics.completedWorkflows, color: COLORS.success },
    { name: 'Pending', value: dashboardMetrics.pendingWorkflows, color: COLORS.warning },
    { name: 'Overdue', value: dashboardMetrics.overdueWorkflows, color: COLORS.danger }
  ];

  const programChartData: ChartData[] = programs.map(program => {
    const programWorkflows = workflows.filter(w => w.programId === program.id);
    return {
      name: program.title,
      value: programWorkflows.length,
      color: COLORS.primary
    };
  });

  const trendChartData = completionStatuses.map((status, index) => ({
    date: `Day ${index + 1}`,
    completed: status.stagesCompleted,
    pending: status.stagesPending,
    overdue: status.stagesOverdue
  }));

  // Run validation and audit
  useEffect(() => {
    const runAnalysis = async () => {
      setIsLoading(true);
      try {
        // Run validation for all workflows
        const validationPromises = workflows.map(async (workflow) => {
          const trainee = trainees.find(t => t.id === workflow.traineeId);
          const program = programs.find(p => p.id === workflow.programId);
          const traineeReviews = reviews.filter(r => r.traineeId === workflow.traineeId);
          
          return await defaultValidationService.validateWorkflowComprehensive(workflow, {
            trainee,
            program,
            users,
            reviews: traineeReviews,
            allWorkflows: workflows
          });
        });

        const validationResults = await Promise.all(validationPromises);
        setValidationResults(validationResults);

        // Run audit
        const auditResult = auditWorkflows(workflows, trainees, programs, reviews, users);
        setAuditResults(auditResult);
      } catch (error) {
        console.error('Error running workflow analysis:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (workflows.length > 0) {
      runAnalysis();
    }
  }, [workflows, trainees, programs, users, reviews]);

  const handleExportData = () => {
    const exportData = {
      metrics: dashboardMetrics,
      validationResults,
      auditResults,
      bottlenecks,
      timestamp: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `workflow-analytics-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Workflow Analytics Dashboard</h1>
          <p className="text-gray-600 mt-1">Monitor and analyze graduate trainee workflow performance</p>
        </div>
        <div className="flex gap-2">
          <select 
            value={selectedProgram} 
            onChange={(e) => setSelectedProgram(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md"
          >
            <option value="all">All Programs</option>
            {programs.map(program => (
              <option key={program.id} value={program.id}>{program.title}</option>
            ))}
          </select>
          <Button variant="outline" onClick={handleExportData}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button onClick={onRefresh} disabled={isLoading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Workflows</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardMetrics.totalWorkflows}</div>
            <p className="text-xs text-muted-foreground">
              {dashboardMetrics.completionRate}% completion rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-warning">{dashboardMetrics.pendingWorkflows}</div>
            <p className="text-xs text-muted-foreground">
              {dashboardMetrics.overdueWorkflows} overdue
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Completion Time</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardMetrics.averageCompletionTime} days</div>
            <p className="text-xs text-muted-foreground">
              Target: 21 days
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Issues Detected</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-danger">
              {dashboardMetrics.validationIssues + dashboardMetrics.bottleneckCount}
            </div>
            <p className="text-xs text-muted-foreground">
              {dashboardMetrics.bottleneckCount} bottlenecks
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="validation">Validation</TabsTrigger>
          <TabsTrigger value="bottlenecks">Bottlenecks</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Workflow Status Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Workflow Status Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={statusChartData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {statusChartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Program Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Workflows by Program</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={programChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="value" fill={COLORS.primary} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Stage Analytics */}
          <Card>
            <CardHeader>
              <CardTitle>Stage Performance Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {completionStatuses.map((status, index) => {
                  const trainee = trainees.find(t => t.id === status.traineeId);
                  const program = programs.find(p => p.id === status.programId);
                  return (
                    <div key={index} className="flex items-center justify-between p-3 border rounded">
                      <div>
                        <h4 className="font-medium">
                          {trainee ? `${trainee.firstName} ${trainee.lastName}` : 'Unknown Trainee'}
                        </h4>
                        <p className="text-sm text-gray-600">
                          {program?.title || 'Unknown Program'} • Stage {status.currentStage}/{status.totalStages}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Progress value={status.completionPercentage} className="w-20" />
                        <span className="text-sm font-medium">{status.completionPercentage}%</span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="validation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Validation Results</CardTitle>
              <p className="text-sm text-gray-600">
                Comprehensive validation of all workflows including compliance and audit checks
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {validationResults.filter(r => !r.isValid).map((result, index) => {
                  const trainee = trainees.find(t => t.id === workflows.find(w => w.id === result.workflowId)?.traineeId);
                  const program = programs.find(p => p.id === workflows.find(w => w.id === result.workflowId)?.programId);
                  
                  return (
                    <Alert key={index} variant={result.hasErrors ? "destructive" : "default"}>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        <div className="flex justify-between items-start">
                          <div>
                            <strong>
                              {trainee ? `${trainee.firstName} ${trainee.lastName}` : 'Unknown Trainee'} - 
                              {program?.title || 'Unknown Program'}
                            </strong>
                            <div className="mt-1 text-sm">
                              {result.summary.errorCount} errors, {result.summary.warningCount} warnings
                            </div>
                          </div>
                          <Badge variant={result.hasErrors ? "destructive" : "secondary"}>
                            {result.hasErrors ? 'Critical' : 'Warning'}
                          </Badge>
                        </div>
                      </AlertDescription>
                    </Alert>
                  );
                })}
                
                {validationResults.filter(r => !r.isValid).length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <CheckCircle className="w-12 h-12 mx-auto mb-2 text-green-500" />
                    <p>All workflows passed validation checks</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bottlenecks" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Identified Bottlenecks</CardTitle>
              <p className="text-sm text-gray-600">
                Workflows and stages experiencing delays or issues
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {bottlenecks.map((bottleneck, index) => {
                  const trainee = trainees.find(t => t.id === bottleneck.traineeId);
                  const workflow = workflows.find(w => w.id === bottleneck.workflowId);
                  const program = programs.find(p => p.id === workflow?.programId);
                  
                  return (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h4 className="font-medium">
                            {trainee ? `${trainee.firstName} ${trainee.lastName}` : 'Unknown Trainee'}
                          </h4>
                          <p className="text-sm text-gray-600">{program?.title || 'Unknown Program'}</p>
                        </div>
                        <Badge variant={bottleneck.severity === 'high' || bottleneck.severity === 'critical' ? 'destructive' : 'secondary'}>
                          {bottleneck.severity}
                        </Badge>
                      </div>
                      <p className="text-sm mb-2">{bottleneck.stageName} - {bottleneck.traineeName}</p>
                      <div className="flex justify-between items-center text-xs text-gray-500">
                        <span>Stage: {bottleneck.stageName}</span>
                        <span>Days pending: {bottleneck.daysPending}</span>
                      </div>
                    </div>
                  );
                })}
                
                {bottlenecks.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <Activity className="w-12 h-12 mx-auto mb-2 text-green-500" />
                    <p>No bottlenecks detected</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Workflow Completion Trends</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <AreaChart data={trendChartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Area 
                    type="monotone" 
                    dataKey="completed" 
                    stackId="1" 
                    stroke={COLORS.success} 
                    fill={COLORS.success} 
                    fillOpacity={0.6}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="pending" 
                    stackId="1" 
                    stroke={COLORS.warning} 
                    fill={COLORS.warning} 
                    fillOpacity={0.6}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="initiated" 
                    stackId="1" 
                    stroke={COLORS.primary} 
                    fill={COLORS.primary} 
                    fillOpacity={0.6}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default WorkflowAnalyticsDashboard;