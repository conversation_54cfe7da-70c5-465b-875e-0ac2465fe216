import React, { createContext, useContext, useCallback, useState, ReactNode } from 'react';

// Mock toast implementation
const useToast = () => {
  return {
    toast: (options: { title: string; description?: string; variant?: string }) => {
      console.log('Toast:', options);
    }
  };
};

// API Configuration
interface ApiConfig {
  baseUrl: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  headers: Record<string, string>;
}

// API Response Types
interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  errors?: Record<string, string[]>;
  meta?: {
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
    [key: string]: any;
  };
}

interface ApiError {
  message: string;
  status: number;
  code?: string;
  details?: any;
}

// Request Options
interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
  retryAttempts?: number;
  skipErrorToast?: boolean;
  skipLoadingState?: boolean;
}

// API Context
interface ApiContextValue {
  config: ApiConfig;
  loading: boolean;
  request: <T = any>(endpoint: string, options?: RequestOptions) => Promise<ApiResponse<T>>;
  get: <T = any>(endpoint: string, options?: Omit<RequestOptions, 'method'>) => Promise<ApiResponse<T>>;
  post: <T = any>(endpoint: string, data?: any, options?: Omit<RequestOptions, 'method' | 'body'>) => Promise<ApiResponse<T>>;
  put: <T = any>(endpoint: string, data?: any, options?: Omit<RequestOptions, 'method' | 'body'>) => Promise<ApiResponse<T>>;
  patch: <T = any>(endpoint: string, data?: any, options?: Omit<RequestOptions, 'method' | 'body'>) => Promise<ApiResponse<T>>;
  delete: <T = any>(endpoint: string, options?: Omit<RequestOptions, 'method'>) => Promise<ApiResponse<T>>;
  updateConfig: (newConfig: Partial<ApiConfig>) => void;
  setAuthToken: (token: string | null) => void;
}

const ApiContext = createContext<ApiContextValue | null>(null);

// Default configuration
const defaultConfig: ApiConfig = {
  baseUrl: (import.meta as any).env?.VITE_API_BASE_URL || '/api',
  timeout: 30000,
  retryAttempts: 3,
  retryDelay: 1000,
  headers: {
    'Content-Type': 'application/json',
  },
};

// Provider Props
interface ApiProviderProps {
  children: ReactNode;
  config?: Partial<ApiConfig>;
  onError?: (error: ApiError) => void;
  onSuccess?: (response: ApiResponse) => void;
}

export const ApiProvider: React.FC<ApiProviderProps> = ({
  children,
  config: configOverrides = {},
  onError,
  onSuccess,
}) => {
  const [config, setConfig] = useState<ApiConfig>({
    ...defaultConfig,
    ...configOverrides,
  });
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const updateConfig = useCallback((newConfig: Partial<ApiConfig>) => {
    setConfig(prev => ({ ...prev, ...newConfig }));
  }, []);

  const setAuthToken = useCallback((token: string | null) => {
    setConfig(prev => ({
      ...prev,
      headers: {
        ...prev.headers,
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
    }));
  }, []);

  const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

  const handleError = useCallback((error: ApiError, skipToast = false) => {
    console.error('API Error:', error);
    
    if (!skipToast) {
      toast({
        title: 'Error',
        description: error.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
    }
    
    onError?.(error);
  }, [toast, onError]);

  const handleSuccess = useCallback((response: ApiResponse) => {
    onSuccess?.(response);
  }, [onSuccess]);

  const request = useCallback(async <T = any>(
    endpoint: string,
    options: RequestOptions = {}
  ): Promise<ApiResponse<T>> => {
    const {
      method = 'GET',
      headers: requestHeaders = {},
      body,
      timeout = config.timeout,
      retryAttempts = config.retryAttempts,
      skipErrorToast = false,
      skipLoadingState = false,
    } = options;

    const url = endpoint.startsWith('http') ? endpoint : `${config.baseUrl}${endpoint}`;
    const requestConfig: RequestInit = {
      method,
      headers: {
        ...config.headers,
        ...requestHeaders,
      } as HeadersInit,
    };

    if (body && method !== 'GET') {
      if (body instanceof FormData) {
        // Remove Content-Type header for FormData (browser will set it with boundary)
        const headers = requestConfig.headers as Record<string, string>;
        delete headers['Content-Type'];
        requestConfig.headers = headers;
        requestConfig.body = body;
      } else {
        requestConfig.body = JSON.stringify(body);
      }
    }

    let lastError: ApiError;
    
    if (!skipLoadingState) {
      setLoading(true);
    }

    try {
      for (let attempt = 0; attempt <= retryAttempts; attempt++) {
        try {
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), timeout);
          
          const response = await fetch(url, {
            ...requestConfig,
            signal: controller.signal,
          });
          
          clearTimeout(timeoutId);

          let responseData: any;
          const contentType = response.headers.get('content-type');
          
          if (contentType?.includes('application/json')) {
            responseData = await response.json();
          } else {
            responseData = await response.text();
          }

          if (!response.ok) {
            const error: ApiError = {
              message: responseData?.message || `HTTP ${response.status}: ${response.statusText}`,
              status: response.status,
              code: responseData?.code,
              details: responseData,
            };
            throw error;
          }

          const apiResponse: ApiResponse<T> = {
            data: responseData?.data || responseData,
            success: responseData?.success ?? true,
            message: responseData?.message,
            errors: responseData?.errors,
            meta: responseData?.meta,
          };

          handleSuccess(apiResponse);
          return apiResponse;
        } catch (error: any) {
          lastError = {
            message: error.message || 'Network error occurred',
            status: error.status || 0,
            code: error.code,
            details: error.details,
          };

          // Don't retry on client errors (4xx) except 408, 429
          if (error.status >= 400 && error.status < 500 && ![408, 429].includes(error.status)) {
            break;
          }

          // Wait before retry (except on last attempt)
          if (attempt < retryAttempts) {
            await sleep(config.retryDelay * Math.pow(2, attempt)); // Exponential backoff
          }
        }
      }

      handleError(lastError!, skipErrorToast);
      throw lastError!;
    } finally {
      if (!skipLoadingState) {
        setLoading(false);
      }
    }
  }, [config, handleError, handleSuccess]);

  const get = useCallback(<T = any>(
    endpoint: string,
    options?: Omit<RequestOptions, 'method'>
  ) => request<T>(endpoint, { ...options, method: 'GET' }), [request]);

  const post = useCallback(<T = any>(
    endpoint: string,
    data?: any,
    options?: Omit<RequestOptions, 'method' | 'body'>
  ) => request<T>(endpoint, { ...options, method: 'POST', body: data }), [request]);

  const put = useCallback(<T = any>(
    endpoint: string,
    data?: any,
    options?: Omit<RequestOptions, 'method' | 'body'>
  ) => request<T>(endpoint, { ...options, method: 'PUT', body: data }), [request]);

  const patch = useCallback(<T = any>(
    endpoint: string,
    data?: any,
    options?: Omit<RequestOptions, 'method' | 'body'>
  ) => request<T>(endpoint, { ...options, method: 'PATCH', body: data }), [request]);

  const deleteMethod = useCallback(<T = any>(
    endpoint: string,
    options?: Omit<RequestOptions, 'method'>
  ) => request<T>(endpoint, { ...options, method: 'DELETE' }), [request]);

  const contextValue: ApiContextValue = {
    config,
    loading,
    request,
    get,
    post,
    put,
    patch,
    delete: deleteMethod,
    updateConfig,
    setAuthToken,
  };

  return (
    <ApiContext.Provider value={contextValue}>
      {children}
    </ApiContext.Provider>
  );
};

// Hook to use API context
export const useApi = (): ApiContextValue => {
  const context = useContext(ApiContext);
  if (!context) {
    throw new Error('useApi must be used within an ApiProvider');
  }
  return context;
};

// Utility hook for common API patterns
export const useApiRequest = <T = any>(endpoint: string, options?: RequestOptions) => {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<ApiError | null>(null);
  const api = useApi();

  const execute = useCallback(async (overrideOptions?: RequestOptions) => {
    try {
      setLoading(true);
      setError(null);
      const response = await api.request<T>(endpoint, { ...options, ...overrideOptions });
      setData(response.data);
      return response;
    } catch (err: any) {
      setError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [api, endpoint, options]);

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setLoading(false);
  }, []);

  return {
    data,
    loading,
    error,
    execute,
    reset,
  };
};

export type {
  ApiConfig,
  ApiResponse,
  ApiError,
  RequestOptions,
  ApiContextValue,
};