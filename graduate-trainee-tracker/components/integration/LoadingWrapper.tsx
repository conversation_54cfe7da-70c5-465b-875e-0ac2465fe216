import React, { Suspense, ReactNode } from 'react';
import { Loader2, AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

interface LoadingWrapperProps {
  children: ReactNode;
  loading?: boolean;
  error?: Error | string | null;
  fallback?: ReactNode;
  errorFallback?: ReactNode;
  skeleton?: ReactNode;
  onRetry?: () => void;
  retryText?: string;
  loadingText?: string;
  errorText?: string;
  className?: string;
  overlay?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'spinner' | 'skeleton' | 'pulse' | 'dots';
  showRetry?: boolean;
  minHeight?: string | number;
  delay?: number;
}

interface AsyncLoadingWrapperProps extends Omit<LoadingWrapperProps, 'loading' | 'error'> {
  promise?: Promise<any>;
  data?: any;
  isLoading?: boolean;
  isError?: boolean;
  error?: Error | string | null;
}

interface SuspenseWrapperProps {
  children: ReactNode;
  fallback?: ReactNode;
  errorBoundary?: boolean;
  onError?: (error: Error) => void;
}

// Loading spinner component
const LoadingSpinner: React.FC<{
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  className?: string;
}> = ({ size = 'md', text, className }) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  return (
    <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>
      <Loader2 className={cn('animate-spin text-primary', sizeClasses[size])} />
      {text && (
        <p className={cn('text-muted-foreground', textSizeClasses[size])}>
          {text}
        </p>
      )}
    </div>
  );
};

// Dots loading animation
const LoadingDots: React.FC<{
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  className?: string;
}> = ({ size = 'md', text, className }) => {
  const dotSizes = {
    sm: 'w-1 h-1',
    md: 'w-2 h-2',
    lg: 'w-3 h-3',
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  return (
    <div className={cn('flex flex-col items-center justify-center space-y-3', className)}>
      <div className="flex space-x-1">
        {[0, 1, 2].map((i) => (
          <div
            key={i}
            className={cn(
              'bg-primary rounded-full animate-pulse',
              dotSizes[size]
            )}
            style={{
              animationDelay: `${i * 0.2}s`,
              animationDuration: '1s',
            }}
          />
        ))}
      </div>
      {text && (
        <p className={cn('text-muted-foreground', textSizeClasses[size])}>
          {text}
        </p>
      )}
    </div>
  );
};

// Pulse loading animation
const LoadingPulse: React.FC<{
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  className?: string;
}> = ({ size = 'md', text, className }) => {
  const pulseClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  return (
    <div className={cn('flex flex-col items-center justify-center space-y-3', className)}>
      <div className={cn(
        'bg-primary/20 rounded-full animate-pulse',
        pulseClasses[size]
      )} />
      {text && (
        <p className={cn('text-muted-foreground animate-pulse', textSizeClasses[size])}>
          {text}
        </p>
      )}
    </div>
  );
};

// Default skeleton component
const DefaultSkeleton: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn('space-y-3', className)}>
    <Skeleton className="h-4 w-3/4" />
    <Skeleton className="h-4 w-1/2" />
    <Skeleton className="h-4 w-5/6" />
  </div>
);

// Error display component
const ErrorDisplay: React.FC<{
  error: Error | string;
  onRetry?: () => void;
  retryText?: string;
  errorText?: string;
  size?: 'sm' | 'md' | 'lg';
  showRetry?: boolean;
  className?: string;
}> = ({ 
  error, 
  onRetry, 
  retryText = 'Try Again', 
  errorText = 'Something went wrong',
  size = 'md',
  showRetry = true,
  className 
}) => {
  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  const errorMessage = typeof error === 'string' ? error : error.message;

  return (
    <div className={cn('flex flex-col items-center justify-center space-y-3 text-center', className)}>
      <AlertCircle className={cn('text-destructive', iconSizes[size])} />
      <div className="space-y-1">
        <p className={cn('font-medium text-destructive', textSizeClasses[size])}>
          {errorText}
        </p>
        {errorMessage && (
          <p className={cn('text-muted-foreground', 
            size === 'sm' ? 'text-xs' : size === 'lg' ? 'text-base' : 'text-sm'
          )}>
            {errorMessage}
          </p>
        )}
      </div>
      {showRetry && onRetry && (
        <Button
          onClick={onRetry}
          variant="outline"
          size={size === 'sm' ? 'sm' : 'default'}
          className="mt-2"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          {retryText}
        </Button>
      )}
    </div>
  );
};

// Delayed loading component to prevent flash of loading state
const DelayedLoading: React.FC<{
  delay: number;
  children: ReactNode;
}> = ({ delay, children }) => {
  const [show, setShow] = React.useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), delay);
    return () => clearTimeout(timer);
  }, [delay]);

  return show ? <>{children}</> : null;
};

// Main LoadingWrapper component
export const LoadingWrapper: React.FC<LoadingWrapperProps> = ({
  children,
  loading = false,
  error = null,
  fallback,
  errorFallback,
  skeleton,
  onRetry,
  retryText = 'Try Again',
  loadingText = 'Loading...',
  errorText = 'Something went wrong',
  className,
  overlay = false,
  size = 'md',
  variant = 'spinner',
  showRetry = true,
  minHeight,
  delay = 0,
}) => {
  const containerStyle = minHeight ? { minHeight } : undefined;

  // Render error state
  if (error) {
    const errorContent = errorFallback || (
      <ErrorDisplay
        error={error}
        onRetry={onRetry}
        retryText={retryText}
        errorText={errorText}
        size={size}
        showRetry={showRetry}
        className={className}
      />
    );

    return (
      <div style={containerStyle} className={cn('flex items-center justify-center p-4', className)}>
        {errorContent}
      </div>
    );
  }

  // Render loading state
  if (loading) {
    let loadingContent: ReactNode;

    if (fallback) {
      loadingContent = fallback;
    } else if (skeleton && variant === 'skeleton') {
      loadingContent = skeleton;
    } else {
      switch (variant) {
        case 'skeleton':
          loadingContent = <DefaultSkeleton className={className} />;
          break;
        case 'dots':
          loadingContent = <LoadingDots size={size} text={loadingText} className={className} />;
          break;
        case 'pulse':
          loadingContent = <LoadingPulse size={size} text={loadingText} className={className} />;
          break;
        case 'spinner':
        default:
          loadingContent = <LoadingSpinner size={size} text={loadingText} className={className} />;
          break;
      }
    }

    const loadingElement = delay > 0 ? (
      <DelayedLoading delay={delay}>
        {loadingContent}
      </DelayedLoading>
    ) : loadingContent;

    if (overlay) {
      return (
        <div className="relative">
          {children}
          <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
            {loadingElement}
          </div>
        </div>
      );
    }

    return (
      <div style={containerStyle} className={cn('flex items-center justify-center p-4', className)}>
        {loadingElement}
      </div>
    );
  }

  // Render children when not loading and no error
  return <div style={containerStyle}>{children}</div>;
};

// Async LoadingWrapper for handling promises
export const AsyncLoadingWrapper: React.FC<AsyncLoadingWrapperProps> = ({
  promise,
  data,
  isLoading = false,
  isError = false,
  error = null,
  children,
  ...props
}) => {
  const [asyncState, setAsyncState] = React.useState<{
    loading: boolean;
    error: Error | null;
    data: any;
  }>({ loading: false, error: null, data: null });

  React.useEffect(() => {
    if (promise) {
      setAsyncState({ loading: true, error: null, data: null });
      
      promise
        .then((result) => {
          setAsyncState({ loading: false, error: null, data: result });
        })
        .catch((err) => {
          setAsyncState({ loading: false, error: err, data: null });
        });
    }
  }, [promise]);

  const loading = isLoading || asyncState.loading;
  const errorState = error || asyncState.error || (isError ? new Error('An error occurred') : null);
  const hasData = data !== undefined ? data : asyncState.data;

  return (
    <LoadingWrapper
      loading={loading}
      error={errorState}
      {...props}
    >
      {hasData ? children : null}
    </LoadingWrapper>
  );
};

// Suspense wrapper with error boundary
export const SuspenseWrapper: React.FC<SuspenseWrapperProps> = ({
  children,
  fallback,
  errorBoundary = true,
  onError,
}) => {
  const defaultFallback = fallback || <LoadingSpinner size="md" text="Loading..." />;

  const content = (
    <Suspense fallback={defaultFallback}>
      {children}
    </Suspense>
  );

  if (errorBoundary) {
    // Note: This would require the ErrorBoundary component to be imported
    // For now, we'll just return the suspense wrapper
    return content;
  }

  return content;
};

// Higher-order component for adding loading wrapper
export const withLoadingWrapper = <P extends object>(
  Component: React.ComponentType<P>,
  loadingProps?: Omit<LoadingWrapperProps, 'children'>
) => {
  const WrappedComponent = (props: P & { loading?: boolean; error?: Error | string | null }) => {
    const { loading, error, ...componentProps } = props;
    
    return (
      <LoadingWrapper
        loading={loading}
        error={error}
        {...loadingProps}
      >
        <Component {...(componentProps as P)} />
      </LoadingWrapper>
    );
  };

  WrappedComponent.displayName = `withLoadingWrapper(${Component.displayName || Component.name})`;
  return WrappedComponent;
};

// Hook for managing loading states
export const useLoadingState = (initialLoading = false) => {
  const [loading, setLoading] = React.useState(initialLoading);
  const [error, setError] = React.useState<Error | null>(null);

  const startLoading = React.useCallback(() => {
    setLoading(true);
    setError(null);
  }, []);

  const stopLoading = React.useCallback(() => {
    setLoading(false);
  }, []);

  const setLoadingError = React.useCallback((error: Error | string) => {
    setLoading(false);
    setError(typeof error === 'string' ? new Error(error) : error);
  }, []);

  const reset = React.useCallback(() => {
    setLoading(false);
    setError(null);
  }, []);

  const executeAsync = React.useCallback(
    async <T,>(asyncFn: () => Promise<T>): Promise<T | null> => {
      try {
        startLoading();
        const result = await asyncFn();
        stopLoading();
        return result;
      } catch (err) {
        setLoadingError(err instanceof Error ? err : new Error(String(err)));
        return null;
      }
    },
    [startLoading, stopLoading, setLoadingError]
  );

  return {
    loading,
    error,
    startLoading,
    stopLoading,
    setError: setLoadingError,
    reset,
    executeAsync,
  };
};

export {
  LoadingSpinner,
  LoadingDots,
  LoadingPulse,
  DefaultSkeleton,
  ErrorDisplay,
};

export type {
  LoadingWrapperProps,
  AsyncLoadingWrapperProps,
  SuspenseWrapperProps,
};