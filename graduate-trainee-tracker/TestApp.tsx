import React from 'react';
import { GraduateTraineeTracker } from './GraduateTraineeTrackerFixed';
import { UserRole } from './types';

// Simple test app to demonstrate the Graduate Trainee Tracker
const TestApp: React.FC = () => {
  // Mock current user for testing
  const mockUser = {
    id: 'user-001',
    employeeId: 'EMP001',
    firstName: 'Test',
    lastName: 'User',
    email: '<EMAIL>',
    role: UserRole.ADMIN,
    department: 'Learning & Development',
    permissions: [],
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <GraduateTraineeTracker 
        currentUser={mockUser}
        onNavigate={(path) => console.log('Navigate to:', path)}
      />
    </div>
  );
};

export default TestApp;