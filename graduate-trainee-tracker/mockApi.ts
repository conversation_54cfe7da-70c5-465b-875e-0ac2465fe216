// Graduate Trainee Tracker - Mock API Implementation
import {
  Trainee,
  TrainingProgram,
  QuarterlyReview,
  ApprovalWorkflow,
  Notification,
  Competency,
  User,
  TraineeStatus,
  ProgramStatus,
  ReviewStatus,
  ReviewType,
  ApprovalStatus,
  StageStatus,
  ApproverType,
  AssessorType,
  CompetencyCategory,
  CompetencyLevel,
  GoalCategory,
  GoalStatus,
  MilestoneStatus,
  NotificationType,
  NotificationPriority,
  UserRole,
  FeedbackCategory,
  ActionItemStatus,
  Priority,
  ResourceType,
  GraduateTraineeTrackerState
} from './types';

// Mock Users
const mockUsers: User[] = [
  {
    id: 'user-001',
    employeeId: 'EMP001',
    firstName: 'Sarah',
    lastName: 'Johnson',
    email: '<EMAIL>',
    role: UserRole.LD_OFFICER,
    department: 'Learning & Development',
    permissions: [
      { id: 'perm-001', name: 'manage_trainees', description: 'Manage trainee records', module: 'trainees' },
      { id: 'perm-002', name: 'manage_programs', description: 'Manage training programs', module: 'programs' },
      { id: 'perm-003', name: 'manage_reviews', description: 'Manage quarterly reviews', module: 'reviews' }
    ],
    isActive: true,
    createdAt: '2024-01-15T08:00:00Z',
    updatedAt: '2024-08-28T10:00:00Z'
  },
  {
    id: 'user-002',
    employeeId: 'EMP002',
    firstName: 'Michael',
    lastName: 'Chen',
    email: '<EMAIL>',
    role: UserRole.MENTOR,
    department: 'Engineering',
    permissions: [
      { id: 'perm-004', name: 'view_trainees', description: 'View assigned trainees', module: 'trainees' },
      { id: 'perm-005', name: 'conduct_reviews', description: 'Conduct quarterly reviews', module: 'reviews' }
    ],
    isActive: true,
    createdAt: '2024-01-20T08:00:00Z',
    updatedAt: '2024-08-28T09:30:00Z'
  },
  {
    id: 'user-003',
    employeeId: 'EMP003',
    firstName: 'Emily',
    lastName: 'Rodriguez',
    email: '<EMAIL>',
    role: UserRole.MANAGER,
    department: 'Engineering',
    permissions: [
      { id: 'perm-006', name: 'approve_programs', description: 'Approve training programs', module: 'approvals' },
      { id: 'perm-007', name: 'view_analytics', description: 'View performance analytics', module: 'analytics' }
    ],
    isActive: true,
    createdAt: '2024-02-01T08:00:00Z',
    updatedAt: '2024-08-27T14:15:00Z'
  },
  {
    id: 'user-004',
    employeeId: 'EMP004',
    firstName: 'David',
    lastName: 'Kim',
    email: '<EMAIL>',
    role: UserRole.TRAINEE,
    department: 'Engineering',
    permissions: [
      { id: 'perm-008', name: 'view_own_profile', description: 'View own trainee profile', module: 'trainees' },
      { id: 'perm-009', name: 'complete_self_assessment', description: 'Complete self assessments', module: 'reviews' }
    ],
    isActive: true,
    createdAt: '2024-06-01T08:00:00Z',
    updatedAt: '2024-08-28T11:00:00Z'
  }
];

// Mock Competencies
const mockCompetencies: Competency[] = [
  {
    id: 'comp-001',
    name: 'JavaScript Fundamentals',
    description: 'Core JavaScript concepts including ES6+ features',
    category: CompetencyCategory.TECHNICAL,
    level: CompetencyLevel.PROFICIENT,
    weight: 0.25,
    assessmentCriteria: [
      'Understanding of closures and scope',
      'Proficiency with async/await and promises',
      'Knowledge of ES6+ features (destructuring, spread operator, etc.)',
      'Ability to write clean, maintainable code'
    ]
  },
  {
    id: 'comp-002',
    name: 'React Development',
    description: 'Modern React development with hooks and state management',
    category: CompetencyCategory.TECHNICAL,
    level: CompetencyLevel.PROFICIENT,
    weight: 0.30,
    assessmentCriteria: [
      'Understanding of React hooks (useState, useEffect, useContext)',
      'Component lifecycle and optimization techniques',
      'State management patterns',
      'Testing React components'
    ]
  },
  {
    id: 'comp-003',
    name: 'Communication Skills',
    description: 'Effective verbal and written communication',
    category: CompetencyCategory.COMMUNICATION,
    level: CompetencyLevel.ADVANCED,
    weight: 0.20,
    assessmentCriteria: [
      'Clear articulation of technical concepts',
      'Active listening in team discussions',
      'Professional email and documentation writing',
      'Presentation skills'
    ]
  },
  {
    id: 'comp-004',
    name: 'Problem Solving',
    description: 'Analytical thinking and systematic problem-solving approach',
    category: CompetencyCategory.PROBLEM_SOLVING,
    level: CompetencyLevel.PROFICIENT,
    weight: 0.15,
    assessmentCriteria: [
      'Break down complex problems into manageable parts',
      'Use debugging tools effectively',
      'Research and evaluate multiple solutions',
      'Document problem-solving process'
    ]
  },
  {
    id: 'comp-005',
    name: 'Team Collaboration',
    description: 'Working effectively in team environments',
    category: CompetencyCategory.BEHAVIORAL,
    level: CompetencyLevel.PROFICIENT,
    weight: 0.10,
    assessmentCriteria: [
      'Participate actively in team meetings',
      'Provide constructive feedback to peers',
      'Share knowledge and best practices',
      'Adapt to team dynamics'
    ]
  }
];

// Mock Training Programs
const mockPrograms: TrainingProgram[] = [
  {
    id: 'prog-001',
    title: 'Software Engineering Graduate Program',
    description: 'Comprehensive 12-month program for software engineering graduates covering full-stack development, best practices, and professional skills.',
    duration: 12,
    objectives: [
      'Develop proficiency in modern web technologies',
      'Master software development best practices',
      'Build strong problem-solving skills',
      'Enhance communication and collaboration abilities'
    ],
    competencies: mockCompetencies,
    milestones: [
      {
        id: 'mile-001',
        title: 'Foundation Phase',
        description: 'Complete foundational training in programming concepts and tools',
        targetDate: '2024-09-30',
        status: MilestoneStatus.IN_PROGRESS,
        deliverables: ['Code repository setup', 'Basic project completion', 'Tool proficiency assessment'],
        dependencies: []
      },
      {
        id: 'mile-002',
        title: 'Project Phase',
        description: 'Work on real-world projects with mentorship',
        targetDate: '2024-12-31',
        status: MilestoneStatus.PENDING,
        deliverables: ['Project proposal', 'Weekly progress reports', 'Final project presentation'],
        dependencies: ['mile-001']
      },
      {
        id: 'mile-003',
        title: 'Advanced Topics',
        description: 'Explore advanced concepts and specialization areas',
        targetDate: '2025-03-31',
        status: MilestoneStatus.PENDING,
        deliverables: ['Specialization project', 'Technical blog post', 'Knowledge sharing session'],
        dependencies: ['mile-002']
      }
    ],
    resources: [
      {
        id: 'res-001',
        title: 'JavaScript: The Good Parts',
        type: ResourceType.DOCUMENT,
        description: 'Essential JavaScript concepts and best practices',
        isRequired: true
      },
      {
        id: 'res-002',
        title: 'React Official Documentation',
        type: ResourceType.REFERENCE,
        description: 'Comprehensive React documentation and tutorials',
        isRequired: true
      },
      {
        id: 'res-003',
        title: 'Clean Code: A Handbook of Agile Software Craftsmanship',
        type: ResourceType.DOCUMENT,
        description: 'Software development best practices and clean code principles',
        isRequired: false
      }
    ],
    templateId: 'template-001',
    status: ProgramStatus.ACTIVE,
    approvalWorkflow: [
      {
        id: 'stage-001',
        stageNumber: 1,
        stageName: 'LD Officer Review',
        approverType: ApproverType.LD_OFFICER,
        approverId: 'user-001',
        status: StageStatus.APPROVED,
        approvedAt: '2024-06-01T10:00:00Z'
      },
      {
        id: 'stage-002',
        stageNumber: 2,
        stageName: 'Manager Approval',
        approverType: ApproverType.MANAGER,
        approverId: 'user-003',
        status: StageStatus.APPROVED,
        approvedAt: '2024-06-05T14:30:00Z'
      }
    ],
    createdBy: 'user-001',
    createdAt: '2024-05-15T08:00:00Z',
    updatedAt: '2024-08-28T10:00:00Z',
    maxParticipants: 20
  }
];

// Mock Trainees
const mockTrainees: Trainee[] = [
  {
    id: 'trainee-001',
    employeeId: 'GT2024001',
    firstName: 'Alex',
    lastName: 'Thompson',
    email: '<EMAIL>',
    department: 'Engineering',
    startDate: '2024-06-01',
    endDate: '2025-06-01',
    programId: 'prog-001',
    mentorId: 'user-002',
    status: TraineeStatus.ACTIVE,
    currentStage: 2,
    profilePicture: 'https://via.placeholder.com/150',
    phoneNumber: '******-0123',
    emergencyContact: {
      name: 'Jane Thompson',
      relationship: 'Mother',
      phoneNumber: '******-0124',
      email: '<EMAIL>'
    },
    skillsMatrix: [
      {
        competencyId: 'comp-001',
        currentLevel: 3,
        targetLevel: 4,
        assessmentDate: '2024-08-15',
        assessorId: 'user-002',
        evidence: ['Completed JavaScript fundamentals course', 'Built calculator app'],
        developmentPlan: 'Focus on advanced concepts like closures and async patterns'
      },
      {
        competencyId: 'comp-002',
        currentLevel: 2,
        targetLevel: 4,
        assessmentDate: '2024-08-15',
        assessorId: 'user-002',
        evidence: ['Created basic React components', 'Completed React tutorial'],
        developmentPlan: 'Build more complex React applications with state management'
      }
    ],
    goals: [
      {
        id: 'goal-001',
        title: 'Master React Development',
        description: 'Become proficient in React development including hooks, state management, and testing',
        category: GoalCategory.SKILL_DEVELOPMENT,
        targetDate: '2024-12-31',
        status: GoalStatus.IN_PROGRESS,
        progress: 45,
        milestones: [
          {
            id: 'gm-001',
            title: 'Complete React Fundamentals',
            targetDate: '2024-09-30',
            status: MilestoneStatus.COMPLETED,
            progress: 100
          },
          {
            id: 'gm-002',
            title: 'Build Portfolio Project',
            targetDate: '2024-11-30',
            status: MilestoneStatus.IN_PROGRESS,
            progress: 30
          }
        ],
        resources: [
          {
            id: 'res-004',
            title: 'React Testing Library',
            type: ResourceType.COURSE,
            description: 'Comprehensive testing course for React applications',
            isRequired: true
          }
        ],
        createdAt: '2024-06-01T08:00:00Z',
        updatedAt: '2024-08-28T10:00:00Z'
      }
    ],
    reviews: [],
    createdAt: '2024-06-01T08:00:00Z',
    updatedAt: '2024-08-28T10:00:00Z'
  },
  {
    id: 'trainee-002',
    employeeId: 'GT2024002',
    firstName: 'Sophia',
    lastName: 'Martinez',
    email: '<EMAIL>',
    department: 'Engineering',
    startDate: '2024-06-01',
    endDate: '2025-06-01',
    programId: 'prog-001',
    mentorId: 'user-002',
    status: TraineeStatus.ACTIVE,
    currentStage: 1,
    phoneNumber: '******-0125',
    emergencyContact: {
      name: 'Carlos Martinez',
      relationship: 'Father',
      phoneNumber: '******-0126'
    },
    skillsMatrix: [
      {
        competencyId: 'comp-001',
        currentLevel: 2,
        targetLevel: 4,
        assessmentDate: '2024-08-20',
        assessorId: 'user-002',
        evidence: ['Basic JavaScript knowledge', 'Completed coding exercises'],
        developmentPlan: 'Strengthen core JavaScript concepts and practice more complex problems'
      },
      {
        competencyId: 'comp-003',
        currentLevel: 3,
        targetLevel: 4,
        assessmentDate: '2024-08-20',
        assessorId: 'user-002',
        evidence: ['Good presentation skills', 'Clear documentation'],
        developmentPlan: 'Practice technical presentations and improve technical writing'
      }
    ],
    goals: [
      {
        id: 'goal-002',
        title: 'Improve Technical Communication',
        description: 'Enhance ability to explain technical concepts clearly to both technical and non-technical audiences',
        category: GoalCategory.SKILL_DEVELOPMENT,
        targetDate: '2025-03-31',
        status: GoalStatus.IN_PROGRESS,
        progress: 25,
        milestones: [
          {
            id: 'gm-003',
            title: 'Present at Team Meeting',
            targetDate: '2024-10-15',
            status: MilestoneStatus.PENDING,
            progress: 0
          }
        ],
        resources: [],
        createdAt: '2024-06-01T08:00:00Z',
        updatedAt: '2024-08-28T10:00:00Z'
      }
    ],
    reviews: [],
    createdAt: '2024-06-01T08:00:00Z',
    updatedAt: '2024-08-28T10:00:00Z'
  },
  {
    id: 'trainee-003',
    employeeId: 'GT2024003',
    firstName: 'James',
    lastName: 'Wilson',
    email: '<EMAIL>',
    department: 'Engineering',
    startDate: '2024-07-01',
    endDate: '2025-07-01',
    programId: 'prog-001',
    mentorId: 'user-003',
    status: TraineeStatus.PENDING,
    currentStage: 1,
    phoneNumber: '******-0127',
    skillsMatrix: [],
    goals: [],
    reviews: [],
    createdAt: '2024-07-01T08:00:00Z',
    updatedAt: '2024-08-28T10:00:00Z'
  }
];

// Mock Quarterly Reviews
const mockReviews: QuarterlyReview[] = [
  {
    id: 'review-001',
    traineeId: 'trainee-001',
    reviewerId: 'user-002',
    type: ReviewType.QUARTERLY,
    quarter: 1,
    year: 2024,
    reviewPeriod: {
      quarter: 1,
      year: 2024,
      startDate: '2024-06-01',
      endDate: '2024-08-31'
    },
    scheduledDate: '2024-09-15',
    dueDate: '2024-09-30',
    completedDate: '2024-09-28',
    status: ReviewStatus.COMPLETED,
    selfAssessment: {
      id: 'assess-001',
      assessorId: 'trainee-001',
      assessorType: AssessorType.SELF,
      competencyRatings: [
        {
          competencyId: 'comp-001',
          rating: 3,
          evidence: ['Completed all JavaScript exercises', 'Built functional applications'],
          comments: 'Good progress on fundamentals'
        },
        {
          competencyId: 'comp-002',
          rating: 2,
          evidence: ['Basic React components', 'Simple state management'],
          comments: 'Need more practice with complex React patterns'
        }
      ],
      overallRating: 3,
      strengths: ['Quick learner', 'Good problem-solving approach', 'Strong work ethic'],
      areasForImprovement: ['Need more experience with React', 'Could improve code organization'],
      comments: 'Making good progress overall',
      recommendations: ['Focus on React best practices', 'Practice with larger codebases'],
      completedAt: '2024-09-25T10:00:00Z'
    },
    mentorAssessment: {
      id: 'assess-002',
      assessorId: 'user-002',
      assessorType: AssessorType.MENTOR,
      competencyRatings: [
        {
          competencyId: 'comp-001',
          rating: 3,
          evidence: ['Solid understanding of core concepts', 'Good code quality'],
          comments: 'Exceeds expectations for this stage'
        },
        {
          competencyId: 'comp-002',
          rating: 2,
          evidence: ['Basic React knowledge', 'Needs more complex projects'],
          comments: 'On track with development plan'
        }
      ],
      overallRating: 3,
      strengths: ['Strong analytical skills', 'Good communication', 'Proactive learner'],
      areasForImprovement: ['Deepen React knowledge', 'Practice with testing'],
      comments: 'Alex is performing well and shows great potential',
      recommendations: ['Continue with current development plan', 'Consider pair programming sessions'],
      completedAt: '2024-09-28T14:00:00Z'
    },
    overallRating: 3,
    feedback: [
      {
        id: 'feedback-001',
        authorId: 'user-002',
        authorType: AssessorType.MENTOR,
        content: 'Great progress on the fundamentals. Keep pushing yourself with more challenging projects.',
        category: FeedbackCategory.POSITIVE,
        isPrivate: false,
        createdAt: '2024-09-28T14:00:00Z'
      }
    ],
    actionItems: [
      {
        id: 'action-001',
        title: 'Complete Advanced React Course',
        description: 'Finish the advanced React patterns course by end of October',
        assigneeId: 'trainee-001',
        dueDate: '2024-10-31',
        status: ActionItemStatus.IN_PROGRESS,
        priority: Priority.HIGH,
        createdAt: '2024-09-28T14:00:00Z'
      },
      {
        id: 'action-002',
        title: 'Build Testing Skills',
        description: 'Complete React testing library tutorial and implement tests in current project',
        assigneeId: 'trainee-001',
        dueDate: '2024-11-15',
        status: ActionItemStatus.PENDING,
        priority: Priority.MEDIUM,
        createdAt: '2024-09-28T14:00:00Z'
      }
    ],
    nextReviewDate: '2024-12-15',
    overallScore: 75,
    createdAt: '2024-09-15T08:00:00Z',
    updatedAt: '2024-09-28T14:00:00Z'
  },
  {
    id: 'review-002',
    traineeId: 'trainee-002',
    reviewerId: 'user-002',
    type: ReviewType.QUARTERLY,
    quarter: 1,
    year: 2024,
    reviewPeriod: {
      quarter: 1,
      year: 2024,
      startDate: '2024-06-01',
      endDate: '2024-08-31'
    },
    scheduledDate: '2024-09-20',
    dueDate: '2024-09-30',
    status: ReviewStatus.SCHEDULED,
    overallRating: 0,
    feedback: [],
    actionItems: [],
    nextReviewDate: '2024-12-20',
    createdAt: '2024-09-15T08:00:00Z',
    updatedAt: '2024-09-15T08:00:00Z'
  }
];

// Mock Approval Workflows
const mockWorkflows: ApprovalWorkflow[] = [
  {
    id: 'workflow-001',
    programId: 'prog-001',
    traineeId: 'trainee-003',
    currentStage: 1,
    stages: [
      {
        id: 'wf-stage-001',
        stageNumber: 1,
        stageName: 'Mentor Review',
        approverType: ApproverType.MENTOR,
        approverId: 'user-003',
        status: StageStatus.PENDING,
        comments: 'Initial review pending'
      },
      {
        id: 'wf-stage-002',
        stageNumber: 2,
        stageName: 'LD Officer Approval',
        approverType: ApproverType.LD_OFFICER,
        approverId: 'user-001',
        status: StageStatus.PENDING
      },
      {
        id: 'wf-stage-003',
        stageNumber: 3,
        stageName: 'Final Approval',
        approverType: ApproverType.MANAGER,
        approverId: 'user-003',
        status: StageStatus.PENDING
      }
    ],
    status: ApprovalStatus.PENDING,
    initiatedBy: 'trainee-003',
    initiatedAt: '2024-08-15T10:00:00Z'
  }
];

// Mock Notifications
const mockNotifications: Notification[] = [
  {
    id: 'notif-001',
    userId: 'user-001',
    recipientId: 'user-001',
    type: NotificationType.REVIEW_DUE,
    title: 'Quarterly Review Due',
    message: 'Quarterly review for Alex Thompson is due on September 30, 2024',
    priority: NotificationPriority.HIGH,
    isRead: false,
    read: false,
    actionRequired: true,
    actionUrl: '/reviews/review-001',
    createdAt: '2024-09-25T08:00:00Z',
    expiresAt: '2024-09-30T23:59:59Z'
  },
  {
    id: 'notif-002',
    userId: 'user-002',
    recipientId: 'user-002',
    type: NotificationType.MILESTONE_REACHED,
    title: 'Milestone Completed',
    message: 'Alex Thompson has completed the "Complete React Fundamentals" milestone',
    priority: NotificationPriority.MEDIUM,
    isRead: true,
    read: true,
    actionRequired: false,
    createdAt: '2024-09-28T10:00:00Z'
  },
  {
    id: 'notif-003',
    userId: 'user-004',
    recipientId: 'user-004',
    type: NotificationType.DEADLINE_APPROACHING,
    title: 'Goal Deadline Approaching',
    message: 'Your goal "Master React Development" deadline is approaching (Dec 31, 2024)',
    priority: NotificationPriority.MEDIUM,
    isRead: false,
    read: false,
    actionRequired: true,
    actionUrl: '/goals/goal-001',
    createdAt: '2024-10-01T08:00:00Z',
    expiresAt: '2024-12-31T23:59:59Z'
  },
  {
    id: 'notif-004',
    userId: 'user-001',
    recipientId: 'user-001',
    type: NotificationType.APPROVAL_REQUEST,
    title: 'Approval Request',
    message: 'James Wilson requires approval to join the Software Engineering Graduate Program',
    priority: NotificationPriority.HIGH,
    isRead: false,
    read: false,
    actionRequired: true,
    actionUrl: '/approvals/workflow-001',
    createdAt: '2024-08-15T10:00:00Z'
  }
];

// Mock API Functions
export const mockApi = {
  // Fetch all trainees
  fetchTrainees: async (): Promise<Trainee[]> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return [...mockTrainees];
  },

  // Fetch all programs
  fetchPrograms: async (): Promise<TrainingProgram[]> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return [...mockPrograms];
  },

  // Fetch all reviews
  fetchReviews: async (): Promise<QuarterlyReview[]> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return [...mockReviews];
  },

  // Fetch all workflows
  fetchWorkflows: async (): Promise<ApprovalWorkflow[]> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return [...mockWorkflows];
  },

  // Fetch all notifications
  fetchNotifications: async (userId?: string): Promise<Notification[]> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    if (userId) {
      return mockNotifications.filter(notification => notification.recipientId === userId);
    }
    return [...mockNotifications];
  },

  // Fetch all competencies
  fetchCompetencies: async (): Promise<Competency[]> => {
    await new Promise(resolve => setTimeout(resolve, 400));
    return [...mockCompetencies];
  },

  // Fetch all users
  fetchUsers: async (): Promise<User[]> => {
    await new Promise(resolve => setTimeout(resolve, 400));
    return [...mockUsers];
  },

  // Fetch mentors (users with mentor role)
  fetchMentors: async (): Promise<User[]> => {
    await new Promise(resolve => setTimeout(resolve, 400));
    // Return users who can be mentors (Mentor, Manager, LD Officer roles)
    return mockUsers.filter(user => 
      user.role === UserRole.MENTOR || 
      user.role === UserRole.MANAGER || 
      user.role === UserRole.LD_OFFICER
    );
  },

  // Fetch complete state
  fetchCompleteState: async (): Promise<GraduateTraineeTrackerState> => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return {
      currentUser: mockUsers[0], // Sarah Johnson (LD Officer)
      trainees: mockTrainees,
      programs: mockPrograms,
      mentors: mockUsers.filter(user => 
        user.role === UserRole.MENTOR || 
        user.role === UserRole.MANAGER || 
        user.role === UserRole.LD_OFFICER
      ),
      reviews: mockReviews,
      workflows: mockWorkflows,
      notifications: mockNotifications,
      competencies: mockCompetencies,
      users: mockUsers,
      loading: false,
      error: null,
      selectedTrainee: null,
      selectedProgram: null,
      filters: {
        status: [],
        department: [],
        program: [],
        mentor: [],
        dateRange: {
          startDate: '2024-01-01',
          endDate: '2025-12-31'
        }
      },
      pagination: {
        page: 1,
        pageSize: 10,
        total: mockTrainees.length
      }
    };
  },

  // Create new trainee
  createTrainee: async (traineeData: Omit<Trainee, 'id' | 'createdAt' | 'updatedAt' | 'reviews'>): Promise<Trainee> => {
    await new Promise(resolve => setTimeout(resolve, 800));
    const newTrainee: Trainee = {
      ...traineeData,
      id: `trainee-${Date.now()}`,
      reviews: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    mockTrainees.push(newTrainee);
    return newTrainee;
  },

  // Update trainee
  updateTrainee: async (id: string, updates: Partial<Trainee>): Promise<Trainee> => {
    await new Promise(resolve => setTimeout(resolve, 600));
    const index = mockTrainees.findIndex(t => t.id === id);
    if (index === -1) throw new Error('Trainee not found');
    
    mockTrainees[index] = { ...mockTrainees[index], ...updates, updatedAt: new Date().toISOString() };
    return mockTrainees[index];
  },

  // Create new program
  createProgram: async (programData: Omit<TrainingProgram, 'id' | 'createdAt' | 'updatedAt'>): Promise<TrainingProgram> => {
    await new Promise(resolve => setTimeout(resolve, 800));
    const newProgram: TrainingProgram = {
      ...programData,
      id: `prog-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    mockPrograms.push(newProgram);
    return newProgram;
  },

  // Update program
  updateProgram: async (id: string, updates: Partial<TrainingProgram>): Promise<TrainingProgram> => {
    await new Promise(resolve => setTimeout(resolve, 600));
    const index = mockPrograms.findIndex(p => p.id === id);
    if (index === -1) throw new Error('Program not found');
    
    mockPrograms[index] = { ...mockPrograms[index], ...updates, updatedAt: new Date().toISOString() };
    return mockPrograms[index];
  },

  // Create new review
  createReview: async (reviewData: Omit<QuarterlyReview, 'id' | 'createdAt' | 'updatedAt'>): Promise<QuarterlyReview> => {
    await new Promise(resolve => setTimeout(resolve, 800));
    const newReview: QuarterlyReview = {
      ...reviewData,
      id: `review-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    mockReviews.push(newReview);
    return newReview;
  },

  // Update review
  updateReview: async (id: string, updates: Partial<QuarterlyReview>): Promise<QuarterlyReview> => {
    await new Promise(resolve => setTimeout(resolve, 600));
    const index = mockReviews.findIndex(r => r.id === id);
    if (index === -1) throw new Error('Review not found');
    
    mockReviews[index] = { ...mockReviews[index], ...updates, updatedAt: new Date().toISOString() };
    return mockReviews[index];
  },

  // Mark notification as read
  markNotificationAsRead: async (notificationId: string): Promise<Notification> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    const notification = mockNotifications.find(n => n.id === notificationId);
    if (notification) {
      notification.isRead = true;
      notification.read = true;
      return notification;
    }
    throw new Error('Notification not found');
  },

  // Create notification
  createNotification: async (notificationData: Omit<Notification, 'id' | 'createdAt' | 'isRead'>): Promise<Notification> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    const newNotification: Notification = {
      ...notificationData,
      id: `notif-${Date.now()}`,
      isRead: false,
      createdAt: new Date().toISOString()
    };
    mockNotifications.unshift(newNotification);
    return newNotification;
  },

  // Submit approval decision
  submitApprovalDecision: async (
    workflowId: string,
    stageId: string,
    decision: 'approve' | 'reject',
    comments?: string
  ): Promise<ApprovalWorkflow> => {
    await new Promise(resolve => setTimeout(resolve, 700));
    const workflow = mockWorkflows.find(w => w.id === workflowId);
    if (!workflow) throw new Error('Workflow not found');
    
    const stage = workflow.stages.find(s => s.id === stageId);
    if (!stage) throw new Error('Stage not found');
    
    if (decision === 'approve') {
      stage.status = StageStatus.APPROVED;
      stage.approvedAt = new Date().toISOString();
      stage.comments = comments;
    } else {
      stage.status = StageStatus.REJECTED;
      stage.rejectedAt = new Date().toISOString();
      stage.rejectionReason = comments;
      workflow.status = ApprovalStatus.REJECTED;
    }
    
    // Check if all stages are approved
    const allApproved = workflow.stages.every(s => s.status === StageStatus.APPROVED);
    if (allApproved) {
      workflow.status = ApprovalStatus.APPROVED;
      workflow.completedAt = new Date().toISOString();
    }
    
    return workflow;
  },

  // Delete trainee
  deleteTrainee: async (id: string): Promise<void> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const index = mockTrainees.findIndex(t => t.id === id);
    if (index === -1) throw new Error('Trainee not found');
    mockTrainees.splice(index, 1);
  }
};

// Export individual functions for convenience
export const {
  fetchTrainees,
  fetchPrograms,
  fetchReviews,
  fetchWorkflows,
  fetchNotifications,
  fetchCompetencies,
  fetchUsers,
  fetchCompleteState,
  createTrainee,
  updateTrainee,
  createProgram,
  updateProgram,
  createReview,
  updateReview,
  markNotificationAsRead,
  createNotification,
  submitApprovalDecision,
  deleteTrainee
} = mockApi;