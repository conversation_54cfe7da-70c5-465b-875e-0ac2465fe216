import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  GraduateTraineeTrackerProps,
  GraduateTraineeTrackerState,
  User,
  Trainee,
  TrainingProgram,
  QuarterlyReview,
  ApprovalWorkflow,
  Notification,
  TraineeStatus,
  GoalCategory,
  GoalStatus,
  MilestoneStatus,
  ResourceType,
  SkillAssessment,
  Competency,
  CompetencyCategory,
  CompetencyLevel,
  ReviewType,
  ReviewStatus,
  AssessorType,
  ApprovalStatus,
  StageStatus,
  ApproverType,
  DevelopmentGoal,
  ActionItem,
  ActionItemStatus,
  Priority,
  Feedback,
  FeedbackCategory,
  ProgramStatus,
  NotificationType,
  NotificationPriority,
  UserRole
} from './types';
import { TraineeTracker } from './Trainees/TraineeTracker';
import { ProgramManagement } from './Programs/ProgramManagement';
import { ReviewSystem } from './Reviews/ReviewSystem';
import { ApprovalWorkflow as ApprovalWorkflowComponent } from './Approvals/ApprovalWorkflow';
import { Dashboard } from './Dashboard/Dashboard';
import { NotificationCenter } from './Notifications/NotificationCenter';

// Import new UI components
import { PageHeader } from './ui/layout/PageHeader';
import { Sidebar } from './ui/layout/Sidebar';
import { ResponsiveWrapper } from './ui/layout/ResponsiveWrapper';
import { TraineeTable } from './ui/display/TraineeTable';
import { TraineeCard } from './ui/display/TraineeCard';
import { StatusBadge } from './ui/display/StatusBadge';
import { ProgressIndicator } from './ui/display/ProgressIndicator';
import { SearchBar } from './ui/forms/SearchBar';
import { FilterForm } from './ui/forms/FilterForm';
import { ActionMenu } from './ui/actions/ActionMenu';
import { BulkActions } from './ui/actions/BulkActions';
import { ExportButton } from './ui/actions/ExportButton';
import { Notification as NotificationComponent } from './ui/feedback/Notification';
import { LoadingSpinner } from './ui/feedback/LoadingSpinner';
import { EmptyState } from './ui/feedback/EmptyState';
import { ErrorBoundary } from './ui/feedback/ErrorBoundary';
import { UserManagementModal } from './ui/modals/UserManagementModal';
import { ConfirmationModal } from './ui/modals/ConfirmationModal';
import { ExportModal } from './ui/modals/ExportModal';

// Import the new hooks
import { useApi } from './hooks/useApi';
import { usePermissions } from './hooks/usePermissions';
import { useExport } from './hooks/useExport';
import { useErrorHandling } from './hooks/useErrorHandling';

type ViewType = 'dashboard' | 'trainees' | 'programs' | 'reviews' | 'approvals' | 'analytics' | 'notifications' | 'people' | 'school' | 'assessment';

const GraduateTraineeTrackerRefactored: React.FC<GraduateTraineeTrackerProps> = ({ currentUser, onNavigate }) => {
  // Use the new hooks
  const { get, post, put, delete: deleteApi, loading: apiLoading } = useApi();
  const { hasPermission, hasRole, canRead, canWrite, canEdit, canDelete } = usePermissions();
  const { exportToFile, copyToClipboard, exportData, isExporting } = useExport();
  const { handleError, handleAsyncError, ErrorMessages, ErrorClassifier } = useErrorHandling();

  const [state, setState] = useState<GraduateTraineeTrackerState>({
    currentUser: currentUser || null,
    trainees: [],
    programs: [],
    mentors: [],
    reviews: [],
    workflows: [],
    notifications: [],
    competencies: [],
    loading: true,
    error: null,
    selectedTrainee: null,
    selectedProgram: null,
    filters: {
      status: [],
      department: [],
      program: [],
      mentor: [],
      dateRange: {
        startDate: '',
        endDate: ''
      }
    },
    pagination: {
      page: 1,
      pageSize: 10,
      total: 0
    }
  });

  const [activeView, setActiveView] = useState<ViewType>('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [selectedTrainees, setSelectedTrainees] = useState<string[]>([]);
  const [showUserModal, setShowUserModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [traineeToDelete, setTraineeToDelete] = useState<Trainee | null>(null);
  const [notifications, setNotifications] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<any>({});

  // API functions using the useApi hook
  const fetchTrainees = useCallback(async (): Promise<Trainee[]> => {
    try {
      const response = await get<Trainee[]>('/api/trainees');
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch trainees');
      }
    } catch (error) {
      handleError(error as Error, {
        fallbackMessage: 'Failed to load trainees',
        logError: true
      });
      return [];
    }
  }, [get, handleError]);

  const fetchPrograms = useCallback(async (): Promise<TrainingProgram[]> => {
    try {
      const response = await get<TrainingProgram[]>('/api/programs');
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch programs');
      }
    } catch (error) {
      handleError(error as Error, {
        fallbackMessage: 'Failed to load programs',
        logError: true
      });
      return [];
    }
  }, [get, handleError]);

  const fetchMentors = useCallback(async (): Promise<User[]> => {
    try {
      const response = await get<User[]>('/api/mentors');
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch mentors');
      }
    } catch (error) {
      handleError(error as Error, {
        fallbackMessage: 'Failed to load mentors',
        logError: true
      });
      return [];
    }
  }, [get, handleError]);

  const fetchReviews = useCallback(async (): Promise<QuarterlyReview[]> => {
    try {
      const response = await get<QuarterlyReview[]>('/api/reviews');
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch reviews');
      }
    } catch (error) {
      handleError(error as Error, {
        fallbackMessage: 'Failed to load reviews',
        logError: true
      });
      return [];
    }
  }, [get, handleError]);

  const fetchWorkflows = useCallback(async (): Promise<ApprovalWorkflow[]> => {
    try {
      const response = await get<ApprovalWorkflow[]>('/api/workflows');
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch workflows');
      }
    } catch (error) {
      handleError(error as Error, {
        fallbackMessage: 'Failed to load workflows',
        logError: true
      });
      return [];
    }
  }, [get, handleError]);

  const fetchNotifications = useCallback(async (): Promise<Notification[]> => {
    try {
      const response = await get<Notification[]>('/api/notifications');
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch notifications');
      }
    } catch (error) {
      handleError(error as Error, {
        fallbackMessage: 'Failed to load notifications',
        logError: true
      });
      return [];
    }
  }, [get, handleError]);

  const fetchCompetencies = useCallback(async (): Promise<Competency[]> => {
    try {
      const response = await get<Competency[]>('/api/competencies');
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch competencies');
      }
    } catch (error) {
      handleError(error as Error, {
        fallbackMessage: 'Failed to load competencies',
        logError: true
      });
      return [];
    }
  }, [get, handleError]);

  // Export functions using the useExport hook
  const exportTraineesData = useCallback(async () => {
    try {
      await exportData(state.trainees, {
        filename: 'trainees-export',
        format: 'csv',
        includeMetadata: true
      });
    } catch (error) {
      handleError(error as Error, {
        fallbackMessage: 'Failed to export trainees data',
        logError: true
      });
    }
  }, [state.trainees, exportData, handleError]);

  const exportProgramsData = useCallback(async () => {
    try {
      await exportData(state.programs, {
        filename: 'programs-export',
        format: 'json',
        includeMetadata: true
      });
    } catch (error) {
      handleError(error as Error, {
        fallbackMessage: 'Failed to export programs data',
        logError: true
      });
    }
  }, [state.programs, exportData, handleError]);

  // Permission checking using the usePermissions hook (mock - always returns true)
  const canViewTrainees = useCallback(() => {
    return true; // Mock: always allow
  }, []);

  const canManagePrograms = useCallback(() => {
    return true; // Mock: always allow
  }, []);

  const canManageReviews = useCallback(() => {
    return true; // Mock: always allow
  }, []);

  // Data loading effect
  useEffect(() => {
    const loadData = async () => {
      try {
        setState(prev => ({ ...prev, loading: true }));

        // Check permissions before loading data
        if (!canRead('trainees') && !canRead('programs') && !canRead('reviews')) {
          throw new Error('Insufficient permissions to view data');
        }

        const [trainees, programs, mentors, reviews, workflows, notifications, competencies] = await Promise.all([
          fetchTrainees(),
          fetchPrograms(),
          fetchMentors(),
          fetchReviews(),
          fetchWorkflows(),
          fetchNotifications(),
          fetchCompetencies()
        ]);

        setState(prev => ({
          ...prev,
          trainees,
          programs,
          mentors,
          reviews,
          workflows,
          notifications,
          competencies,
          loading: false
        }));
      } catch (error) {
        handleError(error as Error, {
          fallbackMessage: 'Failed to load data',
          logError: true
        });

        setState(prev => ({
          ...prev,
          loading: false,
          error: ErrorMessages.getErrorMessage(error as Error, 'Failed to load data')
        }));
      }
    };

    // Load data immediately - no auth required
    loadData();
  }, [currentUser]); // Simplified dependencies

  const handleViewChange = (view: ViewType) => {
    // Check permissions before changing view
    switch (view) {
      case 'trainees':
        if (!canViewTrainees()) {
          handleError(new Error('Insufficient permissions to view trainees'), {
            fallbackMessage: 'You do not have permission to view trainees'
          });
          return;
        }
        break;
      case 'programs':
        if (!canManagePrograms()) {
          handleError(new Error('Insufficient permissions to manage programs'), {
            fallbackMessage: 'You do not have permission to manage programs'
          });
          return;
        }
        break;
      case 'reviews':
        if (!canManageReviews()) {
          handleError(new Error('Insufficient permissions to manage reviews'), {
            fallbackMessage: 'You do not have permission to manage reviews'
          });
          return;
        }
        break;
    }

    setActiveView(view);
  };

  const renderView = () => {
    switch (activeView) {
      case 'dashboard':
        return (
          <Dashboard
            currentUser={state.currentUser!}
            state={state}
            onStateUpdate={setState}
          />
        );
      case 'trainees':
        return (
          <div className="p-6">
            <PageHeader
              title="Trainee Management"
              actions={[
                {
                  label: 'Add Trainee',
                  onClick: () => setShowUserModal(true),
                  variant: 'primary',
                  disabled: !canViewTrainees()
                },
                {
                  label: 'Export',
                  onClick: () => setShowExportModal(true),
                  variant: 'secondary'
                }
              ]}
            />

            <div className="mt-6">
              <Sidebar
                isOpen={sidebarOpen}
                onToggle={() => setSidebarOpen(!sidebarOpen)}
                activeView={activeView}
                onViewChange={(view) => handleViewChange(view as ViewType)}
                currentUser={state.currentUser!}
              >
                <div className="space-y-4">
                  <SearchBar
                    onSearch={setSearchTerm}
                    placeholder="Search trainees..."
                  />
                  <FilterForm
                    onFilter={setFilters}
                  />
                </div>
              </Sidebar>

              {selectedTrainees.length > 0 && (
                <BulkActions
                  selectedCount={selectedTrainees.length}
                  actions={[
                    {
                      label: 'Export Selected',
                      value: 'export'
                    },
                    {
                      label: 'Delete Selected',
                      value: 'delete'
                    }
                  ]}
                  onAction={(actionValue) => {
                    switch (actionValue) {
                      case 'export':
                        setShowExportModal(true);
                        break;
                      case 'delete':
                        // Handle bulk delete - trigger the delete modal for first selected trainee
                        if (selectedTrainees.length > 0 && state.trainees.length > 0) {
                          const traineeToDelete = state.trainees.find(t => t.id === selectedTrainees[0]);
                          setTraineeToDelete(traineeToDelete || null);
                          setShowDeleteModal(true);
                        }
                        break;
                    }
                  }}
                  onClearSelection={() => setSelectedTrainees([])}
                />
              )}

              <ResponsiveWrapper>
                {(isMobile) => (
                  isMobile ? (
                    <div className="grid grid-cols-1 gap-4 mt-4">
                      {state.trainees.map(trainee => (
                        <TraineeCard
                          key={trainee.id}
                          trainee={trainee}
                          programs={state.programs}
                          mentors={state.mentors}
                          onEdit={() => {
                            setTraineeToDelete(trainee);
                            setShowUserModal(true);
                          }}
                          onDelete={() => {
                            setTraineeToDelete(trainee);
                            setShowDeleteModal(true);
                          }}
                          onView={() => {
                            // Handle view trainee
                          }}
                        />
                      ))}
                    </div>
                  ) : (
                    <TraineeTable
                      trainees={state.trainees}
                      programs={state.programs}
                      mentors={state.mentors}
                      onEdit={(trainee) => {
                        setTraineeToDelete(trainee);
                        setShowUserModal(true);
                      }}
                      onDelete={(trainee) => {
                        setTraineeToDelete(trainee);
                        setShowDeleteModal(true);
                      }}
                      onView={(trainee) => {
                        // Handle view trainee
                      }}
                    />
                  )
                )}
              </ResponsiveWrapper>
            </div>
          </div>
        );
      case 'programs':
        return (
          <ProgramManagement
            programs={state.programs}
            onCreateProgram={async (program) => {
              try {
                // Check permission before creating
                if (!await canWrite('programs')) {
                  throw new Error('Insufficient permissions to create program');
                }

                const newProgramData = {
                  title: program.title || 'New Program',
                  description: program.description || '',
                  duration: program.duration || 12,
                  objectives: program.objectives || [],
                  competencies: program.competencies || [],
                  milestones: program.milestones || [],
                  resources: program.resources || [],
                  status: program.status || ProgramStatus.DRAFT,
                  approvalWorkflow: program.approvalWorkflow || [],
                  createdBy: (authUser || currentUser)?.id || 'unknown',
                  maxParticipants: program.maxParticipants || 20
                };

                const response = await post<TrainingProgram>('/api/programs', newProgramData);
                if (response.success) {
                  const newProgram: TrainingProgram = {
                    ...response.data,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                  };

                  setState(prev => ({
                    ...prev,
                    programs: [...prev.programs, newProgram]
                  }));
                } else {
                  throw new Error(response.message || 'Failed to create program');
                }
              } catch (error) {
                handleError(error as Error, {
                  fallbackMessage: 'Failed to create program',
                  logError: true
                });
              }
            }}
            onUpdateProgram={async (id, updates) => {
              try {
                // Check permission before updating
                if (!await canEdit('programs')) {
                  throw new Error('Insufficient permissions to update program');
                }

                const response = await put<TrainingProgram>(`/api/programs/${id}`, updates);
                if (response.success) {
                  setState(prev => ({
                    ...prev,
                    programs: prev.programs.map(p =>
                      p.id === id ? { ...p, ...updates, updatedAt: new Date().toISOString() } : p
                    )
                  }));
                } else {
                  throw new Error(response.message || 'Failed to update program');
                }
              } catch (error) {
                handleError(error as Error, {
                  fallbackMessage: 'Failed to update program',
                  logError: true
                });
              }
            }}
            onDeleteProgram={async (id) => {
              try {
                // Check permission before deleting
                if (!await canDelete('programs')) {
                  throw new Error('Insufficient permissions to delete program');
                }

                const response = await deleteApi<TrainingProgram>(`/api/programs/${id}`);
                if (response.success) {
                  setState(prev => ({
                    ...prev,
                    programs: prev.programs.filter(p => p.id !== id)
                  }));
                } else {
                  throw new Error(response.message || 'Failed to delete program');
                }
              } catch (error) {
                handleError(error as Error, {
                  fallbackMessage: 'Failed to delete program',
                  logError: true
                });
              }
            }}
          />
        );
      case 'reviews':
        return (
          <ReviewSystem
            reviews={state.reviews}
            trainees={state.trainees}
            currentUser={state.currentUser!}
            onSubmitReview={async (review) => {
              try {
                // Check permission before submitting
                if (!await canWrite('reviews')) {
                  throw new Error('Insufficient permissions to submit review');
                }

                const response = await post<QuarterlyReview>('/api/reviews', review);
                if (response.success) {
                  setState(prev => ({
                    ...prev,
                    reviews: [...prev.reviews, response.data]
                  }));
                } else {
                  throw new Error(response.message || 'Failed to submit review');
                }
              } catch (error) {
                handleError(error as Error, {
                  fallbackMessage: 'Failed to submit review',
                  logError: true
                });
              }
            }}
          />
        );
      case 'approvals':
        return (
          <ApprovalWorkflowComponent
            workflows={state.workflows}
            currentUser={state.currentUser!}
            onApprove={async (workflowId, stageId, comments) => {
              try {
                // Check permission before approving
                if (!await canWrite('approvals')) {
                  throw new Error('Insufficient permissions to approve workflow');
                }

                const response = await post(`/api/workflows/${workflowId}/approve`, {
                  stageId,
                  comments
                });

                if (response.success) {
                  setState(prev => ({
                    ...prev,
                    workflows: prev.workflows.map(w =>
                      w.id === workflowId
                        ? {
                            ...w,
                            stages: w.stages.map(s =>
                              s.id === stageId
                                ? { ...s, status: StageStatus.APPROVED, approvedAt: new Date().toISOString(), comments }
                                : s
                            ),
                            status: w.stages.every(s => s.status === StageStatus.APPROVED)
                              ? ApprovalStatus.APPROVED
                              : w.status
                          }
                        : w
                    )
                  }))
                } else {
                  throw new Error(response.message || 'Failed to approve workflow');
                }
              } catch (error) {
                handleError(error as Error, {
                  fallbackMessage: 'Failed to approve workflow',
                  logError: true
                });
              }
            }}
            onReject={async (workflowId, stageId, reason) => {
              try {
                // Check permission before rejecting
                if (!await canWrite('approvals')) {
                  throw new Error('Insufficient permissions to reject workflow');
                }

                const response = await post(`/api/workflows/${workflowId}/reject`, {
                  stageId,
                  reason
                });

                if (response.success) {
                  setState(prev => ({
                    ...prev,
                    workflows: prev.workflows.map(w =>
                      w.id === workflowId
                        ? {
                            ...w,
                            stages: w.stages.map(s =>
                              s.id === stageId
                                ? { ...s, status: StageStatus.REJECTED, rejectedAt: new Date().toISOString(), rejectionReason: reason }
                                : s
                            ),
                            status: ApprovalStatus.REJECTED
                          }
                        : w
                    )
                  }))
                } else {
                  throw new Error(response.message || 'Failed to reject workflow');
                }
              } catch (error) {
                handleError(error as Error, {
                  fallbackMessage: 'Failed to reject workflow',
                  logError: true
                });
              }
            }}
          />
        );
      case 'notifications':
        return (
          <NotificationCenter
            notifications={state.notifications}
            currentUser={state.currentUser!}
            onMarkAsRead={async (notificationId) => {
              try {
                const response = await put<Notification>(`/api/notifications/${notificationId}/read`, {});
                if (response.success) {
                  setState(prev => ({
                    ...prev,
                    notifications: prev.notifications.map(n =>
                      n.id === notificationId ? { ...n, isRead: true, read: true } : n
                    )
                  }));
                } else {
                  throw new Error(response.message || 'Failed to mark notification as read');
                }
              } catch (error) {
                handleError(error as Error, {
                  fallbackMessage: 'Failed to mark notification as read',
                  logError: true
                });
              }
            }}
          />
        );
      case 'analytics':
        return (
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Analytics Dashboard</h2>
              <button
                onClick={exportTraineesData}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Export Trainees Data
              </button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-white p-4 rounded-lg shadow">
                <h3 className="text-lg font-semibold">Total Trainees</h3>
                <p className="text-3xl font-bold text-blue-600">{state.trainees.length}</p>
              </div>
              <div className="bg-white p-4 rounded-lg shadow">
                <h3 className="text-lg font-semibold">Active Programs</h3>
                <p className="text-3xl font-bold text-green-600">
                  {state.programs.filter(p => p.status === ProgramStatus.ACTIVE).length}
                </p>
              </div>
              <div className="bg-white p-4 rounded-lg shadow">
                <h3 className="text-lg font-semibold">Completed Reviews</h3>
                <p className="text-3xl font-bold text-purple-600">
                  {state.reviews.filter(r => r.status === ReviewStatus.COMPLETED).length}
                </p>
              </div>
              <div className="bg-white p-4 rounded-lg shadow">
                <h3 className="text-lg font-semibold">Pending Approvals</h3>
                <p className="text-3xl font-bold text-orange-600">
                  {state.workflows.filter(w => w.status === ApprovalStatus.PENDING).length}
                </p>
              </div>
            </div>
          </div>
        );
      case 'people':
        return (
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">People Management</h2>
              <button
                onClick={exportTraineesData}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Export People Data
              </button>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Trainees List */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold mb-4">Trainees</h3>
                <div className="space-y-3">
                  {state.trainees.map(trainee => (
                    <div key={trainee.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 font-semibold">
                            {trainee.firstName[0]}{trainee.lastName[0]}
                          </span>
                        </div>
                        <div>
                          <p className="font-medium">{trainee.firstName} {trainee.lastName}</p>
                          <p className="text-sm text-gray-500">{trainee.email}</p>
                        </div>
                      </div>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        trainee.status === TraineeStatus.ACTIVE ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                      }`}>
                        {trainee.status}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Mentors/Staff List */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold mb-4">Mentors & Staff</h3>
                <div className="space-y-3">
                  {/* Mock mentors/staff data for now */}
                  {[].map((user: any) => (
                    <div key={user.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                          <span className="text-purple-600 font-semibold">
                            {user.firstName[0]}{user.lastName[0]}
                          </span>
                        </div>
                        <div>
                          <p className="font-medium">{user.firstName} {user.lastName}</p>
                          <p className="text-sm text-gray-500">{user.role}</p>
                        </div>
                      </div>
                      <button className="text-blue-600 hover:text-blue-800 text-sm">
                        View Profile
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );
      case 'school':
        return (
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Training Management</h2>
              <button
                onClick={exportProgramsData}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Export Programs Data
              </button>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
              <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6 rounded-lg">
                <h3 className="text-lg font-semibold mb-2">Active Sessions</h3>
                <p className="text-3xl font-bold">12</p>
              </div>
              <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-6 rounded-lg">
                <h3 className="text-lg font-semibold mb-2">Completed Modules</h3>
                <p className="text-3xl font-bold">48</p>
              </div>
              <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-6 rounded-lg">
                <h3 className="text-lg font-semibold mb-2">Average Score</h3>
                <p className="text-3xl font-bold">85%</p>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-4">Training Programs</h3>
              <div className="space-y-4">
                {state.programs.map(program => (
                  <div key={program.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-semibold">{program.title}</h4>
                        <p className="text-sm text-gray-600">{program.description}</p>
                      </div>
                      <span className={`px-3 py-1 text-sm rounded-full ${
                        program.status === ProgramStatus.ACTIVE ? 'bg-green-100 text-green-800' :
                        program.status === ProgramStatus.ARCHIVED ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {program.status}
                      </span>
                    </div>
                    <div className="mt-3 flex items-center space-x-4 text-sm text-gray-500">
                      <span>Duration: {program.duration} months</span>
                      <span>•</span>
                      <span>Trainees: {state.trainees.filter(t => t.programId === program.id).length}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );
      case 'assessment':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold mb-6">Assessment Center</h2>

            <div className="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-white p-4 rounded-lg shadow">
                <h3 className="text-sm font-medium text-gray-500">Pending Assessments</h3>
                <p className="text-2xl font-bold text-orange-600 mt-2">
                  {state.reviews.filter(r => r.status === ReviewStatus.SCHEDULED).length}
                </p>
              </div>
              <div className="bg-white p-4 rounded-lg shadow">
                <h3 className="text-sm font-medium text-gray-500">In Progress</h3>
                <p className="text-2xl font-bold text-blue-600 mt-2">
                  {state.reviews.filter(r => r.status === ReviewStatus.IN_PROGRESS).length}
                </p>
              </div>
              <div className="bg-white p-4 rounded-lg shadow">
                <h3 className="text-sm font-medium text-gray-500">Completed</h3>
                <p className="text-2xl font-bold text-green-600 mt-2">
                  {state.reviews.filter(r => r.status === ReviewStatus.COMPLETED).length}
                </p>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow">
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-4">Recent Assessments</h3>
                <div className="space-y-4">
                  {state.reviews.slice(0, 5).map(review => {
                    const trainee = state.trainees.find(t => t.id === review.traineeId);
                    return (
                      <div key={review.id} className="border rounded-lg p-4">
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="font-medium">
                              {trainee ? `${trainee.firstName} ${trainee.lastName}` : 'Unknown Trainee'}
                            </h4>
                            <p className="text-sm text-gray-600">
                              {review.type} - Quarter {review.quarter}
                            </p>
                            <div className="mt-2 flex items-center space-x-4 text-sm">
                              <span className="text-gray-500">
                                Overall Score: <span className="font-semibold">{review.overallScore || 'N/A'}</span>
                              </span>
                              <span className="text-gray-500">
                                Date: {new Date(review.scheduledDate).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                          <span className={`px-3 py-1 text-xs rounded-full ${
                            review.status === ReviewStatus.COMPLETED ? 'bg-green-100 text-green-800' :
                            review.status === ReviewStatus.IN_PROGRESS ? 'bg-blue-100 text-blue-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {review.status}
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  if (state.loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner isLoading={state.loading} />
      </div>
    );
  }

  if (state.error) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-2">Error</h2>
          <p className="text-gray-600">{state.error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="flex h-screen bg-gray-50">
        <Sidebar
          isOpen={sidebarOpen}
          onToggle={() => setSidebarOpen(!sidebarOpen)}
          activeView={activeView}
          onViewChange={(view) => handleViewChange(view as ViewType)}
          currentUser={state.currentUser!}
        />

        <div className="flex-1 flex flex-col overflow-hidden">
          <main className="flex-1 overflow-y-auto">
            <AnimatePresence mode="wait">
              <motion.div
                key={activeView}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="h-full"
              >
                {renderView()}
              </motion.div>
            </AnimatePresence>
          </main>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default GraduateTraineeTrackerRefactored;
export { GraduateTraineeTrackerRefactored as GraduateTraineeTracker };