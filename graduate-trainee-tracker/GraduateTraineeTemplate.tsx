"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import { MainLayout } from "./components/layout/main-layout"
import { DashboardPage } from "./pages/dashboard"
import { TraineesPage } from "./pages/trainees"
import { ProgramsPage } from "./pages/programs"
import { ReviewsPage } from "./pages/reviews"
import { ApprovalsPage } from "./pages/approvals"
import { Skeleton } from "@/components/ui/skeleton"
import { mockApi } from "./mockApi"
import {
  GraduateTraineeTrackerProps,
  GraduateTraineeTrackerState,
  User as UserType,
  UserRole
} from "./types"

const mockCurrentUser: UserType = {
  id: "user-001",
  employeeId: "EMP001",
  firstName: "<PERSON>",
  lastName: "Johnson",
  email: "<EMAIL>",
  role: UserRole.LD_OFFICER,
  department: "Learning & Development",
  permissions: [],
  isActive: true,
  createdAt: "2024-01-15T08:00:00Z",
  updatedAt: "2024-08-28T10:00:00Z"
}

export default function GraduateTraineeTemplate({
  currentUser = mockCurrentUser,
  onNavigate
}: GraduateTraineeTrackerProps) {
  const [activeView, setActiveView] = useState("dashboard")
  const [state, setState] = useState<GraduateTraineeTrackerState>({
    currentUser: currentUser,
    trainees: [],
    programs: [],
    mentors: [],
    reviews: [],
    workflows: [],
    notifications: [],
    competencies: [],
    loading: true,
    error: null,
    selectedTrainee: null,
    selectedProgram: null,
    filters: {
      status: [],
      department: [],
      program: [],
      mentor: [],
      dateRange: {
        startDate: "",
        endDate: ""
      }
    },
    pagination: {
      page: 1,
      pageSize: 10,
      total: 0
    }
  })

  useEffect(() => {
    const loadData = async () => {
      try {
        setState(prev => ({ ...prev, loading: true }))

        const [trainees, programs, mentors, reviews, workflows, notifications, competencies] = 
          await Promise.all([
            mockApi.fetchTrainees(),
            mockApi.fetchPrograms(),
            mockApi.fetchMentors(),
            mockApi.fetchReviews(),
            mockApi.fetchWorkflows(),
            mockApi.fetchNotifications(),
            mockApi.fetchCompetencies()
          ])

        setState(prev => ({
          ...prev,
          trainees,
          programs,
          mentors,
          reviews,
          workflows,
          notifications,
          competencies,
          loading: false,
          error: null
        }))
      } catch (error) {
        console.error("Failed to load data:", error)
        setState(prev => ({
          ...prev,
          loading: false,
          error: "Failed to load data. Please try again."
        }))
      }
    }

    loadData()
  }, [])

  const unreadNotifications = state.notifications.filter(n => !n.isRead).length

  if (state.loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="space-y-4">
          <Skeleton className="h-12 w-[250px]" />
          <Skeleton className="h-4 w-[200px]" />
        </div>
      </div>
    )
  }

  return (
    <MainLayout
      currentUser={currentUser}
      activeView={activeView}
      onViewChange={setActiveView}
      notifications={unreadNotifications}
    >
      {activeView === "dashboard" && <DashboardPage state={state} />}
      {activeView === "trainees" && <TraineesPage state={state} setState={setState} />}
      {activeView === "programs" && <ProgramsPage state={state} setState={setState} />}
      {activeView === "reviews" && <ReviewsPage state={state} setState={setState} />}
      {activeView === "approvals" && <ApprovalsPage state={state} setState={setState} />}
    </MainLayout>
  )
}

export { GraduateTraineeTemplate as GraduateTraineeTracker }