// Workflow Audit Utility - Identifies incomplete and problematic workflows
import {
  ApprovalWorkflow,
  ApprovalStatus,
  StageStatus,
  Trainee,
  TrainingProgram,
  QuarterlyReview,
  User,
  UserRole,
  ReviewStatus
} from '../types';

export interface WorkflowIssue {
  id: string;
  workflowId: string;
  type: 'missing_step' | 'overdue' | 'bottleneck' | 'validation_error' | 'incomplete_data' | 'approval_gap' | 'dependency_missing';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  affectedEntity: string; // traineeId, programId, etc.
  suggestedAction: string;
  createdAt: string;
  dueDate?: string;
  metadata?: Record<string, any>;
}

export interface WorkflowAuditResult {
  totalWorkflows: number;
  incompleteWorkflows: number;
  overdueWorkflows: number;
  bottlenecks: number;
  validationErrors: number;
  issues: WorkflowIssue[];
  summary: {
    criticalIssues: number;
    highPriorityIssues: number;
    mediumPriorityIssues: number;
    lowPriorityIssues: number;
  };
  recommendations: string[];
}

export interface WorkflowCompletionStatus {
  workflowId: string;
  traineeId: string;
  programId: string;
  completionPercentage: number;
  currentStage: number;
  totalStages: number;
  stagesCompleted: number;
  stagesPending: number;
  stagesOverdue: number;
  estimatedCompletionDate?: string;
  blockers: string[];
  nextActions: string[];
}

export class WorkflowAuditor {
  private workflows: ApprovalWorkflow[];
  private trainees: Trainee[];
  private programs: TrainingProgram[];
  private reviews: QuarterlyReview[];
  private users: User[];

  constructor(
    workflows: ApprovalWorkflow[],
    trainees: Trainee[],
    programs: TrainingProgram[],
    reviews: QuarterlyReview[],
    users: User[]
  ) {
    this.workflows = workflows;
    this.trainees = trainees;
    this.programs = programs;
    this.reviews = reviews;
    this.users = users;
  }

  /**
   * Performs a comprehensive audit of all workflows
   */
  public auditWorkflows(): WorkflowAuditResult {
    const issues: WorkflowIssue[] = [];
    const now = new Date();

    // Audit each workflow
    this.workflows.forEach(workflow => {
      issues.push(...this.auditSingleWorkflow(workflow, now));
    });

    // Audit for missing workflows
    issues.push(...this.auditMissingWorkflows());

    // Calculate summary statistics
    const summary = this.calculateSummary(issues);
    const recommendations = this.generateRecommendations(issues);

    return {
      totalWorkflows: this.workflows.length,
      incompleteWorkflows: this.workflows.filter(w => w.status === ApprovalStatus.PENDING || w.status === ApprovalStatus.IN_PROGRESS).length,
      overdueWorkflows: this.workflows.filter(w => this.isWorkflowOverdue(w, now)).length,
      bottlenecks: issues.filter(i => i.type === 'bottleneck').length,
      validationErrors: issues.filter(i => i.type === 'validation_error').length,
      issues,
      summary,
      recommendations
    };
  }

  /**
   * Audits a single workflow for issues
   */
  private auditSingleWorkflow(workflow: ApprovalWorkflow, now: Date): WorkflowIssue[] {
    const issues: WorkflowIssue[] = [];

    // Check for overdue workflow stages
    const overdueStages = workflow.stages.filter(stage => 
      stage.status === StageStatus.PENDING && this.isStageOverdue(stage, now)
    );
    
    overdueStages.forEach(stage => {
      issues.push({
        id: `overdue-${workflow.id}-${stage.id}`,
        workflowId: workflow.id,
        type: 'overdue',
        severity: 'high',
        title: 'Overdue Workflow Stage',
        description: `Stage "${stage.stageName}" is overdue`,
        affectedEntity: workflow.traineeId,
        suggestedAction: 'Escalate to supervisor or extend deadline',
        createdAt: now.toISOString(),
        metadata: { stageId: stage.id, stageName: stage.stageName }
      });
    });

    // Check for bottlenecks (stages stuck for too long)
    const bottleneckStages = this.identifyBottlenecks(workflow, now);
    bottleneckStages.forEach(stage => {
      issues.push({
        id: `bottleneck-${workflow.id}-${stage.id}`,
        workflowId: workflow.id,
        type: 'bottleneck',
        severity: 'medium',
        title: 'Workflow Bottleneck',
        description: `Stage "${stage.stageName}" has been pending for an extended period`,
        affectedEntity: workflow.traineeId,
        suggestedAction: 'Contact assigned approver or reassign stage',
        createdAt: now.toISOString(),
        metadata: { stageId: stage.id, daysPending: this.getDaysPending(stage, now) }
      });
    });

    // Check for missing approvers
    workflow.stages.forEach(stage => {
      if (!stage.approverId && stage.status === StageStatus.PENDING) {
        issues.push({
          id: `missing-approver-${workflow.id}-${stage.id}`,
          workflowId: workflow.id,
          type: 'approval_gap',
          severity: 'high',
          title: 'Missing Approver Assignment',
          description: `Stage "${stage.stageName}" has no assigned approver`,
          affectedEntity: workflow.traineeId,
          suggestedAction: 'Assign an appropriate approver based on role requirements',
          createdAt: now.toISOString(),
          metadata: { stageId: stage.id, requiredRole: stage.approverType }
        });
      }
    });

    // Check for validation errors
    const validationIssues = this.validateWorkflowData(workflow);
    issues.push(...validationIssues);

    // Check for incomplete dependencies
    const dependencyIssues = this.checkWorkflowDependencies(workflow);
    issues.push(...dependencyIssues);

    return issues;
  }

  /**
   * Identifies missing workflows that should exist
   */
  private auditMissingWorkflows(): WorkflowIssue[] {
    const issues: WorkflowIssue[] = [];
    const now = new Date();

    // Check for trainees without required approval workflows
    this.trainees.forEach(trainee => {
      const traineeWorkflows = this.workflows.filter(w => w.traineeId === trainee.id);
      
      // Check if trainee needs quarterly review approval
      const hasRecentReview = this.reviews.some(review => 
        review.traineeId === trainee.id && 
        this.isRecentReview(review, now)
      );

      if (!hasRecentReview && trainee.status === TraineeStatus.ACTIVE) {
        const hasReviewWorkflow = traineeWorkflows.some(w => 
          w.stages.some(stage => stage.stageName.toLowerCase().includes('review'))
        );
        if (!hasReviewWorkflow) {
          issues.push({
            id: `missing-review-workflow-${trainee.id}`,
            workflowId: '',
            type: 'missing_step',
            severity: 'medium',
            title: 'Missing Quarterly Review Workflow',
            description: `Trainee ${trainee.firstName} ${trainee.lastName} is missing a quarterly review workflow`,
            affectedEntity: trainee.id,
            suggestedAction: 'Create quarterly review workflow for trainee',
            createdAt: now.toISOString()
          });
        }
      }

      // Check for program completion workflows
      const program = this.programs.find(p => p.id === trainee.programId);
      if (program && this.isNearProgramEnd(trainee, program, now)) {
        const hasCompletionWorkflow = traineeWorkflows.some(w => 
          w.stages.some(stage => stage.stageName.toLowerCase().includes('completion'))
        );
        if (!hasCompletionWorkflow) {
          issues.push({
            id: `missing-completion-workflow-${trainee.id}`,
            workflowId: '',
            type: 'missing_step',
            severity: 'high',
            title: 'Missing Program Completion Workflow',
            description: `Trainee is nearing program end but has no completion workflow`,
            affectedEntity: trainee.id,
            suggestedAction: 'Create program completion workflow',
            createdAt: now.toISOString()
          });
        }
      }
    });

    return issues;
  }

  /**
   * Validates workflow data integrity
   */
  private validateWorkflowData(workflow: ApprovalWorkflow): WorkflowIssue[] {
    const issues: WorkflowIssue[] = [];
    const now = new Date();

    // Check if trainee exists
    const trainee = this.trainees.find(t => t.id === workflow.traineeId);
    if (!trainee) {
      issues.push({
        id: `invalid-trainee-${workflow.id}`,
        workflowId: workflow.id,
        type: 'validation_error',
        severity: 'critical',
        title: 'Invalid Trainee Reference',
        description: 'Workflow references a non-existent trainee',
        affectedEntity: workflow.traineeId,
        suggestedAction: 'Update workflow with valid trainee ID or remove workflow',
        createdAt: now.toISOString()
      });
    }

    // Check if program exists
    const program = this.programs.find(p => p.id === workflow.programId);
    if (!program) {
      issues.push({
        id: `invalid-program-${workflow.id}`,
        workflowId: workflow.id,
        type: 'validation_error',
        severity: 'high',
        title: 'Invalid Program Reference',
        description: 'Workflow references a non-existent program',
        affectedEntity: workflow.programId,
        suggestedAction: 'Update workflow with valid program ID',
        createdAt: now.toISOString()
      });
    }

    // Check stage sequence integrity
    const stageNumbers = workflow.stages.map(s => s.stageNumber).sort((a, b) => a - b);
    for (let i = 0; i < stageNumbers.length; i++) {
      if (stageNumbers[i] !== i + 1) {
        issues.push({
          id: `invalid-stage-sequence-${workflow.id}`,
          workflowId: workflow.id,
          type: 'validation_error',
          severity: 'medium',
          title: 'Invalid Stage Sequence',
          description: 'Workflow stages are not properly sequenced',
          affectedEntity: workflow.traineeId,
          suggestedAction: 'Reorder workflow stages to ensure proper sequence',
          createdAt: now.toISOString()
        });
        break;
      }
    }

    return issues;
  }

  /**
   * Checks workflow dependencies
   */
  private checkWorkflowDependencies(workflow: ApprovalWorkflow): WorkflowIssue[] {
    const issues: WorkflowIssue[] = [];
    const now = new Date();

    workflow.stages.forEach(stage => {
      // Check if assigned approver exists and is active
      if (stage.approverId) {
        const approver = this.users.find(u => u.id === stage.approverId);
        if (!approver) {
          issues.push({
            id: `missing-approver-${workflow.id}-${stage.id}`,
            workflowId: workflow.id,
            type: 'dependency_missing',
            severity: 'high',
            title: 'Missing Approver',
            description: `Stage "${stage.stageName}" references non-existent approver`,
            affectedEntity: workflow.traineeId,
            suggestedAction: 'Assign a valid approver to the stage',
            createdAt: now.toISOString(),
            metadata: { stageId: stage.id, missingApproverId: stage.approverId }
          });
        } else if (!approver.isActive) {
          issues.push({
            id: `inactive-approver-${workflow.id}-${stage.id}`,
            workflowId: workflow.id,
            type: 'dependency_missing',
            severity: 'medium',
            title: 'Inactive Approver',
            description: `Stage "${stage.stageName}" is assigned to inactive user ${approver.firstName} ${approver.lastName}`,
            affectedEntity: workflow.traineeId,
            suggestedAction: 'Reassign stage to an active approver',
            createdAt: now.toISOString(),
            metadata: { stageId: stage.id, inactiveApproverId: stage.approverId }
          });
        }
      }
    });

    return issues;
  }

  /**
   * Gets completion status for a specific workflow
   */
  public getWorkflowCompletionStatus(workflowId: string): WorkflowCompletionStatus | null {
    const workflow = this.workflows.find(w => w.id === workflowId);
    if (!workflow) return null;

    const completedStages = workflow.stages.filter(s => s.status === StageStatus.APPROVED).length;
    const pendingStages = workflow.stages.filter(s => s.status === StageStatus.PENDING).length;
    const overdueStages = workflow.stages.filter(s => 
      s.status === StageStatus.PENDING && this.isStageOverdue(s, new Date())
    ).length;

    const completionPercentage = (completedStages / workflow.stages.length) * 100;

    const blockers: string[] = [];
    const nextActions: string[] = [];

    // Identify current blockers
    const currentStage = workflow.stages.find(s => s.stageNumber === workflow.currentStage);
    if (currentStage) {
      if (!currentStage.approverId) {
        blockers.push('No approver assigned to current stage');
        nextActions.push('Assign approver to current stage');
      }
      
      const approver = this.users.find(u => u.id === currentStage.approverId);
      if (approver && !approver.isActive) {
        blockers.push('Assigned approver is inactive');
        nextActions.push('Reassign to active approver');
      }
    }

    return {
      workflowId: workflow.id,
      traineeId: workflow.traineeId,
      programId: workflow.programId,
      completionPercentage,
      currentStage: workflow.currentStage,
      totalStages: workflow.stages.length,
      stagesCompleted: completedStages,
      stagesPending: pendingStages,
      stagesOverdue: overdueStages,
      estimatedCompletionDate: this.estimateCompletionDate(workflow),
      blockers,
      nextActions
    };
  }

  /**
   * Helper methods
   */
  private isStageOverdue(stage: ApprovalStage, now: Date): boolean {
    // Check if stage has been pending for more than 14 days (default threshold)
    const OVERDUE_THRESHOLD_DAYS = 14;
    const stageStartDate = new Date(stage.approvedAt || stage.rejectedAt || now.toISOString());
    const daysPending = Math.ceil((now.getTime() - stageStartDate.getTime()) / (1000 * 60 * 60 * 24));
    return stage.status === StageStatus.PENDING && daysPending > OVERDUE_THRESHOLD_DAYS;
  }

  private getDaysPastDue(stage: ApprovalStage, now: Date): number {
    const stageDate = new Date(stage.approvedAt || stage.rejectedAt || now.toISOString());
    return Math.ceil((now.getTime() - stageDate.getTime()) / (1000 * 60 * 60 * 24));
  }

  private identifyBottlenecks(workflow: ApprovalWorkflow, now: Date) {
    const BOTTLENECK_THRESHOLD_DAYS = 7;
    return workflow.stages.filter(stage => {
      if (stage.status !== StageStatus.PENDING) return false;
      
      const stageStartDate = new Date(stage.approvedAt || stage.rejectedAt || workflow.initiatedAt);
      const daysPending = Math.ceil((now.getTime() - stageStartDate.getTime()) / (1000 * 60 * 60 * 24));
      
      return daysPending > BOTTLENECK_THRESHOLD_DAYS;
    });
  }

  private getDaysPending(stage: ApprovalStage, now: Date): number {
    const startDate = new Date(stage.approvedAt || stage.rejectedAt || new Date().toISOString());
    return Math.ceil((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  }

  private isRecentReview(review: QuarterlyReview, now: Date): boolean {
    const RECENT_THRESHOLD_DAYS = 90;
    const reviewDate = new Date(review.completedDate || review.scheduledDate);
    const daysSince = Math.ceil((now.getTime() - reviewDate.getTime()) / (1000 * 60 * 60 * 24));
    return daysSince <= RECENT_THRESHOLD_DAYS;
  }

  private isNearProgramEnd(trainee: Trainee, program: TrainingProgram, now: Date): boolean {
    const NEAR_END_THRESHOLD_DAYS = 30;
    const endDate = new Date(trainee.endDate);
    const daysUntilEnd = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilEnd <= NEAR_END_THRESHOLD_DAYS && daysUntilEnd > 0;
  }

  private estimateCompletionDate(workflow: ApprovalWorkflow): string | undefined {
    const remainingStages = workflow.stages.filter(s => s.status === StageStatus.PENDING).length;
    if (remainingStages === 0) return undefined;

    const AVERAGE_STAGE_DAYS = 5;
    const estimatedDays = remainingStages * AVERAGE_STAGE_DAYS;
    const estimatedDate = new Date();
    estimatedDate.setDate(estimatedDate.getDate() + estimatedDays);
    
    return estimatedDate.toISOString();
  }

  private calculateSummary(issues: WorkflowIssue[]) {
    return {
      criticalIssues: issues.filter(i => i.severity === 'critical').length,
      highPriorityIssues: issues.filter(i => i.severity === 'high').length,
      mediumPriorityIssues: issues.filter(i => i.severity === 'medium').length,
      lowPriorityIssues: issues.filter(i => i.severity === 'low').length
    };
  }

  private generateRecommendations(issues: WorkflowIssue[]): string[] {
    const recommendations: string[] = [];
    
    const criticalCount = issues.filter(i => i.severity === 'critical').length;
    const overdueCount = issues.filter(i => i.type === 'overdue').length;
    const bottleneckCount = issues.filter(i => i.type === 'bottleneck').length;
    
    if (criticalCount > 0) {
      recommendations.push(`Address ${criticalCount} critical issues immediately to prevent system failures`);
    }
    
    if (overdueCount > 0) {
      recommendations.push(`Escalate ${overdueCount} overdue workflows to prevent further delays`);
    }
    
    if (bottleneckCount > 0) {
      recommendations.push(`Review ${bottleneckCount} bottlenecks and consider reassigning or escalating`);
    }
    
    if (issues.filter(i => i.type === 'missing_step').length > 0) {
      recommendations.push('Implement automated workflow creation for standard processes');
    }
    
    if (issues.filter(i => i.type === 'approval_gap').length > 0) {
      recommendations.push('Review approver assignment process and ensure backup approvers are designated');
    }
    
    return recommendations;
  }
}

/**
 * Convenience function to perform workflow audit
 */
export const auditWorkflows = (
  workflows: ApprovalWorkflow[],
  trainees: Trainee[],
  programs: TrainingProgram[],
  reviews: QuarterlyReview[],
  users: User[]
): WorkflowAuditResult => {
  const auditor = new WorkflowAuditor(workflows, trainees, programs, reviews, users);
  return auditor.auditWorkflows();
};

/**
 * Get completion status for all workflows
 */
export const getWorkflowCompletionStatuses = (
  workflows: ApprovalWorkflow[],
  trainees: Trainee[],
  programs: TrainingProgram[],
  reviews: QuarterlyReview[],
  users: User[]
): WorkflowCompletionStatus[] => {
  const auditor = new WorkflowAuditor(workflows, trainees, programs, reviews, users);
  return workflows.map(w => auditor.getWorkflowCompletionStatus(w.id)).filter(Boolean) as WorkflowCompletionStatus[];
};