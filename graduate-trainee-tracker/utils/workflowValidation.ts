// Workflow Validation System - Comprehensive validation with compliance checks
import {
  ApprovalWorkflow,
  ApprovalStage,
  ApprovalStatus,
  StageStatus,
  Trainee,
  TrainingProgram,
  QuarterlyReview,
  User
} from '../types';

export interface ValidationRule {
  id: string;
  name: string;
  description: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  category: 'compliance' | 'process' | 'data' | 'timing' | 'approval';
  validate: (context: ValidationContext) => ValidationResult;
}

export interface ValidationContext {
  workflow: ApprovalWorkflow;
  trainee?: Trainee;
  program?: TrainingProgram;
  reviews?: QuarterlyReview[];
  users?: User[];
  allWorkflows?: ApprovalWorkflow[];
}

export interface ValidationResult {
  isValid: boolean;
  message?: string;
  suggestedAction?: string;
  metadata?: Record<string, any>;
}

export interface WorkflowValidationReport {
  workflowId: string;
  traineeName: string;
  programName: string;
  overallStatus: 'valid' | 'warning' | 'error';
  validationResults: {
    ruleId: string;
    ruleName: string;
    severity: string;
    category: string;
    isValid: boolean;
    message?: string;
    suggestedAction?: string;
  }[];
  summary: {
    totalRules: number;
    passedRules: number;
    failedRules: number;
    criticalIssues: number;
    highPriorityIssues: number;
    mediumPriorityIssues: number;
    lowPriorityIssues: number;
  };
  complianceScore: number;
  recommendations: string[];
}

export interface ComplianceCheckResult {
  workflowId: string;
  complianceStatus: 'compliant' | 'non-compliant' | 'partial';
  requiredApprovals: string[];
  missingApprovals: string[];
  documentationComplete: boolean;
  timelineCompliant: boolean;
  auditTrailComplete: boolean;
  issues: string[];
  recommendations: string[];
}

// Validation Rules Registry
export class WorkflowValidationRules {
  private static rules: ValidationRule[] = [
    // Compliance Rules
    {
      id: 'required-stages',
      name: 'Required Stages Present',
      description: 'Workflow must contain all required approval stages',
      severity: 'critical',
      category: 'compliance',
      validate: (context) => {
        const { workflow, program } = context;
        const requiredStages = ['initial_review', 'supervisor_approval', 'hr_approval'];
        
        // Add additional stages based on program requirements
        requiredStages.push('mentor_assignment', 'final_approval');
        
        const workflowStageNames = workflow.stages.map(s => s.stageName.toLowerCase().replace(/\s+/g, '_'));
        const missingStages = requiredStages.filter(stage => 
          !workflowStageNames.some(wsn => wsn.includes(stage.replace('_', '')))
        );
        
        return {
          isValid: missingStages.length === 0,
          message: missingStages.length > 0 
            ? `Missing required stages: ${missingStages.join(', ')}` 
            : 'All required stages present',
          suggestedAction: missingStages.length > 0 
            ? `Add missing stages: ${missingStages.join(', ')}` 
            : undefined
        };
      }
    },
    
    {
      id: 'stage-sequence',
      name: 'Stage Sequence Validation',
      description: 'Workflow stages must follow correct sequence',
      severity: 'high',
      category: 'process',
      validate: (context) => {
        const { workflow } = context;
        const stages = workflow.stages.sort((a, b) => a.stageNumber - b.stageNumber);
        
        // Check for gaps in stage numbers
        const gaps = [];
        for (let i = 1; i < stages.length; i++) {
          if (stages[i].stageNumber !== stages[i-1].stageNumber + 1) {
            gaps.push(`Gap between stage ${stages[i-1].stageNumber} and ${stages[i].stageNumber}`);
          }
        }
        
        // Check for duplicate stage numbers
        const stageNumbers = stages.map(s => s.stageNumber);
        const duplicates = stageNumbers.filter((num, index) => stageNumbers.indexOf(num) !== index);
        
        const issues = [...gaps, ...duplicates.map(d => `Duplicate stage number: ${d}`)];
        
        return {
          isValid: issues.length === 0,
          message: issues.length > 0 ? issues.join('; ') : 'Stage sequence is valid',
          suggestedAction: issues.length > 0 ? 'Renumber stages to ensure proper sequence' : undefined
        };
      }
    },
    
    {
      id: 'approver-assignment',
      name: 'Approver Assignment Validation',
      description: 'All stages must have valid approvers assigned',
      severity: 'critical',
      category: 'approval',
      validate: (context) => {
        const { workflow, users } = context;
        const unassignedStages = workflow.stages.filter(stage => 
          !stage.approverId || stage.approverId.trim() === ''
        );
        
        const invalidApprovers = workflow.stages.filter(stage => {
          if (!stage.approverId) return false;
          return users && !users.some(user => user.id === stage.approverId);
        });
        
        const issues = [];
        if (unassignedStages.length > 0) {
          issues.push(`Unassigned stages: ${unassignedStages.map(s => s.stageName).join(', ')}`);
        }
        if (invalidApprovers.length > 0) {
          issues.push(`Invalid approvers: ${invalidApprovers.map(s => s.stageName).join(', ')}`);
        }
        
        return {
          isValid: issues.length === 0,
          message: issues.length > 0 ? issues.join('; ') : 'All approvers properly assigned',
          suggestedAction: issues.length > 0 ? 'Assign valid approvers to all stages' : undefined
        };
      }
    },
    
    {
      id: 'timeline-compliance',
      name: 'Timeline Compliance Check',
      description: 'Workflow must meet timeline requirements',
      severity: 'medium',
      category: 'timing',
      validate: (context) => {
        const { workflow } = context;
        const now = new Date();
        const initiatedDate = new Date(workflow.initiatedAt);
        const daysSinceInitiation = Math.floor((now.getTime() - initiatedDate.getTime()) / (1000 * 60 * 60 * 24));
        
        // Standard timeline: 30 days for completion
        const standardTimelineDays = 30;
        const isOverdue = daysSinceInitiation > standardTimelineDays && workflow.status !== ApprovalStatus.APPROVED;
        
        // Check individual stage timelines
        const overdueStages = workflow.stages.filter(stage => {
          if (stage.status === StageStatus.APPROVED) return false;
          
          // Assume each stage should take max 7 days
          const stageStartDate = stage.approvedAt ? new Date(stage.approvedAt) : initiatedDate;
          const daysSinceStageStart = Math.floor((now.getTime() - stageStartDate.getTime()) / (1000 * 60 * 60 * 24));
          return daysSinceStageStart > 7;
        });
        
        const issues = [];
        if (isOverdue) {
          issues.push(`Workflow overdue by ${daysSinceInitiation - standardTimelineDays} days`);
        }
        if (overdueStages.length > 0) {
          issues.push(`Overdue stages: ${overdueStages.map(s => s.stageName).join(', ')}`);
        }
        
        return {
          isValid: issues.length === 0,
          message: issues.length > 0 ? issues.join('; ') : 'Timeline compliance maintained',
          suggestedAction: issues.length > 0 ? 'Escalate overdue items and review timeline' : undefined,
          metadata: { daysSinceInitiation, overdueStages: overdueStages.length }
        };
      }
    },
    
    {
      id: 'data-completeness',
      name: 'Data Completeness Check',
      description: 'All required data fields must be populated',
      severity: 'high',
      category: 'data',
      validate: (context) => {
        const { workflow, trainee, program } = context;
        const missingData = [];
        
        if (!trainee) {
          missingData.push('Trainee information');
        } else {
          if (!trainee.email || trainee.email.trim() === '') missingData.push('Trainee email');
          if (!trainee.department || trainee.department.trim() === '') missingData.push('Trainee department');
          // Note: supervisor field not available in current Trainee interface
        }
        
        if (!program) {
          missingData.push('Program information');
        } else {
          if (!program.title || program.title.trim() === '') missingData.push('Program title');
          if (!program.duration) missingData.push('Program duration');
        }
        
        if (!workflow.initiatedBy || workflow.initiatedBy.trim() === '') {
          missingData.push('Workflow initiator');
        }
        
        return {
          isValid: missingData.length === 0,
          message: missingData.length > 0 
            ? `Missing data: ${missingData.join(', ')}` 
            : 'All required data present',
          suggestedAction: missingData.length > 0 
            ? 'Complete missing data fields before proceeding' 
            : undefined
        };
      }
    },
    
    {
      id: 'approval-authority',
      name: 'Approval Authority Validation',
      description: 'Approvers must have appropriate authority for their stages',
      severity: 'critical',
      category: 'approval',
      validate: (context) => {
        const { workflow, users } = context;
        const unauthorizedApprovals = [];
        
        workflow.stages.forEach(stage => {
          if (!stage.approverId || !users) return;
          
          const approver = users.find(u => u.id === stage.approverId);
          if (!approver) return;
          
          // Check role-based authority
          const stageType = stage.stageName.toLowerCase();
          if (stageType.includes('hr') && approver.role !== UserRole.LD_OFFICER && approver.role !== UserRole.ADMIN) {
            unauthorizedApprovals.push(`${stage.stageName}: ${approver.firstName} ${approver.lastName} lacks HR authority`);
          }
          if (stageType.includes('supervisor') && approver.role !== UserRole.MANAGER && approver.role !== UserRole.ADMIN) {
            unauthorizedApprovals.push(`${stage.stageName}: ${approver.firstName} ${approver.lastName} lacks supervisory authority`);
          }
          if (stageType.includes('final') && approver.role !== UserRole.MANAGER && approver.role !== UserRole.ADMIN) {
            unauthorizedApprovals.push(`${stage.stageName}: ${approver.firstName} ${approver.lastName} lacks final approval authority`);
          }
        });
        
        return {
          isValid: unauthorizedApprovals.length === 0,
          message: unauthorizedApprovals.length > 0 
            ? unauthorizedApprovals.join('; ') 
            : 'All approvers have appropriate authority',
          suggestedAction: unauthorizedApprovals.length > 0 
            ? 'Reassign stages to approvers with appropriate authority' 
            : undefined
        };
      }
    },
    
    {
      id: 'documentation-requirements',
      name: 'Documentation Requirements',
      description: 'Required documentation must be present',
      severity: 'medium',
      category: 'compliance',
      validate: (context) => {
        const { workflow, reviews } = context;
        const missingDocs = [];
        
        // Check for quarterly reviews if workflow is in progress for more than 3 months
        const initiatedDate = new Date(workflow.initiatedAt);
        const monthsSinceInitiation = Math.floor((Date.now() - initiatedDate.getTime()) / (1000 * 60 * 60 * 24 * 30));
        
        if (monthsSinceInitiation >= 3) {
          const hasRecentReview = reviews && reviews.some(review => {
            const reviewDate = new Date(review.completedDate || review.scheduledDate);
            const monthsSinceReview = Math.floor((Date.now() - reviewDate.getTime()) / (1000 * 60 * 60 * 24 * 30));
            return monthsSinceReview <= 3;
          });
          
          if (!hasRecentReview) {
            missingDocs.push('Recent quarterly review (required after 3 months)');
          }
        }
        
        // Check for stage-specific documentation
        workflow.stages.forEach(stage => {
          if (stage.status === StageStatus.APPROVED && (!stage.comments || stage.comments.trim() === '')) {
            missingDocs.push(`Comments for completed stage: ${stage.stageName}`);
          }
        });
        
        return {
          isValid: missingDocs.length === 0,
          message: missingDocs.length > 0 
            ? `Missing documentation: ${missingDocs.join(', ')}` 
            : 'All required documentation present',
          suggestedAction: missingDocs.length > 0 
            ? 'Complete missing documentation requirements' 
            : undefined
        };
      }
    },
    
    {
      id: 'conflict-of-interest',
      name: 'Conflict of Interest Check',
      description: 'Detect potential conflicts of interest in approval chain',
      severity: 'high',
      category: 'compliance',
      validate: (context) => {
        const { workflow, trainee, users } = context;
        const conflicts = [];
        
        if (!trainee || !users) {
          return { isValid: true, message: 'Insufficient data for conflict check' };
        }
        
        // Check if trainee is approving their own workflow
        const traineeAsApprover = workflow.stages.find(stage => stage.approverId === trainee.id);
        if (traineeAsApprover) {
          conflicts.push(`Trainee cannot approve their own workflow at stage: ${traineeAsApprover.stageName}`);
        }
        
        // Check for same person approving multiple consecutive stages
        const sortedStages = workflow.stages.sort((a, b) => a.stageNumber - b.stageNumber);
        for (let i = 1; i < sortedStages.length; i++) {
          if (sortedStages[i].approverId === sortedStages[i-1].approverId && 
              sortedStages[i].approverId) {
            const approver = users.find(u => u.id === sortedStages[i].approverId);
            const approverName = approver ? `${approver.firstName} ${approver.lastName}` : 'Unknown';
            conflicts.push(`Same approver (${approverName}) for consecutive stages: ${sortedStages[i-1].stageName} and ${sortedStages[i].stageName}`);
          }
        }
        
        return {
          isValid: conflicts.length === 0,
          message: conflicts.length > 0 
            ? conflicts.join('; ') 
            : 'No conflicts of interest detected',
          suggestedAction: conflicts.length > 0 
            ? 'Reassign conflicting approvers to maintain independence' 
            : undefined
        };
      }
    }
  ];
  
  static getAllRules(): ValidationRule[] {
    return [...this.rules];
  }
  
  static getRulesByCategory(category: string): ValidationRule[] {
    return this.rules.filter(rule => rule.category === category);
  }
  
  static getRulesBySeverity(severity: string): ValidationRule[] {
    return this.rules.filter(rule => rule.severity === severity);
  }
  
  static addCustomRule(rule: ValidationRule): void {
    this.rules.push(rule);
  }
}

// Workflow Validation Engine
export class WorkflowValidator {
  private rules: ValidationRule[];
  
  constructor(customRules: ValidationRule[] = []) {
    this.rules = [...WorkflowValidationRules.getAllRules(), ...customRules];
  }
  
  validateWorkflow(
    workflow: ApprovalWorkflow,
    trainee?: Trainee,
    program?: TrainingProgram,
    reviews?: QuarterlyReview[],
    users?: User[],
    allWorkflows?: ApprovalWorkflow[]
  ): WorkflowValidationReport {
    const context: ValidationContext = {
      workflow,
      trainee,
      program,
      reviews,
      users,
      allWorkflows
    };
    
    const validationResults = this.rules.map(rule => {
      try {
        const result = rule.validate(context);
        return {
          ruleId: rule.id,
          ruleName: rule.name,
          severity: rule.severity,
          category: rule.category,
          isValid: result.isValid,
          message: result.message,
          suggestedAction: result.suggestedAction
        };
      } catch (error) {
        return {
          ruleId: rule.id,
          ruleName: rule.name,
          severity: rule.severity,
          category: rule.category,
          isValid: false,
          message: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`,
          suggestedAction: 'Review validation rule implementation'
        };
      }
    });
    
    const failedResults = validationResults.filter(r => !r.isValid);
    const summary = {
      totalRules: validationResults.length,
      passedRules: validationResults.length - failedResults.length,
      failedRules: failedResults.length,
      criticalIssues: failedResults.filter(r => r.severity === 'critical').length,
      highPriorityIssues: failedResults.filter(r => r.severity === 'high').length,
      mediumPriorityIssues: failedResults.filter(r => r.severity === 'medium').length,
      lowPriorityIssues: failedResults.filter(r => r.severity === 'low').length
    };
    
    const complianceScore = Math.round((summary.passedRules / summary.totalRules) * 100);
    
    const overallStatus: 'valid' | 'warning' | 'error' = 
      summary.criticalIssues > 0 ? 'error' :
      summary.highPriorityIssues > 0 ? 'warning' : 'valid';
    
    const recommendations = this.generateRecommendations(failedResults);
    
    return {
      workflowId: workflow.id,
      traineeName: trainee ? `${trainee.firstName} ${trainee.lastName}` : 'Unknown',
      programName: program?.title || 'Unknown',
      overallStatus,
      validationResults,
      summary,
      complianceScore,
      recommendations
    };
  }
  
  validateMultipleWorkflows(
    workflows: ApprovalWorkflow[],
    trainees: Trainee[],
    programs: TrainingProgram[],
    reviews: QuarterlyReview[],
    users: User[]
  ): WorkflowValidationReport[] {
    return workflows.map(workflow => {
      const trainee = trainees.find(t => t.id === workflow.traineeId);
      const program = programs.find(p => p.id === workflow.programId);
      const traineeReviews = reviews.filter(r => r.traineeId === workflow.traineeId);
      
      return this.validateWorkflow(workflow, trainee, program, traineeReviews, users, workflows);
    });
  }
  
  checkCompliance(
    workflow: ApprovalWorkflow,
    trainee?: Trainee,
    program?: TrainingProgram,
    users?: User[]
  ): ComplianceCheckResult {
    const requiredApprovals = ['supervisor', 'hr', 'final'];
    const workflowApprovals = workflow.stages
      .filter(s => s.status === StageStatus.APPROVED)
      .map(s => s.stageName.toLowerCase());
    
    const missingApprovals = requiredApprovals.filter(approval => 
      !workflowApprovals.some(wa => wa.includes(approval))
    );
    
    const documentationComplete = workflow.stages
      .filter(s => s.status === StageStatus.APPROVED)
      .every(s => s.comments && s.comments.trim() !== '');
    
    const timelineCompliant = this.checkTimelineCompliance(workflow);
    const auditTrailComplete = this.checkAuditTrail(workflow);
    
    const issues = [];
    if (missingApprovals.length > 0) {
      issues.push(`Missing approvals: ${missingApprovals.join(', ')}`);
    }
    if (!documentationComplete) {
      issues.push('Incomplete documentation for completed stages');
    }
    if (!timelineCompliant) {
      issues.push('Timeline compliance issues detected');
    }
    if (!auditTrailComplete) {
      issues.push('Incomplete audit trail');
    }
    
    const complianceStatus: 'compliant' | 'non-compliant' | 'partial' = 
      issues.length === 0 ? 'compliant' :
      issues.length >= 3 ? 'non-compliant' : 'partial';
    
    const recommendations = [];
    if (missingApprovals.length > 0) {
      recommendations.push('Complete all required approval stages');
    }
    if (!documentationComplete) {
      recommendations.push('Add comments to all completed stages');
    }
    if (!timelineCompliant) {
      recommendations.push('Review and address timeline delays');
    }
    
    return {
      workflowId: workflow.id,
      complianceStatus,
      requiredApprovals,
      missingApprovals,
      documentationComplete,
      timelineCompliant,
      auditTrailComplete,
      issues,
      recommendations
    };
  }
  
  private generateRecommendations(failedResults: any[]): string[] {
    const recommendations = new Set<string>();
    
    failedResults.forEach(result => {
      if (result.suggestedAction) {
        recommendations.add(result.suggestedAction);
      }
    });
    
    // Add general recommendations based on failure patterns
    const criticalFailures = failedResults.filter(r => r.severity === 'critical');
    if (criticalFailures.length > 0) {
      recommendations.add('Address critical issues immediately to ensure workflow integrity');
    }
    
    const complianceFailures = failedResults.filter(r => r.category === 'compliance');
    if (complianceFailures.length > 0) {
      recommendations.add('Review compliance requirements and ensure all policies are followed');
    }
    
    return Array.from(recommendations);
  }
  
  private checkTimelineCompliance(workflow: ApprovalWorkflow): boolean {
    const now = new Date();
    const initiatedDate = new Date(workflow.initiatedAt);
    const daysSinceInitiation = Math.floor((now.getTime() - initiatedDate.getTime()) / (1000 * 60 * 60 * 24));
    
    // Standard timeline: 30 days for completion
    return daysSinceInitiation <= 30 || workflow.status === ApprovalStatus.APPROVED;
  }
  
  private checkAuditTrail(workflow: ApprovalWorkflow): boolean {
    // Check if all completed stages have proper audit information
    return workflow.stages
      .filter(s => s.status === StageStatus.APPROVED)
      .every(s => s.approvedAt && s.approverId);
  }
}

// Export validation utilities
export const validateWorkflow = (
  workflow: ApprovalWorkflow,
  trainee?: Trainee,
  program?: TrainingProgram,
  reviews?: QuarterlyReview[],
  users?: User[],
  customRules?: ValidationRule[]
): WorkflowValidationReport => {
  const validator = new WorkflowValidator(customRules);
  return validator.validateWorkflow(workflow, trainee, program, reviews, users);
};

export const validateMultipleWorkflows = (
  workflows: ApprovalWorkflow[],
  trainees: Trainee[],
  programs: TrainingProgram[],
  reviews: QuarterlyReview[],
  users: User[],
  customRules?: ValidationRule[]
): WorkflowValidationReport[] => {
  const validator = new WorkflowValidator(customRules);
  return validator.validateMultipleWorkflows(workflows, trainees, programs, reviews, users);
};

export const checkWorkflowCompliance = (
  workflow: ApprovalWorkflow,
  trainee?: Trainee,
  program?: TrainingProgram,
  users?: User[]
): ComplianceCheckResult => {
  const validator = new WorkflowValidator();
  return validator.checkCompliance(workflow, trainee, program, users);
};