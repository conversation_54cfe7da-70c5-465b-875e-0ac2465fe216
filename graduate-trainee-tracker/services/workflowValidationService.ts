import { ApprovalWorkflow, Trainee, TrainingProgram, User, QuarterlyReview } from '../types';
import { 
  validateWorkflow, 
  checkWorkflowCompliance, 
  WorkflowValidationReport,
  ComplianceCheckResult 
} from '../utils/workflowValidation';
import { auditWorkflows, WorkflowAuditResult } from '../utils/workflowAudit';

export interface ValidationServiceConfig {
  enableStrictValidation: boolean;
  enableComplianceChecks: boolean;
  enableAuditChecks: boolean;
  customRules?: ValidationRule[];
}

export interface ValidationRule {
  id: string;
  name: string;
  description: string;
  severity: 'error' | 'warning' | 'info';
  validate: (workflow: ApprovalWorkflow, context: ValidationContext) => ValidationResult;
}

export interface ValidationContext {
  trainee?: Trainee;
  program?: TrainingProgram;
  users: User[];
  reviews?: QuarterlyReview[];
  allWorkflows: ApprovalWorkflow[];
}

export interface ValidationResult {
  isValid: boolean;
  message: string;
  severity: 'error' | 'warning' | 'info';
  ruleId: string;
}

export interface ComprehensiveValidationResult {
  workflowId: string;
  isValid: boolean;
  hasErrors: boolean;
  hasWarnings: boolean;
  validationReport?: WorkflowValidationReport;
  complianceReport?: ComplianceCheckResult;
  auditReport?: WorkflowAuditResult;
  customValidationResults: ValidationResult[];
  summary: {
    totalIssues: number;
    errorCount: number;
    warningCount: number;
    infoCount: number;
  };
}

export class WorkflowValidationService {
  private config: ValidationServiceConfig;
  private customRules: ValidationRule[];

  constructor(config: ValidationServiceConfig = {
    enableStrictValidation: true,
    enableComplianceChecks: true,
    enableAuditChecks: true
  }) {
    this.config = config;
    this.customRules = config.customRules || [];
  }

  /**
   * Performs comprehensive validation of a workflow
   */
  async validateWorkflowComprehensive(
    workflow: ApprovalWorkflow,
    context: ValidationContext
  ): Promise<ComprehensiveValidationResult> {
    const results: ComprehensiveValidationResult = {
      workflowId: workflow.id,
      isValid: true,
      hasErrors: false,
      hasWarnings: false,
      customValidationResults: [],
      summary: {
        totalIssues: 0,
        errorCount: 0,
        warningCount: 0,
        infoCount: 0
      }
    };

    try {
      // Standard workflow validation
      if (this.config.enableStrictValidation) {
        results.validationReport = validateWorkflow(workflow, context.trainee, context.program, context.reviews, context.users);
        this.processValidationReport(results, results.validationReport);
      }

      // Compliance checks
      if (this.config.enableComplianceChecks) {
        results.complianceReport = checkWorkflowCompliance(workflow, context.trainee, context.program, context.users);
        this.processComplianceReport(results, results.complianceReport);
      }

      // Audit checks
      if (this.config.enableAuditChecks) {
        const auditResult = auditWorkflows([workflow], context.trainee ? [context.trainee] : [], context.program ? [context.program] : [], context.reviews || [], context.users);
        results.auditReport = auditResult;
        this.processAuditReport(results, results.auditReport);
      }

      // Custom validation rules
      for (const rule of this.customRules) {
        try {
          const ruleResult = rule.validate(workflow, context);
          results.customValidationResults.push(ruleResult);
          this.processCustomValidationResult(results, ruleResult);
        } catch (error) {
          console.error(`Error executing custom rule ${rule.id}:`, error);
        }
      }

      // Update overall validity
      results.isValid = !results.hasErrors;

    } catch (error) {
      console.error('Error during workflow validation:', error);
      results.isValid = false;
      results.hasErrors = true;
      results.summary.errorCount++;
      results.summary.totalIssues++;
    }

    return results;
  }

  /**
   * Validates multiple workflows in batch
   */
  async validateWorkflowsBatch(
    workflows: ApprovalWorkflow[],
    getContext: (workflow: ApprovalWorkflow) => Promise<ValidationContext>
  ): Promise<ComprehensiveValidationResult[]> {
    const results: ComprehensiveValidationResult[] = [];

    for (const workflow of workflows) {
      try {
        const context = await getContext(workflow);
        const result = await this.validateWorkflowComprehensive(workflow, context);
        results.push(result);
      } catch (error) {
        console.error(`Error validating workflow ${workflow.id}:`, error);
        results.push({
          workflowId: workflow.id,
          isValid: false,
          hasErrors: true,
          hasWarnings: false,
          customValidationResults: [],
          summary: {
            totalIssues: 1,
            errorCount: 1,
            warningCount: 0,
            infoCount: 0
          }
        });
      }
    }

    return results;
  }

  /**
   * Gets validation summary for multiple workflows
   */
  getValidationSummary(results: ComprehensiveValidationResult[]) {
    return {
      totalWorkflows: results.length,
      validWorkflows: results.filter(r => r.isValid).length,
      invalidWorkflows: results.filter(r => !r.isValid).length,
      workflowsWithErrors: results.filter(r => r.hasErrors).length,
      workflowsWithWarnings: results.filter(r => r.hasWarnings).length,
      totalIssues: results.reduce((sum, r) => sum + r.summary.totalIssues, 0),
      totalErrors: results.reduce((sum, r) => sum + r.summary.errorCount, 0),
      totalWarnings: results.reduce((sum, r) => sum + r.summary.warningCount, 0),
      validationRate: results.length > 0 ? (results.filter(r => r.isValid).length / results.length) * 100 : 0
    };
  }

  /**
   * Adds a custom validation rule
   */
  addCustomRule(rule: ValidationRule): void {
    this.customRules.push(rule);
  }

  /**
   * Removes a custom validation rule
   */
  removeCustomRule(ruleId: string): void {
    this.customRules = this.customRules.filter(rule => rule.id !== ruleId);
  }

  /**
   * Gets all custom rules
   */
  getCustomRules(): ValidationRule[] {
    return [...this.customRules];
  }

  private processValidationReport(results: ComprehensiveValidationResult, report: WorkflowValidationReport): void {
    const failedValidations = report.validationResults.filter(r => !r.isValid);
    if (failedValidations.length > 0) {
      results.hasErrors = true;
      results.summary.errorCount += failedValidations.length;
    }
    results.summary.totalIssues += failedValidations.length;
  }

  private processComplianceReport(results: ComprehensiveValidationResult, report: ComplianceCheckResult): void {
    if (report.complianceStatus !== 'compliant') {
      results.hasWarnings = true;
      results.summary.warningCount++;
      results.summary.totalIssues++;
    }
  }

  private processAuditReport(results: ComprehensiveValidationResult, report: WorkflowAuditResult): void {
    if (report.issues.length > 0) {
      const criticalIssues = report.issues.filter(i => i.severity === 'critical');
      const highIssues = report.issues.filter(i => i.severity === 'high');
      
      if (criticalIssues.length > 0 || highIssues.length > 0) {
        results.hasErrors = true;
        results.summary.errorCount += criticalIssues.length + highIssues.length;
      }
      
      const mediumIssues = report.issues.filter(i => i.severity === 'medium');
      if (mediumIssues.length > 0) {
        results.hasWarnings = true;
        results.summary.warningCount += mediumIssues.length;
      }
      
      results.summary.totalIssues += report.issues.length;
    }
  }

  private processCustomValidationResult(results: ComprehensiveValidationResult, result: ValidationResult): void {
    if (!result.isValid) {
      switch (result.severity) {
        case 'error':
          results.hasErrors = true;
          results.summary.errorCount++;
          break;
        case 'warning':
          results.hasWarnings = true;
          results.summary.warningCount++;
          break;
        case 'info':
          results.summary.infoCount++;
          break;
      }
      results.summary.totalIssues++;
    }
  }
}

// Pre-defined custom validation rules
export const defaultCustomRules: ValidationRule[] = [
  {
    id: 'duplicate-workflow-check',
    name: 'Duplicate Workflow Detection',
    description: 'Checks for duplicate workflows for the same trainee and program',
    severity: 'warning',
    validate: (workflow: ApprovalWorkflow, context: ValidationContext): ValidationResult => {
      const duplicates = context.allWorkflows.filter(w => 
        w.id !== workflow.id &&
        w.traineeId === workflow.traineeId &&
        w.programId === workflow.programId &&
        w.status !== 'rejected'
      );

      return {
        isValid: duplicates.length === 0,
        message: duplicates.length > 0 
          ? `Found ${duplicates.length} other active workflow(s) for the same trainee and program`
          : 'No duplicate workflows found',
        severity: 'warning',
        ruleId: 'duplicate-workflow-check'
      };
    }
  },
  {
    id: 'stage-progression-check',
    name: 'Stage Progression Validation',
    description: 'Ensures stages are completed in the correct order',
    severity: 'error',
    validate: (workflow: ApprovalWorkflow, context: ValidationContext): ValidationResult => {
      const sortedStages = [...workflow.stages].sort((a, b) => a.stageNumber - b.stageNumber);
      
      for (let i = 1; i < sortedStages.length; i++) {
        const currentStage = sortedStages[i];
        const previousStage = sortedStages[i - 1];
        
        if (currentStage.status === 'approved' && previousStage.status === 'pending') {
          return {
            isValid: false,
            message: `Stage ${currentStage.stageName} is approved but previous stage ${previousStage.stageName} is still pending`,
            severity: 'error',
            ruleId: 'stage-progression-check'
          };
        }
      }

      return {
        isValid: true,
        message: 'Stage progression is valid',
        severity: 'info',
        ruleId: 'stage-progression-check'
      };
    }
  },
  {
    id: 'approval-timeline-check',
    name: 'Approval Timeline Validation',
    description: 'Checks if approvals are happening within reasonable timeframes',
    severity: 'warning',
    validate: (workflow: ApprovalWorkflow, context: ValidationContext): ValidationResult => {
      const initiatedDate = new Date(workflow.initiatedAt);
      const daysSinceInitiation = Math.floor((Date.now() - initiatedDate.getTime()) / (1000 * 60 * 60 * 24));
      
      // Flag workflows that have been pending for more than 30 days
      if (workflow.status === 'pending' && daysSinceInitiation > 30) {
        return {
          isValid: false,
          message: `Workflow has been pending for ${daysSinceInitiation} days, which exceeds the 30-day threshold`,
          severity: 'warning',
          ruleId: 'approval-timeline-check'
        };
      }

      return {
        isValid: true,
        message: 'Approval timeline is within acceptable limits',
        severity: 'info',
        ruleId: 'approval-timeline-check'
      };
    }
  }
];

// Export a default instance
export const defaultValidationService = new WorkflowValidationService({
  enableStrictValidation: true,
  enableComplianceChecks: true,
  enableAuditChecks: true,
  customRules: defaultCustomRules
});