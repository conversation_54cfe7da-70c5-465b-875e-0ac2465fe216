import React, { useEffect, useState } from 'react';
import { mockApi } from '../mockApi';
import { GraduateTraineeTracker } from '../index';

// Example usage component
const UsageExample: React.FC = () => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      try {
        const [trainees, programs, reviews, notifications, mentors] = await Promise.all([
          mockApi.fetchTrainees(),
          mockApi.fetchPrograms(),
          mockApi.fetchReviews(),
          mockApi.fetchNotifications(),
          mockApi.fetchMentors()
        ]);
        
        const currentUser = mentors[0]; // Use first mentor as current user
        
        setData({
          currentUser,
          trainees,
          programs,
          reviews,
          notifications,
          mentors
        });
        setLoading(false);
      } catch (error) {
        console.error('Error loading data:', error);
        setLoading(false);
      }
    };

    loadData();
  }, []);

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <h1>Graduate Trainee Tracker - Usage Example</h1>
      
      <div style={{ marginBottom: '2rem' }}>
        <h2>Current User</h2>
        <pre>{JSON.stringify(data?.currentUser, null, 2)}</pre>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h2>Trainees</h2>
        <pre>{JSON.stringify(data?.trainees?.slice(0, 2), null, 2)}</pre>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h2>Programs</h2>
        <pre>{JSON.stringify(data?.programs?.slice(0, 2), null, 2)}</pre>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h2>Reviews</h2>
        <pre>{JSON.stringify(data?.reviews?.slice(0, 2), null, 2)}</pre>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h2>Notifications</h2>
        <pre>{JSON.stringify(data?.notifications?.slice(0, 2), null, 2)}</pre>
      </div>

      <hr />
      
      <div>
        <h2>Full Component</h2>
        <GraduateTraineeTracker
          currentUser={data?.currentUser}
          onNavigate={(path) => console.log('Navigate to:', path)}
        />
      </div>
    </div>
  );
};

export default UsageExample;