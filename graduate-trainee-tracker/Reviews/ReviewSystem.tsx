import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { QuarterlyReview, Trainee, User, ReviewStatus, ReviewType } from '../types';

interface ReviewSystemProps {
  reviews: QuarterlyReview[];
  trainees: Trainee[];
  currentUser: User;
  onSubmitReview: (review: QuarterlyReview) => void;
  selectedTrainee?: Trainee;
}

export const ReviewSystem: React.FC<ReviewSystemProps> = ({
  reviews,
  trainees,
  currentUser,
  onSubmitReview,
  selectedTrainee
}) => {
  const [activeTab, setActiveTab] = useState<'pending' | 'completed' | 'scheduled'>('pending');
  const [selectedReview, setSelectedReview] = useState<QuarterlyReview | null>(null);
  const [showCreateReview, setShowCreateReview] = useState(false);

  const getReviewsForTab = () => {
    let filteredReviews = reviews;
    
    if (selectedTrainee) {
      filteredReviews = filteredReviews.filter(review => review.traineeId === selectedTrainee.id);
    }

    switch (activeTab) {
      case 'pending':
        return filteredReviews.filter(review => 
          review.status === ReviewStatus.SCHEDULED || 
          review.status === ReviewStatus.IN_PROGRESS
        );
      case 'completed':
        return filteredReviews.filter(review => review.status === ReviewStatus.COMPLETED);
      case 'scheduled':
        return filteredReviews.filter(review => review.status === ReviewStatus.SCHEDULED);
      default:
        return filteredReviews;
    }
  };

  const getStatusColor = (status: ReviewStatus) => {
    const colors = {
      [ReviewStatus.SCHEDULED]: 'bg-blue-100 text-blue-800',
      [ReviewStatus.IN_PROGRESS]: 'bg-yellow-100 text-yellow-800',
      [ReviewStatus.COMPLETED]: 'bg-green-100 text-green-800',
      [ReviewStatus.OVERDUE]: 'bg-red-100 text-red-800',
      [ReviewStatus.CANCELLED]: 'bg-gray-100 text-gray-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getReviewTypeColor = (type: ReviewType) => {
    const colors = {
      [ReviewType.QUARTERLY]: 'bg-purple-100 text-purple-800',
      [ReviewType.MID_TERM]: 'bg-orange-100 text-orange-800',
      [ReviewType.FINAL]: 'bg-indigo-100 text-indigo-800',
      [ReviewType.SPECIAL]: 'bg-pink-100 text-pink-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(new Date(date));
  };

  const filteredReviews = getReviewsForTab();

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              {selectedTrainee ? `Reviews for ${selectedTrainee.firstName} ${selectedTrainee.lastName}` : 'Review System'}
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Manage quarterly reviews and assessments
            </p>
          </div>
          <button
            onClick={() => setShowCreateReview(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
          >
            Schedule Review
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="mb-6">
        <nav className="flex space-x-8" aria-label="Tabs">
          {[
            { key: 'pending', label: 'Pending', count: reviews.filter(r => r.status === ReviewStatus.SCHEDULED || r.status === ReviewStatus.IN_PROGRESS).length },
            { key: 'completed', label: 'Completed', count: reviews.filter(r => r.status === ReviewStatus.COMPLETED).length },
            { key: 'scheduled', label: 'Scheduled', count: reviews.filter(r => r.status === ReviewStatus.SCHEDULED).length }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`${
                activeTab === tab.key
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
            >
              <span>{tab.label}</span>
              <span className={`${
                activeTab === tab.key ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
              } inline-flex items-center justify-center px-2 py-1 text-xs font-medium rounded-full`}>
                {tab.count}
              </span>
            </button>
          ))}
        </nav>
      </div>

      {/* Reviews List */}
      <div className="space-y-4">
        {filteredReviews.map((review) => {
          const trainee = trainees.find(t => t.id === review.traineeId);
          const reviewer = review.reviewerId ? { id: review.reviewerId, firstName: 'Reviewer', lastName: review.reviewerId } : null;
          
          return (
            <motion.div
              key={review.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => setSelectedReview(review)}
            >
              <div className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-medium text-gray-900">
                        {trainee ? `${trainee.firstName} ${trainee.lastName}` : 'Unknown Trainee'}
                      </h3>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getReviewTypeColor(review.type)}`}>
                        {review.type.replace('_', ' ')}
                      </span>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(review.status)}`}>
                        {review.status.replace('_', ' ')}
                      </span>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                      <div className="flex items-center">
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 2m8-2l2 2m-2-2v10a2 2 0 01-2 2H10a2 2 0 01-2-2V9" />
                        </svg>
                        Quarter {review.quarter}
                      </div>
                      <div className="flex items-center">
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 2m8-2l2 2m-2-2v10a2 2 0 01-2 2H10a2 2 0 01-2-2V9" />
                        </svg>
                        Due: {formatDate(new Date(review.dueDate))}
                      </div>
                      <div className="flex items-center">
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        Reviewer: {reviewer?.firstName} {reviewer?.lastName}
                      </div>
                    </div>
                    {review.overallScore && (
                      <div className="mt-3">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-gray-700">Overall Score:</span>
                          <div className="flex items-center space-x-1">
                            <span className="text-lg font-bold text-blue-600">{review.overallScore}</span>
                            <span className="text-sm text-gray-500">/5</span>
                          </div>
                          <div className="flex-1 bg-gray-200 rounded-full h-2 ml-3">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${(review.overallScore / 5) * 100}%` }}
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <button className="p-2 text-gray-400 hover:text-gray-600">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                      </svg>
                    </button>
                    <button className="p-2 text-gray-400 hover:text-red-600">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>

      {filteredReviews.length === 0 && (
        <div className="text-center py-12">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No reviews found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {activeTab === 'pending' 
              ? 'No pending reviews at the moment.'
              : activeTab === 'completed'
              ? 'No completed reviews yet.'
              : 'No scheduled reviews found.'}
          </p>
          <div className="mt-6">
            <button
              onClick={() => setShowCreateReview(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              Schedule Review
            </button>
          </div>
        </div>
      )}
    </div>
  );
};